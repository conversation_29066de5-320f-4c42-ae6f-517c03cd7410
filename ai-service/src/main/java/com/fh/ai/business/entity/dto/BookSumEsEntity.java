package com.fh.ai.business.entity.dto;

import com.fh.ai.common.annotation.EsTable;
import lombok.Data;

import java.io.Serializable;

//import org.springframework.data.annotation.Id;
//import org.springframework.data.elasticsearch.annotations.Document;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Data
@EsTable(value = "book_sum")
public class BookSumEsEntity implements Serializable {
    private static final long serialVersionUID = 2867676404517638380L;

    private String name;

    private String Isbn;

    private Long edition;

    private Long printing;

//    private String sum-chatglm;
}
