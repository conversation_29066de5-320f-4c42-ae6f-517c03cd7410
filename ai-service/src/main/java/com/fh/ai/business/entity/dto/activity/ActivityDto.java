package com.fh.ai.business.entity.dto.activity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动表
 * 
 * <AUTHOR>
 * @date 2024-05-13 14:48:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_activity")
public class ActivityDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 活动id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 位置，1历史记录，2积分中心-文字，3积分中心-赚积分 11 首页弹窗,12 通栏广告,13 任务中心文字
	 */
	@TableField("position")
	private Integer position;

	/**
	 * 类型 1图片 2文本
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 详情
	 */
	@TableField("details")
	private String details;

	/**
	 * 状态 1上架 2下架
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("frequency")
	private Integer frequency;

	@TableField("jump_url")
	private String jumpUrl;

	/**
	 * 发布时间
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 展示类型 1-web 2-h5 3-web和h5
	 */
	@TableField("show_type")
	private Integer showType;

	/**
	 * h5内容
	 */
	@TableField("h5_content")
	private String h5Content;

	/**
	 * h5跳转地址
	 */
	@TableField("h5_jump_url")
	private String h5JumpUrl;

	/**
	 * 广告详情页开关 1-开启 2-关闭
	 */
	@TableField("detail_switch")
	private Integer detailSwitch;

	/**
	 * 广告详情页内容
	 */
	@TableField("detail_content")
	private String detailContent;

	/**
	 * 跳转类型 1-无跳转 2-链接 3-意见反馈
	 */
	@TableField("jump_type")
	private Integer jumpType;
}