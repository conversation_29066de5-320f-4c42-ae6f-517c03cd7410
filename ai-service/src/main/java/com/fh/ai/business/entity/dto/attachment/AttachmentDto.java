package com.fh.ai.business.entity.dto.attachment;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 附件表
 * 
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_attachment")
public class AttachmentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 文件oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 原文件名
	 */
	@TableField("original_name")
	private String originalName;

	/**
	 * 新文件名
	 */
	@TableField("new_name")
	private String newName;

	/**
	 * 后缀名
	 */
	@TableField("suffix")
	private String suffix;

	/**
	 * 大小
	 */
	@TableField("size")
	private Long size;

	/**
	 * 封面
	 */
	@TableField("cover")
	private String cover;

	/**
	 * 原始文件路径
	 */
	@TableField("origin_path")
	private String originPath;

	/**
	 * 预览路径
	 */
	@TableField("view_path")
	private String viewPath;

	/**
	 * 时长
	 */
	@TableField("duration")
	private Long duration;

	/**
	 * 千问文件id
	 */
	@TableField("qwen_file_id")
	private String qwenFileId;

	/**
	 * 千问删除状态，1：正常，2：删除
	 */
	@TableField("qwen_delete_state")
	private Integer qwenDeleteState;

	/**
	 * 上传状态，1：上传中，2：上传成功，3：上传失败
	 */
	@TableField("upload_state")
	private Integer uploadState;

	/**
	 * 状态，1：待处理，2：处理中，3：处理成功，4：处理失败
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 备注
	 */
	@TableField("note")
	private String note;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;


	/**
	 * 一个异步任务的id。具体是什么异步任务，通过appType关联。也可以通过会话code知晓
	 */
	@TableField("task_id")
	private String taskId;
	/**
	 * 任务的类型，关联到枚举类：ConversationTaskType
	 */
	@TableField("task_type")
	private Integer taskType;

	/**
	 * 第三方文件url
	 */
	@TableField("third_file_url")
	private String thirdFileUrl;

	/**
	 * 第三方文件url的md5值，方便查询是否是同一个url
	 */
	@TableField("third_file_url_md5")
	private String thirdFileUrlMd5;

}