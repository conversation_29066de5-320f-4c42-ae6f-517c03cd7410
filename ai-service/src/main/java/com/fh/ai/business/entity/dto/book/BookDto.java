package com.fh.ai.business.entity.dto.book;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 书籍信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book")
public class BookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 书籍数据来源来行：1其他，2当当，3微信，4豆瓣，5书苑，6开卷，7图书库，8抖音，9京东
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 书籍排序
	 */
	@TableField("sort_index")
	private Long sortIndex;

	/**
	 * 书籍名称
	 */
	@TableField("book_name")
	private String bookName;

	/**
	 * 书籍介绍
	 */
	@TableField("book_description")
	private String bookDescription;

	/**
	 * 书籍作者
	 */
	@TableField("book_author")
	private String bookAuthor;

	/**
	 * 书籍分类
	 */
	@TableField("book_category")
	private String bookCategory;

	/**
	 * 书籍分类，按照层级
	 */
	@TableField("book_category_all")
	private String bookCategoryAll;

	/**
	 * 书籍标签，多个用英文逗号
	 */
	@TableField("book_label")
	private String bookLabel;

	/**
	 * 出版时间，格式为yyyy年MM月
	 */
	@TableField("publish_time_str")
	private String publishTimeStr;

	/**
	 * 出版社名称
	 */
	@TableField("publish_name")
	private String publishName;

	/**
	 * 书籍isbn
	 */
	@TableField("book_isbn")
	private String bookIsbn;

	/**
	 * 书籍推荐语
	 */
	@TableField("book_recommendation")
	private String bookRecommendation;

	/**
	 * 书籍封面图地址
	 */
	@TableField("book_cover")
	private String bookCover;

	/**
	 * 书籍在第三方系统的id，与source_type结合使用
	 */
	@TableField("book_third_id")
	private String bookThirdId;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 书籍推荐值格式为：87.0，前端要拼上%
	 */
	@TableField("book_recommend_rating")
	private String bookRecommendRating;

	/**
	 * 书籍价格
	 */
	@TableField("book_price")
	private String bookPrice;

	/**
	 * 店铺
	 */
	@TableField("book_shop")
	private String bookShop;

	/**
	 * 销售数量
	 */
	@TableField("sales_count")
	private String salesCount;

	/**
	 * 录入方式：1程序爬取，2后台手动录入
	 */
	@TableField("input_type")
	private Integer inputType;

	/**
	 * 封面类型：1url地址，2本地上传文件的url地址
	 */
	@TableField("cover_type")
	private Integer coverType;

	/**
	 * 出版集团
	 */
	@TableField("publish_group")
	private String publishGroup;

	/**
	 * 出版日期
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 描述处理备注（用字符串备注：例如y）
	 */
	@TableField("book_description_handle_rmark")
	private String bookDescriptionHandleRmark;

	/**
	 * 关键字标签-从介绍总结
	 */
	@TableField("keywords")
	private String keywords;

	/**
	 * 删除原因
	 */
	@TableField("delete_reason")
	private String deleteReason;
}
