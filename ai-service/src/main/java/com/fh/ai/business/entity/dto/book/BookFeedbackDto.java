package com.fh.ai.business.entity.dto.book;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 书籍反馈表
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  15:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_feedback")
public class BookFeedbackDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 书籍id
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 书籍名称
     */
    @TableField("book_name")
    private String bookName;

    /**
     * isbn号
     */
    @TableField("book_isbn")
    private String bookIsbn;

    /**
     * 书籍来源 同p_book表source_type
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 违禁点
     */
    @TableField("remark")
    private String remark;

    /**
     * 用户oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private java.util.Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private java.util.Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;
}
