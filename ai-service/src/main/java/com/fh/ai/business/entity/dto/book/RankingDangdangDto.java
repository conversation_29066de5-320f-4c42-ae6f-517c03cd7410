package com.fh.ai.business.entity.dto.book;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 当当榜单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_ranking_dangdang")
public class RankingDangdangDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 榜单类型：1总榜all，2飙升rising，3新书newbook
	 */
	@TableField("ranking_type")
	private Integer rankingType;

	/**
	 * 榜单排序
	 */
	@TableField("sort_index")
	private Long sortIndex;

	/**
	 * 书籍id,p_book的id
	 */
	@TableField("book_id")
	private Long bookId;

	/**
	 * 榜单相比较于昨日上升xx位
	 */
	@TableField("ranking_up")
	private Long rankingUp;

	/**
	 * 榜单相比较于昨日下降xx位
	 */
	@TableField("ranking_down")
	private Long rankingDown;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 一次任务的uuid
	 */
	@TableField("uuid")
	private String uuid;
	/**
	 * 收集数据日期
	 */
	@TableField("collect_time")
	private Date collectTime;
	/**
	 * 飙升指数
	 */
	@TableField("rising_index")
	private BigDecimal risingIndex;

}
