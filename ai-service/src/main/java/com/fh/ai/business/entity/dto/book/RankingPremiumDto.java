package com.fh.ai.business.entity.dto.book;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 精品书（奖项）表（不含凤凰好书）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_ranking_premium")
public class RankingPremiumDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 榜单类型：1老舍文学奖，2矛盾文学将，3鲁迅文学奖，4曹禺戏剧文学奖，5中国出版政府奖，6中华优秀出版物将，7五个一工程将，8年度好书（按年），9诺贝尔文学奖（按年），10中国好书
	 */
	@TableField("ranking_type")
	private Integer rankingType;

	/**
	 * 分组类型：1中国四大文学奖，2中国出版国家级奖项，3中国好书，4诺贝尔文学奖
	 */
	@TableField("ranking_group_type")
	private Integer rankingGroupType;

	/**
	 * 榜单排序
	 */
	@TableField("sort_index")
	private Long sortIndex;

	/**
	 * 书籍id,p_book的id
	 */
	@TableField("book_id")
	private Long bookId;

	/**
	 * 榜单相比较于昨日上升xx位
	 */
	@TableField("ranking_up")
	private Long rankingUp;

	/**
	 * 榜单相比较于昨日下降xx位
	 */
	@TableField("ranking_down")
	private Long rankingDown;

	/**
	 * 榜单年分份
	 */
	@TableField("ranking_year")
	private Long rankingYear;

	/**
	 * 榜单季度，1-4
	 */
	@TableField("ranking_quarter")
	private Long rankingQuarter;

	/**
	 * 榜单月份，1-12
	 */
	@TableField("ranking_month")
	private Long rankingMonth;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 一次任务的uuid
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 收集数据日期
	 */
	@TableField("collect_time")
	private Date collectTime;

	/**
	 * 飙升指数
	 */
	@TableField("rising_index")
	private BigDecimal risingIndex;

	/**
	 * 分项名称,奖项名称用ranking_type对应的名称
	 */
	@TableField("sub_ranking_name")
	private String subRankingName;

	/**
	 * 获评时间
	 */
	@TableField("selection_time")
	private String selectionTime;
}
