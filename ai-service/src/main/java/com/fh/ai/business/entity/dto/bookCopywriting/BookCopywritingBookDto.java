package com.fh.ai.business.entity.dto.bookCopywriting;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 软文与书籍关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_copywriting_book")
public class BookCopywritingBookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 关系ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 软文唯一标识
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 软文ID
	 */
	@TableField("copywriting_id")
	private Long copywritingId;

	/**
	 * 书籍ID
	 */
	@TableField("book_id")
	private Long bookId;

	/**
	 * 书籍名称
	 */
	@TableField("book_name")
	private String bookName;

	/**
	 * 书籍封面图地址
	 */
	@TableField("book_cover")
	private String bookCover;

	/**
	 * 大模型返回的软文内容-仅单本书
	 */
	@TableField("model_result")
	private String modelResult;

	/**
	 * 大模型返回的软文内容（用户编辑后，如果有）-仅单本书
	 */
	@TableField("model_result_final")
	private String modelResultFinal;

	/**
	 * 大模型返回的图书卖点-多本书时候，每一本的
	 */
	@TableField("selling_points")
	private String sellingPoints;

	/**
	 * 最终卖点（用户编辑后，如果有）-多本书时候，每一本的
	 */
	@TableField("selling_points_final")
	private String sellingPointsFinal;

	/**
	 * 生成状态 1-待生成 2-生成中 3-成功 4-失败。-多本书时候，每一本的
	 */
	@TableField("generate_status")
	private Integer generateStatus;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
