package com.fh.ai.business.entity.dto.bookCopywriting;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 书籍软文表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_copywriting")
public class BookCopywritingDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 软文ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 软文唯一标识
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 软文标题（用户输入或模型生成）
	 */
	@TableField("title")
	private String title;

	/**
	 * 关联书单ID（若基于书单）
	 */
	@TableField("book_list_id")
	private Long bookListId;

	/**
	 * 关联上传书单文件的fileOid
	 */
	@TableField("upload_file_oid")
	private String uploadFileOid;

	/**
	 * 内容类型 1-单本书 2-多本书
	 */
	@TableField("content_type")
	private Integer contentType;

	/**
	 * 来源类型 1-书籍 2-书单 3-上传 4-其他
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 目标受众（如“职场新人”“学生”）
	 */
	@TableField("target_audience")
	private String targetAudience;

	/**
	 * 写作风格 1-微信公众号 2-小红书
	 */
	@TableField("writing_style_type")
	private Integer writingStyleType;

	/**
	 * 营销参考类型 0-无 1-新闻热点 2-节假日 3-活动推广
	 */
	@TableField("reference_type")
	private Integer referenceType;

	/**
	 * 参考标签/关键词（如“双十一,励志”）
	 */
	@TableField("reference_words")
	private String referenceWords;

	/**
	 * 涉及主题（与书单表一致）
	 */
	@TableField("recommend_topic")
	private String recommendTopic;

	/**
	 * 生成条件JSON（如受众、风格、关键词等）
	 */
	@TableField("condition_json")
	private String conditionJson;

	/**
	 * 总体大模型返回的软文内容（content_type=2有值）
	 */
	@TableField("overall_selling_points")
	private String overallSellingPoints;

	/**
	 * 总体最终软文内容（用户编辑后，如果有）（content_type=2有值）
	 */
	@TableField("overall_selling_points_final")
	private String overallSellingPointsFinal;

	/**
	 * 总体生成状态 1-待生成 2-生成中 3-成功 4-失败（content_type=2有值）
	 */
	@TableField("overall_generate_status")
	private Integer overallGenerateStatus;

	/**
	 * 大模型返回的软文内容-仅单本书
	 */
	@TableField("model_result")
	private String modelResult;

	/**
	 * 大模型返回的软文内容（用户编辑后，如果有）-仅单本书
	 */
	@TableField("model_result_final")
	private String modelResultFinal;

	/**
	 * 排序
	 */
	@TableField("sort")
	private Long sort;

	/**
	 * 用户标识，表示是谁的软文，通常是创建人
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
