package com.fh.ai.business.entity.dto.bookList;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 书单表
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  11:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_list")
public class BookListDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 书单id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 书单唯一标识
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 书单名称
     */
    @TableField("name")
    private String name;

    /**
     * 营销参考类型 1-新闻热点 2-节假日
     */
    @TableField("reference_type")
    private Integer referenceType;

    /**
     * 生成条件-参考标签
     */
    @TableField("reference_words")
    private String referenceWords;

    /**
     * 生成条件-涉及主题
     */
    @TableField("recommend_topic")
    private String recommendTopic;

    /**
     * 书单类型分析内容
     */
    @TableField("analysis")
    private String analysis;

    /**
     * 条件json
     */
    @TableField("condition_json")
    private String conditionJson;

    /**
     * 大模型返回结果
     */
    @TableField("model_result")
    private String modelResult;

    /**
     * 书单json
     */
    @TableField("book_list_json")
    private String bookListJson;

    /**
     * 书单json转换状态 1-未转换 2-成功 3-失败
     */
    @TableField("convert_state")
    private Integer convertState;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;



}
