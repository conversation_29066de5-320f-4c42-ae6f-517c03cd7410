package com.fh.ai.business.entity.dto.bookList;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 书单json临时存储表
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  11:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_list_json_temporary_storage")
public class BookListJsonTemporaryStorageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 书单唯一标识
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 书单json
     */
    @TableField("book_list_json")
    private String bookListJson;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;
}
