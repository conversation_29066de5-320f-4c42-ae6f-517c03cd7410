package com.fh.ai.business.entity.dto.bookListPoster;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 书单海报表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-07 10:00:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_book_list_poster")
public class BookListPosterDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 书单海报id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 书单唯一标识
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 书单主键
	 */
	@TableField("book_list_id")
	private Long bookListId;

	/**
	 * 内容类型 1-单本书 2-多本书
	 */
	@TableField("content_type")
	private Integer contentType;

	/**
	 * 来源类型 1-提示词 2-图片 3-代码 4-其他
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 条件json
	 */
	@TableField("condition_json")
	private String conditionJson;

	/**
	 * 参考网页样式
	 */
	@TableField("ref_html_style")
	private String refHtmlStyle;

	/**
	 * 配色
	 */
	@TableField("content_color")
	private String contentColor;

	/**
	 * 大模型返回结果
	 */
	@TableField("model_result")
	private String modelResult;

	/**
	 * 书单海报json
	 */
	@TableField("book_list_poster_json")
	private String bookListPosterJson;

	/**
	 * 书单海报名称
	 */
	@TableField("book_list_poster_name")
	private String bookListPosterName;

	/**
	 * 排序
	 */
	@TableField("sort")
	private Integer sort;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

}
