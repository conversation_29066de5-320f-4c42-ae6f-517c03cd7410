package com.fh.ai.business.entity.dto.calendarEvent;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 日历事件表
 * 
 * <AUTHOR>
 * @date 2024-07-02 14:21:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_calendar_event")
public class CalendarEventDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 日期
	 */
	@TableField("day")
	private String day;

	/**
	 * 年份
	 */
	@TableField("year")
	private Long year;

	/**
	 * 类型：1公历、2农历、3节气、4国际节日
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否推荐展示 1-推荐 2-不推荐
	 */
	@TableField("recommend_type")
	private Integer recommendType;

	/**
	 * 介绍
	 */
	@TableField("introduce")
	private String introduce;

	/**
	 * 大模型智能分析后的结果
	 */
	@TableField("model_analysis")
	private String modelAnalysis;

}