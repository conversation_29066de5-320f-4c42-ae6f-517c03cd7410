package com.fh.ai.business.entity.dto.calendarEvent;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 营销节点
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-09 11:42:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_marketing_node")
public class MarketingNodeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 年月日。yyyy-MM-dd
	 */
	@TableField("day")
	private String day;

	/**
	 * 年份
	 */
	@TableField("year")
	private Long year;

	/**
	 * 月份
	 */
	@TableField("month")
	private Long month;

	/**
	 * 营销关键节点
	 */
	@TableField("marketing_key_points")
	private String marketingKeyPoints;

	/**
	 * 营销关键词
	 */
	@TableField("marketing_key_words")
	private String marketingKeyWords;

	/**
	 * 活动关键节点
	 */
	@TableField("activity_key_points")
	private String activityKeyPoints;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
