package com.fh.ai.business.entity.dto.config;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-02 09:40:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_config")
public class ConfigDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 参数配置id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 参数配置key
	 */
	@TableField("config_key")
	private String configKey;

	/**
	 * 参数配置value
	 */
	@TableField("config_value")
	private String configValue;

	/**
	 * 参数配置说明
	 */
	@TableField("config_description")
	private String configDescription;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	public ConfigDto returnOwn(){
		return this;
	}

}
