package com.fh.ai.business.entity.dto.conversation;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会话表
 * 
 * <AUTHOR>
 * @date 2024-04-17 16:49:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_conversation")
public class ConversationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 对话码
	 */
	@TableField("conversation_code")
	private String conversationCode;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策
	 */
	@TableField("sub_type")
	private Integer subType;

	/**
	 * 提问类型：1人工，2自动
	 */
	@TableField("ask_type")
	private Integer askType;

	/**
	 * 提示词
	 */
	@TableField("prompt")
	private String prompt;

	/**
	 * 原始问题。用户每次提问的问题，注意一个会话多次提问这里会多次更新。
	 */
	@TableField("original_question")
	private String originalQuestion;

	/**
	 * 标题。新增的时候会获取第一次问的message，然后插入到title，后面不会更新，用作用户提问的第一个问题（会带有提示词，如果不想要提示词的，则用title减去提示词即可）。
	 */
	@TableField("title")
	private String title;

	/**
	 * 消息。每次用户提问的问题（前端传参）+提示词（后台入库前拼上的）
	 */
	@TableField("message")
	private String message;

	/**
	 * 参数json
	 */
	@TableField("parameter_json")
	private String parameterJson;

	/**
	 * 上下文
	 */
	@TableField("context")
	private String context;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * coze的会话id
	 */
	@TableField("coze_conversation_id")
	private String cozeConversationId;
	/**
	 * coze的机器人id
	 */
	@TableField("coze_bot_id")
	private String cozeBotId;
	/**
	 * 业务id，用于将会话和业务id关联(具体什么业务看type)
	 */
	@TableField("business_id")
	private String businessId;
	/**
	 * 业务json
	 */
	@TableField("business_json")
	private String businessJson;

	/**
	 * 乐学书籍id
	 */
	@TableField("book_id")
	private String bookId;

	/**
	 * 业务json，存储扩展信息【备份】
	 */
	@TableField("business_json_bak")
	private String businessJsonBak;

	/**
	 * 自定义参数json（会带到本次的问答history里面）
	 */
	@TableField("custom_parameter_json")
	private String customParameterJson;

	/**
	 * coze的工作流id
	 */
	@TableField("coze_workflow_id")
	private String cozeWorkflowId;
}