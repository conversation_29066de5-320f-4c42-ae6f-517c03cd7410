package com.fh.ai.business.entity.dto.conversationFile;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会话文件表
 * 
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_conversation_file")
public class ConversationFileDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 对话码
	 */
	@TableField("conversation_code")
	private String conversationCode;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 文件名称
	 */
	@TableField("file_name")
	private String fileName;

	/**
	 * 文件oid
	 */
	@TableField("file_oid")
	private String fileOid;

	/**
	 * 千问文件id
	 */
	@TableField("qwen_file_id")
	private String qwenFileId;

	/**
	 * 千问状态：1上传成功，2处理中，3处理成功，4处理失败
	 */
	@TableField("qwen_state")
	private Integer qwenState;

	/**
	 * 讯飞订单id
	 */
	@TableField("xf_order_id")
	private String xfOrderId;

	/**
	 * 讯飞状态：1上传成功，2处理中，3处理成功，4处理失败
	 */
	@TableField("xf_state")
	private Integer xfState;

	/**
	 * 内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 一个异步任务的id。具体是什么异步任务，通过appType关联。也可以通过会话code知晓
	 */
	@TableField("task_id")
	private String taskId;
	/**
	 * 任务的类型，关联到枚举类：ConversationTaskType
	 */
	@TableField("task_type")
	private Integer taskType;
	/**
	 * 任务的状态：1上传成功，2处理中，3处理成功，4处理失败
	 */
	@TableField("task_state")
	private Integer taskState;

	/**
	 * 记录id，标识本次task任务和哪一次历史对话有关
	 */
	@TableField("history_id")
	private Long historyId;

	/**
	 * 业务id
	 */
	@TableField("business_id")
	private String businessId;

	/**
	 * 任务内容结果，例如kimi存储文件内容
	 */
	@TableField("task_result")
	private String taskResult;

	/**
	 * 文件内容缓存id
	 */
	@TableField("file_content_cache_id")
	private Long fileContentCacheId;

	/**
	 * ffmpeg处理状态 1-处理中 2-成功 3-失败 4-不需要处理
	 */
	@TableField("ffmpeg_state")
	private Integer ffmpegState;

	/**
	 * ffmpeg转换后的文件路径
	 */
	@TableField("ffmpeg_convert_result")
	private String ffmpegConvertResult;
}