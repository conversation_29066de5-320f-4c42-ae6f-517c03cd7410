package com.fh.ai.business.entity.dto.courseDetail;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程详情
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_course_detail")
public class CourseDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 课程id
	 */
	@TableField("course_id")
	private Long courseId;

	@TableField("exam_paper_id")
	private Long examPaperId;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 播放地址
	 */
	@TableField("url")
	private String url;

	/**
	 * 时长
	 */
	@TableField("duration")
	private String duration;

	@TableField("source_text")
	private String sourceText;

	/**
	 * 作者
	 */
	@TableField("author")
	private String author;

	/**
	 * 排序
	 */
	@TableField("sort")
	private Integer sort;

	@TableField("state")
	private Integer state;

	/**
	 * 封面地址
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
