package com.fh.ai.business.entity.dto.evaluatingRecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 测评记录
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-10-16  10:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_evaluating_record")
public class EvaluatingRecordDto {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 测评任务uid【系统生成】
     */
    @TableField("task_uid")
    private String taskUid;

    /**
     * 语音转文本第三方任务id
     */
    @TableField("audio_to_text_task_uid")
    private String audioToTextTaskUid;

    /**
     * 音频url
     */
    @TableField("audio_url")
    private String audioUrl;

    /**
     * 测评文本
     */
    @TableField("contents")
    private String contents;

    /**
     * 测评类型 1-中文测评 2-英文测评
     */
    @TableField("type")
    private Integer type;

    /**
     * 测评结果
     */
    @TableField("evaluating_result")
    private String evaluatingResult;

    /**
     * 测评状态 1-等待语音转文本 2-待测评 3-测评完成
     */
    @TableField("state")
    private Integer state;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 第三方请求唯一id，用于回调第三方接口
     */
    @TableField("third_unique_id")
    private String thirdUniqueId;

    /**
     * 回调结果
     */
    @TableField("return_result")
    private String returnResult;
}
