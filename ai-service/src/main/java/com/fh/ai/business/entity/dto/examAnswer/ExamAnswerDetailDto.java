package com.fh.ai.business.entity.dto.examAnswer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 题库表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_exam_answer_detail")
public class ExamAnswerDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	@TableField("exam_answer_id")
	private Long examAnswerId;

	@TableField("course_detail_id")
	private Long courseDetailId;

	/**
	 * 试卷id
	 */
	@TableField("exam_paper_id")
	private Long examPaperId;

	/**
	 * 题目id
	 */
	@TableField("question_id")
	private Long questionId;

	/**
	 * 答案
	 */
	@TableField("content")
	private String content;

	/**
	 * 题型
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 是否删除，1：正确，2：错误
	 */
	@TableField("is_right")
	private Integer isRight;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
