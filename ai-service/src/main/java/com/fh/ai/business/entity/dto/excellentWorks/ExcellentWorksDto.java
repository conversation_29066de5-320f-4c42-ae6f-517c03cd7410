package com.fh.ai.business.entity.dto.excellentWorks;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_excellent_works")
public class ExcellentWorksDto {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id，作品关联的活动。
     */
    @TableField("works_active_id")
    private Long worksActiveId;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 组织id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 统计组织id
     */
    @TableField("statistics_organization_id")
    private Long statisticsOrganizationId;

    /**
     * 作品名称
     */
    @TableField("works_name")
    private String worksName;

    /**
     * 作者名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 所属组织名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 审核类型 1-审核中 2-审核通过 3-审核不通过
     */
    @TableField("audit_type")
    private Integer auditType;

    /**
     * 上下架类型：1下架，2上架
     */
    @TableField("hold_type")
    private Integer holdType;

    /**
     * 应用类型
     */
    @TableField("app_type")
    private Integer appType;

    /**
     * 作品类型 1-图片 2-混合类型
     */
    @TableField("works_type")
    private Integer worksType;

    /**
     * 作品简介
     */
    @TableField("works_profile")
    private String worksProfile;

    /**
     * 作品说明
     */
    @TableField("works_describe")
    private String worksDescribe;

    /**
     * 数据来源：1运营添加，2用户投稿
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 业务Id
     */
    @TableField(value = "business_id")
    private Long businessId;

    /**
     * 基础模型
     */
    @TableField("base_model")
    private String baseModel;

    /**
     * 生图模型
     */
    @TableField("image_gen_model")
    private String imageGenModel;

    /**
     * 风格模型
     */
    @TableField("style_model")
    private String styleModel;

    /**
     * 提示词
     */
    @TableField("prompts")
    private String prompts;

    /**
     * 反向提示词
     */
    @TableField("negative_prompt")
    private String negativePrompt;

    /**
     * 用户参数
     */
    @TableField("params")
    private String params;


    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 编号
     */
    @TableField("excellent_works_number")
    private Integer excellentWorksNumber;
}
