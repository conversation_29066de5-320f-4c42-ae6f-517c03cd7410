package com.fh.ai.business.entity.dto.excellentWorksDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-14  11:09
 */
@Data
@TableName("p_excellent_works_detail")
public class ExcellentWorksDetailDto {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 优秀作品表id
     */
    @TableField("excellent_works_id")
    private Long excellentWorksId;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 组织id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 一个媒体的类型 1-图片 2-文档word
     */
    @TableField("works_media_type")
    private Integer worksMediaType;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 文件oid
     */
    @TableField("file_oid")
    private String fileOid;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件url
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 压缩文件oid
     */
    @TableField("compress_file_oid")
    private String compressFileOid;

    /**
     * 压缩文件名称
     */
    @TableField("compress_file_name")
    private String compressFileName;

    /**
     * 压缩文件url
     */
    @TableField("compress_file_url")
    private String compressFileUrl;

    /**
     * 封面oid
     */
    @TableField("conver_oid")
    private String converOid;

    /**
     * 封面地址
     */
    @TableField("conver_url")
    private String converUrl;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;
}
