package com.fh.ai.business.entity.dto.excellentWorksPrize;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户作品获奖记录（和奖品表没关系）
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-21 17:59:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_excellent_works_prize")
public class ExcellentWorksPrizeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * FK，优秀作品表id
	 */
	@TableField("excellent_works_id")
	private Long excellentWorksId;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 获得奖品的原因
	 */
	@TableField("prize_reason")
	private String prizeReason;

	/**
	 * 奖品-积分
	 */
	@TableField("prize_score")
	private Long prizeScore;

	/**
	 * 奖品-卡券信息
	 */
	@TableField("prize_card_info")
	private String prizeCardInfo;

	/**
	 * 奖品类型：1积分，2奖品，3积分和奖品都有
	 */
	@TableField("prize_type")
	private Integer prizeType;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
