package com.fh.ai.business.entity.dto.flowRecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程记录表dto
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  09:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_flow_record")
public class FlowRecordDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 流程唯一code
     */
    @TableField("flow_code")
    private String flowCode;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 组织id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 智能体id 1-图书商品图
     */
    @TableField("agent_id")
    private Long agentId;

    /**
     * 智能体名称
     */
    @TableField("agent_name")
    private String agentName;

    /**
     * 应用类型
     */
    @TableField("app_type")
    private Integer appType;

    /**
     * 应用唯一标识【会话code或作图主键】
     */
    @TableField("app_business_id")
    private String appBusinessId;

    /**
     * 父节点id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 流程开始id
     */
    @TableField("top_id")
    private Long topId;

    /**
     * 参数
     */
    @TableField("param")
    private String param;

    /**
     * 结果
     */
    @TableField("result")
    private String result;

    /**
     * 是否显示 1-显示 2-不显示
     */
    @TableField("show_type")
    private Integer showType;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;
}
