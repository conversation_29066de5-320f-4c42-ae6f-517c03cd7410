package com.fh.ai.business.entity.dto.fzBook;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 图书库表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:00:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_fz_book")
public class FzBookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 书名
	 */
	@TableField("SYS_TOPIC")
	private String sysTopic;

	/**
	 * ISBN
	 */
	@TableField("ISBN")
	private String isbn;

	/**
	 * 版次
	 */
	@TableField("BOOK_VERSION")
	private String bookVersion;

	/**
	 * 印次
	 */
	@TableField("PRINT_VERSION")
	private String printVersion;

	/**
	 * 作者
	 */
	@TableField("SYS_AUTHORS")
	private String sysAuthors;

	/**
	 * 图书库ID
	 */
	@TableField("SYS_DOCLIBID")
	private String sysDoclibid;

	/**
	 * 图书ID
	 */
	@TableField("SYS_DOCUMENTID")
	private String sysDocumentid;

	/**
	 * 发布时间
	 */
	@TableField("PUBDATE")
	private Date pubdate;

	/**
	 * 出版社
	 */
	@TableField("PRESS_NAME")
	private String pressName;

	/**
	 * 图书分类
	 */
	@TableField("BOOK_CAT")
	private String bookCat;

	/**
	 * 图书状态
	 */
	@TableField("SYS_CURRENTSTATUS")
	private String sysCurrentstatus;

	/**
	 * 是否凤凰好书
	 */
	@TableField("SUBTITLE")
	private String subtitle;

	/**
	 * 年份
	 */
	@TableField("EDITION")
	private String edition;

	/**
	 * 月份
	 */
	@TableField("DRAFTSMAN")
	private String draftsman;

	/**
	 * PDF样章地址
	 */
	@TableField("url")
	private String url;

	/**
	 * 封面地址
	 */
	@TableField("picUrl")
	private String picurl;

	/**
	 * 最后修改时间
	 */
	@TableField("SYS_LASTMODIFIED")
	private Date sysLastmodified;

	/**
	 * 责编
	 */
	@TableField("EDITOR")
	private String editor;

	/**
	 * 是否获奖图书（1：是，0：否）
	 */
	@TableField("is_awad")
	private Integer isAwad;

	/**
	 * 获奖图书信息
	 */
	@TableField("awad_cat")
	private String awadCat;

	/**
	 * 原始封面图
	 */
	@TableField("ori_pic_url")
	private String oriPicUrl;

	/**
	 * 单个封面文件路径
	 */
	@TableField("fm_pic_url")
	private String fmPicUrl;

	/**
	 * 封面宽
	 */
	@TableField("img_width")
	private Long imgWidth;

	/**
	 * 封面高
	 */
	@TableField("img_height")
	private Long imgHeight;

	/**
	 * 全本pdf路径
	 */
	@TableField("qb_pdf_url")
	private String qbPdfUrl;

	/**
	 * pdf样书页数
	 */
	@TableField("total_page")
	private Long totalPage;

	/**
	 * 全本pdf名称
	 */
	@TableField("qb_pdf_name")
	private String qbPdfName;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
