package com.fh.ai.business.entity.dto.historyApp;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 应用历史表
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_history_app")
public class HistoryAppDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对话码
     */
    @TableField("conversation_code")
    private String conversationCode;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
     */
    @TableField("type")
    private Integer type;

    /**
     * 提问类型：1人工，2自动
     */
    @TableField("ask_type")
    private Integer askType;

    /**
     * 子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策
     */
    @TableField("sub_type")
    private Integer subType;

    /**
     * 提示词
     */
    @TableField("prompt")
    private String prompt;

    /**
     * 原始问题
     */
    @TableField("original_question")
    private String originalQuestion;

    /**
     * 问题
     */
    @TableField("question")
    private String question;

    /**
     * 参数json
     */
    @TableField("parameter_json")
    private String parameterJson;

    /**
     * 请求id
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 结果
     */
    @TableField("result")
    private String result;

    @TableField("response_data")
    private String responseData;

    /**
     * 渠道：1web端，2H5端
     */
    @TableField("channel")
    private Integer channel;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    @TableField("qwen_file_id")
    private String qwenFileId;

    /**
     * coze的会话id
     */
    @TableField("coze_conversation_id")
    private String cozeConversationId;
    /**
     * coze的机器人id
     */
    @TableField("coze_bot_id")
    private String cozeBotId;
    /**
     * coze的chatid
     */
    @TableField("coze_chat_id")
    private String cozeChatId;
    /**
     * tokens使用量-总
     */
    @TableField("usage_total")
    private Long usageTotal;
    /**
     * tokens使用量-in
     */
    @TableField("usage_in")
    private Long usageIn;
    /**
     * tokens使用量-out
     */
    @TableField("usage_out")
    private Long usageOut;

    /**
     * 搞定作品id
     */
    @TableField("gaoding_id")
    private String gaodingId;

    /**
     * 业务id
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 业务json，存储扩展信息
     */
    @TableField("business_json")
    private String businessJson;

    /**
     * 消息uuid
     */
    @TableField("message_UUID")
    private String messageUUID;

    /**
     * 业务json，存储扩展信息【备份】，我的创作删除aippt的时候，这里留作备份
     */
    @TableField("business_json_bak")
    private String businessJsonBak;

    /**
     * 自定义参数json（从问答带过来的前端透传参数）
     */
    @TableField("custom_parameter_json")
    private String customParameterJson;

    /**
     * 思考结果
     */
    @TableField("result_reasoning")
    private String resultReasoning;

    /**
     * 联网搜索参数
     */
    @TableField("web_search_params")
    private String webSearchParams;

    /**
     * 联网搜索结果
     */
    @TableField("web_search_result")
    private String webSearchResult;

    /**
     * 是否开启联网搜索 1-未开启 2-开启
     */
    @TableField("web_search_enabled")
    private Integer webSearchEnabled;

    /**
     * 模型
     */
    @TableField("model")
    private String model;

    /**
     * 有用 0-默认 1-有用
     */
    @TableField("useful")
    private Integer useful;

    /**
     * coze的工作流id
     */
    @TableField("coze_workflow_id")
    private String cozeWorkflowId;

    /**
     * coze的工作流执行id
     */
    @TableField("coze_execute_id")
    private String cozeExecuteId;

    /**
     * coze的工作流执行状态:执行状态。Success：执行成功。Running：执行中。Fail：执行失败。
     */
    @TableField("coze_execute_status")
    private String cozeExecuteStatus;
}