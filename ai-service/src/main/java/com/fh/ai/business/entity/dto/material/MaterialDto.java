package com.fh.ai.business.entity.dto.material;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户上传素材表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-07 11:15:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_material")
public class MaterialDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 素材类型，1用户上传的素材、2商品抠图
	 */
	@TableField("material_type")
	private Integer materialType;

	/**
	 * 素材文件名称
	 */
	@TableField("file_name")
	private String fileName;

	/**
	 * 文件oid
	 */
	@TableField("file_oid")
	private String fileOid;

	/**
	 * 素材文件路径
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}
