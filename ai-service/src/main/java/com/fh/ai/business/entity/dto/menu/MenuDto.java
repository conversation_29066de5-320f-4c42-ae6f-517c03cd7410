package com.fh.ai.business.entity.dto.menu;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_menu")
public class MenuDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单父id (一级菜单为0)
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 菜单名称
     */
    @TableField("menu_name")
    private String menuName;

    /**
     *
     */
    @TableField("permission")
    private String permission;

    /**
     * 菜单状态(2:不公开 1:公开)
     */
    @TableField("state")
    private Long state;

    /**
     * 菜单地址
     */
    @TableField("url")
    private String url;

    /**
     * 0：目录   1：菜单   2：按钮
     */
    @TableField("type")
    private Integer type;

    /**
     * 排序
     */
    @TableField("sort")
    private Long sort;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private Integer createUser;


    /**
     * 更新人
     */
    @TableField("update_user")
    private Integer updateUser;


}
