package com.fh.ai.business.entity.dto.modelUseHistory;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:14
 */
@Data
@TableName("p_model_use_history")
public class ModelUseHistoryDto {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 应用历史表id
     */
    @TableField("history_app_id")
    private Long historyAppId;

    /**
     * 对话码
     */
    @TableField("conversation_code")
    private String conversationCode;

    /**
     * 消息uuid
     */
    @TableField("message_UUID")
    private String messageUUID;

    /**
     * 使用类型 1-好用/不好用 2-复制 3-重新生成 4-选择模型 5-内容润色 6-生成ppt 7-下载
     */
    @TableField("use_type")
    private Integer useType;

    /**
     * 改变后的value值 用于记录好用/不好用及选择模型变更后的值
     */
    @TableField("use_value")
    private String useValue;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;
}
