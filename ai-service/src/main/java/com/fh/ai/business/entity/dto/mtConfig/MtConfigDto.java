package com.fh.ai.business.entity.dto.mtConfig;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 美图模型配置表
 * 
 * <AUTHOR>
 * @date 2024-08-13 16:06:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_mt_config")
public class MtConfigDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 类型：1模型，2风格模型，3参考模型，4图片尺寸
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 美图id
	 */
	@TableField("mt_id")
	private Long mtId;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 模型
	 */
	@TableField("model")
	private String model;

	/**
	 * 图片，多个逗号隔开
	 */
	@TableField("images")
	private String images;

	/**
	 * 宽
	 */
	@TableField("width")
	private Integer width;

	/**
	 * 高
	 */
	@TableField("height")
	private Integer height;

	/**
	 * 比率
	 */
	@TableField("ratio")
	private String ratio;

	/**
	 * 类别，前后逗号隔开
	 */
	@TableField("categorys")
	private String categorys;

	/**
	 * 数据json
	 */
	@TableField("data_json")
	private String dataJson;
	@TableField("proportion")
	private Integer proportion;
	@TableField("remark")
	private String remark;
	@TableField("style_type")
	private String styleType;
	@TableField("style_name")
	private String styleName;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}