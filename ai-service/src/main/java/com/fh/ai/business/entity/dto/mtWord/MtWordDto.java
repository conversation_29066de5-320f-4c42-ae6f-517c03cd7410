package com.fh.ai.business.entity.dto.mtWord;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 美图词库表
 * 
 * <AUTHOR>
 * @date 2024-08-19 14:52:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_mt_word")
public class MtWordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 父id (一级为0)
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 英文名称
	 */
	@TableField("name_english")
	private String nameEnglish;

	/**
	 * 类型：1类别，2类别值
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 图片业务类型：1美图，2liblib
	 */
	@TableField("source_type")
	private Integer sourceType;
}