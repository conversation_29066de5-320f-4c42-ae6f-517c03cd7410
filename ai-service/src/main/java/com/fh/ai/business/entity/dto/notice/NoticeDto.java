package com.fh.ai.business.entity.dto.notice;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 消息通知
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_notice")
public class NoticeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 通知内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 业务类型
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 关联业务id
	 */
	@TableField("relation_id")
	private Long relationId;

	/**
	 * 是否阅读，1：未阅，2：已阅
	 */
	@TableField("read_state")
	private Integer readState;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
