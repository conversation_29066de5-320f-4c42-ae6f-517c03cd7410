package com.fh.ai.business.entity.dto.organization;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 组织机构表
 * 
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_organization")
public class OrganizationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 父级ID
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 上级组织机构ids
	 */
	@TableField("superior_ids")
	private String superiorIds;

	/**
	 * 组织机构名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 组织机构简称
	 */
	@TableField("short_name")
	private String shortName;

	/**
	 * 状态：1启用、2禁用
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 是否统计：1统计、2不统计
	 */
	@TableField("is_statistics")
	private Integer isStatistics;

	/**
	 * 顺序
	 */
	@TableField("sort")
	private Integer sort;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 生效开始时间
	 */
	@TableField("auth_start_time")
	private Date authStartTime;
	/**
	 * 生效结束时间
	 */
	@TableField("auth_end_time")
	private Date authEndTime;
}