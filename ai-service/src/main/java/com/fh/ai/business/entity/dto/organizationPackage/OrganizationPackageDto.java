package com.fh.ai.business.entity.dto.organizationPackage;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 企业套餐
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_organization_package")
public class OrganizationPackageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 企业套餐id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 权益类型 1-试用客户 2-付费用户
	 */
	@TableField("rights_type")
	private Integer rightsType;

	/**
	 * 企业id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 套餐名称
	 */
	@TableField("package_info_id")
	private String packageInfoId;

	/**
	 * 套餐有效期开始时间
	 */
	@TableField("auth_time_start")
	private Date authTimeStart;

	/**
	 * 套餐有效期截止时间
	 */
	@TableField("auth_time_end")
	private Date authTimeEnd;

	/**
	 * 可开通账号数
	 */
	@TableField("account_num_total")
	private Long accountNumTotal;

	/**
	 * 文本推理可使用总量
	 */
	@TableField("inference_num_total")
	private Long inferenceNumTotal;

	/**
	 * 录音文件转写可使用总量（秒）
	 */
	@TableField("transliterate_num_total")
	private Long transliterateNumTotal;

	/**
	 * 图片生成可使用总量
	 */
	@TableField("mt_num_total")
	private Long mtNumTotal;

	/**
	 * 剩余可开通账号数
	 */
	@TableField("account_num")
	private Long accountNum;

	/**
	 * 文本推理剩余总量
	 */
	@TableField("inference_num")
	private Long inferenceNum;

	/**
	 * 录音文件转写剩余总量（秒）
	 */
	@TableField("transliterate_num")
	private Long transliterateNum;

	/**
	 * 图片生成剩余总量
	 */
	@TableField("mt_num")
	private Long mtNum;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 配额总量
	 */
	@TableField("quota_total")
	private Long quotaTotal;
	/**
	 * 剩余配额
	 */
	@TableField("quota")
	private Long quota;

	/**
	 * 文本推理使用量
	 */
	@TableField("inference_usage_num")
	private Long inferenceUsageNum;

	/**
	 * 录音文件转写使用量（秒）
	 */
	@TableField("transliterate_usage_num")
	private Long transliterateUsageNum;

	/**
	 * 图片生成使用量
	 */
	@TableField("mt_usage_num")
	private Long mtUsageNum;

	/**
	 * ppt使用量
	 */
	@TableField("ppt_usage_num")
	private Long pptUsageNum;

	/**
	 * 余额（元）
	 */
	@TableField("balance")
	private BigDecimal balance;

	/**
	 * 消费金额
	 */
	@TableField("consume_amount")
    private BigDecimal consumeAmount;

	/**
	 * 余量提醒 1-未提醒 2-已提醒
	 */
	@TableField("sms_margin_remind")
	private Integer smsMarginRemind;

	/**
	 * 欠费提醒 1-未提醒 2-已提醒
	 */
	@TableField("sms_arrears_remind")
	private Integer smsArrearsRemind;

	/**
	 * 文本推理消费金额（元）
	 */
	@TableField("inference_usage_amount")
	private BigDecimal inferenceUsageAmount;

	/**
	 * 录音文件转写消费金额（元）
	 */
	@TableField("transliterate_usage_amount")
	private BigDecimal transliterateUsageAmount;

	/**
	 * 图片生成消费金额（元）
	 */
	@TableField("mt_usage_amount")
	private BigDecimal mtUsageAmount;

	/**
	 * ppt生成消费金额（元）
	 */
	@TableField("ppt_usage_amount")
	private BigDecimal pptUsageAmount;
}
