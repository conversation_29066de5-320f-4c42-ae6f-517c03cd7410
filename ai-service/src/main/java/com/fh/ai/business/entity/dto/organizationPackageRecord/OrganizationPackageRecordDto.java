package com.fh.ai.business.entity.dto.organizationPackageRecord;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 企业套餐开通记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_organization_package_record")
public class OrganizationPackageRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 企业开通套餐记录id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 套餐id
	 */
	@TableField("package_info_id")
	private String packageInfoId;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField("update_time")
	private Date updateTime;

	@TableField("amount")
	private BigDecimal amount;

}
