package com.fh.ai.business.entity.dto.organizationReduceQuotaRecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业套餐
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_organization_reduce_quota_record")
public class OrganizationReduceQuotaRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 企业套餐id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@TableField("user_oid")
	private String userOid;

	/**
	 * 企业id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 类型
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 文本推理使用量
	 */
	@TableField("inference_usage_num")
	private Long inferenceUsageNum;

	/**
	 * 录音文件转写使用量（秒）
	 */
	@TableField("transliterate_usage_num")
	private Long transliterateUsageNum;

	/**
	 * 图片生成使用量
	 */
	@TableField("mt_usage_num")
	private Long mtUsageNum;

	/**
	 * 文本推理消费金额（元）
	 */
	@TableField("inference_usage_amount")
	private BigDecimal inferenceUsageAmount;

	/**
	 * 录音文件转写消费金额（元）
	 */
	@TableField("transliterate_usage_amount")
	private BigDecimal transliterateUsageAmount;

	/**
	 * 图片生成消费金额（元）
	 */
	@TableField("mt_usage_amount")
	private BigDecimal mtUsageAmount;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * ppt使用量
	 */
	@TableField("ppt_usage_num")
	private Long pptUsageNum;

	/**
	 * ppt生成消费金额（元）
	 */
	@TableField("ppt_usage_amount")
	private BigDecimal pptUsageAmount;
}
