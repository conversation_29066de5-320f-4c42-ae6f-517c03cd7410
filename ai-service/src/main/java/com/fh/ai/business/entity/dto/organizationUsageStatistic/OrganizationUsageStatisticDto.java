package com.fh.ai.business.entity.dto.organizationUsageStatistic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-29  10:34
 */
@Data
@TableName("p_organization_usage_statistic")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrganizationUsageStatisticDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业套餐id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 统计日期
     */
    @TableField("statistic_day")
    private Date statisticDay;

    /**
     * 文本推理使用量
     */
    @TableField("inference_usage_num")
    private Long inferenceUsageNum;

    /**
     * 录音文件转写使用量（秒）
     */
    @TableField("transliterate_usage_num")
    private Long transliterateUsageNum;

    /**
     * 图片生成使用量
     */
    @TableField("mt_usage_num")
    private Long mtUsageNum;

    /**
     * 使用配额
     */
    @TableField("usage_quota")
    private Long usageQuota;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * ppt生成使用量
     */
    @TableField("ppt_usage_num")
    private Long pptUsageNum;

}
