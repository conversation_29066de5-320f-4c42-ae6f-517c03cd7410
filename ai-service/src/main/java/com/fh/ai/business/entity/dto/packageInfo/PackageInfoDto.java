package com.fh.ai.business.entity.dto.packageInfo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐表
 *
 * <AUTHOR>
 * @email
 * @date 2024-10-23 09:28:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_package_info")
public class PackageInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐id
     */
    @TableId(value = "package_info_id", type = IdType.AUTO)
    private Long packageInfoId;

    /**
     * 套餐名称
     */
    @TableField("package_name")
    private String packageName;

    /**
     * 有效天数
     */
    @TableField("auth_days")
    private Long authDays;

    /**
     * 可开通账号数
     */
    @TableField("account_num_total")
    private Long accountNumTotal;

    /**
     * 文本推理可使用总量
     */
    @TableField("inference_num_total")
    private Long inferenceNumTotal;

    /**
     * 录音文件转写可使用总量（分钟）
     */
    @TableField("transliterate_num_total")
    private Long transliterateNumTotal;

    /**
     * 图片生成可使用总量
     */
    @TableField("mt_num_total")
    private Long mtNumTotal;

    /**
     * 套餐描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 套餐金额
     */
    @TableField("amount")
    private BigDecimal amount;

}

