package com.fh.ai.business.entity.dto.personalFile;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户上传的个人文件表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14 10:00:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_personal_file")
public class PersonalFileDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 个人文件id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 文件名称-展示用
	 */
	@TableField("file_name")
	private String fileName;

	/**
	 * 文件oid，attachment表的oid
	 */
	@TableField("file_oid")
	private String fileOid;

	/**
	 * 文件查看地址
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 业务类型：1用户上传图书海报的参考图片
	 */
	@TableField("biz_type")
	private Integer bizType;

	/**
	 * 顺序，默认0
	 */
	@TableField("sort")
	private Long sort;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

}
