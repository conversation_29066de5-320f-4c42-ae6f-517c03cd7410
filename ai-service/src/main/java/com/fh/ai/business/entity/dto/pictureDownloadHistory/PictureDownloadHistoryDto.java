package com.fh.ai.business.entity.dto.pictureDownloadHistory;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-06  17:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_picture_download_history")
public class PictureDownloadHistoryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户唯一oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 企业id
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 搞定作品id
     */
    @TableField("gaoding_id")
    private String gaodingId;

    /**
     * 结果
     */
    @TableField("result")
    private String result;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


}
