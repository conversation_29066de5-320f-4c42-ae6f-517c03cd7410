package com.fh.ai.business.entity.dto.preInstruction;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 预置指令
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-07 16:45:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_pre_instruction")
public class PreInstructionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户指令唯一uuid
	 */
	@TableField("uuid")
	private String uuid;

	/**
	 * 用户唯一oid，系统预置指令该字段为空
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 指令标题
	 */
	@TableField("title")
	private String title;

	/**
	 * 指令内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
