package com.fh.ai.business.entity.dto.prize;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 奖品表
 * 
 * <AUTHOR>
 * @date 2024-05-16 17:13:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_prize")
public class PrizeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 奖品id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 类型，1实物，2卡卷
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 积分
	 */
	@TableField("score")
	private Long score;

	/**
	 * 库存
	 */
	@TableField("supply")
	private Long supply;

	/**
	 * 图片
	 */
	@TableField("picture")
	private String picture;

	/**
	 * 内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 兑换码类型 1自动生成 2第三方现存 99订单兑换
	 */
	@TableField("redeemcode_type")
	private Integer redeemcodeType;

	/**
	 * 使用描述
	 */
	@TableField("use_description")
	private String useDescription;

	/**
	 * 状态 1上架 2下架
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 提醒阈值
	 */
	@TableField("remind_value")
	private Long remindValue;

	/**
	 * 排序
	 */
	@TableField("sort")
	private Long sort;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("code_name")
	private String codeName;
}