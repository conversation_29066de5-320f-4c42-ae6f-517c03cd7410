package com.fh.ai.business.entity.dto.prizeRedeemcode;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 奖品兑换码表
 * 
 * <AUTHOR>
 * @date 2024-05-13 15:04:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_prize_redeemcode")
public class PrizeRedeemcodeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 奖品id
	 */
	@TableField("prize_id")
	private Long prizeId;

	/**
	 * 码值
	 */
	@TableField("code")
	private String code;

	/**
	 * 状态 1未使用 2已使用
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}