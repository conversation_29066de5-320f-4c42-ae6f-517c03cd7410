package com.fh.ai.business.entity.dto.proofreading;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户审校记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:02:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_proofreading_record")
public class ProofreadingRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 审校类型，1在线校对、2上传文档校对
	 */
	@TableField("record_type")
	private Integer recordType;

	/**
	 * 审校记录状态，-1审校失败、0未处理 1处理中、2已完成
	 */
	@TableField("record_state")
	private Integer recordState;

	/**
	 * 当前审校记录执行的审校任务列表：
	 * 111在线-凤凰文本审校，121在线-方正文本审校，122在线-方正内容审校，211文档-凤凰文件审校，221文档-方正文件审校，222文档-方正内容审校，
	 * 223文档-方正上下文审校，224文档-方正参考文献审校
	 */
	@TableField("executed_task_info")
	private String executedTaskInfo;

	/**
	 * 审校源文件名称
	 */
	@TableField("original_file_name")
	private String originalFileName;

	/**
	 * 审校源文件oid
	 */
	@TableField("original_file_oid")
	private String originalFileOid;

	/**
	 * 审校源文件url
	 */
	@TableField("original_file_url")
	private String originalFileUrl;

	/**
	 * 文件上传状态，1上传中、2上传成功、3上传失败
	 */
	@TableField("upload_state")
	private Integer uploadState;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 提交时间
	 */
	@TableField("submit_time")
	private Date submitTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
