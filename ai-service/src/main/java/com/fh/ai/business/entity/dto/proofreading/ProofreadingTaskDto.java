package com.fh.ai.business.entity.dto.proofreading;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 审校任务表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:03:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_proofreading_task")
public class ProofreadingTaskDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 审校源，1凤凰，2方正
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 审校任务的具体类型
	 */
	@TableField("task_type")
	private Integer taskType;

	/**
	 * 审校任务状态：-1处理失败、0未处理、1处理中、2处理成功
	 */
	@TableField("task_state")
	private Integer taskState;

	/**
	 * 第三方任务id
	 */
	@TableField("third_task_id")
	private String thirdTaskId;

	/**
	 * 第三方文件id
	 */
	@TableField("third_file_id")
	private String thirdFileId;

	/**
	 * 审校结果文件名称
	 */
	@TableField("result_file_name")
	private String resultFileName;

	/**
	 * 审校结果文件oid
	 */
	@TableField("result_file_oid")
	private String resultFileOid;

	/**
	 * 审校结果文件url
	 */
	@TableField("result_file_url")
	private String resultFileUrl;

	/**
	 * 审校报告文件名称
	 */
	@TableField("report_file_name")
	private String reportFileName;

	/**
	 * 审校报告文件oid
	 */
	@TableField("report_file_oid")
	private String reportFileOid;

	/**
	 * 审校报告文件url
	 */
	@TableField("report_file_url")
	private String reportFileUrl;

	/**
	 * 审校设置参数
	 */
	@TableField("setting_option_info")
	private String settingOptionInfo;

	/**
	 * 审校请求信息
	 */
	@TableField("request_info")
	private String requestInfo;

	/**
	 * 审校响应信息
	 */
	@TableField("response_info")
	private String responseInfo;

	/**
	 * 修正后的审校结果文本
	 */
	@TableField("modify_result")
	private String modifyResult;

	/**
	 * 审校任务提交时间
	 */
	@TableField("submit_time")
	private Date submitTime;

	/**
	 * 审校任务完成时间
	 */
	@TableField("finish_time")
	private Date finishTime;

	/**
	 * 审校记录表，p_proofreading_record的主键
	 */
	@TableField("proofreading_record_id")
	private Long proofreadingRecordId;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
