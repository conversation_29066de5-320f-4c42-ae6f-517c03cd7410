package com.fh.ai.business.entity.dto.publishNews;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 热点新闻接口调用地址表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-23 15:10:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_news_hash")
public class NewsHashDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事
	 */
	@TableField("news_type")
	private Integer newsType;

	/**
	 * 资讯文章分类 出版 文化 教育 科技 人物 时事
	 */
	@TableField("news_category")
	private String newsCategory;

	/**
	 * 链接来源于榜眼的哪个渠道（滂湃新闻等）
	 */
	@TableField("channel")
	private String channel;

	/**
	 * 接口调用hashid
	 */
	@TableField("hash_id")
	private String hashId;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 修改人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 1: 未删除, 2: 已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
