package com.fh.ai.business.entity.dto.publishNews;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 出版资讯表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 17:23:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_publish_news")
public class PublishNewsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 资讯来源  1微信公众号，2榜眼数据
	 */
	@TableField("source_type")
	private Integer sourceType;
	/**
	 * 资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事
	 */
	@TableField("news_type")
	private Integer newsType;
	/**
	 * 资讯文章分类 出版 文化 教育 科技 人物 时事
	 */
	@TableField("news_category")
	private String newsCategory;
	/**
	 * 大模型智能分析后的结果
	 */
	@TableField("model_analysis")
	private String modelAnalysis;
	/**
	 * 链接来源于哪个栏目，比如公众号的某个具体的公众号；或者榜眼的某个渠道（滂湃新闻等）
	 */
	@TableField("channel")
	private String channel;

	/**
	 * 录入方式 1程序爬取，2后台手动录入
	 */
	@TableField("input_ype")
	private Integer inputYpe;

	/**
	 * 标题
	 */
	@TableField("title")
	private String title;

	/**
	 * 文章摘要
	 */
	@TableField("summary")
	private String summary;

	/**
	 * 文章内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 阅读次数
	 */
	@TableField("view_count")
	private Integer viewCount;

	/**
	 * 资讯链接
	 */
	@TableField("url")
	private String url;

	/**
	 * 文章原发布时间
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 文章作者（网名/公众号）
	 */
	@TableField("author")
	private String author;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 修改人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 1: 未删除, 2: 已删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 更新时间
	 */
	@TableField("refresh_time")
	private Date refreshTime;

}
