package com.fh.ai.business.entity.dto.question;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 题库表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_question")
public class QuestionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 题型
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 题干
	 */
	@TableField("title")
	private String title;

	/**
	 * 选项内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 正确答案
	 */
	@TableField("right_answer")
	private String rightAnswer;

	@TableField("state")
	private Integer state;
	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
