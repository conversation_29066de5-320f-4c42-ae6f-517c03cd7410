package com.fh.ai.business.entity.dto.role;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_role")
public class RoleDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 角色id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	 * 角色名称
	 */
	@TableField("role_name")
	private String roleName;

	/**
	 * 描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 是否锁定：0：否，1：是
	 */
	@TableField("is_locked")
	private Integer isLocked;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建人
	 */
	@TableField("create_user")
	private Integer createUser;


	/**
	 * 更新人
	 */
	@TableField("update_user")
	private Integer updateUser;


	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Integer isDelete;
}
