package com.fh.ai.business.entity.dto.sceneDetail;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 场景详情表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_scene_detail")
public class SceneDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 场景表id
	 */
	@TableField("scene_id")
	private Long sceneId;

	/**
	 * 媒体资源类型 1-图片
	 */
	@TableField("media_type")
	private Integer mediaType;

	/**
	 * 文件oid
	 */
	@TableField("file_oid")
	private String fileOid;

	/**
	 * 文件地址
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 方向类型 1-横向 2-竖向 3-正方形
	 */
	@TableField("direction_type")
	private Integer directionType;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
