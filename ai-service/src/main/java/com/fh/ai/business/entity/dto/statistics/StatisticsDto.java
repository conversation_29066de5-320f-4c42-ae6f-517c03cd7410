package com.fh.ai.business.entity.dto.statistics;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 统计表
 * 
 * <AUTHOR>
 * @date 2024-05-16 17:08:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_statistics")
public class StatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 访问次数
	 */
	@TableField("visits")
	private Long visits;

	/**
	 * 登录次数
	 */
	@TableField("logins")
	private Long logins;

	/**
	 * 应用使用次数
	 */
	@TableField("app_usage_count")
	private Long appUsageCount;

	/**
	 * 产生积分
	 */
	@TableField("generate_score")
	private Long generateScore;

	/**
	 * 是否新建，1：老用户，2：新用户
	 */
	@TableField("is_new")
	private Integer isNew;

	/**
	 * 兑换积分
	 */
	@TableField("redeem_score")
	private Long redeemScore;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}