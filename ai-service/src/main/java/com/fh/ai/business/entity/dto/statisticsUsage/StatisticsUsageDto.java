package com.fh.ai.business.entity.dto.statisticsUsage;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 使用情况统计表
 * 
 * <AUTHOR>
 * @date 2024-05-15 14:46:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_statistics_usage")
public class StatisticsUsageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 应用类型，1智能问答，2办公应用，3出版应用，4营销应用
	 */
	@TableField("app_type")
	private Integer appType;

	private Integer type;

	/**
	 * 应用使用次数
	 */
	@TableField("app_usage_count")
	private Long appUsageCount;

	/**
	 * 是否收藏：1收藏，2不收藏
	 */
	@TableField("is_favorite")
	private Integer isFavorite;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}