package com.fh.ai.business.entity.dto.systemSetting;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 系统设置表
 * 
 * <AUTHOR>
 * @date 2024-02-23 14:05:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_system_setting")
public class SystemSettingDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 参数名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 值
	 */
	@TableField("value")
	private String value;

	/**
	 * 最小值
	 */
	@TableField("min_value")
	private String minValue;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 内容标题
	 */
	@TableField("remark_title")
	private String remarkTitle;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 
	 */
	@TableField("url")
	private String url;

}