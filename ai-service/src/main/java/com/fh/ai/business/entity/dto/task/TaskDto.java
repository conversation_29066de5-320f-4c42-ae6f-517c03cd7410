package com.fh.ai.business.entity.dto.task;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务表
 * 
 * <AUTHOR>
 * @date 2024-06-04 13:49:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_task")
public class TaskDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 类型：1单任务，2多任务
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 二级类型：1仅体验，2需提交作品
	 */
	@TableField("second_type")
	private String secondType;

	/**
	 * 应用类型，多个逗号隔开
	 */
	@TableField("app_types")
	private String appTypes;

	@TableField("multitask")
	private String multitask;

	/**
	 * 封面
	 */
	@TableField("cover")
	private String cover;

	/**
	 * 开始时间
	 */
	@TableField("start_time")
	private Date startTime;

	/**
	 * 结束时间
	 */
	@TableField("end_time")
	private Date endTime;

	/**
	 * 积分
	 */
	@TableField("score")
	private Long score;

	/**
	 * 详情
	 */
	@TableField("details")
	private String details;

	/**
	 * 状态：1未发布，2已发布
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	@TableField("num")
	private Integer num;

	/**
	 * 排序字段
	 */
	@TableField("sort")
	private Integer sort;

}