package com.fh.ai.business.entity.dto.tip;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 提示词
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_tip")
public class TipDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 分类（1.智能问答，2.智能应用）
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 应用类型
	 */
	@TableField("app_type")
	private String appType;

	/**
	 * 提示词
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 发布时间
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 类型：1应用，2能力
	 */
	@TableField("category_type")
	private Integer categoryType;
	/**
	 * 标签类型，0无标签，1最新，2推荐
	 */
	@TableField("tag_type")
	private Integer tagType;
	/**
	 * 备注
	 */
	@TableField("comment")
	private String comment;
}
