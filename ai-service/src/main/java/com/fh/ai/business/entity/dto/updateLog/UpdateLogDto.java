package com.fh.ai.business.entity.dto.updateLog;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 更新日志
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_update_log")
public class UpdateLogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 参数名称
	 */
	@TableField("version_name")
	private String versionName;

	/**
	 * 标题
	 */
	@TableField("title")
	private String title;

	/**
	 * 更新内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 弹框图片
	 */
	@TableField("image_url")
	private String imageUrl;


	@TableField("image_h5_url")
	private String imageH5Url;

	/**
	 * 跳转地址
	 */
	@TableField("jump_url")
	private String jumpUrl;

	/**
	 * 状态：1未发布，2已发布
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 1WEB，2H5
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 发布时间
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
