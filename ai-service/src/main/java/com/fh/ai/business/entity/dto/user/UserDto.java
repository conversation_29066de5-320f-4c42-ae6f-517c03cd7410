package com.fh.ai.business.entity.dto.user;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户表
 * 
 * <AUTHOR>
 * @date 2024-02-20 16:29:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user")
public class UserDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 账号
	 */
	@TableField("account")
	private String account;

	/**
	 * 密码
	 */
	@TableField("password")
	private String password;

	/**
	 * 手机号
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 真实姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 最后登录时间
	 */
	@TableField("last_login_time")
	private Date lastLoginTime;

	/**
	 * 成长值
	 */
	@TableField("growth")
	private Long growth;

	/**
	 * 积分
	 */
	@TableField("score")
	private Long score;

	/**
	 * 配额
	 */
	@TableField("quota")
	private Long quota;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 是否锁定：1：正常，2：锁定
	 */
	@TableField("is_locked")
	private Integer isLocked;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}