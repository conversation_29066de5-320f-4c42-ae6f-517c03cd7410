package com.fh.ai.business.entity.dto.userFeedback;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户反馈表
 * 
 * <AUTHOR>
 * @date 2024-07-01 10:31:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user_feedback")
public class UserFeedbackDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 反馈内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 赠送积分
	 */
	@TableField("give_score")
	private Long giveScore;

	/**
	 * 赠送时间
	 */
	@TableField("give_time")
	private Date giveTime;

	/**
	 * 状态：1未查阅 2已查阅 3已采纳
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;
	/**
	 * 文件名称
	 */
	@TableField("file_name")
	private String fileName;
	/**
	 * 文件oid
	 */
	@TableField("file_oid")
	private String fileOid;
	/**
	 * 文件url
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 反馈类型
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 应用类型
	 */
	@TableField("app_type")
	private Integer appType;
}