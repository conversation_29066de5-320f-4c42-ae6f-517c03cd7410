package com.fh.ai.business.entity.dto.userPrize;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户奖品兑换表
 * 
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user_prize")
public class UserPrizeDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 订单编号
	 */
	@TableField("order_no")
	private String orderNo;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 奖品id
	 */
	@TableField("prize_id")
	private Long prizeId;

	/**
	 * 奖品名称
	 */
	@TableField("prize_name")
	private String prizeName;

	/**
	 * 兑换码
	 */
	@TableField("redeem_code")
	private String redeemCode;

	/**
	 * 积分
	 */
	@TableField("score")
	private Long score;

	/**
	 * 备注
	 */
	@TableField("notes")
	private String notes;

	/**
	 * 状态 1未领取 2已领取
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}