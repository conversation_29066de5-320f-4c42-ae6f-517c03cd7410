package com.fh.ai.business.entity.dto.userScoreRecord;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户积分记录
 * 
 * <AUTHOR>
 * @date 2024-02-20 17:14:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user_score_record")
public class UserScoreRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 获取类型：1每日登录，2智能问答，3文案生成，4作品创作，5奖品兑换，6积分抽奖，7积分红包，8活动任务，9意见反馈
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 关联id，根据类型区分
	 */
	@TableField("relation_id")
	private Long relationId;

	/**
	 * 积分
	 */
	@TableField("score")
	private Long score;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}