package com.fh.ai.business.entity.dto.userTask;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户任务表
 * 
 * <AUTHOR>
 * @date 2024-06-04 14:45:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user_task")
public class UserTaskDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 任务id
	 */
	@TableField("task_id")
	private Long taskId;

	/**
	 * 应用类型
	 */
	@TableField("app_type")
	private Integer appType;

	@TableField("second_type")
	private Integer secondType;

	/**
	 * 状态：1待审核，2审核通过，3审核不通过
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}