package com.fh.ai.business.entity.dto.worksActive;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作品活动表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-21 18:12:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_works_active")
public class WorksActiveDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid，提交人id
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 活动状态：1开启，2禁用
	 */
	@TableField("active_status")
	private Integer activeStatus;

	/**
	 * 活动名称
	 */
	@TableField("active_name")
	private String activeName;

	/**
	 * 活动简介
	 */
	@TableField("active_profile")
	private String activeProfile;

	/**
	 * 活动开始时间
	 */
	@TableField("active_start_time")
	private Date activeStartTime;

	/**
	 * 活动结束时间
	 */
	@TableField("active_end_time")
	private Date activeEndTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否支持投票 1-支持 2-不支持
	 */
	@TableField("is_support_vote")
	private Integer isSupportVote;

	/**
	 * 投票开始时间
	 */
	@TableField("vote_start_time")
	private Date voteStartTime;

	/**
	 * 投票结束时间
	 */
	@TableField("vote_end_time")
	private Date voteEndTime;

	/**
	 * 每天投票次数限制
	 */
	@TableField("daily_vote_limit")
	private Integer dailyVoteLimit;

}
