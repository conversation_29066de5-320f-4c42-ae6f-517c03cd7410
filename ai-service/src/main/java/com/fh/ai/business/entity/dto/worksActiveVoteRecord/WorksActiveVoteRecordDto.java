package com.fh.ai.business.entity.dto.worksActiveVoteRecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-04  09:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_works_active_vote_record")
public class WorksActiveVoteRecordDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户唯一oid，提交人id
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * FK，活动id
     */
    @TableField("works_active_id")
    private Long worksActiveId;

    /**
     * FK，优秀作品表id
     */
    @TableField("excellent_works_id")
    private Long excellentWorksId;

    /**
     * 票数
     */
    @TableField("vote_number")
    private Integer voteNumber;

    /**
     * 投票时间
     */
    @TableField("vote_time")
    private Date voteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @TableField("is_delete")
    private Integer isDelete;

}
