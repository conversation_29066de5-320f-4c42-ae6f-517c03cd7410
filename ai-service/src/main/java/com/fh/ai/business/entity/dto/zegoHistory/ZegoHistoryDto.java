package com.fh.ai.business.entity.dto.zegoHistory;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 美图生成记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_zego_history")
public class ZegoHistoryDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 任务id
	 */
	@TableField("task_id")
	private String taskId;

	/**
	 * 状态：1处理中，2处理成功，3处理失败
	 */
	@TableField("state")
	private Integer state;

	/**
	 * 参数json
	 */
	@TableField("parameter_json")
	private String parameterJson;

	/**
	 * 结果
	 */
	@TableField("result")
	private String result;

	/**
	 * 接口响应
	 */
	@TableField("response_data")
	private String responseData;

	/**
	 * 渠道：1web端，2H5端
	 */
	@TableField("channel")
	private Integer channel;

	/**
	 * 应用类型，多个逗号隔开
	 */
	@TableField("app_type")
	private String appType;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
