package com.fh.ai.business.entity.vo.activityRead;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 帮助中心
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-20 09:52:37
 */
@Data
public class ActivityReadVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 广告id
     */
    private Long activityId;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 阅读日期
     */
    private String readDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
