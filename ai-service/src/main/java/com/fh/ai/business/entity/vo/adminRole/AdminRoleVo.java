package com.fh.ai.business.entity.vo.adminRole;

import com.fh.ai.business.entity.vo.menu.MenuVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 平台用户角色表
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:50
 */
@Data
public class AdminRoleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 管理员oid
     */
    private String adminOid;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 菜单
     */
    private List<MenuVo> menuVos;
}
