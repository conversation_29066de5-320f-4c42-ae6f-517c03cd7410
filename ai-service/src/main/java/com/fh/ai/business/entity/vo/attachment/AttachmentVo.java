package com.fh.ai.business.entity.vo.attachment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 附件表
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
@Data
public class AttachmentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String oid;

    /**
     * 原文件名
     */
    @ApiModelProperty("原文件名")
    private String originalName;

    /**
     * 新文件名
     */
    @ApiModelProperty("新文件名")
    private String newName;

    /**
     * 后缀名
     */
    @ApiModelProperty("后缀名")
    private String suffix;

    /**
     * 大小
     */
    @ApiModelProperty("大小")
    private Long size;

    /**
     * 封面
     */
    @ApiModelProperty("封面")
    private String cover;

    /**
     * 原始文件路径
     */
    @ApiModelProperty("原始文件路径")
    private String originPath;

    /**
     * 预览路径
     */
    @ApiModelProperty("预览路径")
    private String viewPath;

    /**
     * 时长
     */
    @ApiModelProperty("时长")
    private Long duration;

    /**
     * 下载地址
     */
    @ApiModelProperty("下载地址")
    private String downloadUrl;
    private String scalePath;

    /**
     * 千问文件id
     */
    @ApiModelProperty("千问文件id")
    private String qwenFileId;

    /**
     * 千问删除状态，1：正常，2：删除
     */
    @ApiModelProperty("千问删除状态，1：正常，2：删除")
    private Integer qwenDeleteState;

    /**
     * 上传状态，1：上传中，2：上传成功，3：上传失败
     */
    @ApiModelProperty("上传状态，1：上传中，2：上传成功，3：上传失败")
    private Integer uploadState;

    /**
     * 状态，1：待处理，2：处理中，3：处理成功，4：处理失败
     */
    @ApiModelProperty("状态，1：待处理，2：处理中，3：处理成功，4：处理失败")
    private Integer state;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createName;
    private String firstImage;
    private String mainName;

    /**
     * 第三方文件的id(目前支持：coze)，注意千问因为历史原因存的是qwenFileId
     */
    private String taskId;

    /**
     * 任务的类型，关联到枚举类：ConversationTaskType
     */
    private Integer taskType;

	/**
	 * 第三方文件url
	 */
	@ApiModelProperty("第三方文件url")
	private String thirdFileUrl;

	/**
	 * 第三方文件url的md5值，方便查询是否是同一个url
	 */
	@ApiModelProperty("第三方文件url的md5值，方便查询是否是同一个url")
	private String thirdFileUrlMd5;

    /**
     * 加签的文件查看地址
     */
    @ApiModelProperty("加签的文件查看地址")
    private String signViewPath;
}