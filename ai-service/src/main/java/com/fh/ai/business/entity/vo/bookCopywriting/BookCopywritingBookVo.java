package com.fh.ai.business.entity.vo.bookCopywriting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 软文与书籍关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:03
 */
@Data
public class BookCopywritingBookVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 关系ID
     */
    @ApiModelProperty("关系ID")
    private Long id;

    /**
     * 软文唯一标识
     */
    @ApiModelProperty("软文唯一标识")
    private String uuid;

    /**
     * 软文ID
     */
    @ApiModelProperty("软文ID")
    private Long copywritingId;

    /**
     * 书籍ID
     */
    @ApiModelProperty("书籍ID")
    private Long bookId;

    /**
     * 书籍名称
     */
    @ApiModelProperty("书籍名称")
    private String bookName;

    /**
     * 书籍封面图地址
     */
    @ApiModelProperty("书籍封面图地址")
    private String bookCover;

    /**
     * 大模型返回的软文内容-仅单本书
     */
    @ApiModelProperty("大模型返回的软文内容-仅单本书")
    private String modelResult;

    /**
     * 大模型返回的软文内容（用户编辑后，如果有）-仅单本书
     */
    @ApiModelProperty("大模型返回的软文内容（用户编辑后，如果有）-仅单本书")
    private String modelResultFinal;

    /**
     * 大模型返回的图书卖点-多本书时候，每一本的
     */
    @ApiModelProperty("大模型返回的图书卖点-多本书时候，每一本的")
    private String sellingPoints;

    /**
     * 最终卖点（用户编辑后，如果有）-多本书时候，每一本的
     */
    @ApiModelProperty("最终卖点（用户编辑后，如果有）-多本书时候，每一本的")
    private String sellingPointsFinal;

    /**
     * 生成状态 1-待生成 2-生成中 3-成功 4-失败。-多本书时候，每一本的
     */
    @ApiModelProperty("生成状态 1-待生成 2-生成中 3-成功 4-失败。-多本书时候，每一本的")
    private Integer generateStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public BookCopywritingBookVo returnOwn() {
        return this;
    }

}
