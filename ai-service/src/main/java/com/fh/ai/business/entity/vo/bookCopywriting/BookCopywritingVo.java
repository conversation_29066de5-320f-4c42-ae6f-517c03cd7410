package com.fh.ai.business.entity.vo.bookCopywriting;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 书籍软文表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:02
 */
@Data
public class BookCopywritingVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 软文ID
     */
    @ApiModelProperty("软文ID")
    private Long id;

    /**
     * 软文唯一标识
     */
    @ApiModelProperty("软文唯一标识")
    private String uuid;

    /**
     * 软文标题（用户输入或模型生成）
     */
    @ApiModelProperty("软文标题（用户输入或模型生成）")
    private String title;

    /**
     * 关联书单ID（若基于书单）
     */
    @ApiModelProperty("关联书单ID（若基于书单）")
    private Long bookListId;

    /**
     * 关联上传书单文件的fileOid
     */
    @ApiModelProperty("关联上传书单文件的fileOid")
    private String uploadFileOid;

    /**
     * 内容类型 1-单本书 2-多本书
     */
    @ApiModelProperty("内容类型 1-单本书 2-多本书")
    private Integer contentType;

    /**
     * 来源类型 1-书籍 2-书单 3-上传 4-其他
     */
    @ApiModelProperty("来源类型 1-书籍 2-书单 3-上传 4-其他")
    private Integer sourceType;

    /**
     * 目标受众（如“职场新人”“学生”）
     */
    @ApiModelProperty("目标受众（如“职场新人”“学生”）")
    private String targetAudience;

    /**
     * 写作风格 1-微信公众号 2-小红书
     */
    @ApiModelProperty("写作风格 1-微信公众号 2-小红书")
    private Integer writingStyleType;

    /**
     * 营销参考类型 0-无 1-新闻热点 2-节假日 3-活动推广
     */
    @ApiModelProperty("营销参考类型 0-无 1-新闻热点 2-节假日 3-活动推广")
    private Integer referenceType;

    /**
     * 参考标签/关键词（如“双十一,励志”）
     */
    @ApiModelProperty("参考标签/关键词（如“双十一,励志”）")
    private String referenceWords;

    /**
     * 涉及主题（与书单表一致）
     */
    @ApiModelProperty("涉及主题（与书单表一致）")
    private String recommendTopic;

    /**
     * 生成条件JSON（如受众、风格、关键词等）
     */
    @ApiModelProperty("生成条件JSON（如受众、风格、关键词等）")
    private String conditionJson;

    /**
     * 总体大模型返回的软文内容（content_type=2有值）
     */
    @ApiModelProperty("总体大模型返回的软文内容（content_type=2有值）")
    private String overallSellingPoints;

    /**
     * 总体最终软文内容（用户编辑后，如果有）（content_type=2有值）
     */
    @ApiModelProperty("总体最终软文内容（用户编辑后，如果有）（content_type=2有值）")
    private String overallSellingPointsFinal;

    /**
     * 总体生成状态 1-待生成 2-生成中 3-成功 4-失败（content_type=2有值）
     */
    @ApiModelProperty("总体生成状态 1-待生成 2-生成中 3-成功 4-失败（content_type=2有值）")
    private Integer overallGenerateStatus;

    /**
     * 大模型返回的软文内容-仅单本书
     */
    @ApiModelProperty("大模型返回的软文内容-仅单本书")
    private String modelResult;

    /**
     * 大模型返回的软文内容（用户编辑后，如果有）-仅单本书
     */
    @ApiModelProperty("大模型返回的软文内容（用户编辑后，如果有）-仅单本书")
    private String modelResultFinal;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Long sort;

    /**
     * 用户标识，表示是谁的软文，通常是创建人
     */
    @ApiModelProperty("用户标识，表示是谁的软文，通常是创建人")
    private String userOid;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 图书软文每本书详情列表
     */
    private List<BookCopywritingBookVo> bookCopywritingBookList;

    /*
     * 方便steam流存入自身
     * */
    public BookCopywritingVo returnOwn() {
        return this;
    }

}
