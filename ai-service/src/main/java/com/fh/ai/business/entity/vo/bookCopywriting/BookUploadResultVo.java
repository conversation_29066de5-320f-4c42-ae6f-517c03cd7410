package com.fh.ai.business.entity.vo.bookCopywriting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书籍上传结果VO
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class BookUploadResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件OID
     */
    @ApiModelProperty("文件OID")
    private String fileOid;

    /**
     * 书籍列表
     */
    @ApiModelProperty("书籍列表")
    private List<BookInfoVo> books;

    /**
     * 书籍信息VO
     */
    @Data
    public static class BookInfoVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 图书名称
         */
        @ApiModelProperty("图书名称")
        private String bookName;

        /**
         * 图书简介
         */
        @ApiModelProperty("图书简介")
        private String bookDescription;

        /**
         * 图书作者
         */
        @ApiModelProperty("图书作者")
        private String bookAuthor;

        /**
         * 图书ISBN
         */
        @ApiModelProperty("图书ISBN")
        private String bookIsbn;

        /**
         * 关键字
         */
        @ApiModelProperty("关键字")
        private String keywords;

        /**
         * 图书封面URL
         */
        @ApiModelProperty("图书封面URL")
        private String bookCover;
    }
}
