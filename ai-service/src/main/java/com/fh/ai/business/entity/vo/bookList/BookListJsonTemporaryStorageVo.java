package com.fh.ai.business.entity.vo.bookList;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 书单json临时存储表
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  11:35
 */
@Data
public class BookListJsonTemporaryStorageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 书单唯一标识
     */
    @ApiModelProperty("书单唯一标识")
    private String uuid;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty(value = "用户唯一oid")
    private String userOid;

    /**
     * 书单json
     */
    @ApiModelProperty("书单json")
    private String bookListJson;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除")
    private Integer isDelete;
}
