package com.fh.ai.business.entity.vo.bookList;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-27  17:40
 */
@Data
public class BookListRecommendBookVo {
    @ApiModelProperty("作者")
    private String bookAuthor;

    @ApiModelProperty("封面图")
    private String bookCover;

    @ApiModelProperty("书籍描述")
    private String bookDescription;

    @ApiModelProperty("书籍id")
    private Long bookId;

    @ApiModelProperty("isbn")
    private String bookIsbn;

    @ApiModelProperty("书名")
    private String bookName;

    @ApiModelProperty("关键词")
    private String keywords;

    @ApiModelProperty("出版社")
    private String publishName;

    @ApiModelProperty("来源")
    private Integer sourceType;
}
