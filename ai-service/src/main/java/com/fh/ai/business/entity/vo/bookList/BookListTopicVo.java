package com.fh.ai.business.entity.vo.bookList;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-27  17:39
 */
@Data
public class BookListTopicVo {

    @ApiModelProperty("主题名称")
    private String topicName;

    @ApiModelProperty("营销参考类型")
    private Integer referenceType;

    @ApiModelProperty("推荐理由")
    private String recommendReason;

    @ApiModelProperty("关键词标签")
    private String marketingKeywords;

    @ApiModelProperty("推荐书籍")
    private List<BookListRecommendBookVo> recommendBooks;
}
