package com.fh.ai.business.entity.vo.bookList;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 书单表
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  11:35
 */
@Data
public class BookListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 书单id
     */
    @ApiModelProperty(value = "书单id")
    private Long id;

    /**
     * 书单唯一标识
     */
    @ApiModelProperty(value = "书单唯一标识")
    private String uuid;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty(value = "用户唯一oid")
    private String userOid;

    /**
     * 书单名称
     */
    @ApiModelProperty(value = "书单名称")
    private String name;

    /**
     * 营销参考类型 1-新闻热点 2-节假日
     */
    @ApiModelProperty(value = "营销参考类型 1-新闻热点 2-节假日")
    private Integer referenceType;

    /**
     * 生成条件-参考标签
     */
    @ApiModelProperty(value = "生成条件-参考标签")
    private String referenceWords;

    /**
     * 涉及主题
     */
    @ApiModelProperty(value = "生成条件-涉及主题")
    private String recommendTopic;

    /**
     * 书单类型分析内容
     */
    @ApiModelProperty(value = "书单类型分析内容")
    private String analysis;

    /**
     * 条件json
     */
    @ApiModelProperty(value = "条件json")
    private String conditionJson;

    /**
     * 大模型返回结果
     */
    @ApiModelProperty(value = "大模型返回结果")
    private String modelResult;

    /**
     * 书单json
     */
    @ApiModelProperty(value = "书单json")
    private String bookListJson;

    /**
     * 书单json转换状态 1-未转换 2-成功 3-失败
     */
    @ApiModelProperty(value = "书单json转换状态")
    private Integer convertState;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty(value = "是否删除，1：正常，2：删除")
    private Integer isDelete;



}
