package com.fh.ai.business.entity.vo.bookListPoster;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 书单海报表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-07 10:00:00
 */
@Data
public class BookListPosterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 书单海报id
     */
    @ApiModelProperty("书单海报id")
    private Long id;

    /**
     * 书单唯一标识
     */
    @ApiModelProperty("书单唯一标识")
    private String uuid;

    /**
     * 书单主键
     */
    @ApiModelProperty("书单主键")
    private Long bookListId;

    /**
     * 内容类型 1-单本书 2-多本书
     */
    @ApiModelProperty("内容类型 1-单本书 2-多本书")
    private Integer contentType;

    /**
     * 来源类型 1-提示词 2-图片 3-代码 4-其他
     */
    @ApiModelProperty("来源类型 1-提示词 2-图片 3-代码 4-其他")
    private Integer sourceType;

    /**
     * 条件json
     */
    @ApiModelProperty("条件json")
    private String conditionJson;

    /**
     * 参考网页样式
     */
    @ApiModelProperty("参考网页样式")
    private String refHtmlStyle;

    /**
     * 配色
     */
    @ApiModelProperty("配色")
    private String contentColor;

    /**
     * 大模型返回结果
     */
    @ApiModelProperty("大模型返回结果")
    private String modelResult;

    /**
     * 书单海报json
     */
    @ApiModelProperty("书单海报json")
    private String bookListPosterJson;

    /**
     * 书单海报名称
     */
    @ApiModelProperty("书单海报名称")
    private String bookListPosterName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /*
     * 方便stream流存入自身
     * */
    public BookListPosterVo returnOwn() {
        return this;
    }

}
