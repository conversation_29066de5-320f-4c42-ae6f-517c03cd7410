package com.fh.ai.business.entity.vo.conversation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-18  11:58
 */
@Data
public class ConversationStatisticVo {

    @ApiModelProperty("使用人数")
    private Integer userCount;

    @ApiModelProperty("使用次数")
    private Integer useCount;

    @ApiModelProperty("消耗token量")
    private Integer usageSum;

    @ApiModelProperty("消耗输入token量")
    private Integer usageInSum;

    @ApiModelProperty("消耗输出token量")
    private Integer usageOutSum;

}
