package com.fh.ai.business.entity.vo.conversation;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会话表
 * 
 * <AUTHOR>
 * @date 2024-04-17 16:49:42
 */
@Data
public class ConversationVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 对话码
     */
    @ApiModelProperty("对话码")
    private String conversationCode;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
     */
    @ApiModelProperty("类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件")
    private Integer type;

    /**
     * 子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策
     */
    @ApiModelProperty("子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策")
    private Integer subType;

    /**
     * 提问类型：1人工，2自动
     */
    @ApiModelProperty("提问类型：1人工，2自动")
    private Integer askType;

    /**
     * 提示词
     */
    @ApiModelProperty("提示词")
    private String prompt;

    /**
     * 原始问题
     */
    @ApiModelProperty("原始问题")
    private String originalQuestion;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 消息
     */
    @ApiModelProperty("消息")
    private String message;

    /**
     * 参数json
     */
    @ApiModelProperty("参数json")
    private String parameterJson;

    /**
     * 上下文
     */
    @ApiModelProperty("上下文")
    private String context;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 对话文档
     */
    @ApiModelProperty("对话文档")
    private List<ConversationFileVo> fileVos;

    /**
     * coze的会话id
     */
    @ApiModelProperty("coze的会话id")
    private String cozeConversationId;
    /**
     * coze的机器人id
     */
    @ApiModelProperty("coze的机器人id")
    private String cozeBotId;
    /**
     * 业务id，用于将会话和业务id关联(具体什么业务看type)
     */
    @ApiModelProperty("业务id，用于将会话和业务id关联(具体什么业务看type)")
    private String businessId;
    /**
     * 业务json
     */
    @ApiModelProperty("业务json")
    private String businessJson;

    /**
     * 乐学书籍id
     */
    @ApiModelProperty("乐学书籍id")
    private String bookId;

    /**
     * 业务json【备份】
     */
    @ApiModelProperty("业务json")
    private String businessJsonBak;

    /**
     * 自定义参数json（会带到本次的问答history里面）
     */
    @ApiModelProperty("参数json")
    private String customParameterJson;

    /**
     * coze的工作流id
     */
    @ApiModelProperty("coze的工作流id")
    private String cozeWorkflowId;
}