package com.fh.ai.business.entity.vo.conversationFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 会话文件表
 *
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
@Data
public class AudioConvertVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对话码
     */
    @ApiModelProperty("说话人")
    private String speaker;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;

    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;

}