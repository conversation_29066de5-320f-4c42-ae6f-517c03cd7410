package com.fh.ai.business.entity.vo.conversationFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 会话文件表
 * 
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
@Data
public class ConversationFileVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 对话码
     */
    @ApiModelProperty("对话码")
    private String conversationCode;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
     */
    @ApiModelProperty("类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件")
    private Integer type;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String fileOid;

    /**
     * 讯飞订单id
     */
    @ApiModelProperty("讯飞订单id")
    private String xfOrderId;

    /**
     * 讯飞状态：1上传成功，2处理中，3处理成功，4处理失败
     */
    @ApiModelProperty("讯飞状态：1上传成功，2处理中，3处理成功，4处理失败")
    private Integer xfState;

    /**
     * 内容
     */
    @ApiModelProperty("内容")
    private String content;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 千问文件id
     */
    @ApiModelProperty("千问文件id")
    private String qwenFileId;

    /**
     * 千问状态：1上传成功，2处理中，3处理成功，4处理失败
     */
    @ApiModelProperty("千问状态：1上传成功，2处理中，3处理成功，4处理失败")
    private Integer qwenState;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 后缀名
     */
    @ApiModelProperty("后缀名")
    private String suffix;

    /**
     * 大小
     */
    @ApiModelProperty("大小")
    private Long size;

    /**
     * 原文件名
     */
    @ApiModelProperty("原文件名")
    private String originalName;

    /**
     * 预览路径
     */
    @ApiModelProperty("预览路径")
    private String viewPath;

    /**
     * 时长
     */
    @ApiModelProperty("时长")
    private Long duration;

    /**
     * 一个异步任务的id。具体是什么异步任务，通过appType关联。也可以通过会话code知晓
     */
    private String taskId;
    /**
     * 任务的类型，关联到枚举类：ConversationTaskType
     */
    private Integer taskType;
    /**
     * 任务的状态：1上传成功，2处理中，3处理成功，4处理失败
     */
    private Integer taskState;
    /**
     * 记录id，标识本次task任务和哪一次历史对话有关
     */
    private Long historyId;
    /**
     * 业务id
     */
    private String businessId;

    /**
     * 任务内容结果，例如kimi存储文件内容
     */
    @ApiModelProperty("任务内容结果，例如kimi存储文件内容")
    private String taskResult;

    /**
     * 文件内容缓存id
     */
    @ApiModelProperty("文件内容缓存id")
    private Long fileContentCacheId;

    /**
     * ffmpeg处理状态 1-处理中 2-成功 3-失败 4-不需要处理
     */
    private Integer ffmpegState;

    /**
     * ffmpeg转换后的文件路径
     */
    private String ffmpegConvertResult;
}