package com.fh.ai.business.entity.vo.conversationFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 文件内容缓存表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-27 14:52:45
 */
@Data
public class FileContentCacheVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 本系统文件oid
     */
    @ApiModelProperty("本系统文件oid")
    private String fileOid;

    /**
     * 第三方系任务id或者文件id
     */
    @ApiModelProperty("第三方系任务id或者文件id")
    private String taskId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 任务内容（例如kimi文件内容缓存）
     */
    @ApiModelProperty("任务内容（例如kimi文件内容缓存）")
    private String taskResult;

    /*
     * 方便steam流存入自身
     * */
    public FileContentCacheVo returnOwn() {
        return this;
    }

}
