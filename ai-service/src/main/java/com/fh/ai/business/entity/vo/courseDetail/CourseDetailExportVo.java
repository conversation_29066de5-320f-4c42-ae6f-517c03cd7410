package com.fh.ai.business.entity.vo.courseDetail;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CourseDetailExportVo {
    @Excel(name = "ID", width = 50)
    private String id;

    @Excel(name = "视频名称", width = 50)
    private String name;

    @Excel(name = "课后练习试卷", width = 50)
    private String paperName;

    @Excel(name = "状态", width = 50)
    private String state;

    @Excel(name = "视频查看次数", width = 50)
    private String videoCount;

    @Excel(name = "视频查看人数", width = 50)
    private String videoUserCount;

    @Excel(name = "练习作答次数", width = 50)
    private String ansCount;

    @Excel(name = "练习作答人数", width = 50)
    private String ansUserCount;
    @Excel(name = "作答正确率", width = 50)
    private String accuracy;

    private Integer rightCount;
    private Integer allCount;
}
