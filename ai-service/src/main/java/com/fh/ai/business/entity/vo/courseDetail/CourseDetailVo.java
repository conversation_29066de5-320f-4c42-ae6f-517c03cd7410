package com.fh.ai.business.entity.vo.courseDetail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课程详情
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Data
public class CourseDetailVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 名称
     */
    private String name;
    private String paperName;

    private String sourceText;

    private String author;

    private Long examPaperId;

    /**
     * 播放地址
     */
    private String url;

    /**
     * 时长
     */
    private String duration;

    /**
     * 排序
     */
    private Integer sort;

    private Integer state;

    /**
     * 封面地址
     */
    private String imageUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
