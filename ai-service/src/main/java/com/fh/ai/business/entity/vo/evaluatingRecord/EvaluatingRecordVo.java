package com.fh.ai.business.entity.vo.evaluatingRecord;

import lombok.Data;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-16  10:57
 */
@Data
public class EvaluatingRecordVo {
    /**
     * ID
     */
    private Long id;

    /**
     * 测评任务uid【系统生成】
     */
    private String taskUid;

    /**
     * 语音转文本第三方任务id
     */
    private String audioToTextTaskUid;

    /**
     * 音频url
     */
    private String audioUrl;

    /**
     * 测评文本
     */
    private String contents;

    /**
     * 测评类型 1-中文测评 2-英文测评
     */
    private Integer type;

    /**
     * 测评结果
     */
    private String evaluatingResult;

    /**
     * 测评状态 1-等待语音转文本 2-待测评 3-测评完成
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

    /**
     * 第三方请求唯一id，用于回调第三方接口
     */
    private String thirdUniqueId;

    /**
     * 回调结果
     */
    private String returnResult;
}
