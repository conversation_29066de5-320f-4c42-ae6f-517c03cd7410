package com.fh.ai.business.entity.vo.examAnswer;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 题库表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
public class ExamAnswerVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    private Long courseDetailId;

    /**
     * 试卷id
     */
    private Long examPaperId;

    /**
     * 题目id
     */
    private Long questionId;

    /**
     * 答案
     */
    private String content;

    /**
     * 题型
     */
    private Integer type;

    /**
     * 是否删除，1：正确，2：错误
     */
    private Integer isRight;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;
    private String paperName;
    private String courseName;

}
