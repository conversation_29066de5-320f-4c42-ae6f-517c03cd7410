package com.fh.ai.business.entity.vo.examPaper;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题库表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
public class ExamPaperVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 试卷名称
     */
    private String name;

    /**
     * 试卷类型
     */
    private Integer type;

    private Integer ansTime;
    private Long hasAnsTime;

    /**
     * 管理题目ids
     */
    private String questionIds;

    private Integer state;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

    private List<QuestionVo> questionList;

    private Integer userAnswerCount;
    private Integer bindCount;

}
