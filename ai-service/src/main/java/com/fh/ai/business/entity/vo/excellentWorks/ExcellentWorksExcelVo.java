package com.fh.ai.business.entity.vo.excellentWorks;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:38
 */
@Data
public class ExcellentWorksExcelVo {

    /**
     * 数据来源：1运营添加，2用户投稿
     */
    @Excel(name = "来源", width = 50)
    private String sourceType;

    /**
     * 应用类型
     */
    @Excel(name = "应用类型", width = 50)
    private String appType;

    /**
     * 作品名称
     */
    @Excel(name = "作品名称", width = 50)
    private String worksName;

    /**
     * 作者名称
     */
    @Excel(name = "作者名称", width = 50)
    private String userName;

    /**
     * 所属组织名称
     */
    @Excel(name = "所属组织名称", width = 50)
    private String organizationName;

    /**
     * 上下架类型：1下架，2上架
     */
    @Excel(name = "作品状态", width = 50)
    private String holdType;

    /**
     * 累计获奖次数
     */
    @Excel(name = "累计获奖次数", width = 50)
    private String awardsCumulativeNumber;

    /**
     * 投稿时间
     */
    @Excel(name = "投稿时间", width = 50)
    private String createTime;

    /**
     * 赠送时间
     */
    @Excel(name = "赠送时间", width = 50)
    private String latestAwardTime;

    /**
     * 赠送内容
     */
    @Excel(name = "赠送内容", width = 50)
    private String prizeCardInfo;
}
