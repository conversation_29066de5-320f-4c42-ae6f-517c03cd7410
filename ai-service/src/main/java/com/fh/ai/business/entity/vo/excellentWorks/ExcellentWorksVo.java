package com.fh.ai.business.entity.vo.excellentWorks;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:38
 */
@Data
public class ExcellentWorksVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id，作品关联的活动。
     */
    private Long worksActiveId;

    /**
     * 活动名称
     */
    private String activeName;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 统计组织id
     */
    private Long statisticsOrganizationId;

    /**
     * 作品名称
     */
    private String worksName;

    /**
     * 作者名称
     */
    private String userName;

    /**
     * 所属组织名称
     */
    private String organizationName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 审核类型 1-审核中 2-审核通过 3-审核不通过
     */
    private Integer auditType;

    /**
     * 上下架类型：1下架，2上架
     */
    private Integer holdType;

    /**
     * 应用类型
     */
    private Integer appType;

    /**
     * 作品类型 1-图片，2视频，3ppt，4混合内容
     */
    private Integer worksType;

    /**
     * 作品简介
     */
    private String worksProfile;

    /**
     * 作品说明
     */
    private String worksDescribe;

    /**
     * 数据来源：1运营添加，2用户投稿
     */
    private Integer sourceType;

    /**
     * 业务Id
     */
    private Long businessId;

    /**
     * 基础模型
     */
    private String baseModel;

    /**
     * 生图模型
     */
    private String imageGenModel;

    /**
     * 风格模型
     */
    private String styleModel;

    /**
     * 提示词
     */
    private String prompts;

    /**
     * 反向提示词
     */
    private String negativePrompt;

    /**
     * 用户参数
     */
    private String params;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

    /**
     * 优秀作品详情列表
     */
    private List<ExcellentWorksDetailVo> excellentWorksDetailList;

    /**
     * 累计获奖次数
     */
    private Integer awardsCumulativeNumber;

    /**
     * 赠送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestAwardTime;

    /**
     * 用户作品获奖记录详情列表
     */
    private List<ExcellentWorksPrizeVo> excellentWorksPrizeList;

    @ApiModelProperty("统计组织id")
    private Long statisticOrgId;

    @ApiModelProperty("统计组织名称")
    private String statisticOrgName;

    /**
     * 所属组织路径
     */
    @ApiModelProperty("所属组织路径")
    private String orgPath;

    /**
     * 编号
     */
    private Integer excellentWorksNumber;

    /**
     * 投票数
     */
    private Integer voteNumber;

    /**
     * 排名
     */
    private Integer rank;
}
