package com.fh.ai.business.entity.vo.excellentWorksDetail;

import lombok.Data;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-14  11:09
 */
@Data
public class ExcellentWorksDetailVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 优秀作品表id
     */
    private Long excellentWorksId;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 一个媒体的类型 1-图片 2-文档word
     */
    private Integer worksMediaType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 文件oid
     */
    private String fileOid;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 压缩文件oid
     */
    private String compressFileOid;

    /**
     * 压缩文件名称
     */
    private String compressFileName;

    /**
     * 压缩文件url
     */
    private String compressFileUrl;

    /**
     * 封面oid
     */
    private String converOid;

    /**
     * 封面地址
     */
    private String converUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;
}
