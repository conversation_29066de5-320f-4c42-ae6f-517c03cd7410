package com.fh.ai.business.entity.vo.excellentWorksPrize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户作品获奖记录（和奖品表没关系）
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-21 17:59:13
 */
@Data
public class ExcellentWorksPrizeCountVo implements Serializable{

    /**
     * FK，优秀作品表id
     */
    @ApiModelProperty("FK，优秀作品表id")
    private Long excellentWorksId;


    /**
     * 获奖次数
     */
    @ApiModelProperty("获奖次数")
    private Integer AwardsNumber=0;

    /**
     * 赠送时间
     */
    @ApiModelProperty("赠送时间")
    private Date latestAwardTime;


}
