package com.fh.ai.business.entity.vo.excellentWorksPrize;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 用户作品获奖记录（和奖品表没关系）
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 17:59:13
 */
@Data
public class ExcellentWorksPrizeVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * FK，优秀作品表id
     */
    @ApiModelProperty("FK，优秀作品表id")
    private Long excellentWorksId;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 获得奖品的原因
     */
    @ApiModelProperty("获得奖品的原因")
    private String prizeReason;

    /**
     * 奖品-积分
     */
    @ApiModelProperty("奖品-积分")
    private Long prizeScore;

    /**
     * 奖品-卡券信息
     */
    @ApiModelProperty("奖品-卡券信息")
    private String prizeCardInfo;

    /**
     * 奖品类型：1积分，2奖品，3积分和奖品都有
     */
    @ApiModelProperty("奖品类型：1积分，2奖品，3积分和奖品都有")
    private Integer prizeType;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activeName;

    /*
     * 方便steam流存入自身
     * */
    public ExcellentWorksPrizeVo returnOwn() {
        return this;
    }

}
