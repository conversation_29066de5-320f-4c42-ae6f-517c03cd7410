package com.fh.ai.business.entity.vo.festival;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 节假日表
 * 
 * <AUTHOR>
 * @date 2024-07-02 14:28:17
 */
@Data
public class FestivalVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private Date day;

    /**
     * 类型 1放假日 2调班
     */
    @ApiModelProperty("类型 1放假日 2调班")
    private Integer type;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Long year;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

}