package com.fh.ai.business.entity.vo.flowRecord;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程记录表dto
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  09:23
 */
@Data
public class FlowRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 流程唯一code
     */
    private String flowCode;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 智能体id 1-图书商品图
     */
    private Long agentId;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 应用类型
     */
    private Integer appType;

    /**
     * 应用唯一标识【会话code或作图主键】
     */
    private String appBusinessId;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * 流程开始id
     */
    private Long topId;

    /**
     * 参数
     */
    private String param;

    /**
     * 结果
     */
    private String result;

    /**
     * 是否显示 1-显示 2-不显示
     */
    private Integer showType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

    /**
     * historyApp表id
     */
    private Long historyId;
}
