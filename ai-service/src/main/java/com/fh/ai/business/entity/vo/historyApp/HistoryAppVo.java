package com.fh.ai.business.entity.vo.historyApp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用历史表
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
@Data
public class HistoryAppVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 对话码
     */
    @ApiModelProperty("对话码")
    private String conversationCode;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
     */
    @ApiModelProperty("类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件")
    private Integer type;

    /**
     * 提问类型：1人工，2自动
     */
    @ApiModelProperty("提问类型：1人工，2自动")
    private Integer askType;

    /**
     * 子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策
     */
    @ApiModelProperty("子分类，根据type值设置，1智能问答，20701会议文字记录，20702会议关键词，20703会议摘要，20704发言总结，20705会议待办事项，20706会议决策")
    private Integer subType;

    /**
     * 提示词
     */
    @ApiModelProperty("提示词")
    private String prompt;

    /**
     * 原始问题
     */
    @ApiModelProperty("原始问题")
    private String originalQuestion;

    /**
     * 问题
     */
    @ApiModelProperty(notes = "问题")
    private String question;

    /**
     * 参数json
     */
    @ApiModelProperty("参数json")
    private String parameterJson;

    /**
     * 请求id
     */
    @ApiModelProperty("请求id")
    private String requestId;

    /**
     * 结果
     */
    @ApiModelProperty("结果")
    private String result;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    private String qwenFileId;

    /**
     * tokens使用量-总
     */
    @ApiModelProperty("tokens使用量-总")
    private Long usageTotal;
    /**
     * tokens使用量-in
     */
    @ApiModelProperty("tokens使用量-in")
    private Long usageIn;
    /**
     * tokens使用量-out
     */
    @ApiModelProperty("tokens使用量-out")
    private Long usageOut;

    @ApiModelProperty("搞定作品id")
    private String gaodingId;

    @ApiModelProperty("业务id")
    private String businessId;

    @ApiModelProperty("业务json，存储扩展信息")
    private String businessJson;

    @ApiModelProperty("消息uuid")
    private String messageUUID;

    /**
     * 业务json【备份】
     */
    @ApiModelProperty("业务json")
    private String businessJsonBak;

    /**
     * 自定义参数json（从问答带过来的前端透传参数）
     */
    @ApiModelProperty("自定义参数json")
    private String customParameterJson;

    /**
     * 思考结果
     */
    @ApiModelProperty("思考结果")
    private String resultReasoning;

    @ApiModelProperty("联网搜索参数")
    private String webSearchParams;

    @ApiModelProperty("联网搜索结果")
    private String webSearchResult;

    @ApiModelProperty("是否有用 0-默认 1-有用")
    private Integer useful;

    /**
     * coze的工作流id
     */
    @ApiModelProperty("coze的工作流id")
    private String cozeWorkflowId;

    /**
     * coze的工作流执行id
     */
    @ApiModelProperty("coze的工作流执行id")
    private String cozeExecuteId;

    /**
     * coze的工作流执行状态:执行状态。Success：执行成功。Running：执行中。Fail：执行失败。
     */
    @ApiModelProperty("coze的工作流执行状态")
    private String cozeExecuteStatus;
}