package com.fh.ai.business.entity.vo.historyApp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户使用情况
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
@Data
public class UserUsageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Long usageCount;

    /**
     * 兑换次数
     */
    @ApiModelProperty("兑换次数")
    private Long redeemCount;
}