package com.fh.ai.business.entity.vo.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户上传素材表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-07 11:15:45
 */
@Data
public class MaterialVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 素材类型，1用户上传的素材、2商品抠图
     */
    @ApiModelProperty("素材类型，1用户上传的素材、2商品抠图")
    private Integer materialType;

    /**
     * 素材文件名称
     */
    @ApiModelProperty("素材文件名称")
    private String fileName;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String fileOid;

    /**
     * 素材文件路径
     */
    @ApiModelProperty("素材文件路径")
    private String fileUrl;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /*
     * 方便steam流存入自身
     * */
    public MaterialVo returnOwn() {
        return this;
    }

}
