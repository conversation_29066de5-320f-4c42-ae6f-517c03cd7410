package com.fh.ai.business.entity.vo.modelUseHistory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:21
 */
@Data
public class ModelUseHistoryVo {
    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "用户oid")
    private String userOid;

    /**
     * 应用历史表id
     */
    @ApiModelProperty(value = "应用历史表id")
    private Long historyAppId;

    @ApiModelProperty(value = "对话码")
    private String conversationCode;

    @ApiModelProperty(value = "消息uuid")
    private String messageUUID;

    @ApiModelProperty(value = "使用类型 1-好用/不好用 2-复制 3-重新生成 4-选择模型 5-内容润色 6-生成ppt 7-下载")
    private Integer useType;

    @ApiModelProperty(value = "改变后的value值 用于记录好用/不好用及选择模型变更后的值")
    private String useValue;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除，1：正常，2：删除")
    private Integer isDelete;
}
