package com.fh.ai.business.entity.vo.mtConfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 美图模型配置表
 * 
 * <AUTHOR>
 * @date 2024-08-13 16:06:29
 */
@Data
public class MtConfigVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 类型：1模型，2风格模型，3参考模型，4图片尺寸
     */
    @ApiModelProperty("类型：1模型，2风格模型，3参考模型，4图片尺寸")
    private Integer type;

    /**
     * 美图id
     */
    @ApiModelProperty("美图id")
    private Long mtId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 模型
     */
    @ApiModelProperty("模型")
    private String model;

    /**
     * 图片，多个逗号隔开
     */
    @ApiModelProperty("图片，多个逗号隔开")
    private String images;

    /**
     * 宽
     */
    @ApiModelProperty("宽")
    private Integer width;

    /**
     * 高
     */
    @ApiModelProperty("高")
    private Integer height;

    /**
     * 比率
     */
    @ApiModelProperty("比率")
    private String ratio;

    /**
     * 类别，前后逗号隔开
     */
    @ApiModelProperty("类别，前后逗号隔开")
    private String categorys;

    /**
     * 数据json
     */
    @ApiModelProperty("数据json")
    private String dataJson;

    private Integer proportion;
    private String remark;
    private String styleType;
    private String styleName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

}