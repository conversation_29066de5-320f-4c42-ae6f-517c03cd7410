package com.fh.ai.business.entity.vo.mtHistory;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 附件表
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
@Data
public class MtFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String fileOid;

    /**
     * 美图原图
     */
    @ApiModelProperty("美图原图")
    private String mtUrl;

    /**
     * 小图
     */
    @ApiModelProperty("小图")
    private String smallUrl;

    /**
     * 图片地址
     */
    @ApiModelProperty("图片地址")
    private String viewPath;

    /**
     * 下载地址
     */
    @ApiModelProperty("下载地址")
    private String downloadUrl;


    /**
     * 原图oid(暂时没有放此数据)
     */
    private String originalFileOid;
    /**
     * 原图地址（特别正对放大、重绘等场景）
     */
    private String originalUrl;

}