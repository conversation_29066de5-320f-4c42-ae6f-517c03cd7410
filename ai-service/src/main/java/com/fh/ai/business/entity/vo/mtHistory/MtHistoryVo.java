package com.fh.ai.business.entity.vo.mtHistory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 美图生成记录表
 * 
 * <AUTHOR>
 * @date 2024-08-16 09:35:50
 */
@Data
public class MtHistoryVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 类型：1文生图，2图生图
     */
    @ApiModelProperty("类型：1文生图，2图生图")
    private Integer type;

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 状态：1处理中，2处理成功，3处理失败
     */
    @ApiModelProperty("状态：1处理中，2处理成功，3处理失败")
    private Integer state;

    /**
     * 参数json
     */
    @ApiModelProperty("参数json")
    private String parameterJson;

    /**
     * 结果
     */
    @ApiModelProperty("结果")
    private String result;

    /**
     * 接口响应
     */
    @ApiModelProperty("接口响应")
    private String responseData;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 图片业务类型：1美图，2liblib
     */
    @ApiModelProperty("图片业务类型：1美图，2liblib")
    private Integer sourceType;
}