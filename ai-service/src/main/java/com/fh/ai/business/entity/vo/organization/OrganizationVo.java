package com.fh.ai.business.entity.vo.organization;

import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织机构表
 * 
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
@Data
public class OrganizationVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private Long parentId;

    /**
     * 上级组织机构ids
     */
    @ApiModelProperty("上级组织机构ids")
    private String superiorIds;

    /**
     * 组织机构名称
     */
    @ApiModelProperty("组织机构名称")
    private String name;

    /**
     * 组织机构简称
     */
    @ApiModelProperty("组织机构简称")
    private String shortName;

    /**
     * 状态：1启用、2禁用
     */
    @ApiModelProperty("状态：1启用、2禁用")
    private Integer state;

    /**
     * 是否统计：1统计、2不统计
     */
    @ApiModelProperty("是否统计：1统计、2不统计")
    private Integer isStatistics;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    private Integer sort;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @ApiModelProperty("是否删除（1：正常 2：删除）")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 所属组织路径
     */
    @ApiModelProperty("所属组织路径")
    private String orgPath;

    /**
     * 生效开始时间
     */
    @ApiModelProperty("生效开始时间")
    private Date authStartTime;
    /**
     * 生效结束时间
     */
    @ApiModelProperty("生效结束时间")
    private Date authEndTime;

    @ApiModelProperty("已创建账号数")
    private Integer userCount;

    @ApiModelProperty("企业管理员")
    private AdminVo organizationAdmin;

    @ApiModelProperty("企业套餐")
    private OrganizationPackageVo organizationPackage;
}