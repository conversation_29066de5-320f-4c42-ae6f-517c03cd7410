package com.fh.ai.business.entity.vo.organizationPackage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-29  09:25
 */
@Data
public class OrganizationAccumulateUsageStatisticVo {

    /**
     * 文本推理剩余总量
     */
    @ApiModelProperty("文本推理使用量")
    private Long inferenceUsageNum;

    /**
     * 录音文件转写剩余总量（分钟）
     */
    @ApiModelProperty("录音文件转写使用量（分钟）")
    private Long transliterateUsageNum;

    /**
     * 图片生成剩余总量
     */
    @ApiModelProperty("图片生成使用量")
    private Long mtUsageNum;

    @ApiModelProperty("ppt生成使用量")
    private Long pptUsageNum;

}
