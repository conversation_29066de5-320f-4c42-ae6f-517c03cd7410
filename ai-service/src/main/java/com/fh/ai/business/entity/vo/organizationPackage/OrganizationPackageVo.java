package com.fh.ai.business.entity.vo.organizationPackage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 企业套餐
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
@Data
public class OrganizationPackageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 企业套餐id
     */
    @ApiModelProperty("企业套餐id")
    private Long id;

    /**
     * 权益类型 1-试用客户 2-付费用户
     */
    @ApiModelProperty("权益类型 1-试用客户 2-付费用户")
    private Integer rightsType;

    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    private Long organizationId;

    /**
     * 套餐名称
     */
    @ApiModelProperty("套餐名称")
    private String packageInfoId;

    /**
     * 套餐有效期开始时间
     */
    @ApiModelProperty("套餐有效期开始时间")
    private Date authTimeStart;

    /**
     * 套餐有效期截止时间
     */
    @ApiModelProperty("套餐有效期截止时间")
    private Date authTimeEnd;

    /**
     * 可开通账号数
     */
    @ApiModelProperty("可开通账号数")
    private Long accountNumTotal;

    /**
     * 文本推理可使用总量
     */
    @ApiModelProperty("文本推理可使用总量")
    private Long inferenceNumTotal;

    /**
     * 录音文件转写可使用总量（分钟）
     */
    @ApiModelProperty("录音文件转写可使用总量（秒）")
    private Long transliterateNumTotal;

    /**
     * 图片生成可使用总量
     */
    @ApiModelProperty("图片生成可使用总量")
    private Long mtNumTotal;

    /**
     * 剩余可开通账号数
     */
    @ApiModelProperty("剩余可开通账号数")
    private Long accountNum;

    /**
     * 文本推理剩余总量
     */
    @ApiModelProperty("文本推理剩余总量")
    private Long inferenceNum;

    /**
     * 录音文件转写剩余总量（分钟）
     */
    @ApiModelProperty("录音文件转写剩余总量（秒）")
    private Long transliterateNum;

    /**
     * 图片生成剩余总量
     */
    @ApiModelProperty("图片生成剩余总量")
    private Long mtNum;

    /**
     * 套餐描述
     */
    @ApiModelProperty("套餐描述")
    private String remark;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @ApiModelProperty("是否删除（1：正常 2：删除）")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("套餐名称")
    private String packageName;

    /**
     * 配额总量
     */
    @ApiModelProperty("配额总量")
    private Long quotaTotal;
    /**
     * 剩余配额
     */
    @ApiModelProperty("剩余配额")
    private Long quota;

    /**
     * 文本推理剩余总量
     */
    @ApiModelProperty("文本推理使用量")
    private Long inferenceUsageNum;

    /**
     * 录音文件转写剩余总量（分钟）
     */
    @ApiModelProperty("录音文件转写使用量（秒）")
    private Long transliterateUsageNum;

    /**
     * 图片生成剩余总量
     */
    @ApiModelProperty("图片生成使用量")
    private Long mtUsageNum;

    @ApiModelProperty("ppt使用量")
    private Long pptUsageNum;

    /**
     * 文本推理剩余总量
     */
    @ApiModelProperty("文本推理消费量（元）")
    private BigDecimal inferenceUsageAmount;

    /**
     * 录音文件转写剩余总量（分钟）
     */
    @ApiModelProperty("录音文件转写消费量（元）")
    private BigDecimal transliterateUsageAmount;

    /**
     * 图片生成剩余总量
     */
    @ApiModelProperty("图片生成消费量（元）")
    private BigDecimal mtUsageAmount;

    @ApiModelProperty("ppt生成消费金额（元）")
    private BigDecimal pptUsageAmount;

    @ApiModelProperty("账户余额")
    private BigDecimal balance;

    @ApiModelProperty("消费金额")
    private BigDecimal consumeAmount;

    /**
     * 余量提醒 1-未提醒 2-已提醒
     */
    @ApiModelProperty("余量提醒")
    private Integer smsMarginRemind;

    /**
     * 欠费提醒 1-未提醒 2-已提醒
     */
    @ApiModelProperty("欠费提醒")
    private Integer smsArrearsRemind;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationPackageVo returnOwn() {
        return this;
    }

}
