package com.fh.ai.business.entity.vo.organizationPackageRecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 企业套餐开通记录
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:38
 */
@Data
public class OrganizationPackageRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 企业开通套餐记录id
     */
    @ApiModelProperty("企业开通套餐记录id")
    private Long id;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 套餐id
     */
    @ApiModelProperty("套餐id")
    private String packageInfoId;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @ApiModelProperty("是否删除（1：正常 2：删除）")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("充值金额")
    private BigDecimal amount;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationPackageRecordVo returnOwn() {
        return this;
    }

}
