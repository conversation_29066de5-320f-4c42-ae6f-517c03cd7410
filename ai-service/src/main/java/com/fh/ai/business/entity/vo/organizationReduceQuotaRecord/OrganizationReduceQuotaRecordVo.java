package com.fh.ai.business.entity.vo.organizationReduceQuotaRecord;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业套餐
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
@Data
public class OrganizationReduceQuotaRecordVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 企业套餐id
	 */
	private Long id;

	/**
     * 用户oid
	 */
	private String userOid;

	/**
	 * 企业id
	 */
	private Long organizationId;

    /**
     * 类型
	 */
	private Integer type;

	/**
	 * 文本推理使用量
	 */
	private Long inferenceUsageNum;

	/**
	 * 录音文件转写使用量（秒）
	 */
	private Long transliterateUsageNum;

	/**
	 * 图片生成使用量
	 */
	private Long mtUsageNum;

	/**
	 * 文本推理消费金额（元）
	 */
	private BigDecimal inferenceUsageAmount;

	/**
	 * 录音文件转写消费金额（元）
	 */
	private BigDecimal transliterateUsageAmount;

	/**
	 * 图片生成消费金额（元）
	 */
	private BigDecimal mtUsageAmount;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	private Integer isDelete;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * ppt生成使用量
	 */
	private Long pptUsageNum;

	/**
	 * ppt生成消费金额（元）
	 */
	private BigDecimal pptUsageAmount;

}
