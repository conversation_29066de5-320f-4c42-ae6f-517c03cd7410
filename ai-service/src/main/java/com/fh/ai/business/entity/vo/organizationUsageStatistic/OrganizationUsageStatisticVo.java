package com.fh.ai.business.entity.vo.organizationUsageStatistic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-29  10:34
 */
@Data
public class OrganizationUsageStatisticVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业套餐id
     */
    private Long id;

    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    private Long organizationId;

    @ApiModelProperty("企业名称")
    private String organizationName;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("统计日期")
    private Date statisticDay;

    /**
     * 文本推理使用量
     */
    @ApiModelProperty("文本推理使用量")
    private Long inferenceUsageNum;

    /**
     * 录音文件转写使用量（秒）
     */
    @ApiModelProperty("录音文件转写使用量（秒）")
    private Long transliterateUsageNum;

    /**
     * 图片生成使用量
     */
    @ApiModelProperty("图片生成使用量")
    private Long mtUsageNum;

    /**
     * 使用配额
     */
    @ApiModelProperty("使用配额")
    private Long usageQuota;

    /**
     * 是否删除（1：正常 2：删除）
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * ppt生成使用量
     */
    private Long pptUsageNum;

}
