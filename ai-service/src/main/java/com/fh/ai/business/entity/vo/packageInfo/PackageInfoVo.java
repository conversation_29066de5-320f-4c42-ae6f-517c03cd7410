package com.fh.ai.business.entity.vo.packageInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 套餐表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:13
 */
@Data
public class PackageInfoVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 套餐id
     */
    @ApiModelProperty("套餐id")
    private Long packageInfoId;

    /**
     * 套餐名称
     */
    @ApiModelProperty("套餐名称")
    private String packageName;

    /**
     * 有效天数
     */
    @ApiModelProperty("有效天数")
    private Long authDays;

    /**
     * 可开通账号数
     */
    @ApiModelProperty("可开通账号数")
    private Long accountNumTotal;

    /**
     * 文本推理可使用总量
     */
    @ApiModelProperty("文本推理可使用总量")
    private Long inferenceNumTotal;

    /**
     * 录音文件转写可使用总量（分钟）
     */
    @ApiModelProperty("录音文件转写可使用总量（分钟）")
    private Long transliterateNumTotal;

    /**
     * 图片生成可使用总量
     */
    @ApiModelProperty("图片生成可使用总量")
    private Long mtNumTotal;

    /**
     * 套餐描述
     */
    @ApiModelProperty("套餐描述")
    private String remark;

    /**
     * 是否删除（1：正常 2：删除）
     */
    @ApiModelProperty("是否删除（1：正常 2：删除）")
    private Integer isDelete;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("套餐金额")
    private BigDecimal amount;

    /*
     * 方便steam流存入自身
     * */
    public PackageInfoVo returnOwn() {
        return this;
    }

}
