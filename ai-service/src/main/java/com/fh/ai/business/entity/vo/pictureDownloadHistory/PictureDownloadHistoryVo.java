package com.fh.ai.business.entity.vo.pictureDownloadHistory;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-06  17:58
 */
@Data
public class PictureDownloadHistoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 企业id
     */
    private Long organizationId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 搞定作品id
     */
    private String gaodingId;

    /**
     * 结果
     */
    private String result;

    /**
     * 是否删除（1：正常 2：删除）
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;


}
