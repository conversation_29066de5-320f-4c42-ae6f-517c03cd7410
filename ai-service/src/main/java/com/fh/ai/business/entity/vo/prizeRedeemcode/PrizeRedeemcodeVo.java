package com.fh.ai.business.entity.vo.prizeRedeemcode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 奖品兑换码表
 * 
 * <AUTHOR>
 * @date 2024-05-13 15:04:13
 */
@Data
public class PrizeRedeemcodeVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 奖品id
     */
    @ApiModelProperty("奖品id")
    private Long prizeId;

    /**
     * 码值
     */
    @ApiModelProperty("码值")
    private String code;

    /**
     * 状态 1未使用 2已使用
     */
    @ApiModelProperty("状态 1未使用 2已使用")
    private Integer state;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

}