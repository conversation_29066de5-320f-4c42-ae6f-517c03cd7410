package com.fh.ai.business.entity.vo.proofreading;

import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.common.proofreading.fz.req.FZReviewContentReq;
import com.fh.ai.common.proofreading.fz.req.FZReviewDocsimReq;
import com.fh.ai.common.proofreading.fz.req.FZReviewTxtReq;
import lombok.Data;

/**
 * @Classname FZExecuteParam
 * @Description 方正执行异步任务的入参
 * @Date 2025/2/10 18:42
 * @Created by admin
 */
@Data
public class FZExecuteParam {
    /**
     * 任务类型
     */
    private Integer type;
    /**
     * 任务dto
     */
    private ProofreadingTaskDto taskDto;
    /**
     * 文件审校入参对象
     */
    private FZReviewTxtReq fzReviewTxtReq;

    /**
     * 内容审校/参考文献审校入参对象
     */
    private FZReviewContentReq fzReviewContentReq;
    /**
     * 上下文查重审校入参对象
     */
    private FZReviewDocsimReq fzReviewDocsimReq;
}
