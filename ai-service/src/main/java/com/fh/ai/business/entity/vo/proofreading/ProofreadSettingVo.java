package com.fh.ai.business.entity.vo.proofreading;

import com.fh.ai.common.proofreading.fh.req.PPMSettingOption;
import com.fh.ai.common.proofreading.fz.req.FZSettingOption;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户审校设置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-20 14:39:57
 */
@Data
public class ProofreadSettingVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 设置类型，1凤凰在线审校设置、2方正在线审校设置、3凤凰文档审校设置、
     * 4方正文档审校设置-精准模式、5方正文档审校设置-平衡模式、6方正文档审校设置-全面模式
     */
    @ApiModelProperty("设置类型，1凤凰在线审校设置、2方正在线审校设置、3凤凰文档审校设置、\n" +
            "4方正文档审校设置-精准模式、5方正文档审校设置-平衡模式、6方正文档审校设置-全面模式")
    private Integer settingType;

    /**
     * 审校设置json体
     */
    @ApiModelProperty("审校设置json体")
    private String settingParams;
    /**
     * 凤凰审校设置
     */
    private PPMSettingOption ppmSettingOption;
    /**
     * 方正审校设置
     */
    private FZSettingOption fzSettingOption;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ProofreadSettingVo returnOwn() {
        return this;
    }

}
