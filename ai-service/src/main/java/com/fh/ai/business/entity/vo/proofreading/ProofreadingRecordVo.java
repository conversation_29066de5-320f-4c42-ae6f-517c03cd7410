package com.fh.ai.business.entity.vo.proofreading;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户审校记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:02:37
 */
@Data
public class ProofreadingRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 审校类型，1在线校对、2上传文档校对
     */
    @ApiModelProperty("审校类型，1在线校对、2上传文档校对")
    private Integer recordType;

    /**
     * 审校记录状态，-1审校失败、0未处理 1处理中、2已完成
     */
    @ApiModelProperty("审校记录状态，-1审校失败、0未处理 1处理中、2已完成")
    private Integer recordState;

    /**
     * 当前审校记录执行的审校任务列表：
     * 111在线-凤凰文本审校，121在线-方正文本审校，122在线-方正内容审校，211文档-凤凰文件审校，221文档-方正文件审校，222文档-方正内容审校，
     * 223文档-方正上下文审校，224文档-方正参考文献审校
     */
    @ApiModelProperty("当前审校记录执行的审校任务列表：111在线-凤凰文本审校，121在线-方正文本审校，122在线-方正内容审校，211文档-凤凰文件审校，221文档-方正文件审校，" +
            "222文档-方正内容审校，223文档-方正上下文审校，224文档-方正参考文献审校")
    private String executedTaskInfo;

    /**
     * 审校源文件名称
     */
    @ApiModelProperty("审校源文件名称")
    private String originalFileName;

    /**
     * 审校源文件oid
     */
    @ApiModelProperty("审校源文件oid")
    private String originalFileOid;

    /**
     * 审校源文件url
     */
    @ApiModelProperty("审校源文件url")
    private String originalFileUrl;

    /**
     * 文件上传状态，1上传中、2上传成功、3上传失败
     */
    @ApiModelProperty("文件上传状态，1上传中、2上传成功、3上传失败")
    private Integer uploadState;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private Date submitTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;
    /**
     * 审校记录关联的审校任务
     */
    private List<ProofreadingTaskVo> tasks;

    /*
     * 方便steam流存入自身
     * */
    public ProofreadingRecordVo returnOwn() {
        return this;
    }

}
