package com.fh.ai.business.entity.vo.proofreading;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 审校任务表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:03:26
 */
@Data
public class ProofreadingTaskVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 审校源，1凤凰，2方正
     */
    @ApiModelProperty("审校源，1凤凰，2方正")
    private Integer sourceType;

    /**
     * 审校任务的具体类型
     */
    @ApiModelProperty("审校任务的具体类型")
    private Integer taskType;

    /**
     * 审校任务状态：-1处理失败、0未处理、1处理中、2处理成功
     */
    @ApiModelProperty("-1处理失败、0未处理、1处理中、2处理成功")
    private Integer taskState;

    /**
     * 第三方任务id
     */
    @ApiModelProperty("第三方任务id")
    private String thirdTaskId;

    /**
     * 第三方文件id
     */
    @ApiModelProperty("第三方文件id")
    private String thirdFileId;

    /**
     * 审校结果文件名称
     */
    @ApiModelProperty("审校结果文件名称")
    private String resultFileName;

    /**
     * 审校结果文件oid
     */
    @ApiModelProperty("审校结果文件oid")
    private String resultFileOid;

    /**
     * 审校结果文件url
     */
    @ApiModelProperty("审校结果文件url")
    private String resultFileUrl;

    /**
     * 审校报告文件名称
     */
    @ApiModelProperty("审校报告文件名称")
    private String reportFileName;

    /**
     * 审校报告文件oid
     */
    @ApiModelProperty("审校报告文件oid")
    private String reportFileOid;

    /**
     * 审校报告文件url
     */
    @ApiModelProperty("审校报告文件url")
    private String reportFileUrl;

    /**
     * 审校设置参数
     */
    @ApiModelProperty("审校设置参数")
    private String settingOptionInfo;

    /**
     * 审校请求信息
     */
    @ApiModelProperty("审校请求信息")
    private String requestInfo;

    /**
     * 审校响应信息
     */
    @ApiModelProperty("审校响应信息")
    private String responseInfo;

    /**
     * 修正后的审校结果文本
     */
    @ApiModelProperty("修正后的审校结果文本")
    private String modifyResult;

    /**
     * 审校任务提交时间
     */
    @ApiModelProperty("审校任务提交时间")
    private Date submitTime;

    /**
     * 审校任务完成时间
     */
    @ApiModelProperty("审校任务完成时间")
    private Date finishTime;

    /**
     * 审校记录表，p_proofreading_record的主键
     */
    @ApiModelProperty("审校记录表，p_proofreading_record的主键")
    private Long proofreadingRecordId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ProofreadingTaskVo returnOwn() {
        return this;
    }

}
