package com.fh.ai.business.entity.vo.publishNews;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 热点新闻接口调用地址表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-23 15:10:51
 */
@Data
public class NewsHashVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事
     */
    @ApiModelProperty("资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事")
    private Integer newsType;

    /**
     * 资讯文章分类 出版 文化 教育 科技 人物 时事
     */
    @ApiModelProperty("资讯文章分类 出版 文化 教育 科技 人物 时事")
    private String newsCategory;

    /**
     * 链接来源于榜眼的哪个渠道（滂湃新闻等）
     */
    @ApiModelProperty("链接来源于榜眼的哪个渠道（滂湃新闻等）")
    private String channel;

    /**
     * 接口调用hashid
     */
    @ApiModelProperty("接口调用hashid")
    private String hashId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 1: 未删除, 2: 已删除
     */
    @ApiModelProperty("1: 未删除, 2: 已删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public NewsHashVo returnOwn() {
        return this;
    }

}
