package com.fh.ai.business.entity.vo.publishNews;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 出版资讯表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 17:23:42
 */
@Data
public class PublishNewsVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 资讯来源 1微信公众号 2榜眼数据
     */
    @ApiModelProperty("资讯来源 1微信公众号 2榜眼数据")
    private Integer sourceType;

    /**
     * 资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事
     */
    @ApiModelProperty("资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事")
    private Integer newsType;
    /**
     * 资讯文章分类 出版 文化 教育 科技 人物 时事
     */
    @ApiModelProperty("资讯文章分类 出版 文化 教育 科技 人物 时事")
    private String newsCategory;
    /**
     * 大模型智能分析后的结果
     */
    @ApiModelProperty("大模型智能分析后的结果")
    private String modelAnalysis;
    /**
     * 链接来源于哪个栏目，比如公众号的某个具体的公众号；或者榜眼的某个渠道（滂湃新闻等）
     */
    @ApiModelProperty("链接来源于哪个栏目，比如公众号的某个具体的公众号；或者榜眼的某个渠道（滂湃新闻等）")
    private String channel;

    /**
     * 录入方式 1程序爬取，2后台手动录入
     */
    @ApiModelProperty("录入方式 1程序爬取，2后台手动录入")
    private Integer inputYpe;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 文章摘要
     */
    @ApiModelProperty("文章摘要")
    private String summary;

    /**
     * 文章内容
     */
    @ApiModelProperty("文章内容")
    private String content;

    /**
     * 阅读次数
     */
    @ApiModelProperty("阅读次数")
    private Integer viewCount;

    /**
     * 资讯链接
     */
    @ApiModelProperty("资讯链接")
    private String url;

    /**
     * 文章原发布时间
     */
    @ApiModelProperty("文章原发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 文章作者（网名/公众号）
     */
    @ApiModelProperty("文章作者（网名/公众号）")
    private String author;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 1: 未删除, 2: 已删除
     */
    @ApiModelProperty("1: 未删除, 2: 已删除")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @ApiModelProperty("刷新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshTime;

    /*
     * 方便steam流存入自身
     * */
    public PublishNewsVo returnOwn() {
        return this;
    }

}
