package com.fh.ai.business.entity.vo.roleMenu;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色菜单
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Data
public class RoleMenuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联表id
     */
    private Long id;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Integer createUser;


    /**
     * 更新人
     */
    private Integer updateUser;


}
