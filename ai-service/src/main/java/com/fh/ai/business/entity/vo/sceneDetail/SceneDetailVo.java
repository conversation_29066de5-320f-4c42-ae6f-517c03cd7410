package com.fh.ai.business.entity.vo.sceneDetail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 场景详情表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:26
 */
@Data
public class SceneDetailVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 场景表id
     */
    @ApiModelProperty("场景表id")
    private Long sceneId;

    /**
     * 媒体资源类型 1-图片
     */
    @ApiModelProperty("媒体资源类型 1-图片")
    private Integer mediaType;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String fileOid;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String fileUrl;

    /**
     * 方向类型 1-横向 2-竖向 3-正方形
     */
    @ApiModelProperty("方向类型")
    private Integer directionType;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public SceneDetailVo returnOwn() {
        return this;
    }

}
