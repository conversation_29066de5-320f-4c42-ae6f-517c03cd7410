package com.fh.ai.business.entity.vo.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by cw on 2024/5/16.
 */
@Data
public class OrgStatisticsVo {

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 所属组织名称
     */
    @ApiModelProperty("所属组织名称")
    private String organizationName;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    private Integer sort;

    /**
     * 登录用户数
     */
    @ApiModelProperty("登录用户数")
    private Long loginUsers;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

}
