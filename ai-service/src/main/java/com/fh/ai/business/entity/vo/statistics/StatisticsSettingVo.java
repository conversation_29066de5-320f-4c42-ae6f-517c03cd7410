package com.fh.ai.business.entity.vo.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 统计设置VO
 *
 * <AUTHOR>
 * @date 2024-05-16 17:08:46
 */
@Data
public class StatisticsSettingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 最近一次更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近一次更新时间")
    private Date lastUpdateTime;
}