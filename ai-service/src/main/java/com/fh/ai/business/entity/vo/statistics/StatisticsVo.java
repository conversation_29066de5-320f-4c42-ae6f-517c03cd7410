package com.fh.ai.business.entity.vo.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 统计表
 * 
 * <AUTHOR>
 * @date 2024-05-16 17:08:46
 */
@Data
public class StatisticsVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 访问次数
     */
    @ApiModelProperty("访问次数")
    private Long visits;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    private Long logins;

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long appUsageCount;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

    /**
     * 是否新建，1：老用户，2：新用户
     */
    @ApiModelProperty("是否新建，1：老用户，2：新用户")
    private Integer isNew;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

}