package com.fh.ai.business.entity.vo.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台每日统计
 *
 * <AUTHOR>
 * @date 2024-05-16 17:08:46
 */
@Data
public class SysDailyStatisticsInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("统计日期")
    private Date statisticsDate;

    /**
     * 访问次数
     */
    @ApiModelProperty("访问次数")
    private Long visits;

    /**
     * 访问用户数
     */
    @ApiModelProperty("访问用户数")
    private Long visitUsers;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    private Long logins;

    /**
     * 新增用户数
     */
    @ApiModelProperty("新增用户数")
    private Long newUsers;

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long appUsageCount;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

}