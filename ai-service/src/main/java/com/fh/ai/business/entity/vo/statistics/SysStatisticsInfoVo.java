package com.fh.ai.business.entity.vo.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 统计表
 *
 * <AUTHOR>
 * @date 2024-05-16 17:08:46
 */
@Data
public class SysStatisticsInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 访问次数
     */
    @ApiModelProperty("访问次数")
    private Long visits;

    /**
     * 访问用户数
     */
    @ApiModelProperty("访问用户数")
    private Long visitUsers;

    /**
     * 用户数
     */
    @ApiModelProperty("用户数")
    private Long users;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    private Long logins;

    /**
     * 登录用户数
     */
    @ApiModelProperty("登录用户数")
    private Long loginUsers;

    /**
     * 新增用户数
     */
    @ApiModelProperty("新增用户数")
    private Long newUsers;

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long appUsageCount;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

    /**
     * 应用有用采用率
     */
    @ApiModelProperty("应用有用采用率")
    private String usefulAdoptionRate;

    /**
     * 应用复制采用率
     */
    @ApiModelProperty("应用复制采用率")
    private String copyAdoptionRate;

    /**
     * 应用下载采用率
     */
    @ApiModelProperty("应用下载采用率")
    private String downloadAdoptionRate;
}