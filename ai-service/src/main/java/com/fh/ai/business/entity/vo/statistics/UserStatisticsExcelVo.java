package com.fh.ai.business.entity.vo.statistics;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * Created by cw on 2024/5/16.
 */
@Data
public class UserStatisticsExcelVo {

    /**
     * 账号
     */
    @Excel(name = "用户名", width = 50)
    private String account;

    /**
     * 真实姓名
     */
    @Excel(name = "姓名", width = 50)
    private String realName;

    /**
     * 应用使用次数
     */
    @Excel(name = "应用使用次数", width = 30)
    private Long appUsageCount;

    /**
     * 每日登录
     */
    @Excel(name = "登录次数", width = 30)
    private Long logins;

}
