package com.fh.ai.business.entity.vo.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by cw on 2024/5/16.
 */
@Data
public class UserStatisticsVo {

    /**
     * oid
     */
    @ApiModelProperty("oid")
    private String oid;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 访问次数
     */
    @ApiModelProperty("访问次数")
    private Long visits;

    /**
     * 访问用户数
     */
    @ApiModelProperty("访问用户数")
    private Long visitUsers;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    private Long logins;

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long appUsageCount;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

}
