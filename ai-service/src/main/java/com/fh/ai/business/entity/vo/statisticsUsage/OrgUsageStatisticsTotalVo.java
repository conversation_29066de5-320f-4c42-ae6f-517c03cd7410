package com.fh.ai.business.entity.vo.statisticsUsage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by cw on 2024/5/16.
 */
@Data
public class OrgUsageStatisticsTotalVo {

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long usageCount;

    /**
     * 应用使用用户数
     */
    @ApiModelProperty("应用使用用户数")
    private Long userCount;

    /**
     * 登录用户数
     */
    @ApiModelProperty("登录用户数")
    private Long loginUsers;

    /**
     * 产生积分
     */
    @ApiModelProperty("产生积分")
    private Long generateScore;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;
}
