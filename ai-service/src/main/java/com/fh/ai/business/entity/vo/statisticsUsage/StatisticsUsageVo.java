package com.fh.ai.business.entity.vo.statisticsUsage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 使用情况统计表
 * 
 * <AUTHOR>
 * @date 2024-05-15 14:46:40
 */
@Data
public class StatisticsUsageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 应用类型，1智能问答，2办公应用，3出版应用，4营销应用
     */
    @ApiModelProperty("应用类型，1智能问答，2办公应用，3出版应用，4营销应用")
    private Integer appType;

    private Integer type;

    /**
     * 应用使用次数
     */
    @ApiModelProperty("应用使用次数")
    private Long appUsageCount;

    /**
     * 是否收藏：1收藏，2不收藏
     */
    @ApiModelProperty("是否收藏：1收藏，2不收藏")
    private Integer isFavorite;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

}