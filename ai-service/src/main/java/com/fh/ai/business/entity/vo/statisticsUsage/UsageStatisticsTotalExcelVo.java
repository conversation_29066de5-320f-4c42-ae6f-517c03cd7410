package com.fh.ai.business.entity.vo.statisticsUsage;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * Created by cw on 2024/5/16.
 */
@Data
public class UsageStatisticsTotalExcelVo {

    @Excel(name = "应用类型", width = 50)
    private String appType;

    @Excel(name = "应用名称", width = 50)
    private String type;

    @Excel(name = "应用使用次数", width = 50)
    private Long totalUsageCount;

    @Excel(name = "应用使用人数", width = 50)
    private Long totalUserCount;

    @Excel(name = "应用收藏人数", width = 50)
    private Long totalFavoriteCount;

}
