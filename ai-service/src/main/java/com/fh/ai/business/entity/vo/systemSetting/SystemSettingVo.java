package com.fh.ai.business.entity.vo.systemSetting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 系统设置表
 * 
 * <AUTHOR>
 * @date 2024-02-23 14:05:25
 */
@Data
public class SystemSettingVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 参数名称
     */
    @ApiModelProperty("参数名称")
    private String name;

    /**
     * 值
     */
    @ApiModelProperty("值")
    private String value;

    /**
     * 最小值
     */
    @ApiModelProperty("最小值")
    private String minValue;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 内容标题
     */
    @ApiModelProperty("内容标题")
    private String remarkTitle;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 
     */
    @ApiModelProperty("")
    private String url;

}