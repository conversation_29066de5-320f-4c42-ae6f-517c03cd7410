package com.fh.ai.business.entity.vo.updateLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 更新日志
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Data
public class UpdateLogVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 参数名称
     */
    private String versionName;

    /**
     * 标题
     */
    private String title;

    /**
     * 更新内容
     */
    private String content;

    /**
     * 弹框图片
     */
    private String imageUrl;

    private String imageH5Url;

    /**
     * 跳转地址
     */
    private String jumpUrl;

    /**
     * 状态：1未发布，2已发布
     */
    private Integer state;

    private Integer type;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date publishTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
