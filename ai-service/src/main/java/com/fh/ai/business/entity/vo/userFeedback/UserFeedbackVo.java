package com.fh.ai.business.entity.vo.userFeedback;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户反馈表
 * 
 * <AUTHOR>
 * @date 2024-07-01 10:31:51
 */
@Data
public class UserFeedbackVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 反馈内容
     */
    @ApiModelProperty("反馈内容")
    private String content;

    /**
     * 赠送积分
     */
    @ApiModelProperty("赠送积分")
    private Long giveScore;

    /**
     * 赠送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("赠送时间")
    private Date giveTime;

    /**
     * 状态：1未查阅 2已查阅 3已采纳
     */
    @ApiModelProperty("状态：1未查阅 2已查阅 3已采纳")
    private Integer state;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 所属组织
     */
    @ApiModelProperty("所属组织")
    private String organizationName;

    /**
     * 所属组织路径
     */
    @ApiModelProperty("所属组织路径")
    private String orgPath;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String fileName;
    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String fileOid;
    /**
     * 文件url
     */
    @ApiModelProperty("文件url")
    private String fileUrl;

    /**
     * 反馈类型 1-开发建议 2-问题反馈 3-其他
     */
    @ApiModelProperty("反馈类型")
    private Integer type;

    /**
     * 应用类型
     */
    @ApiModelProperty("应用类型")
    private Integer appType;
}