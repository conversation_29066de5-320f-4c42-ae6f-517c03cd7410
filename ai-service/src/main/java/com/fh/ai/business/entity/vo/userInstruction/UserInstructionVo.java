package com.fh.ai.business.entity.vo.userInstruction;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户指令
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-08  10:47
 */
@Data
public class UserInstructionVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户指令唯一uuid
     */
    private String uuid;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 指令标题
     */
    private String title;

    /**
     * 指令内容
     */
    private String content;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;
}
