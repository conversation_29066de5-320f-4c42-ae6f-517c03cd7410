package com.fh.ai.business.entity.vo.userPrize;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户使用情况
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
@Data
public class UserPrizeTotalVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 兑换次数
     */
    @ApiModelProperty("兑换次数")
    private Long redeemCount;

    /**
     * 兑换积分
     */
    @ApiModelProperty("兑换积分")
    private Long redeemScore;

}