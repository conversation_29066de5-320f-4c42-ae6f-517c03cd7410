package com.fh.ai.business.entity.vo.userPrize;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户奖品兑换表
 * 
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
@Data
public class UserPrizeVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 奖品id
     */
    @ApiModelProperty("奖品id")
    private Long prizeId;

    /**
     * 奖品名称
     */
    @ApiModelProperty("奖品名称")
    private String prizeName;

    /**
     * 兑换码
     */
    @ApiModelProperty("兑换码")
    private String redeemCode;

    /**
     * 积分
     */
    @ApiModelProperty("积分")
    private Long score;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String notes;

    /**
     * 状态 1未领取 2已领取
     */
    @ApiModelProperty("状态 1未领取 2已领取")
    private Integer state;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String picture;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String account;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 所属组织
     */
    @ApiModelProperty("所属组织")
    private String organizationName;

    @ApiModelProperty("所属组织路径")
    private String orgPath;
}