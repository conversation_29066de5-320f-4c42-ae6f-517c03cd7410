package com.fh.ai.business.entity.vo.userScoreRecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ScoreTypeStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取类型：1每日登录，2智能问答，3文案生成，4作品创作，5奖品兑换，6积分抽奖，7积分红包
     */
    @ApiModelProperty("获取类型：1每日登录，2智能问答，3文案生成，4作品创作，5奖品兑换，6积分抽奖，7积分红包")
    private Integer type;

    /**
     * 用户积分
     */
    @ApiModelProperty("用户积分")
    private Long totalScore;

    /**
     * 获取次数
     */
    @ApiModelProperty("获取次数")
    private Long totalCount;

    /**
     * 是否达到上限：false否，true是
     */
    @ApiModelProperty("是否达到上限：false否，true是")
    private boolean reachTop;

    /**
     * 一次积分值
     */
    @ApiModelProperty("一次积分值")
    private Long onceScore;

    /**
     * 积分上限
     */
    @ApiModelProperty("积分上限")
    private Long topScore;

    /**
     * 次数上限
     */
    @ApiModelProperty("次数上限")
    private Long topCount;
}
