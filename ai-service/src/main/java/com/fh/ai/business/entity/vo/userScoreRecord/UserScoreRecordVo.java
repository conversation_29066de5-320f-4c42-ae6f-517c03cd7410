package com.fh.ai.business.entity.vo.userScoreRecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户积分记录
 *
 * <AUTHOR>
 * @date 2024-02-20 17:14:17
 */
@Data
public class UserScoreRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 获取类型：1每日登录，2智能问答，3文案生成，4作品创作，5奖品兑换，6积分抽奖，7积分红包，8活动任务，9意见反馈
     */
    @ApiModelProperty("获取类型：1每日登录，2智能问答，3文案生成，4作品创作，5奖品兑换，6积分抽奖，7积分红包，8活动任务，9意见反馈")
    private Integer type;

    /**
     * 关联id，根据类型区分
     */
    @ApiModelProperty("关联id，根据类型区分")
    private Long relationId;

    /**
     * 积分
     */
    @ApiModelProperty("积分")
    private Long score;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

}