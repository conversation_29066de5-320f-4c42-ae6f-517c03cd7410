package com.fh.ai.business.entity.vo.userScoreRecord;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserScoreVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 用户积分
     */
    @ApiModelProperty("用户积分")
    private Long score;

    /**
     * 成长值
     */
    @ApiModelProperty("成长值")
    private Long growth;

}
