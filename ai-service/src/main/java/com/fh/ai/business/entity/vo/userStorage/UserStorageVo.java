package com.fh.ai.business.entity.vo.userStorage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户本地信息存储表，用于存储用户配置等信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-03 16:27:55
 */
@Data
public class UserStorageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * oid
     */
    @ApiModelProperty("oid")
    private String oid;

    /**
     * 缓存key
     */
    @ApiModelProperty("缓存key")
    private String stKey;

    /**
     * 缓存数据
     */
    @ApiModelProperty("缓存数据")
    private String stValue;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public UserStorageVo returnOwn() {
        return this;
    }

}
