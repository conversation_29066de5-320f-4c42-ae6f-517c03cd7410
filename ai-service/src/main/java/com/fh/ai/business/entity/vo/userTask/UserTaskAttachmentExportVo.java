package com.fh.ai.business.entity.vo.userTask;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class UserTaskAttachmentExportVo {

    @Excel(name = "姓名", width = 50)
    private String realName;
    @Excel(name = "单位名称", width = 50)
    private String orgPath;
    @Excel(name = "体验模块", width = 50)
    private String model;

    @Excel(name = "作品名称", width = 50)
    private String attName;

    @Excel(name = "下载链接", width = 50)
    private String url;
}
