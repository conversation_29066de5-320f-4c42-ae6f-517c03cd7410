package com.fh.ai.business.entity.vo.userTask;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class UserTaskExportVo {
    @Excel(name = "用户名", width = 50)
    private String account;

    @Excel(name = "姓名", width = 50)
    private String realName;

    @Excel(name = "单位名称", width = 50)
    private String orgPath;

    @Excel(name = "任务进度", width = 50)
    private String progress;

    @Excel(name = "审核状态", width = 50)
    private String state;

    @Excel(name = "积分发放时间", width = 50)
    private String scoreTime;
}
