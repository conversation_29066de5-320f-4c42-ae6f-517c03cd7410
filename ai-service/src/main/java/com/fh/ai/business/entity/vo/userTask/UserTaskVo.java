package com.fh.ai.business.entity.vo.userTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户任务表
 * 
 * <AUTHOR>
 * @date 2024-06-04 14:45:06
 */
@Data
public class UserTaskVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private Long taskId;
    private Integer appType;
    private Integer secondType;

    /**
     * 状态：1待审核，2审核通过，3审核不通过
     */
    @ApiModelProperty("状态：1待审核，2审核通过，3审核不通过")
    private Integer state;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 账号
     */
    private String account;

    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 所属组织路径
     */
    @ApiModelProperty("所属组织路径")
    private String orgPath;
    /**
     * 积分发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scoreTime;
    private Integer num;
    private Integer userNum;
}