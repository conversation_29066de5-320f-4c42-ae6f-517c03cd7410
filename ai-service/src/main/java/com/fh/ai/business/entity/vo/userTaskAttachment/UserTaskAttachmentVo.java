package com.fh.ai.business.entity.vo.userTaskAttachment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 任务附件表
 * 
 * <AUTHOR>
 * @date 2024-06-04 14:55:41
 */
@Data
public class UserTaskAttachmentVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 附件oid， p_attachment主键
     */
    @ApiModelProperty("附件oid， p_attachment主键")
    private String attachmentOid;

    /**
     * 类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件
     */
    @ApiModelProperty("类型：100智能问答，201内容润色，202演讲稿生成，203新闻稿生成，204日报周报，205PPT大纲，206SWOT分析，207智能会议，301选题策划，302书评，303AI绘图，304智能校对，305图书课程开发，306古籍加标点，307AI创作痕迹检测，308老编辑，309图书调研报告，310初步审稿，311出版授权委托书，312数字版权授权合同，313起草版权授权书，314文档解读助手，401标题生成，402运营文案，403活动策划，404短视频脚本，405口播脚本，406营销配图，407海报绘图，408图书广告文案大师，409图书市场策划方案，410图书宣传推广策略，411直播话术，412营销应用，501行业新闻，502规章制度，503政策文件")
    private Long type;

    private Integer appType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String oid;

    /**
     * 原文件名
     */
    @ApiModelProperty("原文件名")
    private String originalName;

    /**
     * 后缀名
     */
    @ApiModelProperty("后缀名")
    private String suffix;

    /**
     * 大小
     */
    @ApiModelProperty("大小")
    private Long size;

    /**
     * 封面
     */
    @ApiModelProperty("封面")
    private String cover;

    /**
     * 原始文件路径
     */
    @ApiModelProperty("原始文件路径")
    private String originPath;

    /**
     * 预览路径
     */
    @ApiModelProperty("预览路径")
    private String viewPath;

    /**
     * 上传状态，1：上传中，2：上传成功，3：上传失败
     */
    @ApiModelProperty("上传状态，1：上传中，2：上传成功，3：上传失败")
    private Integer uploadState;

    /**
     * 状态，1：待处理，2：处理中，3：处理成功，4：处理失败
     */
    @ApiModelProperty("状态，1：待处理，2：处理中，3：处理成功，4：处理失败")
    private Integer state;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    private String realName;
    private String orgPath;

}