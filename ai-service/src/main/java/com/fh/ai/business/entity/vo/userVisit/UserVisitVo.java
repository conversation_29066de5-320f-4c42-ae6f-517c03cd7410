package com.fh.ai.business.entity.vo.userVisit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户访问记录表
 * 
 * <AUTHOR>
 * @date 2024-05-15 13:56:34
 */
@Data
public class UserVisitVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户唯一oid
     */
    @ApiModelProperty("用户唯一oid")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 访问类型：1登录 2访问
     */
    @ApiModelProperty("访问类型：1登录 2访问")
    private Integer type;

    /**
     * 渠道：1web端，2H5端
     */
    @ApiModelProperty("渠道：1web端，2H5端")
    private Integer channel;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

}