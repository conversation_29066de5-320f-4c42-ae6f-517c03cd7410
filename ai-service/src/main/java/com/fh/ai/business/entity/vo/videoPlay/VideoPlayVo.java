package com.fh.ai.business.entity.vo.videoPlay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 视频播放表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Data
public class VideoPlayVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    private Long courseId;
    /**
     * 课程详情id
     */
    private Long courseDetailId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
