package com.fh.ai.business.entity.vo.worksActive;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 作品活动表
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 18:12:16
 */
@Data
public class WorksActiveVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 用户唯一oid，提交人id
     */
    @ApiModelProperty("用户唯一oid，提交人id")
    private String userOid;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 活动状态：1开启，2禁用
     */
    @ApiModelProperty("活动状态：1开启，2禁用")
    private Integer activeStatus;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activeName;

    /**
     * 活动简介
     */
    @ApiModelProperty("活动简介")
    private String activeProfile;

    /**
     * 活动开始时间
     */
    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activeStartTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activeEndTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 活动开始状态：1未开始，2已开始，3已结束
     */
    @ApiModelProperty("活动开始状态：1未开始，2已开始，3已结束")
    private Integer worksActiveStartType;

    /**
     * 是否支持投票 1-支持 2-不支持
     */
    @ApiModelProperty("是否支持投票 1-支持 2-不支持")
    private Integer isSupportVote;

    /**
     * 投票开始时间
     */
    @ApiModelProperty("投票开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date voteStartTime;

    /**
     * 投票结束时间
     */
    @ApiModelProperty("投票结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date voteEndTime;

    /**
     * 每天投票次数限制
     */
    @ApiModelProperty("每天投票次数限制")
    private Integer dailyVoteLimit;

    /*
     * 方便steam流存入自身
     * */
    public WorksActiveVo returnOwn() {
        return this;
    }

}
