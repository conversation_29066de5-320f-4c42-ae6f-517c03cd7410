package com.fh.ai.business.entity.vo.worksActiveVoteRecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-04  09:38
 */
@Data
public class WorksActiveVoteRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long id;

    /**
     * 用户唯一oid，提交人id
     */
    @ApiModelProperty("用户唯一oid，提交人id")
    private String userOid;

    /**
     * FK，活动id
     */
    @ApiModelProperty("FK，活动id")
    private Long worksActiveId;

    /**
     * FK，优秀作品表id
     */
    @ApiModelProperty("FK，优秀作品表id")
    private Long excellentWorksId;

    /**
     * 票数
     */
    @ApiModelProperty("票数")
    private Integer voteNumber;

    /**
     * 投票时间
     */
    @ApiModelProperty("投票时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date voteTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;
}
