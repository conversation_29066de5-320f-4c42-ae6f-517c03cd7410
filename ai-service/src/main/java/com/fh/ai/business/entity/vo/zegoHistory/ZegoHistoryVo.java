package com.fh.ai.business.entity.vo.zegoHistory;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 美图生成记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Data
public class ZegoHistoryVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 状态：1处理中，2处理成功，3处理失败
     */
    private Integer state;

    /**
     * 参数json
     */
    private String parameterJson;

    /**
     * 结果
     */
    private String result;

    /**
     * 接口响应
     */
    private String responseData;

    /**
     * 渠道：1web端，2H5端
     */
    private Integer channel;

    /**
     * 应用类型，多个逗号隔开
     */
    private String appType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
