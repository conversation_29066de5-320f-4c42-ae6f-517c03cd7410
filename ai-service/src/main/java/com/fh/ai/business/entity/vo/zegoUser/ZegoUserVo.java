package com.fh.ai.business.entity.vo.zegoUser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 美图用户使用表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Data
public class ZegoUserVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户唯一oid
     */
    private String userOid;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    private String name;

    private String cover;
    /**
     * 结果
     */
    private String result;

    /**
     * 渠道：1web端，2H5端
     */
    private Integer channel;

    /**
     * 用户参数
     */
    private String params;

    /**
     * 记录id
     */
    private Long historyId;

    /**
     * 状态：1处理中，2处理成功，3处理失败
     */
    private Integer state;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 应用类型，多个逗号隔开
     */
    private String appType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    private Integer isDelete;

}
