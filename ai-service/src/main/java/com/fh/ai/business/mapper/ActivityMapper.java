package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.activity.ActivityConditionBo;
import com.fh.ai.business.entity.dto.activity.ActivityDto;
import com.fh.ai.business.entity.vo.activity.ActivityVo;

import java.util.List;

/**
 * 活动表Mapper
 *
 * <AUTHOR>
 * @date 2024-05-13 14:48:18
 */
public interface ActivityMapper extends BaseMapper<ActivityDto> {

    List<ActivityVo> getActivityListByCondition(ActivityConditionBo condition);

}