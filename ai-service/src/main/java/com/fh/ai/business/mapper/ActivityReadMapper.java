package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.activityRead.ActivityReadConditionBo;
import com.fh.ai.business.entity.dto.activityRead.ActivityReadDto;
import com.fh.ai.business.entity.vo.activityRead.ActivityReadVo;


/**
 * 帮助中心Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-20 09:52:37
 */
public interface ActivityReadMapper extends BaseMapper<ActivityReadDto> {

	List<ActivityReadVo> getActivityReadListByCondition(ActivityReadConditionBo condition);

}
