package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.admin.AdminConditionBo;
import com.fh.ai.business.entity.dto.admin.AdminDto;
import com.fh.ai.business.entity.vo.admin.AdminVo;

import java.util.List;

/**
 * 平台用户表Mapper
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:49
 */
public interface AdminMapper extends BaseMapper<AdminDto> {

    List<AdminVo> getAdminListByCondition(AdminConditionBo condition);

}
