package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.adminRole.AdminRoleDto;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleConditionBo;
import com.fh.ai.business.entity.vo.adminRole.AdminRoleVo;

import java.util.List;

/**
 * 平台用户角色表Mapper
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:50
 */
public interface AdminRoleMapper extends BaseMapper<AdminRoleDto> {

    List<AdminRoleVo> getAdminRoleListByCondition(AdminRoleConditionBo condition);

    List<AdminRoleVo> getAdminRoleByAdminOid(String adminOid);

}
