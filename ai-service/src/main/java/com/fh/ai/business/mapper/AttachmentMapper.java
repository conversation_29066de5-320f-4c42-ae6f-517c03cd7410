package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.attachment.AttachmentConditionBo;
import com.fh.ai.business.entity.dto.attachment.AttachmentDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;

import java.util.List;

/**
 * 附件表Mapper
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
public interface AttachmentMapper extends BaseMapper<AttachmentDto> {

    List<AttachmentVo> getAttachmentListByCondition(AttachmentConditionBo condition);

}