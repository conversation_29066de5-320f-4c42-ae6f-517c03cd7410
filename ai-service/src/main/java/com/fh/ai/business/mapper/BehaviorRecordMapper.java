package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.behaviorRecord.BehaviorRecordConditionBo;
import com.fh.ai.business.entity.dto.behaviorRecord.BehaviorRecordDto;
import com.fh.ai.business.entity.vo.behaviorRecord.BehaviorRecordVo;

import java.util.List;

/**
 * 行为记录表Mapper
 *
 * <AUTHOR>
 * @date 2024-03-11 14:44:58
 */
public interface BehaviorRecordMapper extends BaseMapper<BehaviorRecordDto> {

    List<BehaviorRecordVo> getBehaviorRecordListByCondition(BehaviorRecordConditionBo condition);

    List<BehaviorRecordVo> getDistinctType(BehaviorRecordConditionBo condition);

}