package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingBookDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo;

/**
 * 软文与书籍关系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:03
 */
public interface BookCopywritingBookMapper extends BaseMapper<BookCopywritingBookDto> {

	List<BookCopywritingBookVo> getBookCopywritingBookListByCondition(BookCopywritingBookConditionBo condition);

	BookCopywritingBookVo getBookCopywritingBookByCondition(BookCopywritingBookConditionBo condition);

}
