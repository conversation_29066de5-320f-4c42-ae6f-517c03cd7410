package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingVo;

/**
 * 书籍软文表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:02
 */
public interface BookCopywritingMapper extends BaseMapper<BookCopywritingDto> {

	List<BookCopywritingVo> getBookCopywritingListByCondition(BookCopywritingConditionBo condition);

	BookCopywritingVo getBookCopywritingByCondition(BookCopywritingConditionBo condition);

}
