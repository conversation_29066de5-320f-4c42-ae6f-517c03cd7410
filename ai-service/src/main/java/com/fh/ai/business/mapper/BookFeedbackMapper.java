package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.BookFeedbackConditionBo;
import com.fh.ai.business.entity.dto.book.BookFeedbackDto;
import com.fh.ai.business.entity.vo.book.BookFeedbackVo;

import java.util.List;

/**
 * 书单mapper
 *
 * <AUTHOR>
 * @date 2025-04-18 13:48:06
 */
public interface BookFeedbackMapper extends BaseMapper<BookFeedbackDto> {

    List<BookFeedbackVo> getBookFeedbackListByCondition(BookFeedbackConditionBo condition);

    BookFeedbackVo getBookFeedbackByCondition(BookFeedbackConditionBo condition);

}
