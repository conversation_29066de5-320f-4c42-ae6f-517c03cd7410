package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.BookConditionBo;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.QuerySmartSaleBo;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageConditionBo;
import com.fh.ai.business.entity.dto.book.BookDto;
import com.fh.ai.business.entity.dto.bookList.BookListJsonTemporaryStorageDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankAndSaleInfo;
import com.fh.ai.business.entity.vo.book.SmartSaleInfo;
import com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 书单json临时存储表
 *
 * <AUTHOR>
 * @date 2025-04-18 13:48:06
 */
public interface BookListJsonTemporaryStorageMapper extends BaseMapper<BookListJsonTemporaryStorageDto> {

    List<BookListJsonTemporaryStorageVo> getBookListJsonTemporaryStorageListByCondition(BookListJsonTemporaryStorageConditionBo condition);

    BookListJsonTemporaryStorageVo getBookListJsonTemporaryStorageByCondition(BookListJsonTemporaryStorageConditionBo condition);
}
