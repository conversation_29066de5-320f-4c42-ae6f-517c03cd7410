package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.BookConditionBo;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.QuerySmartSaleBo;
import com.fh.ai.business.entity.bo.bookList.BookListBo;
import com.fh.ai.business.entity.bo.bookList.BookListConditionBo;
import com.fh.ai.business.entity.dto.book.BookDto;
import com.fh.ai.business.entity.dto.bookList.BookListDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankAndSaleInfo;
import com.fh.ai.business.entity.vo.book.SmartSaleInfo;
import com.fh.ai.business.entity.vo.bookList.BookListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 书单mapper
 *
 * <AUTHOR>
 * @date 2025-04-18 13:48:06
 */
public interface BookListMapper extends BaseMapper<BookListDto> {

    List<BookListVo> getBookListListByCondition(BookListConditionBo condition);

    BookListVo getBookListByCondition(BookListConditionBo condition);

}
