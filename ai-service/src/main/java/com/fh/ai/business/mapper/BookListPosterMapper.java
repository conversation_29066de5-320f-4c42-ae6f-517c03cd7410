package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.bookListPoster.BookListPosterConditionBo;
import com.fh.ai.business.entity.dto.bookListPoster.BookListPosterDto;
import com.fh.ai.business.entity.vo.bookListPoster.BookListPosterVo;

/**
 * 书单海报表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-07 10:00:00
 */
public interface BookListPosterMapper extends BaseMapper<BookListPosterDto> {

	List<BookListPosterVo> getBookListPosterListByCondition(BookListPosterConditionBo condition);

	BookListPosterVo getBookListPosterByCondition(BookListPosterConditionBo condition);

}
