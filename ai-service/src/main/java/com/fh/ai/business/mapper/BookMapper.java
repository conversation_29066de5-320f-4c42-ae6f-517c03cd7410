package com.fh.ai.business.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.BookConditionBo;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.QuerySmartSaleBo;
import com.fh.ai.business.entity.dto.book.BookDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankAndSaleInfo;
import com.fh.ai.business.entity.vo.book.SmartSaleInfo;

/**
 * 书籍信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface BookMapper extends BaseMapper<BookDto> {

	List<BookVo> getBookListByCondition(BookConditionBo condition);

	BookVo getBookByCondition(BookConditionBo condition);

	/**
	 * 找出特定系统的书籍
	 * @return
	 */
	List<BookVo> selectIds(Integer sourceType);

	/**
	 * 根据sourceType获取分类列表（去重）
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return
	 */
	List<String> getCategoryListBySourceType(@Param("sourceType") Integer sourceType);
	/**
	 * 根据sourceType获取shop列表（去重）
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return
	 */
	List<String> getShopListBySourceType(@Param("sourceType") Integer sourceType);
	/**
	 * 根据sourceType获取publish列表（去重）
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return
	 */
	List<String> getPublishListBySourceType(@Param("sourceType") Integer sourceType);

	/**
	 * 获取特定书号的排名信息（微信）
	 * @param rankAndSaleBo
	 * @return
	 */
	List<RankAndSaleInfo> getWeiXinRankInfo(QueryRankAndSaleBo rankAndSaleBo);

	/**
	 * 获取特定书号的排名信息（当当）
	 * @param rankAndSaleBo
	 * @return
	 */
	List<RankAndSaleInfo> getDangDangRankInfo(QueryRankAndSaleBo rankAndSaleBo);

	/**
	 * 获取特定书号的排名信息（豆瓣）
	 * @param rankAndSaleBo
	 * @return
	 */
	List<RankAndSaleInfo> getDouBanRankInfo(QueryRankAndSaleBo rankAndSaleBo);

	/**
	 * 获取特定书号的排名信息（抖音）
	 * @param rankAndSaleBo
	 * @return
	 */
	List<RankAndSaleInfo> getDouYinSaleInfo(QueryRankAndSaleBo rankAndSaleBo);

	/**
	 * 获取特定书号的排名信息（京东）
	 * @param rankAndSaleBo
	 * @return
	 */
	List<RankAndSaleInfo> getJingDongSaleInfo(QueryRankAndSaleBo rankAndSaleBo);

	/**
	 * 根据isbn查询销量
	 * @param conditionBo
	 * @return
	 */
	List<SmartSaleInfo> getSalesCountByIsbnOfSmart(QuerySmartSaleBo conditionBo);

	/**
	 * 查询coze工作流需要的图书信息（书籍信息字段（具体看前端界面）、条件是：标签数组字符串、出版社名称数组字符串、source_type=7、is_delete=1）
	 * 手写limit，限制拉取条数
	 */
	List<BookVo> getCozeBookListByCondition(BookConditionBo conditionBo);
}
