package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.calendarEvent.CalendarEventDto;
import com.fh.ai.business.entity.bo.calendarEvent.CalendarEventConditionBo;
import com.fh.ai.business.entity.vo.calendarEvent.CalendarEventVo;

/**
 * 日历事件表Mapper
 *
 * <AUTHOR>
 * @date 2024-07-02 14:21:19
 */
public interface CalendarEventMapper extends BaseMapper<CalendarEventDto> {

	List<CalendarEventVo> getCalendarEventListByCondition(CalendarEventConditionBo condition);

	List<CalendarEventVo> selectCalendarEventList();

	/**
	 * 查询2025年日历事件
	 * @return
	 */
//	List<CalendarEventVo> select2025();

}