package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.config.ConfigConditionBo;
import com.fh.ai.business.entity.dto.config.ConfigDto;
import com.fh.ai.business.entity.vo.config.ConfigVo;


/**
 * 配置表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-02 09:40:20
 */
public interface ConfigMapper extends BaseMapper<ConfigDto> {

	List<ConfigVo> getConfigListByCondition(ConfigConditionBo condition);

	ConfigVo getConfigByCondition(ConfigConditionBo condition);

}
