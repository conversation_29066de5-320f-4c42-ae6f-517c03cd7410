package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileConditionBo;
import com.fh.ai.business.entity.dto.conversationFile.ConversationFileDto;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;

import java.util.List;

/**
 * 会话文件表Mapper
 *
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
public interface ConversationFileMapper extends BaseMapper<ConversationFileDto> {

    List<ConversationFileVo> getConversationFileListByCondition(ConversationFileConditionBo condition);

}