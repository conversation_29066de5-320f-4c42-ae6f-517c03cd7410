package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.conversation.ConversationBo;
import com.fh.ai.business.entity.dto.conversation.ConversationDto;
import com.fh.ai.business.entity.bo.conversation.ConversationConditionBo;
import com.fh.ai.business.entity.vo.conversation.ConversationStatisticVo;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;

/**
 * 会话表Mapper
 *
 * <AUTHOR>
 * @date 2024-04-17 15:40:02
 */
public interface ConversationMapper extends BaseMapper<ConversationDto> {

	List<ConversationVo> getConversationListByCondition(ConversationConditionBo condition);

	ConversationStatisticVo getConversationStatistics(ConversationBo conversationBo);

}