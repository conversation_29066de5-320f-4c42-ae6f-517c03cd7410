package com.fh.ai.business.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.course.CourseBo;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailConditionBo;
import com.fh.ai.business.entity.dto.courseDetail.CourseDetailDto;
import com.fh.ai.business.entity.vo.course.CourseVo;
import com.fh.ai.business.entity.vo.courseDetail.CourseDetailVo;
import org.apache.ibatis.annotations.Param;

/**
 * 课程详情Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface CourseDetailMapper extends BaseMapper<CourseDetailDto> {

	List<CourseDetailVo> getCourseDetailListByCondition(CourseDetailConditionBo condition);

	List<Map> getType();

	List<Map> statistics(CourseBo coursebo);

	Integer videoUserCount(CourseBo coursebo);

	Integer ansUserCount(CourseBo coursebo);

	List<CourseVo> otherCourse(@Param("id") Long id, @Param("type") Integer type);

}
