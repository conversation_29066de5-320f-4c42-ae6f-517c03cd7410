package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.course.CourseConditionBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.dto.course.CourseDto;
import com.fh.ai.business.entity.vo.course.CourseVo;
import com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo;
import org.apache.ibatis.annotations.Param;

/**
 * 课程Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface CourseMapper extends BaseMapper<CourseDto> {

	List<CourseVo> getCourseListByCondition(CourseConditionBo condition);

	List<ExamAnswerVo> myList(ExamAnswerConditionBo condition);

	Integer userAnswerCount(@Param("courseId") Long courseId);
}
