package com.fh.ai.business.mapper;

import com.alibaba.fastjson.JSON;
import com.fh.ai.business.entity.vo.PageVo;
import com.fh.ai.common.annotation.*;
import com.fh.ai.common.exception.BusinessException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * es操作
 *
 * <AUTHOR>
 * @date 2022/5/9
 */
@Slf4j
public class EsBaseRepository<T> {

    @Resource
    public RestHighLevelClient client;

//    @Value("${spring.profiles.active}")
//    private String env;

    /**
     * 添加
     *
     * @param t
     * @throws IOException
     * @throws IllegalAccessException
     */
    public void add(T t) throws IOException, IllegalAccessException {
        Class<?> clzss = t.getClass();
        //获取索引
        EsTable table = clzss.getAnnotation(EsTable.class);
//        IndexRequest indexRequest = new IndexRequest(table.value().concat("-").concat(env));
        IndexRequest indexRequest = new IndexRequest(table.value());
        //处理EsNotExist
        for (Field f : clzss.getDeclaredFields()) {
            if (null != f.getAnnotation(EsNotExist.class)) {
                f.setAccessible(true);
                f.set(t, null);
            }
        }
        indexRequest.source(JSON.toJSONString(t), XContentType.JSON);
        indexRequest.timeout(TimeValue.timeValueSeconds(1));
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        indexRequest.create(true);
        indexRequest.id(getId(t));
        client.index(indexRequest, RequestOptions.DEFAULT);
    }

    /**
     * 根据主键更新
     *
     * @param t
     * @throws IOException
     * @throws IllegalAccessException
     */
    public void updateById(T t) throws IOException, IllegalAccessException {
        Class<?> clzss = t.getClass();
        //获取索引
        EsTable table = clzss.getAnnotation(EsTable.class);
        Map<String, Object> kvs = new HashMap<>();
        Field[] fields = clzss.getDeclaredFields();
        for (Field f : fields) {
            //去除序列化和EsNotExist字段
            if ("serialVersionUID".equals(f.getName()) || null != f.getAnnotation(EsNotExist.class)) {
                continue;
            }
            f.setAccessible(true);
            Object value = f.get(t);
            if (null != value) {
                if (f.getType().equals(Date.class)) {
                    Date date = (Date) value;
                    value = date.getTime();
                }
                kvs.put(f.getName(), value);
            }
        }
//        UpdateRequest updateRequest = new UpdateRequest(table.value().concat("-").concat(env), getId(t));
        UpdateRequest updateRequest = new UpdateRequest(table.value(), getId(t));
        updateRequest.doc(kvs);
        updateRequest.timeout(TimeValue.timeValueSeconds(1));
        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        //数据为存储而不是更新
        client.update(updateRequest, RequestOptions.DEFAULT);
    }

    /**
     * 删除（根据主键）
     */
    public void delById(T t) throws IOException, IllegalAccessException {
        Class<?> clzss = t.getClass();
        //获取索引
        EsTable table = clzss.getAnnotation(EsTable.class);
        DeleteRequest deleteRequest = new DeleteRequest();
//        deleteRequest.index(table.value().concat("-").concat(env));
        deleteRequest.index(table.value());
        //获取主键
        String idStr = getId(t);
        deleteRequest.id(idStr);
        client.delete(deleteRequest, RequestOptions.DEFAULT);
    }

    /**
     * 分页查询,默认id倒序
     *
     * @param t
     * @param size
     * @param lastRow
     * @return
     * @throws IOException
     */
    public PageVo<T> page(T t, Integer size, Object[] lastRow) throws Exception {
        Class<?> clazs = t.getClass();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildQuery(t);
        searchSourceBuilder.query(boolQueryBuilder);
        //排序条件
        Arrays.stream(clazs.getDeclaredFields()).filter(f -> null != f.getAnnotation(EsOrder.class))
                .forEach(f -> {
                    try {
                        f.setAccessible(true);
                        if (null != f.get(t)) {
                            SortOrder order = SortOrder.ASC;
                            if (SortOrder.DESC.toString().equals(f.getAnnotation(EsOrder.class).order())) {
                                order = SortOrder.DESC;
                            }
                            searchSourceBuilder.sort((String) f.get(t), order);
                        }
                    } catch (IllegalAccessException e) {
                        log.error("sort error {}", JSON.toJSONString(t), e);
                        throw new BusinessException("查询异常");
                    }
                });
        //scoll after分页
        if (null != lastRow) {
            searchSourceBuilder.searchAfter(lastRow);
        }
        searchSourceBuilder.size(size);
        SearchHits hits = remoteSearch(t, searchRequest, searchSourceBuilder);
        //总计
        long total = hits.getHits().length;
        PageVo<T> pageVo = new PageVo<>();
        pageVo.setTotal(total);
        pageVo.setSize(size);

        SearchHit[] hitsArray = hits.getHits();
        //数据转换
        List<T> list = Lists.newArrayList();
        Arrays.stream(hitsArray).forEach(h -> {
            T o = (T) JSON.parseObject(h.getSourceAsString(), t.getClass());
            list.add(o);
        });
        pageVo.setRecords(list);
        if (null != hitsArray && hitsArray.length > 1) {
            //查询最后一笔数据
            SearchHit result = hitsArray[hitsArray.length - 1];
            pageVo.setLastRow(result.getSortValues());
        }
        return pageVo;
    }

    /**
     * 列表查询,默认id倒序
     *
     * @param t
     * @return
     * @throws IOException
     */
    public List<T> list(T t) throws Exception {
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildQuery(t);
        searchSourceBuilder.query(boolQueryBuilder);
        //排序条件
        searchSourceBuilder.sort("id", SortOrder.DESC);
        SearchHits hits = remoteSearch(t, searchRequest, searchSourceBuilder);
        SearchHit[] hitsArray = hits.getHits();
        //数据转换
        List<T> list = Lists.newArrayList();
        Arrays.stream(hitsArray).forEach(h -> {
            T o = (T) JSON.parseObject(h.getSourceAsString(), t.getClass());
            list.add(o);
        });
        return list;
    }

    /**
     * 构建多条件查询，字段不为null则查询
     * must=and，should=or，范围查询用fliter range
     *
     * @param t
     * @return
     * @throws IllegalAccessException
     */
    private BoolQueryBuilder buildQuery(T t) throws Exception {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Field[] fields = t.getClass().getDeclaredFields();
        for (Field f : fields) {
            //去除序列化
            if ("serialVersionUID".equals(f.getName()) || null != f.getAnnotation(EsNotExist.class)) {
                continue;
            }
            f.setAccessible(true);
            if (null != f.get(t)) {
                if (null != f.getAnnotation(EsNoQuery.class)) {
                    EsNoQuery noQuery = f.getAnnotation(EsNoQuery.class);
                    String[] noArray = (String[]) f.get(t);
                    for (String value : noArray) {
                        boolQueryBuilder.mustNot(QueryBuilders.matchQuery(noQuery.name(), value));
                    }
                    continue;
                }

                if (f.getType() == List.class) {
                    EsQuery query = f.getAnnotation(EsQuery.class);
                    Object o = f.get(t);
                    List list = (List) o;
                    if (CollectionUtils.isEmpty(list)) {
                        continue;
                    }
                    BoolQueryBuilder childQueryBuilder = new BoolQueryBuilder();
                    for (Object value : list) {
                        childQueryBuilder.should().add(QueryBuilders.matchQuery(query.name(), value));
                    }
                    boolQueryBuilder.must().add(childQueryBuilder);
                } else {
                    if (f.getName().toLowerCase().contains("time") && f.getName().contains("start") ||
                            f.getName().toLowerCase().contains("day") && f.getName().contains("start")) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(f.getName()).from(((Date) f.get(t)).getTime()));
                    } else if (f.getName().toLowerCase().contains("time") && f.getName().contains("end") ||
                            f.getName().toLowerCase().contains("day") && f.getName().contains("end")) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(f.getName()).to(((Date) f.get(t)).getTime()));
                    } else {
                        boolQueryBuilder.must(QueryBuilders.matchQuery(f.getName(), f.get(t)));
                    }
                }


            }
        }
        return boolQueryBuilder;
    }

    //远程调用
    public SearchHits remoteSearch(T t, SearchRequest searchRequest, SearchSourceBuilder searchSourceBuilder)
            throws IOException {
        //获取索引
        Class<?> clzss = t.getClass();
        EsTable table = clzss.getAnnotation(EsTable.class);
//        searchRequest.indices(table.value().concat("-").concat(env));
        searchRequest.indices(table.value());
        searchRequest.source(searchSourceBuilder);
        log.info("dsl:" + searchSourceBuilder.toString());
        SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
        SearchHits hits = response.getHits();
        return hits;
    }

    /**
     * 获取主键
     *
     * @param t
     * @return
     * @throws IllegalAccessException
     */
    private String getId(T t) throws IllegalAccessException {
        Class<?> clzss = t.getClass();
        Field id = Arrays.stream(clzss.getDeclaredFields()).filter(f -> null != f.getAnnotation(EsId.class)).findFirst()
                .orElse(null);
        id.setAccessible(true);
        String idStr = id.get(t).toString();
        return idStr;
    }

}
