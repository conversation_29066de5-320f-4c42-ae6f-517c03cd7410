package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.evaluatingRecord.EvaluatingRecordConditionBo;
import com.fh.ai.business.entity.dto.evaluatingRecord.EvaluatingRecordDto;
import com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo;

import java.util.List;

/**
 * 帮助中心Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface EvaluatingRecordMapper extends BaseMapper<EvaluatingRecordDto> {

	List<EvaluatingRecordVo> getEvaluatingRecordList(EvaluatingRecordConditionBo condition);

	EvaluatingRecordVo getEvaluatingRecordByTaskUid(String taskUid);

}
