package com.fh.ai.business.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.course.CourseBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperBo;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDetailDto;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDto;
import com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo;
import org.apache.ibatis.annotations.Param;

/**
 * 题库表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface ExamAnswerMapper extends BaseMapper<ExamAnswerDto> {

	List<ExamAnswerVo> getExamAnswerListByCondition(ExamAnswerConditionBo condition);

	int batchInsert(@Param("list") List<ExamAnswerDetailDto> list);

	List<ExamAnswerDetailDto> getExamAnswerDetail(@Param("examAnswerId")Long examAnswerId);

}
