package com.fh.ai.business.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperBo;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperConditionBo;
import com.fh.ai.business.entity.dto.examPaper.ExamPaperDto;
import com.fh.ai.business.entity.vo.examPaper.ExamPaperVo;

/**
 * 题库表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface ExamPaperMapper extends BaseMapper<ExamPaperDto> {

	List<ExamPaperVo> getExamPaperListByCondition(ExamPaperConditionBo condition);

	List<Map> statistics(ExamPaperBo examPaperBo);

	List<Map> orgStatistics(ExamPaperBo examPaperBo);

	List<Map> orgUserStatistics(ExamPaperBo examPaperBo);

	List<Map> orgNumStatistics(ExamPaperBo examPaperBo);

}
