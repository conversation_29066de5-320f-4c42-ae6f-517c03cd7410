package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailConditionBo;
import com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto;
import com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-14  13:49
 */
public interface ExcellentWorksDetailMapper extends BaseMapper<ExcellentWorksDetailDto> {

    List<ExcellentWorksDetailVo> getExcellentWorksDetailListByCondition(ExcellentWorksDetailConditionBo conditionBo);

}
