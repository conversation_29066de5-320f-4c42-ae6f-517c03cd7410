package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.excellentWorks.ExcellentWorksConditionBo;
import com.fh.ai.business.entity.bo.excellentWorks.ExcellentWorksUserStatisticsConditionBo;
import com.fh.ai.business.entity.bo.myWorks.MyWorksConditionBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksUserStatisticsVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo;
import com.fh.ai.business.entity.vo.myWorks.MyWorksVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:43
 */
public interface ExcellentWorksMapper extends BaseMapper<ExcellentWorksDto> {

    List<ExcellentWorksVo> getExcellentWorksListByCondition(ExcellentWorksConditionBo conditionBo);




    /**
     * 获得包含细节优秀作品
     * <p>
     * 传入参数: @param conditionBo
     * 返回值: @return {@link List }<{@link ExcellentWorksVo }>
     * 创建人: 杨圣君
     * 创建时间: 2024/11/15
     */
    ExcellentWorksVo getExcellentWorksWithDetailsById(@Param("id") Long id);

    List<ExcellentWorksUserStatisticsVo> getStatisticsByOrganization(ExcellentWorksUserStatisticsConditionBo excellentWorksUserStatisticsConditionBo);

    Integer getExcellentWorksNumber(@Param("worksActiveId") Long worksActiveId);

}
