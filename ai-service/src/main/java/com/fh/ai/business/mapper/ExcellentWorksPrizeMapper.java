package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeBo;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeConditionBo;
import com.fh.ai.business.entity.dto.excellentWorksPrize.ExcellentWorksPrizeDto;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeCountVo;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;

/**
 * 用户作品获奖记录（和奖品表没关系）Mapper
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 17:59:13
 */
public interface ExcellentWorksPrizeMapper extends BaseMapper<ExcellentWorksPrizeDto> {

	List<ExcellentWorksPrizeVo> getExcellentWorksPrizeListByCondition(ExcellentWorksPrizeBo condition);

	ExcellentWorksPrizeVo getExcellentWorksPrizeByCondition(ExcellentWorksPrizeConditionBo condition);

	List<ExcellentWorksPrizeCountVo> getExcellentWorksPrizeCountVo(List<Long> excellentWorksIdList);
}
