package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.festival.FestivalDto;
import com.fh.ai.business.entity.bo.festival.FestivalConditionBo;
import com.fh.ai.business.entity.vo.festival.FestivalVo;

/**
 * 节假日表Mapper
 *
 * <AUTHOR>
 * @date 2024-07-02 14:28:17
 */
public interface FestivalMapper extends BaseMapper<FestivalDto> {

	List<FestivalVo> getFestivalListByCondition(FestivalConditionBo condition);

}