package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.conversationFile.FileContentCacheConditionBo;
import com.fh.ai.business.entity.dto.conversationFile.FileContentCacheDto;
import com.fh.ai.business.entity.vo.conversationFile.FileContentCacheVo;

/**
 * 文件内容缓存表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-27 14:52:45
 */
public interface FileContentCacheMapper extends BaseMapper<FileContentCacheDto> {

	List<FileContentCacheVo> getFileContentCacheListByCondition(FileContentCacheConditionBo condition);

	FileContentCacheVo getFileContentCacheByCondition(FileContentCacheConditionBo condition);

}
