package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.flowRecord.FlowRecordConditionBo;
import com.fh.ai.business.entity.dto.flowRecord.FlowRecordDto;
import com.fh.ai.business.entity.vo.flowRecord.FlowRecordVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  10:18
 */
public interface FlowRecordMapper extends BaseMapper<FlowRecordDto> {
    List<FlowRecordVo> getFlowRecordListByCondition(FlowRecordConditionBo conditionBo);

    FlowRecordVo getFlowRecordByCondition(FlowRecordConditionBo conditionBo);
}
