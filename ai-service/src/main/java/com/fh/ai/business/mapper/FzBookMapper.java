package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.fzBook.FzBookConditionBo;
import com.fh.ai.business.entity.dto.fzBook.FzBookDto;
import com.fh.ai.business.entity.vo.fzBook.FzBookVo;

/**
 * 图书库表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:00:52
 */
public interface FzBookMapper extends BaseMapper<FzBookDto> {

	List<FzBookVo> getFzBookListByCondition(FzBookConditionBo condition);

	FzBookVo getFzBookByCondition(FzBookConditionBo condition);

}
