package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.guiji.GuijiHistoryConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiHistoryDto;
import com.fh.ai.business.entity.vo.guiji.GuijiHistoryVo;

/**
 * 硅基数智人生成记录表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface GuijiHistoryMapper extends BaseMapper<GuijiHistoryDto> {

	List<GuijiHistoryVo> getGuijiHistoryListByCondition(GuijiHistoryConditionBo condition);

	GuijiHistoryVo getGuijiHistoryByCondition(GuijiHistoryConditionBo condition);

}
