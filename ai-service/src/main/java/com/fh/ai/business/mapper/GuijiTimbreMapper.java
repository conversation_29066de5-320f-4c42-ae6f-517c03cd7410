package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.guiji.GuijiTimbreConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiTimbreDto;
import com.fh.ai.business.entity.vo.guiji.GuijiTimbreVo;

/**
 * 硅基数智人音色表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface GuijiTimbreMapper extends BaseMapper<GuijiTimbreDto> {

	List<GuijiTimbreVo> getGuijiTimbreListByCondition(GuijiTimbreConditionBo condition);

	GuijiTimbreVo getGuijiTimbreByCondition(GuijiTimbreConditionBo condition);

}
