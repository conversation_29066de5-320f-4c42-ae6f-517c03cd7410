package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.guiji.GuijiUserConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiUserDto;
import com.fh.ai.business.entity.vo.guiji.GuijiUserVo;

/**
 * 硅基数智人用户使用表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface GuijiUserMapper extends BaseMapper<GuijiUserDto> {

	List<GuijiUserVo> getGuijiUserListByCondition(GuijiUserConditionBo condition);

	/**
	 * 查询zego和guji所有的数据
	 *
	 * @param condition the condition
	 * @return zego guiji user list by condition
	 */
	List<GuijiUserVo> getZegoGuijiUserListByCondition(GuijiUserConditionBo condition);

	GuijiUserVo getGuijiUserByCondition(GuijiUserConditionBo condition);

}
