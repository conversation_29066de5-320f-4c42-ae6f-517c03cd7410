package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.help.HelpConditionBo;
import com.fh.ai.business.entity.dto.help.HelpDto;
import com.fh.ai.business.entity.vo.help.HelpVo;

/**
 * 帮助中心Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface HelpMapper extends BaseMapper<HelpDto> {

	List<HelpVo> getHelpListByCondition(HelpConditionBo condition);

}
