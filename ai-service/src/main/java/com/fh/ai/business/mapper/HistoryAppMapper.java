package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.business.entity.vo.historyApp.UserUsageVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用历史表Mapper
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
public interface HistoryAppMapper extends BaseMapper<HistoryAppDto> {

    List<HistoryAppVo> getHistoryAppListByCondition(HistoryAppConditionBo condition);

    List<UserUsageVo> getUserUsageCount(HistoryAppConditionBo condition);

    List<UsageStatisticsTotalVo> getActualTimeTop10(HistoryAppConditionBo condition);

    List<HistoryAppVo> getMeetingAssistantList(HistoryAppConditionBo condition);

    HistoryAppVo getMeetingConvertText(HistoryAppConditionBo condition);

    HistoryAppVo getByIdOrMessageUUID(@Param("id") Long id, @Param("messageUUID") String messageUUID);

    /**
     * 查询状态为Running的Coze工作流记录
     * @return 符合条件的记录列表
     */
    List<HistoryAppVo> getRunningCozeWorkflowRecords();

}