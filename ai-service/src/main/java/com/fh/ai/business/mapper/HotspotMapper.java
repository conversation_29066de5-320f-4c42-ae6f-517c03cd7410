package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.hotspot.HotspotDto;
import com.fh.ai.business.entity.bo.hotspot.HotspotConditionBo;
import com.fh.ai.business.entity.vo.hotspot.HotspotVo;

/**
 * 热点表Mapper
 *
 * <AUTHOR>
 * @date 2024-07-02 15:15:14
 */
public interface HotspotMapper extends BaseMapper<HotspotDto> {

	List<HotspotVo> getHotspotListByCondition(HotspotConditionBo condition);

}