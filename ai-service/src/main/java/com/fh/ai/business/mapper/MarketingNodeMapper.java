package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.calendarEvent.MarketingNodeConditionBo;
import com.fh.ai.business.entity.dto.calendarEvent.MarketingNodeDto;
import com.fh.ai.business.entity.vo.calendarEvent.MarketingNodeVo;

/**
 * 营销节点Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-09 11:42:41
 */
public interface MarketingNodeMapper extends BaseMapper<MarketingNodeDto> {

	List<MarketingNodeVo> getMarketingNodeListByCondition(MarketingNodeConditionBo condition);

	MarketingNodeVo getMarketingNodeByCondition(MarketingNodeConditionBo condition);

}
