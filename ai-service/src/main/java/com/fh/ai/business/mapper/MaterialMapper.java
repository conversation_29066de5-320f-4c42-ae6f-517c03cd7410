package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.material.MaterialConditionBo;
import com.fh.ai.business.entity.dto.material.MaterialDto;
import com.fh.ai.business.entity.vo.material.MaterialVo;
import org.apache.ibatis.annotations.Param;


/**
 * 用户上传素材表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-07 11:15:45
 */
public interface MaterialMapper extends BaseMapper<MaterialDto> {

	List<MaterialVo> getMaterialListByCondition(MaterialConditionBo condition);

	MaterialVo getMaterialByCondition(MaterialConditionBo condition);

	Integer updateDeleteFlag(@Param("id") Long id);

	 MaterialVo selectIdByIdAndDeleteFlag(@Param("id") Long id);

}
