package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.menu.MenuConditionBo;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.business.entity.dto.menu.MenuDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
public interface MenuMapper extends BaseMapper<MenuDto> {

    List<MenuVo> getMenuListByCondition(MenuConditionBo condition);

    List<MenuVo> getMenuListByRole(@Param("roleId") Long roleId);

    MenuVo getMenuVoById(Long id);
}
