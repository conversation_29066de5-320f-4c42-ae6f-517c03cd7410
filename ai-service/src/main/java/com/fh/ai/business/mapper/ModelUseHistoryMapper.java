package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryConditionBo;
import com.fh.ai.business.entity.dto.modelUseHistory.ModelUseHistoryDto;
import com.fh.ai.business.entity.vo.modelUseHistory.ModelUseHistoryVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:27
 */
public interface ModelUseHistoryMapper extends BaseMapper<ModelUseHistoryDto> {

    List<ModelUseHistoryVo> getModelUseHistoryListByCondition(ModelUseHistoryConditionBo conditionBo);

}
