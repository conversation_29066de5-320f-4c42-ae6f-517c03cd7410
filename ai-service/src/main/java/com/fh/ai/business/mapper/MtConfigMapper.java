package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.mtConfig.MtConfigDto;
import com.fh.ai.business.entity.bo.mtConfig.MtConfigConditionBo;
import com.fh.ai.business.entity.vo.mtConfig.MtConfigVo;

/**
 * 美图模型配置表Mapper
 *
 * <AUTHOR>
 * @date 2024-08-13 16:06:29
 */
public interface MtConfigMapper extends BaseMapper<MtConfigDto> {

	List<MtConfigVo> getMtConfigListByCondition(MtConfigConditionBo condition);

}