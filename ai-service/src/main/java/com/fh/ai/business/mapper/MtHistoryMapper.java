package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.dto.mtHistory.MtHistoryDto;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryConditionBo;
import com.fh.ai.business.entity.vo.historyApp.UserUsageVo;
import com.fh.ai.business.entity.vo.mtHistory.MtHistoryVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;

/**
 * 美图生成记录表Mapper
 *
 * <AUTHOR>
 * @date 2024-08-16 09:35:50
 */
public interface MtHistoryMapper extends BaseMapper<MtHistoryDto> {

	List<MtHistoryVo> getMtHistoryListByCondition(MtHistoryConditionBo condition);

	List<UserUsageVo> getUserUsageCount(HistoryAppConditionBo condition);

}