package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.mtUserFavorite.MtUserFavoriteDto;
import com.fh.ai.business.entity.bo.mtUserFavorite.MtUserFavoriteConditionBo;
import com.fh.ai.business.entity.vo.mtUserFavorite.MtUserFavoriteVo;

/**
 * 美图风格用户收藏表Mapper
 *
 * <AUTHOR>
 * @date 2024-08-16 09:50:56
 */
public interface MtUserFavoriteMapper extends BaseMapper<MtUserFavoriteDto> {

	List<MtUserFavoriteVo> getMtUserFavoriteListByCondition(MtUserFavoriteConditionBo condition);

}