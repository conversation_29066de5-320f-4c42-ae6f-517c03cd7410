package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.mtUser.MtUserDto;
import com.fh.ai.business.entity.bo.mtUser.MtUserConditionBo;
import com.fh.ai.business.entity.vo.mtUser.MtUserVo;
import org.apache.ibatis.annotations.Param;

/**
 * 美图用户使用表Mapper
 *
 * <AUTHOR>
 * @date 2024-08-16 09:45:28
 */
public interface MtUserMapper extends BaseMapper<MtUserDto> {

	List<MtUserVo> getMtUserListByCondition(MtUserConditionBo condition);

	List<MtUserVo> getMtUserListByHistoryIds(@Param("historyIds") List<Long> historyIds);

}