package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.mtWord.MtWordDto;
import com.fh.ai.business.entity.bo.mtWord.MtWordConditionBo;
import com.fh.ai.business.entity.vo.mtWord.MtWordVo;

/**
 * 美图词库表Mapper
 *
 * <AUTHOR>
 * @date 2024-08-19 14:52:50
 */
public interface MtWordMapper extends BaseMapper<MtWordDto> {

	List<MtWordVo> getMtWordListByCondition(MtWordConditionBo condition);

}