package com.fh.ai.business.mapper;

import com.fh.ai.business.entity.bo.myWorks.MyWorksConditionBo;
import com.fh.ai.business.entity.vo.myWorks.MyWorksVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-26  14:05
 */
@Mapper
public interface MyWorksMapper {
    /**
     * 获取我的作品列表  图片
     *
     * @param conditionBo
     * @return java.util.List<com.fh.ai.business.entity.vo.excellentWorks.MyWorksVo>
     * <AUTHOR>
     * @date 2024/11/12 14:49
     **/
    List<MyWorksVo> getMyImageWorks(MyWorksConditionBo conditionBo);

    /**
     * 获取我的作品列表 ppt
     *
     * @param conditionBo
     * @return java.util.List<com.fh.ai.business.entity.vo.excellentWorks.MyWorksVo>
     * <AUTHOR>
     * @date 2024/11/21 17:23
     **/
    List<MyWorksVo> getMyPPTWorks(MyWorksConditionBo conditionBo);
}
