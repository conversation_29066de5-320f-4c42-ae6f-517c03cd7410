package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.publishNews.NewsHashConditionBo;
import com.fh.ai.business.entity.dto.publishNews.NewsHashDto;
import com.fh.ai.business.entity.vo.publishNews.NewsHashVo;

import java.util.List;



/**
 * 热点新闻接口调用地址表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-23 15:10:51
 */
public interface NewsHashMapper extends BaseMapper<NewsHashDto> {

	List<NewsHashVo> getNewsHashListByCondition(NewsHashConditionBo condition);

	NewsHashVo getNewsHashByCondition(NewsHashConditionBo condition);

	List<NewsHashVo> selectAllApi();

}
