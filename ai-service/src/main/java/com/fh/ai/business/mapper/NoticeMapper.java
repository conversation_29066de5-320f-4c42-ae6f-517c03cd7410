package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.notice.NoticeConditionBo;
import com.fh.ai.business.entity.dto.notice.NoticeDto;
import com.fh.ai.business.entity.vo.notice.NoticeVo;
import org.apache.ibatis.annotations.Param;

/**
 * 消息通知Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface NoticeMapper extends BaseMapper<NoticeDto> {

	List<NoticeVo> getNoticeListByCondition(NoticeConditionBo condition);

	int insertNoticeBatch (@Param("type") Integer type, @Param("relationId")Long relationId, @Param("content")String content);
}
