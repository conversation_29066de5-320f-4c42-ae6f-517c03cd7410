package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;

import java.util.List;

/**
 * 组织机构表Mapper
 *
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
public interface OrganizationMapper extends BaseMapper<OrganizationDto> {

    List<OrganizationVo> getOrganizationListByCondition(OrganizationConditionBo condition);

    List<OrganizationVo> getStatisticsOrganizationList();

}