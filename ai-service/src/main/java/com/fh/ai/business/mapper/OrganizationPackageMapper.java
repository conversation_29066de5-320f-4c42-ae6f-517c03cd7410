package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageConditionBo;
import com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationAccumulateUsageStatisticVo;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo;

/**
 * 企业套餐Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
public interface OrganizationPackageMapper extends BaseMapper<OrganizationPackageDto> {

	OrganizationPackageVo getOrganizationPackageByCondition(OrganizationPackageConditionBo condition);

	List<OrganizationPackageVo> getOrganizationPackageListByCondition(OrganizationPackageConditionBo condition);

	OrganizationAccumulateUsageStatisticVo getOrganizationAccumulateUsageStatistic();
}
