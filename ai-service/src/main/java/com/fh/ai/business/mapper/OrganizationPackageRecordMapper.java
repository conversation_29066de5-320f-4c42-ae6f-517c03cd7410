package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.organizationPackageRecord.OrganizationPackageRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationPackageRecord.OrganizationPackageRecordDto;
import com.fh.ai.business.entity.vo.organizationPackageRecord.OrganizationPackageRecordVo;

/**
 * 企业套餐开通记录Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:38
 */
public interface OrganizationPackageRecordMapper extends BaseMapper<OrganizationPackageRecordDto> {

	List<OrganizationPackageRecordVo> getPOrganizationPackageRecordListByCondition(OrganizationPackageRecordConditionBo condition);

	OrganizationPackageRecordVo getPOrganizationPackageRecordByCondition(OrganizationPackageRecordConditionBo condition);

}
