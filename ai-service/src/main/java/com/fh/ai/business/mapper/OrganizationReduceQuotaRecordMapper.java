package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordDto;
import com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  13:48
 */
public interface OrganizationReduceQuotaRecordMapper extends BaseMapper<OrganizationReduceQuotaRecordDto> {
    List<OrganizationReduceQuotaRecordVo> getOrganizationReduceQuotaRecordListByCondition(OrganizationReduceQuotaRecordConditionBo conditionBo);

    OrganizationReduceQuotaRecordVo getOrganizationReduceQuotaRecordByCondition(OrganizationReduceQuotaRecordConditionBo conditionBo);

    List<OrganizationReduceQuotaRecordVo> getOrganizationReduceQuotaRecordListByDay(OrganizationReduceQuotaRecordConditionBo conditionBo);
}
