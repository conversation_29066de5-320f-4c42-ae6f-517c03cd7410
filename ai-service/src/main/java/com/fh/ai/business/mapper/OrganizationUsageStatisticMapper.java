package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.organizationUsageStatistic.OrganizationUsageStatisticConditionBo;
import com.fh.ai.business.entity.dto.organizationUsageStatistic.OrganizationUsageStatisticDto;
import com.fh.ai.business.entity.vo.organizationUsageStatistic.OrganizationUsageStatisticVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-30  15:20
 */
public interface OrganizationUsageStatisticMapper extends BaseMapper<OrganizationUsageStatisticDto> {

    List<OrganizationUsageStatisticVo> getOrganizationUsageStatisticListByCondition(OrganizationUsageStatisticConditionBo condition);

    OrganizationUsageStatisticVo getOrganizationUsageStatisticByCondition(OrganizationUsageStatisticConditionBo condition);

}
