package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.packageInfo.PackageInfoConditionBo;
import com.fh.ai.business.entity.dto.packageInfo.PackageInfoDto;
import com.fh.ai.business.entity.vo.packageInfo.PackageInfoVo;

/**
 * 套餐表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:13
 */
public interface PackageInfoMapper extends BaseMapper<PackageInfoDto> {

	List<PackageInfoVo> getPackageInfoListByCondition(PackageInfoConditionBo condition);

	PackageInfoVo getPackageInfoByCondition(PackageInfoConditionBo condition);

}
