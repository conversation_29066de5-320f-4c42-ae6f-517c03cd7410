package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.personalFile.PersonalFileConditionBo;
import com.fh.ai.business.entity.dto.personalFile.PersonalFileDto;
import com.fh.ai.business.entity.vo.personalFile.PersonalFileVo;

/**
 * 用户上传的个人文件表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14 10:00:00
 */
public interface PersonalFileMapper extends BaseMapper<PersonalFileDto> {

	List<PersonalFileVo> getPersonalFileListByCondition(PersonalFileConditionBo condition);

	PersonalFileVo getPersonalFileByCondition(PersonalFileConditionBo condition);

}
