package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.pictureDownloadHistory.PictureDownloadHistoryConditionBo;
import com.fh.ai.business.entity.dto.pictureDownloadHistory.PictureDownloadHistoryDto;
import com.fh.ai.business.entity.vo.pictureDownloadHistory.PictureDownloadHistoryVo;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-06  18:03
 */
public interface PictureDownloadHistoryMapper extends BaseMapper<PictureDownloadHistoryDto> {
    List<PictureDownloadHistoryVo> getPictureDownloadHistoryListByCondition(PictureDownloadHistoryConditionBo condition);

    PictureDownloadHistoryVo getPictureDownloadHistoryByCondition(PictureDownloadHistoryConditionBo conditionBo);
}
