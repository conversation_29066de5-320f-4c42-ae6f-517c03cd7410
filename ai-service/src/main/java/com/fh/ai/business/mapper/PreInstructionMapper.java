package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.preInstruction.PreInstructionConditionBo;
import com.fh.ai.business.entity.dto.preInstruction.PreInstructionDto;
import com.fh.ai.business.entity.vo.preInstruction.PreInstructionVo;

/**
 * 预置指令Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-07 16:45:32
 */
public interface PreInstructionMapper extends BaseMapper<PreInstructionDto> {

	List<PreInstructionVo> getPreInstructionListByCondition(PreInstructionConditionBo condition);

	PreInstructionVo getPreInstructionByCondition(PreInstructionConditionBo condition);

}
