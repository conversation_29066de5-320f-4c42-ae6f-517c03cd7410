package com.fh.ai.business.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.prize.PrizeDto;
import com.fh.ai.business.entity.bo.prize.PrizeConditionBo;
import com.fh.ai.business.entity.vo.prize.PrizeVo;
import org.apache.ibatis.annotations.Param;

/**
 * 奖品表Mapper
 *
 * <AUTHOR>
 * @date 2024-02-20 17:21:24
 */
public interface PrizeMapper extends BaseMapper<PrizeDto> {

	List<PrizeVo> getPrizeListByCondition(PrizeConditionBo condition);

	List<Map> code();

	void updateCode(@Param("id") Long id,@Param("codeName") String codeName,@Param("num") Long num);

}