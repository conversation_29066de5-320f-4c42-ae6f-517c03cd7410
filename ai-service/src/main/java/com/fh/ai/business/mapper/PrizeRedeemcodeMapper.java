package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.prizeRedeemcode.PrizeRedeemcodeDto;
import com.fh.ai.business.entity.bo.prizeRedeemcode.PrizeRedeemcodeConditionBo;
import com.fh.ai.business.entity.vo.prizeRedeemcode.PrizeRedeemcodeVo;

/**
 * 奖品兑换码表Mapper
 *
 * <AUTHOR>
 * @date 2024-05-13 15:04:13
 */
public interface PrizeRedeemcodeMapper extends BaseMapper<PrizeRedeemcodeDto> {

	List<PrizeRedeemcodeVo> getPrizeRedeemcodeListByCondition(PrizeRedeemcodeConditionBo condition);

}