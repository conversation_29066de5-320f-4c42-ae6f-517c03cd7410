package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.proofreading.ProofreadSettingConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadSettingDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户审校设置表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-20 14:39:57
 */
public interface ProofreadSettingMapper extends BaseMapper<ProofreadSettingDto> {

	List<ProofreadSettingVo> getProofreadSettingListByCondition(ProofreadSettingConditionBo condition);

	ProofreadSettingVo getProofreadSettingByCondition(ProofreadSettingConditionBo condition);
	ProofreadSettingVo getUserProofreadSetting(@Param("condition") ProofreadSettingConditionBo condition);

}
