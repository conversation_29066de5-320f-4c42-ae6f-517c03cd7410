package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingRecordDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;

/**
 * 用户审校记录Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:02:37
 */
public interface ProofreadingRecordMapper extends BaseMapper<ProofreadingRecordDto> {

	List<ProofreadingRecordVo> getProofreadingRecordListByCondition(ProofreadingRecordConditionBo condition);

	ProofreadingRecordVo getProofreadingRecordByCondition(ProofreadingRecordConditionBo condition);

	/**
	 * 查询当前表中状态为处理中的审校记录
	 * @return
	 */
	List<ProofreadingRecordVo> getHandingRecord();

	/**
	 * 根据审校记录主键获取审校记录
	 * @param recordId
	 * @return
	 */
	ProofreadingRecordVo selectByRecordId(Long recordId);

	List<ProofreadingRecordVo> getProofreadingRecordList(ProofreadingRecordConditionBo condition);
}
