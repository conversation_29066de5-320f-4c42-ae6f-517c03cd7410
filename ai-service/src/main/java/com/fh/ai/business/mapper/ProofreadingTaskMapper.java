package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 审校任务表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:03:26
 */
public interface ProofreadingTaskMapper extends BaseMapper<ProofreadingTaskDto> {

	List<ProofreadingTaskVo> getProofreadingTaskListByCondition(ProofreadingTaskConditionBo condition);

	ProofreadingTaskVo getProofreadingTaskByCondition(ProofreadingTaskConditionBo condition);

	/**
	 * 通过审校记录获取所有审校任务，包括所有字段
	 * @param condition
	 * @return
	 */
	List<ProofreadingTaskVo> getTaskByRecordId(ProofreadingTaskConditionBo condition);

	/**
	 * 通过审校记录获取所有审校任务，不包括 request_info、response_info、setting_option_info、modify_result 长文本字段
	 * @param condition
	 * @return
	 */
	List<ProofreadingTaskVo> getTaskByRecordIdExcludeLongColumn(ProofreadingTaskConditionBo condition);

	Integer updateTaskIsDeleteBatch(@Param("ids") List<Long> ids);

	/**
	 * 通过审校记录id，将正在处理中的审校任务列表置为失败。
	 * 用于处理那些因为网络或者重启原因导致状态无法变更的审校任务，使得重试接口得以生效。
	 * @param recordId
	 * @return
	 */
	Integer updateTaskFailByRecordId(@Param("recordId") Long recordId);

	/**
	 * 根据id获取某个审校任务
	 * @param id
	 * @return
	 */
	ProofreadingTaskVo getOneTaskById(@Param("id") Long id);

}
