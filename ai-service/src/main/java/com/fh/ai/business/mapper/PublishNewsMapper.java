package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.publishNews.PublishNewsConditionBo;
import com.fh.ai.business.entity.bo.publishNews.QueryPublishListBo;
import com.fh.ai.business.entity.dto.publishNews.PublishNewsDto;
import com.fh.ai.business.entity.vo.publishNews.PublishNewsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出版资讯表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 17:23:42
 */
public interface PublishNewsMapper extends BaseMapper<PublishNewsDto> {

	List<PublishNewsVo> getPublishNewsListByCondition(PublishNewsConditionBo condition);

	PublishNewsVo getPublishNewsByCondition(PublishNewsConditionBo condition);

	/**
	 * 根据id查询未删除的新闻详情
	 * @param id
	 * @return
	 */
	PublishNewsVo getPublishNewsDetail(@Param("id") Long id);

	/**
	 * 列表查询
	 * @param request
	 * @return
	 */
	List<PublishNewsVo> getPublishNewsList(QueryPublishListBo request);

	/**
	 * 查询所有新闻
	 * @return
	 */
	List<PublishNewsVo> selectAllNews();

	/**
	 * 通过id查询id，快速判断资讯是否存在。
	 * @param id
	 * @return
	 */
	Long selectIdById(Long id);

}
