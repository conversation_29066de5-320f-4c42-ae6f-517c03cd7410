package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.question.QuestionConditionBo;
import com.fh.ai.business.entity.dto.question.QuestionDto;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 题库表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface QuestionMapper extends BaseMapper<QuestionDto> {

	List<QuestionVo> getQuestionListByCondition(QuestionConditionBo condition);

	List<QuestionVo> getQuestionListByIds(@Param("ids") String ids);

	Integer userAnswerCount(@Param("questionId") Long questionId);

}
