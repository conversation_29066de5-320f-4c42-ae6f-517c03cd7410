package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.RankingDangdangConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDangdangDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankingDangdangVo;

/**
 * 当当榜单表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingDangdangMapper extends BaseMapper<RankingDangdangDto> {

    List<RankingDangdangVo> getRankingDangdangListByCondition(RankingDangdangConditionBo condition);

    RankingDangdangVo getRankingDangdangByCondition(RankingDangdangConditionBo condition);

    /**
     * 根据条件查询某个榜单最新的一个日期的uuid是多少，方便用于后面列表查询最新的榜单。
     * @param condition
     * @return
     */
    RankingDangdangVo getLatestRankingDangdangDateUuid(RankingDangdangConditionBo condition);

    /**
     * 查询当当带有榜单数据的书籍列表
     *
     * @param condition the condition
     * @return ranking dangdang list with book by condition
     */
    List<RankingDangdangVo> getRankingDangdangListWithBookByCondition(RankingDangdangConditionBo condition);




}
