package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDoubanDto;
import com.fh.ai.business.entity.vo.book.RankingDangdangVo;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;

/**
 * 豆瓣榜单表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingDoubanMapper extends BaseMapper<RankingDoubanDto> {

	List<RankingDoubanVo> getRankingDoubanListByCondition(RankingDoubanConditionBo condition);

	RankingDoubanVo getRankingDoubanByCondition(RankingDoubanConditionBo condition);

	/**
	 * 根据条件查询某个榜单最新的一个日期的uuid是多少，方便用于后面列表查询最新的榜单。
	 * @param condition
	 * @return
	 */
	RankingDoubanVo getLatestRankingDoubanDateUuid(RankingDoubanConditionBo condition);

	/**
	 * 查询豆瓣带有榜单数据的书籍列表
	 *
	 * @param condition the condition
	 * @return ranking dangdang list with book by condition
	 */
	List<RankingDoubanVo> getRankingDoubanListWithBookByCondition(RankingDoubanConditionBo condition);


}
