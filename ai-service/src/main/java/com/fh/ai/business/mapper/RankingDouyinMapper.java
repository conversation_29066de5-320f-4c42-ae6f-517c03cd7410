package com.fh.ai.business.mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingDouyinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDouyinDto;
import com.fh.ai.business.entity.vo.book.RankingDouyinVo;

/**
 * 抖音榜单表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
public interface RankingDouyinMapper extends BaseMapper<RankingDouyinDto> {

	List<RankingDouyinVo> getRankingDouyinListByCondition(RankingDouyinConditionBo condition);

	RankingDouyinVo getRankingDouyinByCondition(RankingDouyinConditionBo condition);

	RankingDouyinVo getLatestRankingDouyinDateUuid(RankingDouyinConditionBo condition);

	List<RankingDouyinVo> selectLastDayRankInfo(LocalDate collectDay);

}
