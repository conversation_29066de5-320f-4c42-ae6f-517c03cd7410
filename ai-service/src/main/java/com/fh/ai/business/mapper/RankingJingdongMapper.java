package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingJingdongConditionBo;
import com.fh.ai.business.entity.dto.book.RankingJingdongDto;
import com.fh.ai.business.entity.vo.book.RankingJingdongVo;

/**
 * 京东榜单表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
public interface RankingJingdongMapper extends BaseMapper<RankingJingdongDto> {

	RankingJingdongVo getLatestRankingJingdongDateUuid(RankingJingdongConditionBo conditionBo);

	List<RankingJingdongVo> getRankingJingdongListByCondition(RankingJingdongConditionBo condition);

	RankingJingdongVo getRankingJingdongByCondition(RankingJingdongConditionBo condition);

}
