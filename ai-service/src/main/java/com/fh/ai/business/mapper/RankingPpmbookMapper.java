package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingPpmbookConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPpmbookDto;
import com.fh.ai.business.entity.vo.book.RankingPpmbookVo;

/**
 * 凤凰书苑ppmbook榜单表（书苑）Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingPpmbookMapper extends BaseMapper<RankingPpmbookDto> {

	List<RankingPpmbookVo> getRankingPpmbookListByCondition(RankingPpmbookConditionBo condition);

	RankingPpmbookVo getRankingPpmbookByCondition(RankingPpmbookConditionBo condition);

	/**
	 * 根据条件查询某个榜单最新的一个日期的uuid是多少，方便用于后面列表查询最新的榜单。
	 * @param condition
	 * @return
	 */
	String getLatestRankingPpmbookDateUuid(RankingPpmbookConditionBo condition);

	/**
	 * 根据条件查询某个榜单最新的一个日期数据
	 * @param condition
	 * @return
	 */
	RankingPpmbookVo getLatestRankingPpmbookInfo(RankingPpmbookConditionBo condition);

	/**
	 * 查询书苑带有榜单数据的书籍列表(榜单+书籍，这个和其他不一样。后面可以直接修改getRankingPpmbookListByCondition的查询)
	 *
	 * @param condition the condition
	 * @return ranking dangdang list with book by condition
	 */
	List<RankingPpmbookVo> getRankingPpmbookListWithBookByCondition(RankingPpmbookConditionBo condition);
}
