package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingPremiumConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPremiumDto;
import com.fh.ai.business.entity.vo.book.RankingPremiumVo;

/**
 * 精品书（奖项）表（不含凤凰好书）Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingPremiumMapper extends BaseMapper<RankingPremiumDto> {

	List<RankingPremiumVo> getRankingPremiumListByCondition(RankingPremiumConditionBo condition);

	RankingPremiumVo getRankingPremiumByCondition(RankingPremiumConditionBo condition);

}
