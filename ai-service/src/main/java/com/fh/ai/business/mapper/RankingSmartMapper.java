package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.bo.book.RankingSmartConditionBo;
import com.fh.ai.business.entity.dto.book.RankingSmartDto;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;
import com.fh.ai.business.entity.vo.book.RankingSmartVo;

/**
 * 凤凰本版畅销书表-有销售数据（开卷）Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingSmartMapper extends BaseMapper<RankingSmartDto> {

	List<RankingSmartVo> getRankingSmartListByCondition(RankingSmartConditionBo condition);

	RankingSmartVo getRankingSmartByCondition(RankingSmartConditionBo condition);

	List<RankingSmartVo> selectRankInfo(RankingSmartConditionBo condition);

	/**
	 * 查询开卷带有榜单数据的书籍列表
	 *
	 * @param condition the condition
	 * @return ranking dangdang list with book by condition
	 */
	List<RankingSmartVo> getRankingSmartListWithBookByCondition(RankingSmartConditionBo condition);

	/**
	 * 获取最新的查询批次
	 * @param conditionBo
	 * @return
	 */
	RankingSmartVo getLatestRankingSmartUuid(RankingSmartConditionBo conditionBo);
}
