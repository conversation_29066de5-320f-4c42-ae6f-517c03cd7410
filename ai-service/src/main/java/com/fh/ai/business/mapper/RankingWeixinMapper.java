package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.book.RankingWeixinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingWeixinDto;
import com.fh.ai.business.entity.vo.book.RankingWeixinVo;

/**
 * 微信榜单表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface RankingWeixinMapper extends BaseMapper<RankingWeixinDto> {

	List<RankingWeixinVo> getRankingWeixinListByCondition(RankingWeixinConditionBo condition);

	RankingWeixinVo getRankingWeixinByCondition(RankingWeixinConditionBo condition);

	/**
	 * 根据条件查询某个榜单最新的一个日期的uuid是多少，方便用于后面列表查询最新的榜单。
	 * @param condition
	 * @return
	 */
	RankingWeixinVo getLatestRankingWeixinDateUuid(RankingWeixinConditionBo condition);

	/**
	 * 查询微信带有榜单数据的书籍列表
	 *
	 * @param condition the condition
	 * @return ranking weixin list with book by condition
	 */
	List<RankingWeixinVo> getRankingWeixinListWithBookByCondition(RankingWeixinConditionBo condition);
}
