package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.role.RoleListConditionBo;
import com.fh.ai.business.entity.dto.role.RoleDto;
import com.fh.ai.business.entity.vo.role.RoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
public interface RoleMapper extends BaseMapper<RoleDto> {

    List<RoleDto> getRoleListByUserId(@Param("userId") Integer userId);

    List<RoleVo> getRoleListByCondition(RoleListConditionBo condition);
}
