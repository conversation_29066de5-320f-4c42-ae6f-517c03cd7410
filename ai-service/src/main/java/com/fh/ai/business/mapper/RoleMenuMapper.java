package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.roleMenu.RoleMenuDto;
import com.fh.ai.business.entity.bo.roleMenu.RoleMenuConditionBo;
import com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo;

import java.util.List;

/**
 * 角色菜单Mapper
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
public interface RoleMenuMapper extends BaseMapper<RoleMenuDto> {

    List<RoleMenuVo> getRoleMenuListByCondition(RoleMenuConditionBo condition);

    List<RoleMenuVo> getRoleMenuByRoleId(Long roleId);

    List<RoleMenuVo> getAllRoleMenu();
}
