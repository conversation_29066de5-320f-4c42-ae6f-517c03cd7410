package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.sceneDetail.SceneDetailConditionBo;
import com.fh.ai.business.entity.dto.sceneDetail.SceneDetailDto;
import com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo;

/**
 * 场景详情表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:26
 */
public interface SceneDetailMapper extends BaseMapper<SceneDetailDto> {

	List<SceneDetailVo> getSceneDetailListByCondition(SceneDetailConditionBo condition);

	SceneDetailVo getSceneDetailByCondition(SceneDetailConditionBo condition);

}
