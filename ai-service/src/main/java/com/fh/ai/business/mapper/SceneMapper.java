package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.scene.SceneDto;
import com.fh.ai.business.entity.bo.scene.SceneConditionBo;
import com.fh.ai.business.entity.vo.scene.SceneVo;

/**
 * 场景表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:12
 */
public interface SceneMapper extends BaseMapper<SceneDto> {

	List<SceneVo> getSceneListByCondition(SceneConditionBo condition);

	SceneVo getSceneByCondition(SceneConditionBo condition);

	List<SceneVo> getSceneListWithDetailList(SceneConditionBo condition);

}
