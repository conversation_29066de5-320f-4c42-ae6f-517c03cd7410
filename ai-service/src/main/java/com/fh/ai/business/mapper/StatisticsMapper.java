package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.dto.statistics.StatisticsDto;
import com.fh.ai.business.entity.vo.statistics.*;

import java.util.List;

/**
 * 统计表Mapper
 *
 * <AUTHOR>
 * @date 2024-05-15 14:17:53
 */
public interface StatisticsMapper extends BaseMapper<StatisticsDto> {

    List<StatisticsVo> getStatisticsListByCondition(StatisticsConditionBo condition);

    SysStatisticsInfoVo getSysStatistics(StatisticsConditionBo condition);

    List<SysDailyStatisticsInfoVo> getSysDailyStatistics(StatisticsConditionBo condition);

    List<OrgStatisticsVo> getOrgStatistics(StatisticsConditionBo condition);

    List<UserStatisticsVo> getUserStatistics(StatisticsConditionBo condition);
    List<UserStatisticsVo> getUserStatisticsWithoutNull(StatisticsConditionBo condition);

}