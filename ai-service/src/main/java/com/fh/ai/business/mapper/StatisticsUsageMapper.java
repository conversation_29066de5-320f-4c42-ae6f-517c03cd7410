package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.UsageStatisticsTotalBo;
import com.fh.ai.business.entity.dto.statisticsUsage.StatisticsUsageDto;
import com.fh.ai.business.entity.vo.statisticsUsage.OrgUsageStatisticsVo;
import com.fh.ai.business.entity.vo.statisticsUsage.StatisticsUsageVo;
import com.fh.ai.business.entity.vo.statisticsUsage.SysUsageStatisticsVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 使用情况统计表Mapper
 *
 * <AUTHOR>
 * @date 2024-05-15 14:46:40
 */
public interface StatisticsUsageMapper extends BaseMapper<StatisticsUsageDto> {

    List<StatisticsUsageVo> getStatisticsUsageListByCondition(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> getUsageStatistics(@Param("organizationId") Long organizationId, @Param("startDay") String startDay);

    List<UsageStatisticsTotalVo> getTop10UsageStatistics(UsageStatisticsTotalBo bo);

    List<OrgUsageStatisticsVo> getOrgUsageStatistics(StatisticsConditionBo bo);

    List<OrgUsageStatisticsVo> getOrgUsageTop10(StatisticsConditionBo bo);

    SysUsageStatisticsVo getSysUsageStatistics(StatisticsConditionBo bo);

    List<UsageStatisticsTotalVo> findAll(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> findAllUser(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> findAllByType(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> findClickAllByType(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> findAllUserByType(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> findFavoriteByType(StatisticsUsageConditionBo condition);

    List<UsageStatisticsTotalVo> getTop10UserUsageSStatistics(@Param("organizationId") Long organizationId);

}