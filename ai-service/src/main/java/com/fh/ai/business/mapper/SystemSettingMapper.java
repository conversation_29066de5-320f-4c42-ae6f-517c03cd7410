package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.systemSetting.SystemSettingConditionBo;
import com.fh.ai.business.entity.dto.systemSetting.SystemSettingDto;
import com.fh.ai.business.entity.vo.systemSetting.SystemSettingVo;

import java.util.List;

/**
 * 系统设置表Mapper
 *
 * <AUTHOR>
 * @date 2024-02-23 14:05:25
 */
public interface SystemSettingMapper extends BaseMapper<SystemSettingDto> {

    List<SystemSettingVo> getSystemSettingListByCondition(SystemSettingConditionBo condition);

}