package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.task.TaskDto;
import com.fh.ai.business.entity.bo.task.TaskConditionBo;
import com.fh.ai.business.entity.vo.task.TaskVo;

/**
 * 任务表Mapper
 *
 * <AUTHOR>
 * @date 2024-06-04 13:49:38
 */
public interface TaskMapper extends BaseMapper<TaskDto> {

	List<TaskVo> getTaskListByCondition(TaskConditionBo condition);

	List<TaskVo> getMyTaskListByCondition(TaskConditionBo condition);

}