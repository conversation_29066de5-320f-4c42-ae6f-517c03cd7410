package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.tip.TipConditionBo;
import com.fh.ai.business.entity.dto.tip.TipDto;
import com.fh.ai.business.entity.vo.tip.TipVo;

/**
 * 提示词Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface TipMapper extends BaseMapper<TipDto> {

	List<TipVo> getTipListByCondition(TipConditionBo condition);

}
