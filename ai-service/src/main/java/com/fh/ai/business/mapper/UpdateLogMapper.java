package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.updateLog.UpdateLogConditionBo;
import com.fh.ai.business.entity.dto.updateLog.UpdateLogDto;
import com.fh.ai.business.entity.vo.updateLog.UpdateLogVo;
import org.apache.ibatis.annotations.Param;

/**
 * 更新日志Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface UpdateLogMapper extends BaseMapper<UpdateLogDto> {

    List<UpdateLogVo> getUpdateLogListByCondition(UpdateLogConditionBo condition);

    Integer updateUpdateLog(@Param("id")Long id);

    Integer check(@Param("id") Long id, @Param("oid")String oid);

    Integer read(@Param("id")Long id, @Param("oid")String oid);

}
