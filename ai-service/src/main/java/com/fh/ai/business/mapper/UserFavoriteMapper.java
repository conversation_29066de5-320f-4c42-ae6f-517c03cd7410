package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.userFavorite.UserFavoriteDto;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteConditionBo;
import com.fh.ai.business.entity.vo.userFavorite.UserFavoriteVo;

/**
 * 用户收藏表Mapper
 *
 * <AUTHOR>
 * @date 2024-06-04 10:38:07
 */
public interface UserFavoriteMapper extends BaseMapper<UserFavoriteDto> {

	List<UserFavoriteVo> getUserFavoriteListByCondition(UserFavoriteConditionBo condition);

	List<UserFavoriteVo> findLatestFavoriteByType(UserFavoriteConditionBo condition);

}