package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.userFeedback.UserFeedbackDto;
import com.fh.ai.business.entity.bo.userFeedback.UserFeedbackConditionBo;
import com.fh.ai.business.entity.vo.userFeedback.UserFeedbackVo;

/**
 * 用户反馈表Mapper
 *
 * <AUTHOR>
 * @date 2024-07-01 10:31:51
 */
public interface UserFeedbackMapper extends BaseMapper<UserFeedbackDto> {

	List<UserFeedbackVo> getUserFeedbackListByCondition(UserFeedbackConditionBo condition);

}