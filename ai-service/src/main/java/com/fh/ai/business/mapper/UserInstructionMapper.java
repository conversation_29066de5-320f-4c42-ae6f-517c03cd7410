package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.userInstruction.UserInstructionConditionBo;
import com.fh.ai.business.entity.dto.userInstruction.UserInstructionDto;
import com.fh.ai.business.entity.vo.userInstruction.UserInstructionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-08  11:16
 */
public interface UserInstructionMapper extends BaseMapper<UserInstructionDto> {

    List<UserInstructionVo> getUserInstructionListByCondition(UserInstructionConditionBo conditionBo);

    UserInstructionVo getUserInstructionByOid(UserInstructionConditionBo conditionBo);

}
