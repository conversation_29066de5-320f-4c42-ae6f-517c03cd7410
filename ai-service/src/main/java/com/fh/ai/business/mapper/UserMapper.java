package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.user.UserConditionBo;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.vo.user.UserAccountCountVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表Mapper
 *
 * <AUTHOR>
 * @date 2024-02-20 16:29:06
 */
public interface UserMapper extends BaseMapper<UserDto> {

    List<UserVo> getUserListByCondition(UserConditionBo condition);

    Integer checkUserPhone(@Param("account") String account, @Param("phone") String phone);

    List<UserVo> getUserListWithUsageCount(UserConditionBo conditionBo);

    List<UserAccountCountVo> getUserAccountCountByOrganizationIds(@Param("organizationIds") List<Long> organizationIds);

}