package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.vo.historyApp.UserUsageVo;
import com.fh.ai.business.entity.vo.userPrize.UserPrizeTotalVo;
import com.fh.ai.business.entity.vo.userPrize.UserPrizeVo;

import java.util.List;

/**
 * 用户奖品兑换表Mapper
 *
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
public interface UserPrizeMapper extends BaseMapper<UserPrizeDto> {

    List<UserPrizeVo> getUserPrizeListByCondition(UserPrizeConditionBo condition);

    List<UserUsageVo> getUserRedeemCount(UserPrizeConditionBo condition);

    UserPrizeTotalVo getUserPrizeTotal(UserPrizeConditionBo condition);

}