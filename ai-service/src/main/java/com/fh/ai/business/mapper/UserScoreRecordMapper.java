package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.vo.userScoreRecord.ScoreTypeStatisticsVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo;

import java.util.List;

/**
 * 用户积分记录Mapper
 *
 * <AUTHOR>
 * @date 2024-02-20 17:14:17
 */
public interface UserScoreRecordMapper extends BaseMapper<UserScoreRecordDto> {

    List<UserScoreRecordVo> getUserScoreRecordListByCondition(UserScoreRecordConditionBo condition);

    List<ScoreTypeStatisticsVo> getScoreTypeStatistics(UserScoreRecordConditionBo condition);

    UserScoreVo getUserScore(UserScoreRecordConditionBo condition);

    List<UserScoreVo> getUserScoreList(UserScoreRecordConditionBo condition);

    List<UserScoreRecordVo> getLastLoginRecordList(UserScoreRecordConditionBo condition);

}