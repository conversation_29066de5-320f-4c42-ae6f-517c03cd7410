package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.userStorage.UserStorageConditionBo;
import com.fh.ai.business.entity.dto.userStorage.UserStorageDto;
import com.fh.ai.business.entity.vo.userStorage.UserStorageVo;

/**
 * 用户本地信息存储表，用于存储用户配置等信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-03 16:27:55
 */
public interface UserStorageMapper extends BaseMapper<UserStorageDto> {

	List<UserStorageVo> getUserStorageListByCondition(UserStorageConditionBo condition);

	UserStorageVo getUserStorageByCondition(UserStorageConditionBo condition);

}
