package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.userTaskAttachment.UserTaskAttachmentDto;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentConditionBo;
import com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo;

/**
 * 任务附件表Mapper
 *
 * <AUTHOR>
 * @date 2024-06-04 14:55:41
 */
public interface UserTaskAttachmentMapper extends BaseMapper<UserTaskAttachmentDto> {

	List<UserTaskAttachmentVo> getUserTaskAttachmentListByCondition(UserTaskAttachmentConditionBo condition);

	List<UserTaskAttachmentVo> getUserTaskAttachmentInfoListByCondition(UserTaskAttachmentConditionBo condition);

	List<UserTaskAttachmentVo> getUserTaskAttachmentListByTask(Long taskId);

}