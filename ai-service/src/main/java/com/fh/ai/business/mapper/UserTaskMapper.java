package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.userTask.UserTaskDto;
import com.fh.ai.business.entity.bo.userTask.UserTaskConditionBo;
import com.fh.ai.business.entity.vo.userTask.UserTaskVo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户任务表Mapper
 *
 * <AUTHOR>
 * @date 2024-06-04 14:45:06
 */
public interface UserTaskMapper extends BaseMapper<UserTaskDto> {

	List<UserTaskVo> getUserTaskListByCondition(UserTaskConditionBo condition);

	Integer noPass(@Param("oid") String oid);
}