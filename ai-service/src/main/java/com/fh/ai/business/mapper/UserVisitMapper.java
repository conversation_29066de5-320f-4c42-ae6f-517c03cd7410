package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.dto.userVisit.UserVisitDto;
import com.fh.ai.business.entity.bo.userVisit.UserVisitConditionBo;
import com.fh.ai.business.entity.vo.userVisit.UserVisitVo;

/**
 * 用户访问记录表Mapper
 *
 * <AUTHOR>
 * @date 2024-05-15 13:56:34
 */
public interface UserVisitMapper extends BaseMapper<UserVisitDto> {

	List<UserVisitVo> getUserVisitListByCondition(UserVisitConditionBo condition);

}