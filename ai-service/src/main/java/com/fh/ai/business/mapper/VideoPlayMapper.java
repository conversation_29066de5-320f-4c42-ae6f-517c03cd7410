package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.videoPlay.VideoPlayConditionBo;
import com.fh.ai.business.entity.dto.videoPlay.VideoPlayDto;
import com.fh.ai.business.entity.vo.videoPlay.VideoPlayVo;

/**
 * 视频播放表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface VideoPlayMapper extends BaseMapper<VideoPlayDto> {

	List<VideoPlayVo> getVideoPlayListByCondition(VideoPlayConditionBo condition);

}
