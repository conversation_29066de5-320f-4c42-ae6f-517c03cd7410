package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveConditionBo;
import com.fh.ai.business.entity.dto.worksActive.WorksActiveDto;
import com.fh.ai.business.entity.vo.worksActive.WorksActiveVo;

/**
 * 作品活动表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-21 18:12:16
 */
public interface WorksActiveMapper extends BaseMapper<WorksActiveDto> {

	List<WorksActiveVo> getWorksActiveListByCondition(WorksActiveConditionBo condition);

	WorksActiveVo getWorksActiveByCondition(WorksActiveConditionBo condition);

}
