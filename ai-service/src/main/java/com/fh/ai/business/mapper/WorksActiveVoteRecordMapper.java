package com.fh.ai.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.worksActiveVoteRecord.WorksActiveVoteRecordConditionBo;
import com.fh.ai.business.entity.dto.worksActiveVoteRecord.WorksActiveVoteRecordDto;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteCountVo;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRankVo;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 作品活动投票记录表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2025-03-04 10:12:16
 */
public interface WorksActiveVoteRecordMapper extends BaseMapper<WorksActiveVoteRecordDto> {

	List<WorksActiveVoteRecordVo> getWorksActiveVoteRecordListByCondition(WorksActiveVoteRecordConditionBo condition);

	WorksActiveVoteRecordVo getWorksActiveVoteRecordByCondition(WorksActiveVoteRecordConditionBo condition);

	Integer getWorksActiveVoteCountByUserOid(@Param("userOid") String userOid, @Param("worksActiveId") Long worksActiveId);

	WorksActiveVoteCountVo getWorksActiveVoteCount(@Param("worksActiveId") Long worksActiveId);

	List<WorksActiveVoteRankVo> getWorksActiveVoteRank(@Param("worksActiveId") Long worksActiveId, @Param("startDay") Date startDay, @Param("endDay") Date endDay);
}
