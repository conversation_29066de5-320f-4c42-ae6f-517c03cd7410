package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.zegoHistory.ZegoHistoryConditionBo;
import com.fh.ai.business.entity.dto.zegoHistory.ZegoHistoryDto;
import com.fh.ai.business.entity.vo.zegoHistory.ZegoHistoryVo;

/**
 * 美图生成记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
public interface ZegoHistoryMapper extends BaseMapper<ZegoHistoryDto> {

	List<ZegoHistoryVo> getZegoHistoryListByCondition(ZegoHistoryConditionBo condition);

}
