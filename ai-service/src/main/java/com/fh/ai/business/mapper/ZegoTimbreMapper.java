package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.zego.ZegoTimbreConditionBo;
import com.fh.ai.business.entity.dto.zego.ZegoTimbreDto;
import com.fh.ai.business.entity.vo.zego.ZegoTimbreVo;

/**
 * 即构音色表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-14 16:34:49
 */
public interface ZegoTimbreMapper extends BaseMapper<ZegoTimbreDto> {

	List<ZegoTimbreVo> getZegoTimbreListByCondition(ZegoTimbreConditionBo condition);

	ZegoTimbreVo getZegoTimbreByCondition(ZegoTimbreConditionBo condition);

}
