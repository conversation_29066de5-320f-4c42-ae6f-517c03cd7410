package com.fh.ai.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.ai.business.entity.bo.zegoUser.ZegoUserConditionBo;
import com.fh.ai.business.entity.dto.zegoUser.ZegoUserDto;
import com.fh.ai.business.entity.vo.zegoUser.ZegoUserVo;

/**
 * 美图用户使用表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
public interface ZegoUserMapper extends BaseMapper<ZegoUserDto> {

	List<ZegoUserVo> getZegoUserListByCondition(ZegoUserConditionBo condition);

}
