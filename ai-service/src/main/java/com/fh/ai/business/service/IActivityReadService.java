package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.activityRead.ActivityReadBo;
import com.fh.ai.business.entity.bo.activityRead.ActivityReadConditionBo;
import com.fh.ai.business.entity.dto.activityRead.ActivityReadDto;
import com.fh.ai.business.entity.vo.activityRead.ActivityReadVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 帮助中心接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-20 09:52:37
 */
public interface IActivityReadService extends IService<ActivityReadDto> {

    List<ActivityReadVo> getActivityReadListByCondition(ActivityReadConditionBo condition);

	AjaxResult addActivityRead(ActivityReadBo activityReadBo);

	AjaxResult updateActivityRead(ActivityReadBo activityReadBo);

	Map<String, Object> getDetail(Long id);

}

