package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.activity.ActivityBo;
import com.fh.ai.business.entity.bo.activity.ActivityConditionBo;
import com.fh.ai.business.entity.dto.activity.ActivityDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 活动表接口
 *
 * <AUTHOR>
 * @date 2024-05-13 14:48:18
 */
public interface IActivityService extends IService<ActivityDto> {

    Map<String, Object> getActivityListByCondition(ActivityConditionBo conditionBo);

	AjaxResult addActivity(ActivityBo activityBo);

	AjaxResult updateActivity(ActivityBo activityBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(ActivityBo activityBo);

    AjaxResult deleteActivity(ActivityBo activityBo);

}