package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleBo;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleConditionBo;
import com.fh.ai.business.entity.dto.adminRole.AdminRoleDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 平台用户角色表接口
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:50
 */
public interface IAdminRoleService extends IService<AdminRoleDto> {

    Map<String, Object> getAdminRoleListByCondition(AdminRoleConditionBo conditionBo);

    AjaxResult addAdminRole(AdminRoleBo adminRoleBo);

    AjaxResult updateAdminRole(AdminRoleBo adminRoleBo);

    Map<String, Object> getDetail(Long id);

}

