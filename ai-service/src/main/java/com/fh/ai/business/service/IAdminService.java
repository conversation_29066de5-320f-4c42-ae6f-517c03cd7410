package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.admin.AdminBo;
import com.fh.ai.business.entity.bo.admin.AdminConditionBo;
import com.fh.ai.business.entity.dto.admin.AdminDto;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 平台用户表接口
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:49
 */
public interface IAdminService extends IService<AdminDto> {

    AjaxResult checkToken(String token);

    AdminVo login(AdminBo adminBo);

    AdminVo ppmLogin(AdminBo adminBo);

    Map<String, Object> getAdminListByCondition(AdminConditionBo condition);

    AjaxResult addAdmin(AdminBo adminBo);

    AjaxResult updateAdmin(AdminBo adminBo);

    AjaxResult updateState(AdminBo adminBo);

    AjaxResult deleteAdmin(AdminBo adminBo);

    Boolean changePassword(AdminBo adminBo);

    AjaxResult resetPassword(AdminBo adminBo);

    AjaxResult getDetail(String oid);
}

