package com.fh.ai.business.service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.attachment.AttachmentConditionBo;
import com.fh.ai.business.entity.dto.attachment.AttachmentDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 附件表接口
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
public interface IAttachmentService extends IService<AttachmentDto> {

    Map<String, Object> getAttachmentListByCondition(AttachmentConditionBo conditionBo);

    AjaxResult addAttachment(AttachmentBo attachmentBo);

    AjaxResult updateAttachment(AttachmentBo attachmentBo);

    AjaxResult getDetail(Long id);

    AttachmentVo getDetail(String fileOid);

    AjaxResult updateState(AttachmentBo attachmentBo);

    AjaxResult deleteAttachment(AttachmentBo attachmentBo);

    void deleteQwenFiles();

    AttachmentVo uploadFile(String originalName, String remoteFoldPath, ByteArrayOutputStream byteArrayOutputStream, AttachmentBo attachmentBo) throws Exception;

    AjaxResult uploadFile(MultipartFile file, AttachmentBo attachmentBo);

    AjaxResult uploadVideo(MultipartFile file, AttachmentBo attachmentBo);

    AjaxResult uploadRichTextToFile(String inputHtml, AttachmentBo attachmentBo);

    AjaxResult getUrlWithToken(String oid);

    AjaxResult writeToFile(String text, String originalName);

    /**
     * 将用户上传的文件存入磁盘，返回磁盘的文件对象
     * @param multipartFile
     * @return
     */
    File toFile(MultipartFile multipartFile);

    AttachmentVo uploadImage(String imageUrl, AttachmentBo attachmentBo);

    /**
     * 上传一个文件地址到服务器
     * @param imageUrl
     * @param attachmentBo
     * @return
     */
    AttachmentVo uploadUrl(String imageUrl, AttachmentBo attachmentBo);

    /**
     * 将第三方图片转成我们服务器的图片
     * @param attachmentBo
     * @return
     */
    AjaxResult getAndUploadImage(AttachmentBo attachmentBo);

}