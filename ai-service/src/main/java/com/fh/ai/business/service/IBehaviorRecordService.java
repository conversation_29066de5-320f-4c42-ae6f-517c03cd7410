package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.behaviorRecord.BehaviorRecordBo;
import com.fh.ai.business.entity.bo.behaviorRecord.BehaviorRecordConditionBo;
import com.fh.ai.business.entity.dto.behaviorRecord.BehaviorRecordDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 行为记录表接口
 *
 * <AUTHOR>
 * @date 2024-03-11 14:44:58
 */
public interface IBehaviorRecordService extends IService<BehaviorRecordDto> {

    Map<String, Object> getBehaviorRecordListByCondition(BehaviorRecordConditionBo conditionBo);

    Map<String, Object> getDistinctType(BehaviorRecordConditionBo conditionBo);

    AjaxResult addBehaviorRecord(BehaviorRecordBo behaviorRecordBo);

    AjaxResult updateBehaviorRecord(BehaviorRecordBo behaviorRecordBo);

    AjaxResult getDetail(Long id);

}