package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookBo;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingBookDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 软文与书籍关系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:03
 */
public interface IBookCopywritingBookService extends IService<BookCopywritingBookDto> {

    /**
     * 列表的分页和不分页查询都支持-根据page或者limit是否为空判断
     *
     * @param conditionBo the condition bo
     * @return book list by condition and page
     */
    Map<String, Object> getBookCopywritingBookListByConditionAndPage(BookCopywritingBookConditionBo conditionBo);

    List<BookCopywritingBookVo> getBookCopywritingBookListByCondition(BookCopywritingBookConditionBo condition);

    AjaxResult addBookCopywritingBook(BookCopywritingBookBo bookCopywritingBookBo);

    AjaxResult updateBookCopywritingBook(BookCopywritingBookBo bookCopywritingBookBo);

    BookCopywritingBookVo getBookCopywritingBookByCondition(BookCopywritingBookConditionBo condition);

    /**
     * 批量新增
     * 
     * @param bookCopywritingBookBos
     */
    void addBookCopywritingBookBatch(List<BookCopywritingBookBo> bookCopywritingBookBos);

    /**
     * 批量保存或更新（新增/更新/删除）
     * 
     * @param bookCopywritingBookBos 要保存或更新的数据
     * @param copywritingId 软文ID
     * @param updateBy 更新人
     */
    void saveOrUpdateBookCopywritingBookBatch(List<BookCopywritingBookBo> bookCopywritingBookBos, Long copywritingId,
        String updateBy);
}
