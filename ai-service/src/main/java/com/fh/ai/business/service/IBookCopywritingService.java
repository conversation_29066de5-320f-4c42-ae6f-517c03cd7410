package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBo;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 书籍软文表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:02
 */
public interface IBookCopywritingService extends IService<BookCopywritingDto> {

	/**
	 * 列表的分页和不分页查询都支持-根据page或者limit是否为空判断
	 *
	 * @param conditionBo the condition bo
	 * @return book list by condition and page
	 */
	Map<String, Object> getBookCopywritingListByConditionAndPage(BookCopywritingConditionBo conditionBo);

    List<BookCopywritingVo> getBookCopywritingListByCondition(BookCopywritingConditionBo condition);

	Long addBookCopywriting(BookCopywritingBo bookCopywritingBo);

	/**
	 * 新增软文主表+详情（每本书卖点信息）
	 *
	 * @param bookCopywritingBo the book copywriting bo
	 * @return ajax result
	 * <AUTHOR>
	 * @date 2025 -06-25 10:58:44
	 */
	AjaxResult addBookCopywritingWithDetail(BookCopywritingBo bookCopywritingBo);

	AjaxResult updateBookCopywriting(BookCopywritingBo bookCopywritingBo);

	/**
	 * 更新软文主表+详情（每本书卖点信息）
	 *
	 * @param bookCopywritingBo the book copywriting bo
	 * @return ajax result
	 * <AUTHOR>
	 * @date 2025 -06-30 10:58:44
	 */
	AjaxResult updateBookCopywritingWithDetail(BookCopywritingBo bookCopywritingBo);

	BookCopywritingVo getBookCopywritingByCondition(BookCopywritingConditionBo condition);

	/**
	 * 通过uuid精确查询软文记录
	 *
	 * @param uuid 软文唯一标识
	 * @return BookCopywritingVo
	 * <AUTHOR>
	 * @date 2025-06-30 11:35:00
	 */
	BookCopywritingVo getBookCopywritingByUuid(String uuid);

}

