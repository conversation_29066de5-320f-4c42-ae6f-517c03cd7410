package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.BookFeedbackBo;
import com.fh.ai.business.entity.bo.book.BookFeedbackConditionBo;
import com.fh.ai.business.entity.dto.book.BookFeedbackDto;
import com.fh.ai.business.entity.vo.book.BookFeedbackVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:25
 */
public interface IBookFeedbackService extends IService<BookFeedbackDto> {
    Map<String, Object> getBookFeedbackPageByCondition(BookFeedbackConditionBo condition);

    AjaxResult addBookFeedback(BookFeedbackBo bo);

    AjaxResult updateBookFeedback(BookFeedbackBo bo);

    BookFeedbackVo getDetail(BookFeedbackConditionBo condition);
}
