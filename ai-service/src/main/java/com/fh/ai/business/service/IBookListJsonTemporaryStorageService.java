package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageBo;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageConditionBo;
import com.fh.ai.business.entity.dto.bookList.BookListJsonTemporaryStorageDto;
import com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 书单json临时存储service
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:24
 */
public interface IBookListJsonTemporaryStorageService extends IService<BookListJsonTemporaryStorageDto> {
    Map<String, Object> getBookListJsonTemporaryStoragePageByCondition(BookListJsonTemporaryStorageConditionBo condition);

    AjaxResult addBookListJsonTemporaryStorage(BookListJsonTemporaryStorageBo bo);

    AjaxResult updateBookListJsonTemporaryStorage(BookListJsonTemporaryStorageBo bo);

    BookListJsonTemporaryStorageVo getDetail(BookListJsonTemporaryStorageConditionBo condition);
}
