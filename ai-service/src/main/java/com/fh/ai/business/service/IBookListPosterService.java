package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.bookListPoster.BookListPosterBo;
import com.fh.ai.business.entity.bo.bookListPoster.BookListPosterConditionBo;
import com.fh.ai.business.entity.dto.bookListPoster.BookListPosterDto;
import com.fh.ai.business.entity.vo.bookListPoster.BookListPosterVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 书单海报表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-07 10:00:00
 */
public interface IBookListPosterService extends IService<BookListPosterDto> {

	/**
	 * 列表的分页和不分页查询都支持-根据page或者limit是否为空判断
	 *
	 * @param conditionBo the condition bo
	 * @return book list by condition and page
	 */
	Map<String, Object> getBookListPosterListByConditionAndPage(BookListPosterConditionBo conditionBo);

    List<BookListPosterVo> getBookListPosterListByCondition(BookListPosterConditionBo condition);

    BookListPosterVo getBookListPosterByCondition(BookListPosterConditionBo condition);

    /**
     * 新增书单海报
     *
     * @param bookListPosterBo the book list poster bo
     * @return ajax result
     */
    AjaxResult addBookListPoster(BookListPosterBo bookListPosterBo);

    /**
     * 修改书单海报
     *
     * @param bookListPosterBo the book list poster bo
     * @return ajax result
     */
    AjaxResult updateBookListPoster(BookListPosterBo bookListPosterBo);

    /**
     * 删除书单海报
     *
     * @param id the id
     * @return ajax result
     */
    AjaxResult deleteBookListPoster(Long id);

    /**
     * 批量删除书单海报
     *
     * @param ids the ids
     * @return ajax result
     */
    AjaxResult batchDeleteBookListPoster(List<Long> ids);

    /**
     * 根据uuid查询书单海报
     *
     * @param uuid the uuid
     * @return book list poster vo
     */
    BookListPosterVo getBookListPosterByUuid(String uuid);

    /**
     * 根据uuid更新书单海报
     *
     * @param uuid the uuid
     * @param bookListPosterBo the book list poster bo
     * @return ajax result
     */
    AjaxResult updateBookListPosterByUuid(String uuid, BookListPosterBo bookListPosterBo);

}
