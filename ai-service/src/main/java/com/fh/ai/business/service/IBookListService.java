package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.bookList.BookListBo;
import com.fh.ai.business.entity.bo.bookList.BookListConditionBo;
import com.fh.ai.business.entity.dto.bookList.BookListDto;
import com.fh.ai.business.entity.vo.bookList.BookListVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 书单service
 *
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:24
 */
public interface IBookListService extends IService<BookListDto> {


    Map<String, Object> getBookListPageByCondition(BookListConditionBo condition);

    AjaxResult addBookList(BookListBo bookListBo);

    AjaxResult updateBookList(BookListBo bookList);

    BookListVo getDetail(BookListConditionBo condition);

    AjaxResult removeBookList(BookListConditionBo condition);

    void convertBookListJsonBatch();

}
