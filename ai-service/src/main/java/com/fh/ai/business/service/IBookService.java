package com.fh.ai.business.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.BookBo;
import com.fh.ai.business.entity.bo.book.BookConditionBo;
import com.fh.ai.business.entity.bo.book.BookExcelBo;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.QuerySmartSaleBo;
import com.fh.ai.business.entity.dto.book.BookDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankAndSaleInfo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 书籍信息表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IBookService extends IService<BookDto> {

	/**
	 * 列表的分页和不分页查询都支持-根据page或者limit是否为空判断
	 *
	 * @param conditionBo the condition bo
	 * @return book list by condition and page
	 */
	Map<String, Object> getBookListByConditionAndPage(BookConditionBo conditionBo);

    List<BookVo> getBookListByCondition(BookConditionBo condition);

	AjaxResult addBook(BookBo bookBo);

	AjaxResult updateBook(BookBo bookBo);

	BookVo getBookByCondition(BookConditionBo condition);

	/**
	 * 远程获取开卷类型的book
	 */
	void getBookRpcFromSmartClient();

	/**
	 * 根据sourceType获取分类列表（去重）
	 *
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return category list by source type
	 */
	List<String> getCategoryListBySourceType(Integer sourceType);
	/**
	 * 根据sourceType获取店铺列表（去重）
	 *
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return category list by source type
	 */
	List<String> getShopListBySourceType(Integer sourceType);
	/**
	 * 根据sourceType获取出版社列表（去重）
	 *
	 * @param sourceType {@link com.fh.ai.common.enums.BookSourceType}
	 * @return category list by source type
	 */
	List<String> getPublishListBySourceType(Integer sourceType);

	/**
	 * 获取 图书榜单和销售信息
	 * @return key：图书渠道名称的拼写，value：（key：榜单时间yyyy-MM-dd，value：排名/销量/评论数量）
	 */
	Map<String, Map<String,RankAndSaleInfo>> getBookRankAndSaleInfo(QueryRankAndSaleBo queryRankAndSaleBo);

	/**
	 * 从excel 导入书籍
	 * @param sourceType 渠道类型 8 抖音 9 京东
	 * @param bookExcelBos 待导入数据
	 */
	void importBooksFromExcel(Integer sourceType, List<BookExcelBo> bookExcelBos, Date collectTime);
	/**
	 * 根据isbn获取凤凰本版书销量数据
	 * @param querySmartSaleBo
	 * @return 返回数据的格式(key：月份yyyy-MM, value：销量)
	 */
	Map<String,Integer> getMonthSalesByIsbnOfSmart(QuerySmartSaleBo querySmartSaleBo);

	/**
	 * 同步图书库书籍
	 *
	 * @param startTime
	 * @param endTime
	 * @return void
	 * <AUTHOR>
	 * @date 2024/12/31 9:56
	 **/
	void fzBookSync(String startTime, String endTime);

	/**
	 * 查询coze工作流需要的图书信息（书籍信息字段（具体看前端界面）、条件是：标签数组字符串、出版社名称数组字符串、source_type=7、is_delete=1）
	 * 手写limit，限制拉取条数
	 */
	List<BookVo> getCozeBookListByCondition(BookConditionBo conditionBo);
}

