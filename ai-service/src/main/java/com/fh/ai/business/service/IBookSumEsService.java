package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.dto.BookSumEsEntity;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.vo.PageVo;

/**
 * 用户奖品兑换表接口
 *
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
public interface IBookSumEsService extends IService<UserPrizeDto> {

    PageVo<BookSumEsEntity> page(UserPrizeConditionBo conditionBo);

}