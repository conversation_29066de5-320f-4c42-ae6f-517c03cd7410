package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.calendarEvent.CalendarEventBo;
import com.fh.ai.business.entity.bo.calendarEvent.CalendarEventConditionBo;
import com.fh.ai.business.entity.dto.calendarEvent.CalendarEventDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 日历事件表接口
 *
 * <AUTHOR>
 * @date 2024-07-02 14:21:19
 */
public interface ICalendarEventService extends IService<CalendarEventDto> {

    Map<String, Object> getCalendarEventListByCondition(CalendarEventConditionBo conditionBo);

	AjaxResult addCalendarEvent(CalendarEventBo calendarEventBo);

	AjaxResult updateCalendarEvent(CalendarEventBo calendarEventBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteCalendarEvent(CalendarEventBo calendarEventBo);

    /**
     * 查询日历列表 （包含历史上的今天）
     *
     * @param conditionBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/11/18 14:10
     **/
    Map<String, Object> getCalendarEventListWithHistoryEvents(CalendarEventConditionBo conditionBo);
}