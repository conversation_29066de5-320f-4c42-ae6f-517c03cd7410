package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.config.ConfigBo;
import com.fh.ai.business.entity.bo.config.ConfigConditionBo;
import com.fh.ai.business.entity.dto.config.ConfigDto;
import com.fh.ai.business.entity.vo.config.ConfigVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 配置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-02 09:40:20
 */
public interface IConfigService extends IService<ConfigDto> {

	List<ConfigVo> getConfigListByCondition(ConfigConditionBo condition);

	AjaxResult addConfig(ConfigBo configBo);

	AjaxResult updateConfig(ConfigBo configBo);

	Map<String, Object> getDetail(Long configId);

	ConfigDto getConfigByKey(String configKey);

	ConfigDto getConfigFromRedis(String configKey);

	AjaxResult refresh();

	AjaxResult getConfigValue(String configKey);

	AjaxResult updateByConfigKey(ConfigBo configBo);

	ConfigDto getConfigByKeyFromDB(String configKey);

}

