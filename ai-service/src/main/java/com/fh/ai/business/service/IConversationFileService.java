package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileBo;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileConditionBo;
import com.fh.ai.business.entity.dto.conversationFile.ConversationFileDto;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 会话文件表接口
 *
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
public interface IConversationFileService extends IService<ConversationFileDto> {

    Map<String, Object> getConversationFileListByCondition(ConversationFileConditionBo conditionBo);

    /**
     * 根据条件查询-不分页
     * @param conditionBo
     * @return
     */
    List<ConversationFileVo> getConversationFileListByConditionNoPage(ConversationFileConditionBo conditionBo);

    AjaxResult uploadFile(MultipartFile file, ConversationFileBo conversationFileBo);

    AjaxResult changeState(ConversationFileBo conversationFileBo);

    AjaxResult xfConvert(ConversationFileBo conversationFileBo);

    void getXfConvertResult();

    AjaxResult addConversationFile(ConversationFileBo conversationFileBo);

    /**
     * 根据主键id更新会话文件表
     * @param conversationFileBo
     * @return
     */
    AjaxResult updateConversationFile(ConversationFileBo conversationFileBo);

    /**
     * 根据会话码更新会话文件表
     *
     * @param conversationFileBo the conversation file bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-15 10:00:59
     */
    AjaxResult updateConversationFileByConversationCode(ConversationFileBo conversationFileBo);

    /**
     * 根据会话码新增/更新会话文件表。注意：原来的本表数据添加不会添加historyId，因此如果用于aippt的下载任务，那么一定是和historyId关联
     *
     * @param conversationFileBo the conversation file bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-15 10:00:59
     */
    AjaxResult saveConversationFileByConversationCode(ConversationFileBo conversationFileBo);

    AjaxResult getDetail(Long id);

    ConversationFileVo getDetail(ConversationFileBo conversationFileBo);

    AjaxResult deleteConversationFile(ConversationFileBo conversationFileBo);

    String uploadQwenFile(File file);

    String uploadKimiFile(File file);

    /**
     * 获取aippt的下载结果
     *
     * @param taskStates the task states
     */
    void getAipptDownloadResult(List<Integer> taskStates);

    /**
     * 获取taskId和内容的map
     *
     * @param taskIds the task ids
     * @param taskType the task type
     * @return map map
     * <AUTHOR>
     * @date 2025 -01-10 14:20:01
     */
    Map<String,String> taskIdResultMap(List<String> taskIds,Integer taskType);
}