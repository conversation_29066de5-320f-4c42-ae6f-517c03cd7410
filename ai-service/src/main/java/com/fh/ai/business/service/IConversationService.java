package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.conversation.ConversationBo;
import com.fh.ai.business.entity.bo.conversation.ConversationConditionBo;
import com.fh.ai.business.entity.dto.conversation.ConversationDto;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 会话表接口
 *
 * <AUTHOR>
 * @date 2024-04-17 15:40:02
 */
public interface IConversationService extends IService<ConversationDto> {

    Map<String, Object> getConversationListByCondition(ConversationConditionBo conditionBo);

    AjaxResult addConversation(ConversationBo conversationBo);

    /**
     * 使用conversionCode更新会话
     * @param conversationBo
     * @return
     */
    AjaxResult updateConversation(ConversationBo conversationBo);

    AjaxResult getDetail(Long id);

    ConversationVo getDetail(String conversationCode, Integer type);

    AjaxResult deleteConversation(ConversationBo conversationBo);

    AjaxResult getConversationStatistic(ConversationBo conversationBo);

    /**
     * 删除业务json （智能ppt删除作品）
     *
     * @param conversationBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/11/25 14:44
     **/
    AjaxResult removeBusinessJson(ConversationBo conversationBo);

}