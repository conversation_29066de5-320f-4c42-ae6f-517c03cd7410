package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailBo;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailConditionBo;
import com.fh.ai.business.entity.dto.courseDetail.CourseDetailDto;
import com.fh.ai.business.entity.vo.courseDetail.CourseDetailVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 课程详情接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface ICourseDetailService extends IService<CourseDetailDto> {

    List<CourseDetailVo> getCourseDetailListByCondition(CourseDetailConditionBo condition);

	AjaxResult addCourseDetail(CourseDetailBo courseDetailBo);

	AjaxResult updateCourseDetail(CourseDetailBo courseDetailBo);

	Map<String, Object> getDetail(Long id);

}

