package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.course.CourseBo;
import com.fh.ai.business.entity.bo.course.CourseConditionBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.dto.course.CourseDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 课程接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface ICourseService extends IService<CourseDto> {

	Map<String, Object> getCourseListByCondition(CourseConditionBo condition);

	Map<String, Object> myList(ExamAnswerConditionBo condition);

	AjaxResult addOrEdit(CourseBo courseBo);

	AjaxResult addCourse(CourseBo courseBo);

	AjaxResult updateCourse(CourseBo courseBo);

	Map<String, Object> getDetail(Long id,Integer state);

	List<Map> getType();

	List<Map> statistics(CourseBo coursebo);

	Map amountStatistics(CourseBo coursebo);

	AjaxResult otherCourse(Long id);

}

