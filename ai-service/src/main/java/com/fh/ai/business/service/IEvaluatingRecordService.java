package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.evaluatingRecord.EvaluatingRecordBo;
import com.fh.ai.business.entity.bo.evaluatingRecord.EvaluatingRecordConditionBo;
import com.fh.ai.business.entity.dto.evaluatingRecord.EvaluatingRecordDto;
import com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 帮助中心接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface IEvaluatingRecordService extends IService<EvaluatingRecordDto> {

    /**
     * 查询测评记录列表
     *
     * @param condition
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2024/10/16 14:21
     **/
    Map<String, Object> getEvaluatingRecordListByCondition(EvaluatingRecordConditionBo condition);

    /**
     * 新增测评记录
     *
     * @param evaluatingRecordBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/16 14:21
     **/
    AjaxResult addEvaluatingRecord(EvaluatingRecordBo evaluatingRecordBo);

    /**
     * 更新测评记录
     *
     * @param evaluatingRecordBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/16 14:21
     **/
    AjaxResult updateEvaluatingRecord(EvaluatingRecordBo evaluatingRecordBo);

    /**
     * 根据taskUid获取测评记录信息
     *
     * @param taskUid
     * @return com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo
     * <AUTHOR>
     * @date 2024/10/16 14:22
     **/
    EvaluatingRecordVo getByTaskUid(String taskUid);

    /**
     * 语音转文本
     *
     * @param taskUid
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/16 14:22
     **/
    String audioToText(String taskUid);



}

