package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDto;
import com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 题库表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface IExamAnswerService extends IService<ExamAnswerDto> {

	Map<String, Object> getExamAnswerListByCondition(ExamAnswerConditionBo condition);

	AjaxResult addExamAnswer(ExamAnswerBo examAnswerBo);

	AjaxResult updateExamAnswer(ExamAnswerBo examAnswerBo);

	Map<String, Object> getDetail(Long id);

}

