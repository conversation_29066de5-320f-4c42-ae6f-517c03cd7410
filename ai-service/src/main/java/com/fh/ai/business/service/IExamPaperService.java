package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.course.CourseBo;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperBo;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperConditionBo;
import com.fh.ai.business.entity.dto.examPaper.ExamPaperDto;
import com.fh.ai.business.entity.vo.examPaper.ExamPaperVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;
import java.util.Map;

/**
 * 题库表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface IExamPaperService extends IService<ExamPaperDto> {

	Map<String, Object> getExamPaperListByCondition(ExamPaperConditionBo condition);

	AjaxResult addExamPaper(ExamPaperBo examPaperBo);

	AjaxResult updateExamPaper(ExamPaperBo examPaperBo);

	Map<String, Object> getDetail(Long id);

	Map<String, Object> getDetailNoRight(Long id,String oid);

	Map<String, Object> answerDetail(Long examPaperId,Long courseDetailId,String oid);

	AjaxResult statistics(ExamPaperBo examPaperBo);

	List<Map> orgNumStatistics(ExamPaperBo examPaperBo);

}

