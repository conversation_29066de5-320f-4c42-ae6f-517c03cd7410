package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailBo;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailConditionBo;
import com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-14  14:28
 */
public interface IExcellentWorksDetailService extends IService<ExcellentWorksDetailDto> {

    AjaxResult addExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo);

    AjaxResult updateExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo);

    Map<String, Object> getExcellentWorksDetailListByCondition(ExcellentWorksDetailConditionBo conditionBo);

    AjaxResult deleteExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo);

}
