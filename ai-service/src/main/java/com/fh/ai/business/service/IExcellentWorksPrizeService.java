package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeBo;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeConditionBo;
import com.fh.ai.business.entity.dto.excellentWorksPrize.ExcellentWorksPrizeDto;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 用户作品获奖记录（和奖品表没关系）接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-21 17:59:13
 */
public interface IExcellentWorksPrizeService extends IService<ExcellentWorksPrizeDto> {

    List<ExcellentWorksPrizeVo> getExcellentWorksPrizeListByCondition(ExcellentWorksPrizeBo condition);

	AjaxResult addExcellentWorksPrize(ExcellentWorksPrizeBo excellentWorksPrizeBo);

	AjaxResult updateExcellentWorksPrize(ExcellentWorksPrizeBo excellentWorksPrizeBo);

	ExcellentWorksPrizeVo getExcellentWorksPrizeByCondition(ExcellentWorksPrizeConditionBo condition);

}

