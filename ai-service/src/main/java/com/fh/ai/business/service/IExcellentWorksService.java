package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.excellentWorks.*;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksExcelVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksUserStatisticsVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:39
 */
public interface IExcellentWorksService extends IService<ExcellentWorksDto> {

    AjaxResult addExcellentWorks(ExcellentWorksAddOrUpdateBo excellentWorksAddOrUpdateBo);

    AjaxResult updateExcellentWorks(ExcellentWorksAddOrUpdateBo excellentWorksAddOrUpdateBo);

    Map<String, Object> getExcellentWorksListByCondition(ExcellentWorksConditionBo conditionBo);

    AjaxResult deleteExcellentWorks(ExcellentWorksBo excellentWorksBo);

    AjaxResult deleteExcellentWorks(ExcellentWorksDetailBo excellentWorksDetail);

    AjaxResult releaseCancelExcellentWorks(ExcellentWorksBo excellentWorksBo);

    /**
     * 获取优秀作品列表
     *
     * @param excellentWorksBo 优秀作品页面请求对象，包含分页信息和查询条件
     * @return 返回一个包含优秀作品列表和总数的映射对象
     */
    Map<String, Object> getExcellentWorksListPage(ExcellentWorksConditionBo excellentWorksBo);

    /**
     * 获取优秀作品列表
     *
     * @param excellentWorksBo 优秀作品页面请求对象，包含分页信息和查询条件
     * @return 返回一个包含优秀作品列表和总数的映射对象
     */
    List<ExcellentWorksVo> getExcellentWorksList(ExcellentWorksConditionBo excellentWorksBo);

    /**
     * 获取优秀作品导出列表
     *
     * @param excellentWorksBo 优秀作品页面请求对象，包含分页信息和查询条件
     * @return 返回一个包含优秀作品列表和总数的映射对象
     */
    List<ExcellentWorksExcelVo> getExcellentWorksExcelList(ExcellentWorksConditionBo excellentWorksBo);

    /**
     * 根据ID获取优秀作品的详细信息
     *
     * @param id 优秀作品的ID，用于查询特定作品的详细信息
     * @return 返回一个包含优秀作品详细信息的ExcellentWorksVo对象
     */
    ExcellentWorksVo getExcellentWorksDetails(Long id, Boolean queryVoteAndRank);

    AjaxResult updateSort(ExcellentWorksBo excellentWorksBo);

    AjaxResult checkUserAdd(ExcellentWorksBo excellentWorksBo);

    /**
     * 用户统计
     * <p>
     * 传入参数: @param excellentWorksUserStatisticsConditionBo 优秀作品 用户统计 状况 bo
     * 返回值: @return {@link Map }<{@link String }, {@link Object }>
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */

    List<ExcellentWorksUserStatisticsVo> userStatistics(ExcellentWorksUserStatisticsConditionBo excellentWorksUserStatisticsConditionBo);

    /**
     * 发布校验
     * <p>
     * 传入参数: @param userOid 用户 oid
     * 传入参数: @param worksActiveId 优秀作品 活动 id
     * 返回值: @return {@link AjaxResult }
     */
    AjaxResult releaseCheck(String userOid, Long worksActiveId);
}
