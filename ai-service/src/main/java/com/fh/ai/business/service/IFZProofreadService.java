package com.fh.ai.business.service;

import com.fh.ai.common.proofreading.fz.req.FZSetting;
import com.fh.ai.common.vo.AjaxResult;

/**
 * @Classname IFZProofreadService
 * @Description 方正审校接口
 * @Date 2025/1/17 15:14
 * @Created by admin
 */
public interface IFZProofreadService {

    /***
     * 方正在线同步审校
     * @param fzSetting 选项
     * @return
     */
    AjaxResult createFZOnlineSyncTask(FZSetting fzSetting);

    /**
     * 方正在线内容审校，目前主要是重要讲话审校。
     * @param fzSetting
     * @return
     */
    AjaxResult createFZContentSyncTask(FZSetting fzSetting);

    /***
     * 方正文档审校
     * @param fzSetting
     * @return
     */
    AjaxResult createFZFileTask(FZSetting fzSetting);

}
