package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.festival.FestivalBo;
import com.fh.ai.business.entity.bo.festival.FestivalConditionBo;
import com.fh.ai.business.entity.dto.festival.FestivalDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 节假日表接口
 *
 * <AUTHOR>
 * @date 2024-07-02 14:28:17
 */
public interface IFestivalService extends IService<FestivalDto> {

    Map<String, Object> getFestivalListByCondition(FestivalConditionBo conditionBo);

	AjaxResult addFestival(FestivalBo festivalBo);

	AjaxResult updateFestival(FestivalBo festivalBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteFestival(FestivalBo festivalBo);

}