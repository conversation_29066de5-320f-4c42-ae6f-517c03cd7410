package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.conversationFile.FileContentCacheBo;
import com.fh.ai.business.entity.bo.conversationFile.FileContentCacheConditionBo;
import com.fh.ai.business.entity.dto.conversationFile.FileContentCacheDto;
import com.fh.ai.business.entity.vo.conversationFile.FileContentCacheVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 文件内容缓存表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-27 14:52:45
 */
public interface IFileContentCacheService extends IService<FileContentCacheDto> {

    List<FileContentCacheVo> getFileContentCacheListByCondition(FileContentCacheConditionBo condition);

	AjaxResult addFileContentCache(FileContentCacheBo fileContentCacheBo);

	AjaxResult updateFileContentCache(FileContentCacheBo fileContentCacheBo);

	FileContentCacheVo getFileContentCacheByCondition(FileContentCacheConditionBo condition);

	/**
	 * 根据条件查询返回map<主键，对象>
	 * @param ids 查询条件
	 * @return Map<Long,FileContentCacheVo>
	 */
	Map<Long, String> getFileContentCacheMapByIds(List<Long> ids);

	/**
	 * 根据主键集合查询返回map<主键，对象>-优先从redis缓存查询，查询不到则从数据库查询（后并放入缓存）
	 * @param ids 主键集合
	 * @return Map<Long,FileContentCacheVo>
	 */
	Map<Long, String> getFileContentCacheMapByIdsWithRedisCache(List<Long> ids);

	/**
	 * 新增文件内容缓存表并返回主键
	 *
	 * @param fileOid the file oid
	 * @param taskId the task id
	 * @param taskResult the task result
	 * @return long
	 * <AUTHOR>
	 * @date 2025 -02-27 15:21:17
	 */
	Long addFileContentCacheReturnId(String fileOid, String taskId, String taskResult);
}

