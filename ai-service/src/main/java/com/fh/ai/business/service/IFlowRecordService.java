package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.flowRecord.FlowRecordBo;
import com.fh.ai.business.entity.bo.flowRecord.FlowRecordConditionBo;
import com.fh.ai.business.entity.dto.flowRecord.FlowRecordDto;
import com.fh.ai.business.entity.vo.flowRecord.FlowRecordVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  10:53
 */
public interface IFlowRecordService extends IService<FlowRecordDto> {

    AjaxResult addFlowRecord(FlowRecordBo flowRecordBo);

    AjaxResult updateFlowRecord(FlowRecordBo flowRecordBo);

    Map<String, Object> getFlowRecordList(FlowRecordConditionBo conditionBo);
}
