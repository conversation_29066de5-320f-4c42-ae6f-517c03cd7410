package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.fzBook.FzBookBo;
import com.fh.ai.business.entity.bo.fzBook.FzBookConditionBo;
import com.fh.ai.business.entity.dto.fzBook.FzBookDto;
import com.fh.ai.business.entity.vo.fzBook.FzBookVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 图书库表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:00:52
 */
public interface IFzBookService extends IService<FzBookDto> {

    List<FzBookVo> getFzBookListByCondition(FzBookConditionBo condition);

	AjaxResult addFzBook(FzBookBo fzBookBo);

	AjaxResult updateFzBook(FzBookBo fzBookBo);

	FzBookVo getFzBookByCondition(FzBookConditionBo condition);

	void fzBookSync(String startTime, String endTime);

}

