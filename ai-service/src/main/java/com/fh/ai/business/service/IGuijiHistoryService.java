package com.fh.ai.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.guiji.GuijiHistoryBo;
import com.fh.ai.business.entity.bo.guiji.GuijiHistoryConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiHistoryDto;
import com.fh.ai.business.entity.vo.guiji.GuijiHistoryVo;
import com.fh.ai.common.guiji.GuijiHumanVideoBo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 硅基数智人生成记录表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface IGuijiHistoryService extends IService<GuijiHistoryDto> {

    List<GuijiHistoryVo> getGuijiHistoryListByCondition(GuijiHistoryConditionBo condition);

	AjaxResult addGuijiHistory(GuijiHistoryBo guijiHistoryBo);

	AjaxResult updateGuijiHistory(GuijiHistoryBo guijiHistoryBo);

	GuijiHistoryVo getGuijiHistoryByCondition(GuijiHistoryConditionBo condition);

	void addCreateImageMetaHumanVideoHistoryApp(GuijiHumanVideoBo guijiHumanVideoBo);

}

