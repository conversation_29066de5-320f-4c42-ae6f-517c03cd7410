package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.guiji.GuijiTimbreBo;
import com.fh.ai.business.entity.bo.guiji.GuijiTimbreConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiTimbreDto;
import com.fh.ai.business.entity.vo.guiji.GuijiTimbreVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 硅基数智人音色表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface IGuijiTimbreService extends IService<GuijiTimbreDto> {

    List<GuijiTimbreVo> getGuijiTimbreListByCondition(GuijiTimbreConditionBo condition);

	AjaxResult addGuijiTimbre(GuijiTimbreBo guijiTimbreBo);

	AjaxResult updateGuijiTimbre(GuijiTimbreBo guijiTimbreBo);

	GuijiTimbreVo getGuijiTimbreByCondition(GuijiTimbreConditionBo condition);

	/**
	 * 根据条件获取音色map<音色id,音色音频地址>
	 *
	 * @param condition the condition
	 * @return zego timbre map by condition
	 */
	Map<String,String> getGuijiTimbreMapByCondition(GuijiTimbreConditionBo condition);
}

