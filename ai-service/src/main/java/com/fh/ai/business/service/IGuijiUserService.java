package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.guiji.GuijiUserBo;
import com.fh.ai.business.entity.bo.guiji.GuijiUserConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiUserDto;
import com.fh.ai.business.entity.vo.guiji.GuijiUserVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 硅基数智人用户使用表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
public interface IGuijiUserService extends IService<GuijiUserDto> {

    List<GuijiUserVo> getGuijiUserListByCondition(GuijiUserConditionBo condition);

	/**
	 * 查询zego和guji所有的数据
	 *
	 * @param condition the condition
	 * @return zego guiji user list by condition
	 */
	List<GuijiUserVo> getZegoGuijiUserListByCondition(GuijiUserConditionBo condition);

	AjaxResult addGuijiUser(GuijiUserBo guijiUserBo);

	AjaxResult updateGuijiUser(GuijiUserBo guijiUserBo);

	GuijiUserVo getGuijiUserByCondition(GuijiUserConditionBo condition);

	Map<String, Object> getDetail(Long id);

	/**
	 * 定时任务5分钟一次，查询异步结果然后更新到数据库
	 */
	void performTask();
}

