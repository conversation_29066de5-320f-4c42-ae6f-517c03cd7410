package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.help.HelpBo;
import com.fh.ai.business.entity.bo.help.HelpConditionBo;
import com.fh.ai.business.entity.dto.help.HelpDto;
import com.fh.ai.business.entity.vo.help.HelpVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 帮助中心接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface IHelpService extends IService<HelpDto> {

	Map<String, Object> getHelpListByCondition(HelpConditionBo condition);

	AjaxResult addHelp(HelpBo helpBo);

	AjaxResult updateHelp(HelpBo helpBo);

	Map<String, Object> getDetail(Long id);

}

