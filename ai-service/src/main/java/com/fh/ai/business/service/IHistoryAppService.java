package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zego.MetaHumanVideoVo;
import com.fh.ai.common.zhipuai.ContextBo;

import java.util.List;
import java.util.Map;

/**
 * 应用历史表接口
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
public interface IHistoryAppService extends IService<HistoryAppDto> {

    Map<String, Object> getHistoryAppListByCondition(HistoryAppConditionBo conditionBo);

    AjaxResult addHistoryApp(HistoryAppBo historyAppBo);

    AjaxResult updateHistoryApp(HistoryAppBo historyAppBo);

    AjaxResult getDetail(Long id);

    HistoryAppVo getById(Long id);

    AjaxResult deleteHistoryApp(HistoryAppBo historyAppBo);

    List<UsageStatisticsTotalVo> getActualTimeTop10( HistoryAppConditionBo condition );

    HistoryAppVo getMeetingConvertText(HistoryAppConditionBo condition);

    String getMeetingConvertText(String result);

    String getMeetingConvertRichText(String result);

    List<HistoryAppVo> getMeetingAssistantList(HistoryAppConditionBo condition);

    List<ContextBo> getConversation(String conversationCode, Integer type, Integer num);

    AjaxResult saveMarketHistory(HistoryAppBo historyAppBo);

    AjaxResult removeBusinessJson(HistoryAppBo historyAppBo);

    AjaxResult useful(HistoryAppBo historyAppBo);

    /**
     * 查询状态为Running的Coze工作流记录
     * @return 符合条件的记录列表
     */
    List<HistoryAppVo> getRunningCozeWorkflowRecords();

    /**
     * 批量更新Coze工作流执行结果
     * 查询所有状态为Running的记录，调用Coze API获取最新结果并更新数据库
     * @return 处理结果统计信息
     */
    Map<String, Integer> batchUpdateCozeWorkflowResults();

    /**
     * 处理单个Coze工作流执行结果
     * @param historyAppVo HistoryApp记录
     * @param updateBy 更新人
     * @return 处理结果
     */
    AjaxResult processCozeWorkflowResult(HistoryAppVo historyAppVo, String updateBy);
}