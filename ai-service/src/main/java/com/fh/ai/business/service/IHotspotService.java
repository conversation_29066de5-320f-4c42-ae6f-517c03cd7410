package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.hotspot.HotspotBo;
import com.fh.ai.business.entity.bo.hotspot.HotspotConditionBo;
import com.fh.ai.business.entity.dto.hotspot.HotspotDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 热点表接口
 *
 * <AUTHOR>
 * @date 2024-07-02 15:15:14
 */
public interface IHotspotService extends IService<HotspotDto> {

    Map<String, Object> getHotspotListByCondition(HotspotConditionBo conditionBo);

	AjaxResult addHotspot(HotspotBo hotspotBo);

	AjaxResult updateHotspot(HotspotBo hotspotBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteHotspot(HotspotBo hotspotBo);

    void updateData();
}