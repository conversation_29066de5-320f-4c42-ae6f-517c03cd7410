package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.calendarEvent.MarketingNodeBo;
import com.fh.ai.business.entity.bo.calendarEvent.MarketingNodeConditionBo;
import com.fh.ai.business.entity.bo.hotspot.HotspotBo;
import com.fh.ai.business.entity.dto.calendarEvent.MarketingNodeDto;
import com.fh.ai.business.entity.vo.calendarEvent.MarketingNodeVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 营销节点接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-09 11:42:41
 */
public interface IMarketingNodeService extends IService<MarketingNodeDto> {

	Map<String, Object> getMarketingNodeListByCondition(MarketingNodeConditionBo condition);

	AjaxResult getDetail(Long id);

	AjaxResult addMarketingNode(MarketingNodeBo marketingNodeBo);

	AjaxResult updateMarketingNode(MarketingNodeBo marketingNodeBo);

	AjaxResult deleteMarketingNode(Long id,String oid);

	MarketingNodeVo getMarketingNodeByCondition(MarketingNodeConditionBo condition);

}

