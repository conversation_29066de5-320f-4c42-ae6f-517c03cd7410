package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.material.MaterialBo;
import com.fh.ai.business.entity.bo.material.MaterialConditionBo;
import com.fh.ai.business.entity.dto.material.MaterialDto;
import com.fh.ai.business.entity.vo.material.MaterialVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;
import java.util.Map;

/**
 * 用户上传素材表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-07 11:15:45
 */
public interface IMaterialService extends IService<MaterialDto> {

	Map<String, Object> getMaterialListByCondition(MaterialConditionBo condition);

	AjaxResult addMaterial(MaterialBo materialBo);

	AjaxResult updateMaterial(MaterialBo materialBo);

	MaterialVo getMaterialByCondition(MaterialConditionBo condition);

	AjaxResult deleteMaterial(Long id);

}

