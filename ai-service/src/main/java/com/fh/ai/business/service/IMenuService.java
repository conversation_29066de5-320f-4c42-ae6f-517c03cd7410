package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.menu.MenuBo;
import com.fh.ai.business.entity.bo.menu.MenuConditionBo;
import com.fh.ai.business.entity.dto.menu.MenuDto;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 接口
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
public interface IMenuService extends IService<MenuDto> {

    Map<String, Object> getMenuListByCondition(MenuConditionBo condition);

    List<MenuVo> getMenuListByRole(Long roleId);

    AjaxResult addMenu(MenuBo menuBo);

    AjaxResult updateMenu(MenuBo menuBo);

    AjaxResult getDetail(Long id);

}

