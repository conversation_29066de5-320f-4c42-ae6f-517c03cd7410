package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryBo;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryConditionBo;
import com.fh.ai.business.entity.dto.modelUseHistory.ModelUseHistoryDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:36
 */
public interface IModelUseHistoryService extends IService<ModelUseHistoryDto> {

    AjaxResult addModelUseHistory(ModelUseHistoryBo modelUseHistoryBo);

    AjaxResult updateModelUseHistory(ModelUseHistoryBo modelUseHistoryBo);

    AjaxResult getDetail(Long id);

    Map<String, Object> getModelUseHistoryListByCondition(ModelUseHistoryConditionBo conditionBo);

}
