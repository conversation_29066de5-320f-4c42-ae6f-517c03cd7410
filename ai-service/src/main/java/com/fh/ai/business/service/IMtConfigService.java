package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.mtConfig.MtConfigBo;
import com.fh.ai.business.entity.bo.mtConfig.MtConfigConditionBo;
import com.fh.ai.business.entity.dto.mtConfig.MtConfigDto;
import com.fh.ai.business.entity.vo.mtConfig.MtCategoryVo;
import com.fh.ai.common.meitu.vo.ConfigData;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 美图模型配置表接口
 *
 * <AUTHOR>
 * @date 2024-08-13 16:06:29
 */
public interface IMtConfigService extends IService<MtConfigDto> {

    Map<String, Object> getMtConfigListByCondition(MtConfigConditionBo conditionBo);

    AjaxResult addMtConfig(MtConfigBo mtConfigBo);

    AjaxResult updateMtConfig(MtConfigBo mtConfigBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteMtConfig(MtConfigBo mtConfigBo);

    void saveMtConfigDate(ConfigData configData);

    List<MtCategoryVo> getMtCategoryList();
}