package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryBo;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryConditionBo;
import com.fh.ai.business.entity.dto.mtHistory.MtHistoryDto;
import com.fh.ai.business.entity.vo.mtHistory.MtFileVo;
import com.fh.ai.business.entity.vo.mtUser.MtUserVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 美图生成记录表接口
 *
 * <AUTHOR>
 * @date 2024-08-16 09:35:50
 */
public interface IMtHistoryService extends IService<MtHistoryDto> {

    Map<String, Object> getMtHistoryListByCondition(MtHistoryConditionBo conditionBo);

    AjaxResult addMtHistory(MtHistoryBo mtHistoryBo);

    AjaxResult updateMtHistory(MtHistoryBo mtHistoryBo);

    MtUserVo saveMtUseInfo(MtHistoryBo mtHistoryBo);
    void updateInfo(Long id,MtHistoryBo mtHistoryBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(MtHistoryBo mtHistoryBo);

    AjaxResult deleteMtHistory(MtHistoryBo mtHistoryBo);

    AjaxResult getByTaskId(String taskId);

    void getMtCreateResult();

    void performTaskError();

    List<MtFileVo> dealPic(String parameterJson, List<String> urls, String userOid);

    List<MtFileVo> dealLiblibPic(String parameterJson, List<String> urls, String userOid);
}