package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.mtUserFavorite.MtUserFavoriteBo;
import com.fh.ai.business.entity.bo.mtUserFavorite.MtUserFavoriteConditionBo;
import com.fh.ai.business.entity.dto.mtUserFavorite.MtUserFavoriteDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 美图风格用户收藏表接口
 *
 * <AUTHOR>
 * @date 2024-08-16 09:50:56
 */
public interface IMtUserFavoriteService extends IService<MtUserFavoriteDto> {

    Map<String, Object> getMtUserFavoriteListByCondition(MtUserFavoriteConditionBo conditionBo);

	AjaxResult addMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo);

	AjaxResult updateMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo);

}