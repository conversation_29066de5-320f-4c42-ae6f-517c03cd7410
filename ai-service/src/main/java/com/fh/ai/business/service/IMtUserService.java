package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.mtUser.MtUserBo;
import com.fh.ai.business.entity.bo.mtUser.MtUserConditionBo;
import com.fh.ai.business.entity.dto.mtUser.MtUserDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 美图用户使用表接口
 *
 * <AUTHOR>
 * @date 2024-08-16 09:45:28
 */
public interface IMtUserService extends IService<MtUserDto> {

    Map<String, Object> getMtUserListByCondition(MtUserConditionBo conditionBo);

//    Integer countStatus(String oid,Integer type);

    AjaxResult addMtUser(MtUserBo mtUserBo);

    AjaxResult updateMtUser(MtUserBo mtUserBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteMtUser(MtUserBo mtUserBo);

    AjaxResult deleteOne(MtUserBo mtUserBo);

    AjaxResult getByTaskId(String taskId);

}