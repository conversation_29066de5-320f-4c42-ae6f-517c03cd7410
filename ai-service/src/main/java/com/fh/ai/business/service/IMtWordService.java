package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.mtWord.MtWordBo;
import com.fh.ai.business.entity.bo.mtWord.MtWordConditionBo;
import com.fh.ai.business.entity.dto.mtWord.MtWordDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 美图词库表接口
 *
 * <AUTHOR>
 * @date 2024-08-19 14:52:50
 */
public interface IMtWordService extends IService<MtWordDto> {

    Map<String, Object> getMtWordListByCondition(MtWordConditionBo conditionBo);

	AjaxResult addMtWord(MtWordBo mtWordBo);

	AjaxResult updateMtWord(MtWordBo mtWordBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteMtWord(MtWordBo mtWordBo);

}