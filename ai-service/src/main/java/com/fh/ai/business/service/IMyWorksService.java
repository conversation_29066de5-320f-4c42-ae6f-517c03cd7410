package com.fh.ai.business.service;

import com.fh.ai.business.entity.bo.myWorks.MyWorksBo;
import com.fh.ai.business.entity.bo.myWorks.MyWorksConditionBo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-26  14:05
 */
public interface IMyWorksService {

    /**
     * 我的创作
     *
     * @param conditionBo
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2024/11/25 13:52
     **/
    Map<String, Object> getMyWorksListByCondition(MyWorksConditionBo conditionBo);

    /**
     * 删除我的创作
     *
     * @param myWorksBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/11/25 13:53
     **/
    AjaxResult removeMyWorks(MyWorksBo myWorksBo);

    /**
     * 更新我的创作
     *
     * @param myWorksBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/11/26 16:26
     **/
    AjaxResult updateMyWorks(MyWorksBo myWorksBo);

}
