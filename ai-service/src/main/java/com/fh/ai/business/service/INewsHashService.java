package com.fh.ai.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.publishNews.NewsHashBo;
import com.fh.ai.business.entity.bo.publishNews.NewsHashConditionBo;
import com.fh.ai.business.entity.dto.publishNews.NewsHashDto;
import com.fh.ai.business.entity.vo.publishNews.NewsHashVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 热点新闻接口调用地址表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-23 15:10:51
 */
public interface INewsHashService extends IService<NewsHashDto> {

    List<NewsHashVo> getNewsHashListByCondition(NewsHashConditionBo condition);

	AjaxResult addNewsHash(NewsHashBo newsHashBo);

	AjaxResult updateNewsHash(NewsHashBo newsHashBo);

	NewsHashVo getNewsHashByCondition(NewsHashConditionBo condition);

}

