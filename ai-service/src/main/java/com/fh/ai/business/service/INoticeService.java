package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.notice.NoticeBo;
import com.fh.ai.business.entity.bo.notice.NoticeConditionBo;
import com.fh.ai.business.entity.dto.notice.NoticeDto;
import com.fh.ai.business.entity.vo.notice.NoticeVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;
import java.util.Map;

/**
 * 消息通知接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface INoticeService extends IService<NoticeDto> {

	Map<String, Object> getNoticeListByCondition(NoticeConditionBo condition);

	AjaxResult addNotice(NoticeBo noticeBo);

	AjaxResult updateNotice(NoticeBo noticeBo);

	AjaxResult readNotice(NoticeBo noticeBo);

	AjaxResult readAllNotice(NoticeBo noticeBo);

	Map<String, Object> getDetail(Long id);

}

