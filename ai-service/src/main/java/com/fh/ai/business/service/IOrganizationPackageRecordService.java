package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.organizationPackageRecord.OrganizationPackageRecordBo;
import com.fh.ai.business.entity.bo.organizationPackageRecord.OrganizationPackageRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationPackageRecord.OrganizationPackageRecordDto;
import com.fh.ai.business.entity.vo.organizationPackageRecord.OrganizationPackageRecordVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 企业套餐开通记录接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:38
 */
public interface IOrganizationPackageRecordService extends IService<OrganizationPackageRecordDto> {

    List<OrganizationPackageRecordVo> getPOrganizationPackageRecordListByCondition(OrganizationPackageRecordConditionBo condition);

	AjaxResult addPOrganizationPackageRecord(OrganizationPackageRecordBo organizationPackageRecordBo);

	AjaxResult updatePOrganizationPackageRecord(OrganizationPackageRecordBo organizationPackageRecordBo);

	OrganizationPackageRecordVo getPOrganizationPackageRecordByCondition(OrganizationPackageRecordConditionBo condition);

}

