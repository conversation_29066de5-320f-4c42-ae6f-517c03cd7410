package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageBo;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageConditionBo;
import com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 企业套餐接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
public interface IOrganizationPackageService extends IService<OrganizationPackageDto> {

    List<OrganizationPackageVo> getPOrganizationPackageListByCondition(OrganizationPackageConditionBo condition);

	AjaxResult addPOrganizationPackage(OrganizationPackageBo organizationPackageBo);

	AjaxResult updatePOrganizationPackage(OrganizationPackageBo organizationPackageBo);

	OrganizationPackageVo getPOrganizationPackageByCondition(OrganizationPackageConditionBo condition);

	/**
	 * 获取一个机构的用量（为了兼容生产saas测试数据，这里也会追寻到顶级组织。）
	 *
	 * @param condition the condition
	 * @return organization quota by condition
	 */
	Long getOrganizationQuotaByCondition(Long organizationId);
}

