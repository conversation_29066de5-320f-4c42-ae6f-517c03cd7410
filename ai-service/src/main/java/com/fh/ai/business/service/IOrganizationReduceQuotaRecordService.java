package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordBo;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordDto;
import com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  13:55
 */
public interface IOrganizationReduceQuotaRecordService extends IService<OrganizationReduceQuotaRecordDto> {
    AjaxResult addOrganizationReduceQuotaRecord(OrganizationReduceQuotaRecordBo organizationReduceQuotaRecordBo);

    List<OrganizationReduceQuotaRecordVo> getOrganizationReduceQuotaRecordListByCondition(OrganizationReduceQuotaRecordConditionBo conditionBo);
}
