package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.organization.OrganizationBo;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationAccumulateUsageStatisticVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 组织机构表接口
 *
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
public interface IOrganizationService extends IService<OrganizationDto> {

    Map<String, Object> getOrganizationListByCondition(OrganizationConditionBo conditionBo);

    List<OrganizationVo> getStatisticsOrganizationList();

    OrganizationDto analysisDepOrganization(String depPath, String createOid);

    Map<String, OrganizationDto> analysisAllOrganization(String depName, String createOid);

    List<OrgTreeNodeVo> getOrganizationTree(List<Long> orgIds);

    OrgTreeNodeVo getSubOrganizationTree(Long orgId);

    OrgTreeNodeVo getSubOrganizationTreeWithAdmin(Long orgId);

    List<OrgTreeNodeVo> getFamilyOrganizationTree(Long bottomOrgId, Long topOrgId);

    String findOrgNamePath(List<OrgTreeNodeVo> orgVos, Long orgId);

    List<OrgTreeNodeVo> getSubOrgNodes(List<OrgTreeNodeVo> orgVos, Long id);

    AjaxResult addOrganization(OrganizationBo organizationBo);

    AjaxResult updateOrganization(OrganizationBo organizationBo);

    AjaxResult getDetail(Long id);

    /**
     * 获取组织机构信息
     * @param condition
     * @return
     */
    OrganizationVo getOrganizationByCondition(OrganizationConditionBo condition);

    AjaxResult updateState(OrganizationBo organizationBo);

    AjaxResult deleteOrganization(OrganizationBo organizationBo);

    AjaxResult organizationDetailWithPackage(Long id);

    /**
     * 获取组织列表分页（包含套餐信息）
     *
     * @param conditionBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/29 9:35
     **/
    Map<String, Object> organizationListWithPackage(OrganizationConditionBo conditionBo);

    /**
     * 查询组织使用消耗统计
     *
     * @param
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/29 9:24
     **/
    OrganizationAccumulateUsageStatisticVo getOrganizationAccumulateUsageStatistic();

    /**
     * 企业配额扣减
     *
     * @param organizationReduceQuotaBo
     * @return boolean
     * <AUTHOR>
     * @date 2024/10/29 15:00
     **/
    boolean reduceQuota(OrganizationReduceQuotaBo organizationReduceQuotaBo);

    /**
     * 校验企业是否欠费 true:未欠费 false:已欠费
     *
     * @param organizationId
     * @return
     */
    boolean checkOrganizationQuota(Long organizationId);

    /**
     *
     * @param orgVos
     * @param orgId
     * @return
     */
    OrgTreeNodeVo findStatisticOrg(List<OrgTreeNodeVo> orgVos, List<Long> statisticOrgIds, Long orgId);

}