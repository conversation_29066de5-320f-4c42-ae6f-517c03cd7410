package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.organizationUsageStatistic.OrganizationUsageStatisticConditionBo;
import com.fh.ai.business.entity.dto.organizationUsageStatistic.OrganizationUsageStatisticDto;
import com.fh.ai.business.entity.vo.organizationUsageStatistic.OrganizationUsageStatisticVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-30  16:39
 */
public interface IOrganizationUsageStatisticService extends IService<OrganizationUsageStatisticDto> {

    Map<String, Object> getOrganizationUsageStatisticListByCondition(OrganizationUsageStatisticConditionBo condition);

    void organizationUsageStatisticTask(Date statisticDay, List<Long> organizationIds);
}
