package com.fh.ai.business.service;

import com.fh.ai.common.proofreading.fh.req.PPMSetting;
import com.fh.ai.common.vo.AjaxResult;

/**
 * @Classname IPPMProofreadService
 * @Description 凤凰审校接口
 * @Date 2025/1/17 15:14
 * @Created by admin
 */
public interface IPPMProofreadService {


    /***
     * 凤凰在线同步审校
     * @param ppmSetting 选项
     * @return
     */
    AjaxResult createPPMOnlineSyncTask(PPMSetting ppmSetting);

    /***
     * 凤凰文档审校
     * @param ppmSetting 选项
     * @return
     */
    AjaxResult createPPMFileTask(PPMSetting ppmSetting);


    AjaxResult flashToken();


}
