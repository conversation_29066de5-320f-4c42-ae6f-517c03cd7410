package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.packageInfo.PackageInfoBo;
import com.fh.ai.business.entity.bo.packageInfo.PackageInfoConditionBo;
import com.fh.ai.business.entity.dto.packageInfo.PackageInfoDto;
import com.fh.ai.business.entity.vo.packageInfo.PackageInfoVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;

/**
 * 套餐表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:13
 */
public interface IPackageInfoService extends IService<PackageInfoDto> {

    List<PackageInfoVo> getPPackageInfoListByCondition(PackageInfoConditionBo condition);

	AjaxResult addPPackageInfo(PackageInfoBo packageInfoBo);

	AjaxResult updatePPackageInfo(PackageInfoBo packageInfoBo);

	PackageInfoVo getPPackageInfoByCondition(PackageInfoConditionBo condition);

}

