package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.personalFile.PersonalFileBo;
import com.fh.ai.business.entity.bo.personalFile.PersonalFileConditionBo;
import com.fh.ai.business.entity.dto.personalFile.PersonalFileDto;
import com.fh.ai.business.entity.vo.personalFile.PersonalFileVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 用户上传的个人文件表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14 10:00:00
 */
public interface IPersonalFileService extends IService<PersonalFileDto> {

	/**
	 * 列表的分页和不分页查询都支持-根据page或者limit是否为空判断
	 *
	 * @param conditionBo the condition bo
	 * @return personal file list by condition and page
	 */
	Map<String, Object> getPersonalFileListByConditionAndPage(PersonalFileConditionBo conditionBo);

    List<PersonalFileVo> getPersonalFileListByCondition(PersonalFileConditionBo condition);

    PersonalFileVo getPersonalFileByCondition(PersonalFileConditionBo condition);

    /**
     * 新增个人文件
     *
     * @param personalFileBo the personal file bo
     * @return ajax result
     */
    AjaxResult addPersonalFile(PersonalFileBo personalFileBo);

    /**
     * 修改个人文件
     *
     * @param personalFileBo the personal file bo
     * @return ajax result
     */
    AjaxResult updatePersonalFile(PersonalFileBo personalFileBo);

    /**
     * 删除个人文件
     *
     * @param id the id
     * @param userOid the user oid
     * @return ajax result
     */
    AjaxResult deletePersonalFile(Long id, String userOid);

    /**
     * 批量删除个人文件
     *
     * @param ids the ids
     * @param userOid the user oid
     * @return ajax result
     */
    AjaxResult batchDeletePersonalFile(List<Long> ids, String userOid);

}
