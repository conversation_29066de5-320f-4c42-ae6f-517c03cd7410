package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.pictureDownloadHistory.PictureDownloadHistoryBo;
import com.fh.ai.business.entity.dto.pictureDownloadHistory.PictureDownloadHistoryDto;
import com.fh.ai.common.vo.AjaxResult;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  09:49
 */
public interface IPictureDownloadHistoryService extends IService<PictureDownloadHistoryDto> {

    AjaxResult addPictureDownloadHistory(PictureDownloadHistoryBo pictureDownloadHistoryBo);

}
