package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.preInstruction.PreInstructionBo;
import com.fh.ai.business.entity.bo.preInstruction.PreInstructionConditionBo;
import com.fh.ai.business.entity.dto.preInstruction.PreInstructionDto;
import com.fh.ai.business.entity.vo.preInstruction.PreInstructionVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 预置指令接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-07 16:45:32
 */
public interface IPreInstructionService extends IService<PreInstructionDto> {

    List<PreInstructionVo> getPreInstructionListByCondition(PreInstructionConditionBo condition);

	AjaxResult addPreInstruction(PreInstructionBo preInstructionBo);

	AjaxResult updatePreInstruction(PreInstructionBo preInstructionBo);

	PreInstructionVo getPreInstructionByCondition(PreInstructionConditionBo condition);

	Map<String, Object> getPreInstructListByConditionPage(PreInstructionConditionBo conditionBo);

	AjaxResult deletePreInstruct(PreInstructionBo preInstructionBo);
}

