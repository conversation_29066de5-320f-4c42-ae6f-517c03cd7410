package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.prizeRedeemcode.PrizeRedeemcodeBo;
import com.fh.ai.business.entity.bo.prizeRedeemcode.PrizeRedeemcodeConditionBo;
import com.fh.ai.business.entity.dto.prizeRedeemcode.PrizeRedeemcodeDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 奖品兑换码表接口
 *
 * <AUTHOR>
 * @date 2024-05-13 15:04:13
 */
public interface IPrizeRedeemcodeService extends IService<PrizeRedeemcodeDto> {

    Map<String, Object> getPrizeRedeemcodeListByCondition(PrizeRedeemcodeConditionBo conditionBo);

	AjaxResult addPrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo);

	AjaxResult updatePrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(PrizeRedeemcodeBo prizeRedeemcodeBo);

    AjaxResult deletePrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo);

}