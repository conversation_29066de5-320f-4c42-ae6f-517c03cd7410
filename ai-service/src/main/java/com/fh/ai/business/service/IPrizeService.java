package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.prize.PrizeBo;
import com.fh.ai.business.entity.bo.prize.PrizeConditionBo;
import com.fh.ai.business.entity.dto.prize.PrizeDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 奖品表接口
 *
 * <AUTHOR>
 * @date 2024-02-20 17:21:24
 */
public interface IPrizeService extends IService<PrizeDto> {

    Map<String, Object> getPrizeListByCondition(PrizeConditionBo conditionBo);

    AjaxResult addPrize(PrizeBo prizeBo);

    AjaxResult updatePrize(PrizeBo prizeBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(PrizeBo prizeBo);

    AjaxResult deletePrize(PrizeBo prizeBo);

    boolean reduceStock(Long id);

    void prizeSupplyRemind();

    AjaxResult code();
}