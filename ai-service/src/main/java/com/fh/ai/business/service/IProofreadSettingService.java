package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.proofreading.ProofreadSettingBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadSettingConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadSettingDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;

/**
 * 用户审校设置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-20 14:39:57
 */
public interface IProofreadSettingService extends IService<ProofreadSettingDto> {

    List<ProofreadSettingVo> getProofreadSettingListByCondition(ProofreadSettingConditionBo condition);

	AjaxResult addProofreadSetting(ProofreadSettingBo proofreadSettingBo);

	AjaxResult updateProofreadSetting(ProofreadSettingBo proofreadSettingBo);

	ProofreadSettingVo getProofreadSettingByCondition(ProofreadSettingConditionBo condition);

}

