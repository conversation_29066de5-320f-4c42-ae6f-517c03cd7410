package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadRetryBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingRecordDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageInfo;


import java.util.List;

/**
 * 用户审校记录接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:02:37
 */
public interface IProofreadingRecordService extends IService<ProofreadingRecordDto> {

	PageInfo<ProofreadingRecordVo> getProofreadingRecordListByCondition(ProofreadingRecordConditionBo condition);

	/**
	 * 获取审校记录详情
	 * @param recordId
	 * @return
	 */
	AjaxResult getRecordDetail(Long recordId);

	/**
	 * 创建审校记录
	 * @param proofreadingRecordBo
	 * @return
	 */
	AjaxResult addProofreadingRecord(ProofreadingRecordBo proofreadingRecordBo);

	/**
	 * 更改审校任务
	 * @param proofreadingRecordBo
	 * @return
	 */
	AjaxResult updateProofreadingRecord(ProofreadingRecordBo proofreadingRecordBo);

	ProofreadingRecordVo getProofreadingRecordByCondition(ProofreadingRecordConditionBo condition);

	/**
	 * 校验审校记录是否符合当前调用的接口。即：在线审校记录只能发起凤凰或者方正的在线审校接口。文档审校记录只能发起凤凰或者方正的文档审校接口。
	 * @param record 审校记录id
	 * @param type 1 凤凰在线审校，3凤凰文档审校 ，2 方正在线审校，4方正文档审校
	 * @return
	 */
	AjaxResult checkRecordLegal(ProofreadingRecordVo record,Integer type);

	/**
	 * 修改审校记录状态
	 */
	void changeRecordStateCronJob();

	/**
	 * 获取我的审校记录
	 * @param condition
	 * @return
	 */
	AjaxResult getMyProofreadRecord(ProofreadingRecordConditionBo condition);

	/**
	 * 获取审校结果SheetData对象
	 * @param recordId
	 * @return
	 */
	AjaxResult getProofreadSheetData(Long recordId);

	/**
	 * 检查是否需要下载excel
	 * @param recordId
	 * @return
	 */
	AjaxResult checkIsNeedDownloadExcel(Long recordId);

}

