package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import com.fh.ai.common.proofreading.fz.vo.FileResource;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 审校任务表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:03:26
 */
public interface IProofreadingTaskService extends IService<ProofreadingTaskDto> {

    List<ProofreadingTaskVo> getProofreadingTaskListByCondition(ProofreadingTaskConditionBo condition);

	AjaxResult addProofreadingTask(ProofreadingTaskBo proofreadingTaskBo);

	AjaxResult updateProofreadingTask(ProofreadingTaskBo proofreadingTaskBo);

	ProofreadingTaskVo getProofreadingTaskByCondition(ProofreadingTaskConditionBo condition);

	AjaxResult createProofreadingTask(ProofreadingTaskBo proofreadingTaskBo);

	/**
	 * 将某个任务记为失败，并记录返回信息
	 *
	 * @param id 主键
	 * @param responseStr 接口返回
	 * @param remark 错误详细信息
	 * @return
	 */
	AjaxResult failProofreadingTask(Long id,String responseStr,String remark);

	/**
	 * 将凤凰/方正的审校结果文件上传至服务器，并保存本地文件信息到审校任务中
	 * @param fileResources 文件资源信息，包括资源类型，url等
	 * @param updateTask 待更新的审校任务
	 * @param originalName 原始文件名称
	 * @param planName 方案名称，包括方案一（凤凰审校），方案二（方正审校）
	 * @return
	 */
	AjaxResult uploadFileProofreadingTask(List<FileResource> fileResources,ProofreadingTaskDto updateTask,String originalName,String planName);
}

