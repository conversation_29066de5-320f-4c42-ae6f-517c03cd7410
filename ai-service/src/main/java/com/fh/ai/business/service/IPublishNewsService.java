package com.fh.ai.business.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.common.tophub.GetHistoryParam;
import com.fh.ai.business.entity.bo.publishNews.PublishNewsBo;
import com.fh.ai.business.entity.bo.publishNews.PublishNewsConditionBo;
import com.fh.ai.business.entity.bo.publishNews.QueryPublishListBo;
import com.fh.ai.business.entity.dto.publishNews.PublishNewsDto;
import com.fh.ai.business.entity.vo.publishNews.NewsHashVo;
import com.fh.ai.business.entity.vo.publishNews.PublishNewsVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 出版资讯表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 17:23:42
 */
public interface IPublishNewsService extends IService<PublishNewsDto> {

    List<PublishNewsVo> getPublishNewsListByCondition(PublishNewsConditionBo condition);

	AjaxResult addPublishNews(PublishNewsBo publishNewsBo);

	AjaxResult updatePublishNews(PublishNewsBo publishNewsBo);

	AjaxResult getPublishNewsByCondition(Long id);

	Map<String,Object> queryNewsForList(QueryPublishListBo requestBo);

	/**
	 * 远程获取并保存资讯信息
	 * @param newsHashVo
	 * @param historyParam
	 */
	AjaxResult getAndSaveNewsRpc(NewsHashVo newsHashVo, GetHistoryParam historyParam,String topHubDataUrl,String token);

	/**
	 * 远程获取并保存资讯信息，重试时使用改方法传入参数集合
	 * @param paramMap  key 参数名，value 参数体。
	 * @return
	 */
	AjaxResult getAndSaveNewsRpc(Map<String,Object> paramMap);

}

