package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.question.QuestionBo;
import com.fh.ai.business.entity.bo.question.QuestionConditionBo;
import com.fh.ai.business.entity.dto.question.QuestionDto;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;
import java.util.Map;

/**
 * 题库表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface IQuestionService extends IService<QuestionDto> {

	Map<String, Object> getQuestionListByCondition(QuestionConditionBo condition);

	AjaxResult addQuestion(QuestionBo questionBo);

	AjaxResult updateQuestion(QuestionBo questionBo);

	Map<String, Object> getDetail(Long id);

}

