package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingDangdangBo;
import com.fh.ai.business.entity.bo.book.RankingDangdangConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDangdangDto;
import com.fh.ai.business.entity.vo.book.RankingDangdangVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 当当榜单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingDangdangService extends IService<RankingDangdangDto> {

    /**
     * 当当榜单列表的查询，支持分页，支持查询具体书籍信息
     *
     * @param condition the condition
     * @return ranking dangdang list by condition and page
     */
    Map<String, Object> getRankingDangdangListByConditionAndPage(RankingDangdangConditionBo condition);

    List<RankingDangdangVo> getRankingDangdangListByCondition(RankingDangdangConditionBo condition);

    AjaxResult addRankingDangdang(RankingDangdangBo rankingDangdangBo);

    AjaxResult updateRankingDangdang(RankingDangdangBo rankingDangdangBo);

    RankingDangdangVo getRankingDangdangByCondition(RankingDangdangConditionBo condition);

}
