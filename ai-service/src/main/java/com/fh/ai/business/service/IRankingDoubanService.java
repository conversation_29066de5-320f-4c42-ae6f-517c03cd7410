package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingDoubanBo;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDoubanDto;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 豆瓣榜单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingDoubanService extends IService<RankingDoubanDto> {

	/**
	 * 豆瓣榜单列表的查询，支持分页，支持查询具体书籍信息
	 *
	 * @param condition the condition
	 * @return ranking dangdang list by condition and page
	 */
	Map<String, Object> getRankingDoubanListByConditionAndPage(RankingDoubanConditionBo condition);

    List<RankingDoubanVo> getRankingDoubanListByCondition(RankingDoubanConditionBo condition);

	AjaxResult addRankingDouban(RankingDoubanBo rankingDoubanBo);

	AjaxResult updateRankingDouban(RankingDoubanBo rankingDoubanBo);

	RankingDoubanVo getRankingDoubanByCondition(RankingDoubanConditionBo condition);

}

