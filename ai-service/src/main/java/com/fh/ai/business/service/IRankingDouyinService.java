package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingDouyinBo;
import com.fh.ai.business.entity.bo.book.RankingDouyinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDouyinDto;
import com.fh.ai.business.entity.vo.book.RankingDouyinVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 抖音榜单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
public interface IRankingDouyinService extends IService<RankingDouyinDto> {

    List<RankingDouyinVo> getRankingDouyinListByCondition(RankingDouyinConditionBo condition);

	AjaxResult addRankingDouyin(RankingDouyinBo rankingDouyinBo);

	AjaxResult updateRankingDouyin(RankingDouyinBo rankingDouyinBo);

	RankingDouyinVo getRankingDouyinByCondition(RankingDouyinConditionBo condition);

	Map<String, Object> getRankingDouYinListByConditionAndPage(RankingDouyinConditionBo conditionBo);

}

