package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.bo.book.RankingJingdongBo;
import com.fh.ai.business.entity.bo.book.RankingJingdongConditionBo;
import com.fh.ai.business.entity.dto.book.RankingJingdongDto;
import com.fh.ai.business.entity.vo.book.RankingJingdongVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 京东榜单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
public interface IRankingJingdongService extends IService<RankingJingdongDto> {

	Map<String, Object> getRankingJingDongListByConditionAndPage(RankingJingdongConditionBo conditionBo);

    List<RankingJingdongVo> getRankingJingdongListByCondition(RankingJingdongConditionBo condition);

	AjaxResult addRankingJingdong(RankingJingdongBo rankingJingdongBo);

	AjaxResult updateRankingJingdong(RankingJingdongBo rankingJingdongBo);

	RankingJingdongVo getRankingJingdongByCondition(RankingJingdongConditionBo condition);

}

