package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingPpmbookBo;
import com.fh.ai.business.entity.bo.book.RankingPpmbookConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPpmbookDto;
import com.fh.ai.business.entity.vo.book.RankingPpmbookVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 凤凰书苑ppmbook榜单表（书苑）接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingPpmbookService extends IService<RankingPpmbookDto> {

	/**
	 * 书苑榜单列表的查询，支持分页，支持查询具体书籍信息
	 *
	 * @param condition the condition
	 * @return ranking dangdang list by condition and page
	 */
	Map<String, Object> getRankingPpmbookListByConditionAndPage(RankingPpmbookConditionBo condition);

    List<RankingPpmbookVo> getRankingPpmbookListByCondition(RankingPpmbookConditionBo condition);

	AjaxResult addRankingPpmbook(RankingPpmbookBo rankingPpmbookBo);

	AjaxResult updateRankingPpmbook(RankingPpmbookBo rankingPpmbookBo);

	RankingPpmbookVo getRankingPpmbookByCondition(RankingPpmbookConditionBo condition);

}

