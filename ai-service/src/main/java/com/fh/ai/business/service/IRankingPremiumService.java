package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingPremiumBo;
import com.fh.ai.business.entity.bo.book.RankingPremiumConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPremiumDto;
import com.fh.ai.business.entity.vo.book.RankingPremiumVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 精品书（奖项）表（不含凤凰好书）接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingPremiumService extends IService<RankingPremiumDto> {

    List<RankingPremiumVo> getRankingPremiumListByCondition(RankingPremiumConditionBo condition);

	AjaxResult addRankingPremium(RankingPremiumBo rankingPremiumBo);

	AjaxResult updateRankingPremium(RankingPremiumBo rankingPremiumBo);

	RankingPremiumVo getRankingPremiumByCondition(RankingPremiumConditionBo condition);

	/**
	 * 分页查询
	 * @param conditionBo
	 * @return
	 */
	Map<String, Object> getRankingPremiumListByConditionAndPage(RankingPremiumConditionBo conditionBo);
}

