package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingSmartBo;
import com.fh.ai.business.entity.bo.book.RankingSmartConditionBo;
import com.fh.ai.business.entity.dto.book.RankingSmartDto;
import com.fh.ai.business.entity.vo.book.RankingSmartVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 凤凰本版畅销书表-有销售数据（开卷）接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingSmartService extends IService<RankingSmartDto> {

    List<RankingSmartVo> getRankingSmartListByCondition(RankingSmartConditionBo condition);

	AjaxResult addRankingSmart(RankingSmartBo rankingSmartBo);

	AjaxResult updateRankingSmart(RankingSmartBo rankingSmartBo);

	RankingSmartVo getRankingSmartByCondition(RankingSmartConditionBo condition);

	/**
	 * 查询开卷榜单表分页列表
	 * @param conditionBo
	 * @return
	 */
	Map<String, Object>  getRankingSmartListByConditionAndPage(RankingSmartConditionBo conditionBo);

}

