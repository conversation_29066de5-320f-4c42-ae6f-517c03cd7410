package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.book.RankingWeixinBo;
import com.fh.ai.business.entity.bo.book.RankingWeixinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingWeixinDto;
import com.fh.ai.business.entity.vo.book.RankingWeixinVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 微信榜单表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
public interface IRankingWeixinService extends IService<RankingWeixinDto> {

	/**
	 * 微信榜单列表的查询，支持分页，支持查询具体书籍信息
	 *
	 * @param condition the condition
	 * @return ranking dangdang list by condition and page
	 */
	Map<String, Object> getRankingWeixinListByConditionAndPage(RankingWeixinConditionBo condition);

    List<RankingWeixinVo> getRankingWeixinListByCondition(RankingWeixinConditionBo condition);

	AjaxResult addRankingWeixin(RankingWeixinBo rankingWeixinBo);

	AjaxResult updateRankingWeixin(RankingWeixinBo rankingWeixinBo);

	RankingWeixinVo getRankingWeixinByCondition(RankingWeixinConditionBo condition);

}

