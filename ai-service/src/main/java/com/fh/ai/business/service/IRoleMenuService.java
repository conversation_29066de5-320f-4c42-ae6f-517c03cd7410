package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.roleMenu.RoleMenuBo;
import com.fh.ai.business.entity.bo.roleMenu.RoleMenuConditionBo;
import com.fh.ai.business.entity.dto.roleMenu.RoleMenuDto;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 角色菜单接口
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
public interface IRoleMenuService extends IService<RoleMenuDto> {

    Map<String, Object> getRoleMenuListByCondition(RoleMenuConditionBo condition);

    AjaxResult addRoleMenu(RoleMenuBo roleMenuBo);

    AjaxResult updateRoleMenu(RoleMenuBo roleMenuBo);

    AjaxResult getDetail(Long id);

    List<MenuVo> getRoleMenuVosByKey(Long roleId);

}

