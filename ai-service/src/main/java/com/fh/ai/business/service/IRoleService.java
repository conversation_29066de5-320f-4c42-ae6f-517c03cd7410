package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.role.RoleBo;
import com.fh.ai.business.entity.bo.role.RoleListConditionBo;
import com.fh.ai.business.entity.dto.role.RoleDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 接口
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
public interface IRoleService extends IService<RoleDto> {

    /**
     * 获取用户角色信息
     *
     * @param userId
     * @return
     */
    List<RoleDto> getRoleListByUserId(Integer userId);

    /**
     * 获取用户角色信息
     * @param roleId
     * @return
     */
    RoleDto getRoleByIdCache(Integer roleId);


    Map<String, Object> getRoleListByCondition(RoleListConditionBo condition);

    AjaxResult addRole(RoleBo roleBo);

    AjaxResult updateRole(RoleBo roleBo);

    AjaxResult updateState(RoleBo roleBo);

    AjaxResult deleteRole(RoleBo roleBo);

    AjaxResult getDetail(Integer id);
}

