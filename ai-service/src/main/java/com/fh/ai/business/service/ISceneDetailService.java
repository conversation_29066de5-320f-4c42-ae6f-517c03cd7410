package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.sceneDetail.SceneDetailBo;
import com.fh.ai.business.entity.bo.sceneDetail.SceneDetailConditionBo;
import com.fh.ai.business.entity.dto.sceneDetail.SceneDetailDto;
import com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * 场景详情表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:26
 */
public interface ISceneDetailService extends IService<SceneDetailDto> {

    List<SceneDetailVo> getSceneDetailListByCondition(SceneDetailConditionBo condition);

	AjaxResult addSceneDetail(SceneDetailBo sceneDetailBo);

	AjaxResult updateSceneDetail(SceneDetailBo sceneDetailBo);

	SceneDetailVo getSceneDetailByCondition(SceneDetailConditionBo condition);

}

