package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.scene.SceneBo;
import com.fh.ai.business.entity.bo.scene.SceneConditionBo;
import com.fh.ai.business.entity.dto.scene.SceneDto;
import com.fh.ai.business.entity.vo.scene.SceneVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 场景表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:12
 */
public interface ISceneService extends IService<SceneDto> {

    Map<String, Object> getSceneListByCondition(SceneConditionBo condition);

	AjaxResult addScene(SceneBo sceneBo);

	AjaxResult updateScene(SceneBo sceneBo);

	SceneVo getSceneByCondition(SceneConditionBo condition);

	Map<String, Object> getSceneListWithDetailList(SceneConditionBo condition);

}

