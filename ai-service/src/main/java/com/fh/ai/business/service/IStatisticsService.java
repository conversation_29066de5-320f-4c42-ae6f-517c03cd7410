package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.statistics.StatisticsBo;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.dto.statistics.StatisticsDto;
import com.fh.ai.business.entity.vo.statistics.StatisticsSettingVo;
import com.fh.ai.business.entity.vo.statistics.SysDailyStatisticsInfoVo;
import com.fh.ai.business.entity.vo.statistics.SysStatisticsInfoVo;
import com.fh.ai.business.entity.vo.statistics.UserStatisticsVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 统计表接口
 *
 * <AUTHOR>
 * @date 2024-05-15 14:17:53
 */
public interface IStatisticsService extends IService<StatisticsDto> {

    StatisticsSettingVo getStatisticsSetting();

    SysStatisticsInfoVo getSysStatistics(StatisticsConditionBo conditionBo);

    List<SysDailyStatisticsInfoVo> getSysDailyStatistics(StatisticsConditionBo conditionBo);

    Map<String, Object> getStatisticsListByCondition(StatisticsConditionBo conditionBo);

    AjaxResult addStatistics(StatisticsBo statisticsBo);

    AjaxResult updateStatistics(StatisticsBo statisticsBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteStatistics(StatisticsBo statisticsBo);

    Map<String, Object> getUserStatistics(StatisticsConditionBo conditionBo);

    List<UserStatisticsVo> getAllUserStatistics(StatisticsConditionBo conditionBo);

    void updateStatistics(Date now);
}