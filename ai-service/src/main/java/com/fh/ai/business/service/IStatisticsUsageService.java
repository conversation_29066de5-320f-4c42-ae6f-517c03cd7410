package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageBo;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.UsageStatisticsTotalBo;
import com.fh.ai.business.entity.dto.statisticsUsage.StatisticsUsageDto;
import com.fh.ai.business.entity.vo.statisticsUsage.OrgUsageStatisticsVo;
import com.fh.ai.business.entity.vo.statisticsUsage.SummaryStatisticsVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 使用情况统计表接口
 *
 * <AUTHOR>
 * @date 2024-05-15 14:46:40
 */
public interface IStatisticsUsageService extends IService<StatisticsUsageDto> {

    Map<String, Object> getStatisticsUsageListByCondition(StatisticsUsageConditionBo conditionBo);

    AjaxResult addStatisticsUsage(StatisticsUsageBo statisticsUsageBo);

    AjaxResult updateStatisticsUsage(StatisticsUsageBo statisticsUsageBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteStatisticsUsage(StatisticsUsageBo statisticsUsageBo);

    void updateStatistics(Date nowDate);

    List<UsageStatisticsTotalVo> getUsageStatistics(Long organizationId, String startDay);

    List<UsageStatisticsTotalVo> getTop10UsageStatistics(UsageStatisticsTotalBo bo);

    PageInfo<UsageStatisticsTotalVo> getUsageStatistics(StatisticsUsageConditionBo bo);

    List<UsageStatisticsTotalVo> getExportUsageStatistics(StatisticsUsageConditionBo bo);

    List<UsageStatisticsTotalVo> getAllUsageStatistics(StatisticsUsageConditionBo bo);

    SummaryStatisticsVo getSummaryStatistics(String month,Long organizationId);

    List<OrgUsageStatisticsVo> getOrgUsageStatistics(StatisticsConditionBo bo);

    List<OrgUsageStatisticsVo> getOrgUsageTop10(StatisticsConditionBo bo);

    Map<String, Object> getOrgUsageTableInfo(StatisticsConditionBo bo);

    OrgUsageStatisticsVo getOrgUsageTotalDetail(Long organizationId);

    List<UsageStatisticsTotalVo> getTop10UserUsageStatistics(Long organizationId);

}