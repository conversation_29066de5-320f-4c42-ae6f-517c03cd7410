package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.systemSetting.SystemSettingBo;
import com.fh.ai.business.entity.bo.systemSetting.SystemSettingConditionBo;
import com.fh.ai.business.entity.dto.systemSetting.SystemSettingDto;
import com.fh.ai.business.entity.vo.systemSetting.SystemSettingVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 系统设置表接口
 *
 * <AUTHOR>
 * @date 2024-02-23 14:05:25
 */
public interface ISystemSettingService extends IService<SystemSettingDto> {

    Map<String, Object> getSystemSettingListByCondition(SystemSettingConditionBo conditionBo);

    AjaxResult addSystemSetting(SystemSettingBo systemSettingBo);

    AjaxResult updateSystemSetting(SystemSettingBo systemSettingBo);

    AjaxResult getDetail(Long id);

    SystemSettingVo getDetail(String name);
}