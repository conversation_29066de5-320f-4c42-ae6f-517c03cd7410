package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.task.TaskBo;
import com.fh.ai.business.entity.bo.task.TaskConditionBo;
import com.fh.ai.business.entity.dto.task.TaskDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 任务表接口
 *
 * <AUTHOR>
 * @date 2024-06-04 13:49:38
 */
public interface ITaskService extends IService<TaskDto> {

    Map<String, Object> getTaskListByCondition(TaskConditionBo conditionBo);

    Map<String, Object> getMyTaskListByCondition(TaskConditionBo conditionBo);

	AjaxResult addTask(TaskBo taskBo);

	AjaxResult updateTask(TaskBo taskBo);

    AjaxResult getDetail(Long id,String oid);

    AjaxResult updateState(TaskBo taskBo);

    AjaxResult deleteTask(TaskBo taskBo);

    // 更新排序
    AjaxResult updateSort(TaskBo taskBo);

}