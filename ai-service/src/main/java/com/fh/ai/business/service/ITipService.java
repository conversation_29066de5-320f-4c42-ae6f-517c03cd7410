package com.fh.ai.business.service;

import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.tip.TipBo;
import com.fh.ai.business.entity.bo.tip.TipConditionBo;
import com.fh.ai.business.entity.dto.tip.TipDto;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 提示词接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface ITipService extends IService<TipDto> {

	Map<String, Object> getTipListByCondition(TipConditionBo condition);

	AjaxResult addTip(TipBo tipBo);

	AjaxResult updateTip(TipBo tipBo);

	Map<String, Object> getDetail(Long id);

	/**
	 * 清除标签，支持根据ids清除，不传则清空全部
	 * @param condition
	 */
	void clearTag(TipConditionBo condition);
}

