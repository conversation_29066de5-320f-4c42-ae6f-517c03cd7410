package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.updateLog.UpdateLogBo;
import com.fh.ai.business.entity.bo.updateLog.UpdateLogConditionBo;
import com.fh.ai.business.entity.dto.updateLog.UpdateLogDto;
import com.fh.ai.business.entity.vo.updateLog.UpdateLogVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 更新日志接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
public interface IUpdateLogService extends IService<UpdateLogDto> {

	Map<String, Object> getUpdateLogListByCondition(UpdateLogConditionBo condition);

	AjaxResult addUpdateLog(UpdateLogBo updateLogBo);

	AjaxResult updateUpdateLog(UpdateLogBo updateLogBo);

	Map<String, Object> getDetail(Long id);

	AjaxResult delete(Long id);

	AjaxResult check(Long id,String oid);

	AjaxResult read(Long id,String oid);

}

