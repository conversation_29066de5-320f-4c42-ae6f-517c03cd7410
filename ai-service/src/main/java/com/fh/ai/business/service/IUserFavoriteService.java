package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteBo;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteConditionBo;
import com.fh.ai.business.entity.dto.userFavorite.UserFavoriteDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户收藏表接口
 *
 * <AUTHOR>
 * @date 2024-06-04 10:38:07
 */
public interface IUserFavoriteService extends IService<UserFavoriteDto> {

    Map<String, Object> getUserFavoriteListByCondition(UserFavoriteConditionBo conditionBo);

    AjaxResult addUserFavorite(UserFavoriteBo userFavoriteBo);

    AjaxResult updateUserFavorite(UserFavoriteBo userFavoriteBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteUserFavorite(UserFavoriteBo userFavoriteBo);

    AjaxResult favorite(UserFavoriteBo userFavoriteBo);

    AjaxResult cancelFavorite(UserFavoriteBo userFavoriteBo);
}