package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userFeedback.UserFeedbackBo;
import com.fh.ai.business.entity.bo.userFeedback.UserFeedbackConditionBo;
import com.fh.ai.business.entity.dto.userFeedback.UserFeedbackDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户反馈表接口
 *
 * <AUTHOR>
 * @date 2024-07-01 10:31:51
 */
public interface IUserFeedbackService extends IService<UserFeedbackDto> {

    Map<String, Object> getUserFeedbackListByCondition(UserFeedbackConditionBo conditionBo);

    AjaxResult addUserFeedback(UserFeedbackBo userFeedbackBo);

    AjaxResult updateUserFeedback(UserFeedbackBo userFeedbackBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(UserFeedbackBo userFeedbackBo);

    AjaxResult giveFeedbackScore(UserFeedbackBo userFeedbackBo);

    AjaxResult deleteUserFeedback(UserFeedbackBo userFeedbackBo);

}