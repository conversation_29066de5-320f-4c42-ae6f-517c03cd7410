package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userInstruction.UserInstructionBo;
import com.fh.ai.business.entity.bo.userInstruction.UserInstructionConditionBo;
import com.fh.ai.business.entity.dto.userInstruction.UserInstructionDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-08  16:23
 */
public interface IUserInstructionService extends IService<UserInstructionDto> {

    AjaxResult addUserInstruct(UserInstructionBo userInstructionBo);

    AjaxResult updateUserInstruct(UserInstructionBo userInstructionBo);

    Map<String, Object> getUserInstructListByCondition(UserInstructionConditionBo conditionBo);

    AjaxResult deleteUserInstruct(UserInstructionBo userInstructionBo);

}
