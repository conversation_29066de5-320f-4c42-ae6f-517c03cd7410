package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeBo;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.vo.userPrize.UserPrizeTotalVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户奖品兑换表接口
 *
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
public interface IUserPrizeService extends IService<UserPrizeDto> {

    Map<String, Object> getUserPrizeListByCondition(UserPrizeConditionBo conditionBo);

    UserPrizeTotalVo getUserPrizeTotal(UserPrizeConditionBo conditionBo);

    AjaxResult addUserPrize(UserPrizeBo userPrizeBo);

    AjaxResult updateUserPrize(UserPrizeBo userPrizeBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(UserPrizeBo userPrizeBo);

    AjaxResult deleteUserPrize(UserPrizeBo userPrizeBo);

    AjaxResult redeemPrize(UserPrizeBo userPrizeBo);

    AjaxResult lottery(UserPrizeBo userPrizeBo);
}