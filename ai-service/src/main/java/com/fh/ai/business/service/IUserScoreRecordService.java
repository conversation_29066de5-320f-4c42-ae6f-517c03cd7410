package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.vo.userScoreRecord.ScoreTypeStatisticsVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 用户积分记录接口
 *
 * <AUTHOR>
 * @date 2024-02-20 17:14:17
 */
public interface IUserScoreRecordService extends IService<UserScoreRecordDto> {

    Map<String, Object> getUserScoreRecordListByCondition(UserScoreRecordConditionBo conditionBo);

    List<ScoreTypeStatisticsVo> getScoreTypeStatistics(UserScoreRecordConditionBo conditionBo);

    List<ScoreTypeStatisticsVo> getUserDailyScoreTypeStatistics(UserScoreRecordConditionBo conditionBo);

    UserScoreVo getUserScore(UserScoreRecordConditionBo conditionBo);

    AjaxResult addUserScoreRecord(UserScoreRecordBo userScoreRecordBo);

    boolean haveReceived(UserScoreRecordBo userScoreRecordBo);

    AjaxResult updateUserScoreRecord(UserScoreRecordBo userScoreRecordBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteUserScoreRecord(UserScoreRecordBo userScoreRecordBo);

    Map<String, Long> getScoreOnceSetting();
}