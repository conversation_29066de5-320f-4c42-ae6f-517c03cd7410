package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.user.UserBo;
import com.fh.ai.business.entity.bo.user.UserConditionBo;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户表接口
 *
 * <AUTHOR>
 * @date 2024-02-20 16:29:06
 */
public interface IUserService extends IService<UserDto> {

    AjaxResult checkToken(String token);

    UserVo passwordLogin(UserBo userBo);

    UserVo ppmLogin(UserBo userBo);

    UserVo ppmAppLogin(UserBo userBo);

    AjaxResult userPhoneLogin(UserBo userBo);

    /**
     * 用户手机号绑定
     *
     * @param userBo the user bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -10-18 17:40:38
     */
    AjaxResult userPhoneBind(UserBo userBo);

    AjaxResult sendCode(UserBo userBo);

    /**
     * 发送绑定手机号的验证码
     *
     * @param userBo the user bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -10-18 17:28:38
     */
    AjaxResult sendCodeBindPhone(UserBo userBo);

    Map<String, Object> getUserListByCondition(UserConditionBo conditionBo);

    Map<String, Object> getUserListByOrganizationAdmin(UserConditionBo conditionBo);

    AjaxResult giveScore(UserBo userBo);

    AjaxResult addUser(UserBo userBo);

    AjaxResult updateUser(UserBo userBo);

    AjaxResult getDetail(Long id);

    AjaxResult getDetail(String oid);

    /**
     * 根据oid获取用户信息
     * @param accountBo
     * @return
     */
    AjaxResult getDetail(UserBo accountBo);

    AjaxResult deleteUser(UserBo userBo);

    boolean reduceQuota(String userOid);

    UserVo queryByAccount(String account);

    boolean updatePasswordByAccount(String account, String rawPassword);

    /**
     * 乐学用户登录（如果用户不存在会新增一个用户）
     *
     * @param userBo the user bo
     * @return user vo
     * <AUTHOR>
     * @date 2024 -10-10 10:49:45
     */
    UserVo lxLogin(UserBo userBo);

    /**
     * 学伴或者使用云屏sso的用户登录
     *
     * @param userBo the user bo
     * @return user vo
     * <AUTHOR>
     * @date 2025 -03-16 15:53:23
     */
    UserVo ssoLogin(UserBo userBo);

    /**
     * 修改手机号
     *
     * @param account
     * @param phone
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/22 14:35
     **/
    AjaxResult changePhone(String account, String phone);

    /**
     * 重置密码
     *
     * @param oid
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/22 15:26
     **/
    AjaxResult resetPassword(String oid);
}
