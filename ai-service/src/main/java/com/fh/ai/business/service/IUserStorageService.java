package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userStorage.UserStorageBo;
import com.fh.ai.business.entity.bo.userStorage.UserStorageConditionBo;
import com.fh.ai.business.entity.dto.userStorage.UserStorageDto;
import com.fh.ai.business.entity.vo.userStorage.UserStorageVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 用户本地信息存储表，用于存储用户配置等信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-03 16:27:55
 */
public interface IUserStorageService extends IService<UserStorageDto> {

	Map<String, Object> getUserStorageListByConditionPage(UserStorageConditionBo condition);

    List<UserStorageVo> getUserStorageListByCondition(UserStorageConditionBo condition);

	AjaxResult addUserStorage(UserStorageBo userStorageBo);

	AjaxResult updateUserStorage(UserStorageBo userStorageBo);

	UserStorageVo getUserStorageByCondition(UserStorageConditionBo condition);

}

