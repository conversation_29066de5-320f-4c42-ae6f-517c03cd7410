package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentBo;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentConditionBo;
import com.fh.ai.business.entity.dto.userTaskAttachment.UserTaskAttachmentDto;
import com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 任务附件表接口
 *
 * <AUTHOR>
 * @date 2024-06-04 14:55:41
 */
public interface IUserTaskAttachmentService extends IService<UserTaskAttachmentDto> {

    Map<String, Object> getUserTaskAttachmentListByCondition(UserTaskAttachmentConditionBo conditionBo);

    AjaxResult userTaskInfo(UserTaskAttachmentBo userTaskAttachmentBo);

    List<UserTaskAttachmentVo> getUserTaskAttachmentListByTask(Long taskId);

	AjaxResult addUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo);

	AjaxResult updateUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo);

}