package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userTask.UserTaskBo;
import com.fh.ai.business.entity.bo.userTask.UserTaskConditionBo;
import com.fh.ai.business.entity.dto.userTask.UserTaskDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户任务表接口
 *
 * <AUTHOR>
 * @date 2024-06-04 14:45:06
 */
public interface IUserTaskService extends IService<UserTaskDto> {

    Map<String, Object> getUserTaskListByCondition(UserTaskConditionBo conditionBo);

	AjaxResult addUserTask(UserTaskBo userTaskBo);

	AjaxResult updateUserTask(UserTaskBo userTaskBo);

    AjaxResult getDetail(Long id);

    AjaxResult updateState(UserTaskBo userTaskBo);

    AjaxResult deleteUserTask(UserTaskBo userTaskBo);

}