package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.userVisit.UserVisitBo;
import com.fh.ai.business.entity.bo.userVisit.UserVisitConditionBo;
import com.fh.ai.business.entity.dto.userVisit.UserVisitDto;
import com.fh.ai.common.vo.AjaxResult;

import java.util.Map;

/**
 * 用户访问记录表接口
 *
 * <AUTHOR>
 * @date 2024-05-15 13:56:34
 */
public interface IUserVisitService extends IService<UserVisitDto> {

    Map<String, Object> getUserVisitListByCondition(UserVisitConditionBo conditionBo);

    AjaxResult addUserVisit(UserVisitBo userVisitBo);

    AjaxResult updateUserVisit(UserVisitBo userVisitBo);

    AjaxResult getDetail(Long id);

    AjaxResult deleteUserVisit(UserVisitBo userVisitBo);

}