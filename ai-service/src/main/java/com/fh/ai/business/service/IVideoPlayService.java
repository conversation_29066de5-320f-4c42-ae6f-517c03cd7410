package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.videoPlay.VideoPlayBo;
import com.fh.ai.business.entity.bo.videoPlay.VideoPlayConditionBo;
import com.fh.ai.business.entity.dto.videoPlay.VideoPlayDto;
import com.fh.ai.business.entity.vo.videoPlay.VideoPlayVo;
import com.fh.ai.common.vo.AjaxResult;


import java.util.List;
import java.util.Map;

/**
 * 视频播放表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
public interface IVideoPlayService extends IService<VideoPlayDto> {

	Map<String, Object> getVideoPlayListByCondition(VideoPlayConditionBo condition);

	AjaxResult addVideoPlay(VideoPlayBo videoPlayBo);

	AjaxResult updateVideoPlay(VideoPlayBo videoPlayBo);

	Map<String, Object> getDetail(Long id);

}

