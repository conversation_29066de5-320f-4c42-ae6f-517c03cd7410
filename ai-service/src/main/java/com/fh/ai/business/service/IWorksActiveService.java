package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.activity.ActivityBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveConditionBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveVoteBo;
import com.fh.ai.business.entity.dto.worksActive.WorksActiveDto;
import com.fh.ai.business.entity.vo.worksActive.WorksActiveVo;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteCountVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作品活动表接口
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 18:12:16
 */
public interface IWorksActiveService extends IService<WorksActiveDto> {

	Map<String, Object> getWorksActiveListByConditionPage(WorksActiveConditionBo condition);

	List<WorksActiveVo> getWorksActiveListByCondition(WorksActiveConditionBo condition);

	AjaxResult addWorksActive(WorksActiveBo worksActiveBo);

	AjaxResult updateWorksActive(WorksActiveBo worksActiveBo);

	AjaxResult getDetail(Long id);

	AjaxResult deleteActivity(WorksActiveBo activityBo);

	WorksActiveVo getWorksActiveByCondition(WorksActiveConditionBo condition);

	WorksActiveVoteCountVo voteCount(Long worksActiveId);

	AjaxResult voteCheck(String userOid, Long worksActiveId);

	AjaxResult vote(WorksActiveVoteBo voteBo);

}

