package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.worksActiveVoteRecord.WorksActiveVoteRecordBo;
import com.fh.ai.business.entity.bo.worksActiveVoteRecord.WorksActiveVoteRecordConditionBo;
import com.fh.ai.business.entity.dto.worksActiveVoteRecord.WorksActiveVoteRecordDto;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRecordVo;
import com.fh.ai.common.vo.AjaxResult;

import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-04  10:16
 */
public interface IWorksActiveVoteRecordService extends IService<WorksActiveVoteRecordDto> {

    List<WorksActiveVoteRecordVo> getWorksActiveVoteRecordListByCondition(WorksActiveVoteRecordConditionBo condition);

    AjaxResult addWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo);

    AjaxResult updateWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo);

    AjaxResult deleteWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo);

    WorksActiveVoteRecordVo getWorksActiveVoteRecordByCondition(WorksActiveVoteRecordConditionBo condition);
}
