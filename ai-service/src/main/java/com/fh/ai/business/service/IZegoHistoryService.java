package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.zegoHistory.ZegoHistoryBo;
import com.fh.ai.business.entity.bo.zegoHistory.ZegoHistoryConditionBo;
import com.fh.ai.business.entity.dto.zegoHistory.ZegoHistoryDto;
import com.fh.ai.business.entity.vo.zegoHistory.ZegoHistoryVo;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zego.HumanVideo;
import com.fh.ai.common.zego.MetaHumanVideoVo;
import com.fh.ai.common.zego.VideoOption;


import java.util.List;
import java.util.Map;

/**
 * 美图生成记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
public interface IZegoHistoryService extends IService<ZegoHistoryDto> {

    List<ZegoHistoryVo> getZegoHistoryListByCondition(ZegoHistoryConditionBo condition);

	AjaxResult addZegoHistory(ZegoHistoryBo zegoHistoryBo);

	AjaxResult updateZegoHistory(ZegoHistoryBo zegoHistoryBo);

	Map<String, Object> getDetail(Long id);

	void addCreateImageMetaHumanVideoHistoryApp(VideoOption videoOption,HumanVideo humanVideo, MetaHumanVideoVo imageMetaHuman, Long id, String oid, Long orgId);

}

