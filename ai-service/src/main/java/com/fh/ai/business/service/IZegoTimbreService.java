package com.fh.ai.business.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.zego.ZegoTimbreBo;
import com.fh.ai.business.entity.bo.zego.ZegoTimbreConditionBo;
import com.fh.ai.business.entity.dto.zego.ZegoTimbreDto;
import com.fh.ai.business.entity.vo.zego.ZegoTimbreVo;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 即构音色表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-14 16:34:49
 */
public interface IZegoTimbreService extends IService<ZegoTimbreDto> {

    List<ZegoTimbreVo> getZegoTimbreListByCondition(ZegoTimbreConditionBo condition);

	AjaxResult addZegoTimbre(ZegoTimbreBo zegoTimbreBo);

	AjaxResult updateZegoTimbre(ZegoTimbreBo zegoTimbreBo);

	ZegoTimbreVo getZegoTimbreByCondition(ZegoTimbreConditionBo condition);

	/**
	 * 根据条件获取音色map<音色id,音色音频地址>
	 *
	 * @param condition the condition
	 * @return zego timbre map by condition
	 */
	Map<String,String> getZegoTimbreMapByCondition(ZegoTimbreConditionBo condition);
}

