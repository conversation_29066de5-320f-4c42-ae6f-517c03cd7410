package com.fh.ai.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.ai.business.entity.bo.zegoUser.ZegoUserBo;
import com.fh.ai.business.entity.bo.zegoUser.ZegoUserConditionBo;
import com.fh.ai.business.entity.dto.zegoUser.ZegoUserDto;
import com.fh.ai.business.entity.vo.zegoUser.ZegoUserVo;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zego.MetaHumanVideoVo;

import java.util.List;
import java.util.Map;

/**
 * 美图用户使用表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
public interface IZegoUserService extends IService<ZegoUserDto> {

	Map<String, Object> getZegoUserListByCondition(ZegoUserConditionBo condition);

	AjaxResult addZegoUser(ZegoUserBo zegoUserBo);

	AjaxResult updateZegoUser(ZegoUserBo zegoUserBo);

	Map<String, Object> getDetail(Long id);

	void performTask();

}

