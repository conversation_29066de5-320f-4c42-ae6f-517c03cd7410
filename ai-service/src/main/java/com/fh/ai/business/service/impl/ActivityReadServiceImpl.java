package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.activityRead.ActivityReadBo;
import com.fh.ai.business.entity.bo.activityRead.ActivityReadConditionBo;
import com.fh.ai.business.entity.dto.activityRead.ActivityReadDto;
import com.fh.ai.business.entity.vo.activityRead.ActivityReadVo;
import com.fh.ai.business.mapper.ActivityReadMapper;
import com.fh.ai.business.service.IActivityReadService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

/**
 * 帮助中心接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-20 09:52:37
 */
@Service
public class ActivityReadServiceImpl extends ServiceImpl<ActivityReadMapper, ActivityReadDto> implements IActivityReadService {

	@Resource
	private ActivityReadMapper activityReadMapper;
	
    @Override
	public List<ActivityReadVo> getActivityReadListByCondition(ActivityReadConditionBo condition) {
        return activityReadMapper.getActivityReadListByCondition(condition);
	}

	@Override
	public AjaxResult addActivityRead(ActivityReadBo activityReadBo) {
		ActivityReadDto activityRead = new ActivityReadDto();
		BeanUtils.copyProperties(activityReadBo, activityRead);
		activityRead.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(activityRead)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivityRead(ActivityReadBo activityReadBo) {
		ActivityReadDto activityRead = new ActivityReadDto();
		BeanUtils.copyProperties(activityReadBo, activityRead);
		if(updateById(activityRead)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<ActivityReadDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ActivityReadDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(ActivityReadDto::getId, id);
		ActivityReadDto activityRead = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("activityReadVo", activityRead==null?new ActivityReadVo():activityRead);
		return reuslt;
	}

}