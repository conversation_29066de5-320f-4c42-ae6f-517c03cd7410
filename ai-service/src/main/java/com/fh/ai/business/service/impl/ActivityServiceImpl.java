package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.activity.ActivityBo;
import com.fh.ai.business.entity.bo.activity.ActivityConditionBo;
import com.fh.ai.business.entity.dto.activity.ActivityDto;
import com.fh.ai.business.entity.dto.activityRead.ActivityReadDto;
import com.fh.ai.business.entity.vo.activity.ActivityVo;
import com.fh.ai.business.mapper.ActivityMapper;
import com.fh.ai.business.mapper.ActivityReadMapper;
import com.fh.ai.business.service.IActivityService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.DateUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 活动表接口实现类
 *
 * <AUTHOR>
 * @date 2024-05-13 14:48:18
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, ActivityDto> implements IActivityService {

	@Resource
	private ActivityMapper activityMapper;
    @Resource
	private ActivityReadMapper activityReadMapper;

    @Override
	public Map<String, Object> getActivityListByCondition(ActivityConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        conditionBo.setReadDate(DateUtil.toYmd(new Date()));
        List<ActivityVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = activityMapper.getActivityListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ActivityVo> activityVos = activityMapper.getActivityListByCondition(conditionBo);
            PageInfo<ActivityVo> pageInfo = new PageInfo<>(activityVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
	}

	@Override
	public AjaxResult addActivity(ActivityBo activityBo) {
		ActivityDto activity = new ActivityDto();
		BeanUtils.copyProperties(activityBo, activity);

		activity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		activity.setCreateTime(new Date());
        save(activity);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult updateActivity(ActivityBo activityBo) {
		ActivityDto activity = new ActivityDto();
		BeanUtils.copyProperties(activityBo, activity);

		activity.setUpdateTime(new Date());
        updateById(activity);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<ActivityDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ActivityDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(ActivityDto::getId, id);

        ActivityDto activity = getOne(lqw);
        if(null == activity) {
            return AjaxResult.fail("活动表数据不存在");
        }

		ActivityVo activityVo = new ActivityVo();
		BeanUtils.copyProperties(activity, activityVo);

		return AjaxResult.success(activityVo);
	}

    @Override
    public AjaxResult updateState(ActivityBo activityBo) {
        // 更新状态
        LambdaQueryWrapper<ActivityDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ActivityDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ActivityDto::getId, activityBo.getId());

        ActivityDto activity = getOne(lqw);
        if(null == activity) {
            return AjaxResult.fail("活动表数据不存在");
        }

		ActivityDto dto = new ActivityDto();
        dto.setId(activity.getId());
        dto.setState(activityBo.getState());
        dto.setUpdateBy(activityBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        dto.setPublishTime(new Date());
        updateById(dto);
        if(activityBo.getState().equals(2)){
            update(dto,new LambdaUpdateWrapper<ActivityDto>()
                    .eq(ActivityDto::getId, dto.getId())
                    .set(ActivityDto::getPublishTime, null)
            );
        }
        activityReadMapper.update(null,new LambdaUpdateWrapper<ActivityReadDto>()
                .eq(ActivityReadDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode())
                .eq(ActivityReadDto::getActivityId,activity.getId())
                .set(ActivityReadDto::getIsDelete,IsDeleteEnum.ISDELETE.getCode())
        );
        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteActivity(ActivityBo activityBo) {
        // 删除信息
        LambdaQueryWrapper<ActivityDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ActivityDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ActivityDto::getId, activityBo.getId());

        ActivityDto activity = getOne(lqw);
        if(null == activity) {
            return AjaxResult.fail("活动表数据不存在");
        }

        ActivityDto dto = new ActivityDto();
        dto.setId(activity.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(activityBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}