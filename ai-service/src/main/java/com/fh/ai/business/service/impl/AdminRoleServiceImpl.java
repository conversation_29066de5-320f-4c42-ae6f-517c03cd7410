package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleBo;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleConditionBo;
import com.fh.ai.business.entity.vo.adminRole.AdminRoleVo;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.business.mapper.MenuMapper;
import com.fh.ai.business.service.IAdminRoleService;
import com.fh.ai.business.entity.dto.adminRole.AdminRoleDto;
import com.fh.ai.business.mapper.AdminRoleMapper;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台用户角色表接口实现类
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:50
 */
@Service
public class AdminRoleServiceImpl extends ServiceImpl<AdminRoleMapper, AdminRoleDto> implements IAdminRoleService {

    @Resource
    private AdminRoleMapper adminRoleMapper;

    @Resource
    private MenuMapper menuMapper;

    @Override
    public Map<String, Object> getAdminRoleListByCondition(AdminRoleConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<AdminRoleVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            //page为0，则不分页（查询全部）
            list = adminRoleMapper.getAdminRoleListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<AdminRoleVo> adminRoleVos = adminRoleMapper.getAdminRoleListByCondition(conditionBo);
            PageInfo<AdminRoleVo> pageInfo = new PageInfo<>(adminRoleVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        // 查询菜单
        if (CollectionUtil.isNotEmpty(list)) {
            for (AdminRoleVo adminRoleVo : list) {
                List<MenuVo> menuVos = menuMapper.getMenuListByRole(adminRoleVo.getRoleId());
                adminRoleVo.setMenuVos(menuVos);
            }
        }

        return map;
    }

    @Override
    public AjaxResult addAdminRole(AdminRoleBo adminRoleBo) {
        AdminRoleDto adminRole = new AdminRoleDto();
        BeanUtils.copyProperties(adminRoleBo, adminRole);
        if (save(adminRole)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateAdminRole(AdminRoleBo adminRoleBo) {
        AdminRoleDto adminRole = new AdminRoleDto();
        BeanUtils.copyProperties(adminRoleBo, adminRole);
        if (updateById(adminRole)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<AdminRoleDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AdminRoleDto::getId, id);
        AdminRoleDto adminRole = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("adminRoleVo", adminRole == null ? new AdminRoleVo() : adminRole);
        return reuslt;
    }

}