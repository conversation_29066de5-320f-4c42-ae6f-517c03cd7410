package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.common.JwtUtil;
import com.fh.ai.business.entity.bo.admin.AdminBo;
import com.fh.ai.business.entity.bo.admin.AdminConditionBo;
import com.fh.ai.business.entity.bo.adminRole.AdminRoleConditionBo;
import com.fh.ai.business.entity.dto.admin.AdminDto;
import com.fh.ai.business.entity.dto.adminRole.AdminRoleDto;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.entity.vo.adminRole.AdminRoleVo;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.mapper.AdminMapper;
import com.fh.ai.business.service.IAdminRoleService;
import com.fh.ai.business.service.IAdminService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsLocked;
import com.fh.ai.common.enums.RedisKeyEnum;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.utils.RSAUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台用户表接口实现类
 *
 * <AUTHOR>
 * @date 2023-05-04 09:19:49
 */
@Service
@Slf4j
public class AdminServiceImpl extends ServiceImpl<AdminMapper, AdminDto> implements IAdminService {

    @Autowired
    HttpServletRequest request;

    @Value("${server.encry.privateKey}")
    private String privateKey;

    @Value("${initPassword}")
    private String initPassword;

    @Value("${loginSingle}")
    private boolean loginSingle;

    @Value("${shiro.jwt.expireTime}")
    private long expireTime;

    @Value("${passwordReg}")
    private String passwordReg;

    @Value("${passwordRegAlert}")
    private String passwordRegAlert;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private IAdminRoleService adminRoleService;

    @Resource
    private IOrganizationService organizationService;

    // 登录最大错误次数阈值
    private static final int MAX_FAIL_ATTEMPTS = 5;
    // 登录失败记录的过期时间，单位为分钟
    private static final long FAIL_ATTEMPT_EXPIRE = 5;
    // 账号锁定时间，单位为分钟
    private static final long LOCK_TIME = 5;

    @Override
    public AjaxResult checkToken(String token) {
        String username = JwtUtil.getUserName(token);
        if (StrUtil.isBlank(username)) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！!");
        }
        // 查询用户信息
        AdminDto adminDto = adminMapper.selectOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getAccountName, username).eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (adminDto == null) {
            return AjaxResult.fail(500, "用户名或者密码错误,请检查是否正确!");
        }
        //其次验证token
        try {
            JwtUtil.verify(token, username, adminDto.getPassword());
        } catch (RuntimeException e) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
        }
        //验证token是否在缓存中
        if (!verifyToken(adminDto, token)) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
        }
        // 判断用户状态
        if (IsLocked.YES.getCode().equals(adminDto.getIsLocked())) {
            log.warn("该账号已被禁用，请联系管理员重新启用");
            return AjaxResult.fail(406, "该账号已被禁用，请联系管理员重新启用");
        }

        AdminVo adminVo = new AdminVo();
        BeanUtils.copyProperties(adminDto, adminVo);

        //放入缓存
        String userKey = RedisKeyEnum.ADMIN.getValue() + adminVo.getAccountName();
        redisComponent.set(userKey, adminVo);

        //如果成功登陆则将用户信息存入request
        request.setAttribute("currentAdmin", adminVo);

        // 重置token过期时间
        String tokenKey = RedisKeyEnum.ADMIN_TOKEN.getValue() + adminVo.getAccountName();
        redisComponent.sSet(tokenKey, token);
        redisComponent.expire(tokenKey, (expireTime / 1000));

        return AjaxResult.success();
    }

    /**
     * 验证token是否在缓存内
     *
     * @param adminDto 用户信息
     * @param token    token
     * @return 是否存在
     * @description
     * <AUTHOR>
     * @date 2020/9/2
     */
    private Boolean verifyToken(AdminDto adminDto, String token) {
        //先通过缓存取值
        String tokenKey = RedisKeyEnum.ADMIN_TOKEN.getValue() + adminDto.getAccountName();
        return redisComponent.sHasKey(tokenKey, token);
    }

    /**
     * 生成密码
     *
     * @param salt
     * @param initPassword
     * @return
     */
    private String generatePsd(String salt, String initPassword) {
        return SecureUtil.sha1(salt + initPassword.trim());
    }


    @Override
    public AdminVo login(AdminBo adminBo) {
        log.debug("account:" + adminBo.getAccountName() + "------->>>  password" + adminBo.getPassword());
        // 账号登录失败计数key
        String errorsKey = RedisKeyEnum.ADMIN_LOGIN_ERRORS.getValue() + adminBo.getAccountName();
        // 锁定账号key
        String lockKey = RedisKeyEnum.USER_LOCK_KEY + adminBo.getAccountName();

        if (redisComponent.hasKey(lockKey)){
            throw new BusinessException("该账号已被锁定，请稍后重新登录");
        }

        //获取当前用户信息
        AdminDto sysUser = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getAccountName, adminBo.getAccountName())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        //判断当前用户是否存在
        if (sysUser == null) {
            long count = failLoginCount(errorsKey, lockKey,adminBo.getAccountName());
            long remaining = MAX_FAIL_ATTEMPTS - count;
            throw new BusinessException(500, "账号密码错误，已失败"+count+"次，剩余"+remaining+"次，如您忘记密码，请联系平台管理员处理。");
        }

        //验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(sysUser.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        String password = "";
        try {
            String passwordStr = adminBo.getPassword();
            password = RSAUtil.privateDecrypt(passwordStr, privateKey);
        } catch (Exception e) {
            log.error("密码解密失败!原因:{}", e.getMessage());
        }
        // 给密码进行加密
        String encryPwd = generatePsd(adminBo.getAccountName(), password);
        // 检验密码是否正确
        if (!encryPwd.equals(sysUser.getPassword())) {
//            boolean hasKey = redisComponent.hasKey(errorsKey);
//            Integer errors = 1;
//            if (hasKey) {
//                errors = (Integer) redisComponent.get(errorsKey);
//                if (null != errors) {
//                    errors++;
//                } else {
//                    errors = 1;
//                }
//            }
//
//            // 记录密码错误次数
//            redisComponent.set(errorsKey, errors, 5 * 60);


//            if (errors >= 5) {
//                // 登录错误5次，锁定账号
//                AdminDto user = new AdminDto();
//                user.setId(sysUser.getId());
//                user.setIsLocked(IsLocked.YES.getCode());
//                user.setUpdateTime(new Date());
//                baseMapper.updateById(user);
//
//                throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
//            }

            long count = failLoginCount(errorsKey, lockKey, adminBo.getAccountName());
            long remaining = MAX_FAIL_ATTEMPTS - count;
            throw new BusinessException(500, "账号密码错误，已失败"+count+"次，剩余"+remaining+"次，如您忘记密码，请联系平台管理员处理。");
        }

//        if (!checkOrganizationAuth(sysUser.getOrganizationId())) {
//            throw new BusinessException(500, "当前企业服务期已结束，如需继续使用，请联系商务处理。");
//        }

        // 删除登录错误次数
        redisComponent.del(errorsKey);

        //进行token签发
        String token = JwtUtil.sign(adminBo.getAccountName(), encryPwd);

        AdminVo adminVo = new AdminVo();
        BeanUtils.copyProperties(sysUser, adminVo);
        //检查是否是使用初始密码登录
        adminVo.setInitPwd(initPassword.equals(password));
        //存入签发的token
        adminVo.setToken(token);

        request.setAttribute("currentAdmin", adminVo);
        //每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.ADMIN.getValue() + adminBo.getAccountName();
        redisComponent.del(userKey);
        redisComponent.set(userKey, sysUser);
        //登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.ADMIN_TOKEN.getValue() + adminBo.getAccountName();
        if (loginSingle) {
            //如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        //设置过期时间
        redisComponent.expire(tokenKey, (expireTime / 1000));

        // 更新登录时间
        AdminDto user = new AdminDto();
        user.setId(sysUser.getId());
        user.setLastLoginTime(new Date());
        baseMapper.updateById(user);

        // 获取角色、菜单权限
        AdminRoleConditionBo adminRoleConditionBo = new AdminRoleConditionBo();
        adminRoleConditionBo.setAdminOid(sysUser.getOid());
        adminRoleConditionBo.setIsLocked(IsLocked.NO.getCode());
        Map<String, Object> adminRoleListMap = adminRoleService.getAdminRoleListByCondition(adminRoleConditionBo);
        List<AdminRoleVo> adminRoleVos = (List<AdminRoleVo>) adminRoleListMap.get("list");
        if (CollectionUtil.isEmpty(adminRoleVos)) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        // 管理员只有一个角色
        AdminRoleVo adminRoleVo = adminRoleVos.get(0);
        adminVo.setRoleId(adminRoleVo.getRoleId());
        adminVo.setRoleName(adminRoleVo.getRoleName());
        adminVo.setMenuVos(adminRoleVo.getMenuVos());

        // 组织信息
        if (adminVo.getOrganizationId() != null) {
            OrganizationDto organizationDto = organizationService.getById(adminVo.getOrganizationId());
            adminVo.setOrganizationName(organizationDto.getName());
        }

        // 返回前将密码置空
        adminVo.setPassword(null);
        return adminVo;
    }


    /**
     * 账号登录失败次数累加，锁定账号
     * @param failLoginCountKey
     * @param lockKey
     * @param account
     * @return
     */
    private long failLoginCount(String failLoginCountKey,String lockKey,String account){
        // 累加
        long incr = redisComponent.incr(failLoginCountKey, Constants.NUM_ONE);
        // 首次，设置计数key过期时间
        if (incr==Constants.NUM_ONE){
            redisComponent.expire(failLoginCountKey,FAIL_ATTEMPT_EXPIRE*60);
        }
        // 超过指定次数，锁定账号，删除计数key
        if (incr>=MAX_FAIL_ATTEMPTS){
            redisComponent.set(lockKey,incr,LOCK_TIME*60);
            redisComponent.del(failLoginCountKey);
            log.warn("账号{}，连续输错用户名或者密码超过5次，已被锁定",account);
            throw new BusinessException("连续输入密码"+MAX_FAIL_ATTEMPTS+"次错误，账号已被锁定，请"+LOCK_TIME+"分钟后重新登录");
        }
        return incr;
    }


    /**
     * Query and set organization info.
     *
     * @param organizationId the organization id
     * <AUTHOR>
     * @date 2024 -10-21 15:39:13
     */
    private boolean checkOrganizationAuth(Long organizationId) {
        // 查询机构信息
        if (organizationId != null) {
            OrganizationDto organizationDto = organizationService.getById(organizationId);
            if (organizationDto != null) {

                // 机构起止时间
                Date authStartTime = organizationDto.getAuthStartTime();
                Date authEndTime = organizationDto.getAuthEndTime();
                if (authStartTime == null || authEndTime == null) {
                    // 获取第一层机构的起止时间
                    Long rootOrganizationId =
                            StringKit.getRootOrganizationId(organizationDto.getId(), organizationDto.getSuperiorIds());
                    if (rootOrganizationId != null) {
                        OrganizationDto rootOrganizationDto = organizationService.getById(rootOrganizationId);
                        if (rootOrganizationDto != null) {
                            authStartTime = rootOrganizationDto.getAuthStartTime();
                            authEndTime = rootOrganizationDto.getAuthEndTime();
                        }
                    }
                }

                // 当前时间在有效期内
                Date now = new Date();
                if (authStartTime!= null && authEndTime!= null && now.after(authStartTime) && now.before(authEndTime)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println(SecureUtil.sha1("chuban01" + "gkqhkiG9w0!"));
    }

    @Override
    public AdminVo ppmLogin(AdminBo adminBo) {
        log.debug("account:" + adminBo.getAccountName());

        //获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getAccountName, adminBo.getAccountName())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        // 判断当前用户是否存在
        Date now = new Date();
        if (adminDto == null) {
            adminDto = new AdminDto();
            adminDto.setOid(IdUtil.simpleUUID());
            adminDto.setAccountName(adminBo.getAccountName());
            adminDto.setAdminName(adminBo.getAdminName());
            adminDto.setPassword(SecureUtil.sha1(adminBo.getAccountName() + initPassword.trim()));
            adminDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            adminDto.setIsLocked(IsLocked.NO.getCode());
            adminDto.setCreateBy(adminDto.getOid());
            adminDto.setCreateTime(now);

            // 解析部门
            OrganizationDto organizationDto = organizationService.analysisDepOrganization(adminBo.getPpmDepName(), adminDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }
            adminDto.setOrganizationId(organizationDto.getId());

            // 新增
            baseMapper.insert(adminDto);

//            // 设置角色
//            AdminRoleDto adminRoleDto = new AdminRoleDto();
//            adminRoleDto.setAdminOid(adminDto.getOid());
//            adminRoleDto.setCreateBy(adminDto.getCreateBy());
//            adminRoleDto.setCreateTime(now);
//            adminRoleDto.setRoleId(RoleEnum.DEP_STAFF.getCode());
//            adminRoleDto.setOrganizationId(adminDto.getOrganizationId());
//
//            adminRoleMapper.insert(adminRoleDto);
        } else {
            // 解析部门
            OrganizationDto organizationDto = organizationService.analysisDepOrganization(adminBo.getPpmDepName(), adminDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }

            // 更新
            adminDto.setAdminName(adminBo.getAdminName());
            adminDto.setPhone(adminBo.getPhone());
            if (StringUtils.isBlank(adminDto.getPassword())) {
                adminDto.setPassword(SecureUtil.sha1(adminBo.getAccountName() + initPassword.trim()));
            }
            if (!organizationDto.getId().equals(adminDto.getOrganizationId())) {
                adminDto.setOrganizationId(organizationDto.getId());
            }
            baseMapper.updateById(adminDto);
        }

        //验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(adminDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        //进行token签发
        String token = JwtUtil.sign(adminBo.getAccountName(), adminDto.getPassword());

        AdminVo adminVo = new AdminVo();
        BeanUtils.copyProperties(adminDto, adminVo);
        adminVo.setPassword(null);
        //检查是否是使用初始密码登录
        adminVo.setInitPwd(initPassword.equals(adminDto.getPassword()));
        //存入签发的token
        adminVo.setToken(token);

        // 更新登录时间
        AdminDto user = new AdminDto();
        user.setId(adminDto.getId());
        user.setLastLoginTime(new Date());
        baseMapper.updateById(user);

        // 获取角色
        AdminRoleConditionBo adminRoleConditionBo = new AdminRoleConditionBo();
        adminRoleConditionBo.setAdminOid(adminDto.getOid());
        adminRoleConditionBo.setIsLocked(IsLocked.NO.getCode());
        Map<String, Object> adminRoleListMap = adminRoleService.getAdminRoleListByCondition(adminRoleConditionBo);
        List<AdminRoleVo> adminRoleVos = (List<AdminRoleVo>) adminRoleListMap.get("list");
        if (CollectionUtil.isEmpty(adminRoleVos)) {
//            // 设置角色
//            AdminRoleDto adminRoleDto = new AdminRoleDto();
//            adminRoleDto.setAdminOid(adminDto.getOid());
//            adminRoleDto.setCreateBy(adminDto.getCreateBy());
//            adminRoleDto.setCreateTime(now);
//            adminRoleDto.setRoleId(RoleEnum.DEP_STAFF.getCode());
//            adminRoleDto.setOrganizationId(adminDto.getOrganizationId());
//
//            adminRoleMapper.insert(adminRoleDto);
//
//            adminVo.setRoleId(adminRoleDto.getRoleId());
//            adminVo.setRoleName(RoleEnum.getMsg(RoleEnum.DEP_STAFF.getCode()));
//            adminVo.setRoleOrganizationId(adminRoleDto.getOrganizationId());
        } else {
            // 管理员只有一个角色
            AdminRoleVo adminRoleVo = adminRoleVos.get(0);
            adminVo.setRoleId(adminRoleVo.getRoleId());
            adminVo.setRoleName(adminRoleVo.getRoleName());
            adminVo.setMenuVos(adminRoleVo.getMenuVos());
//            adminVo.setRoleOrganizationId(adminRoleVo.getOrganizationId());
        }

        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            String orgNamePath = organizationService.findOrgNamePath(orgVos, adminVo.getOrganizationId());
            adminVo.setOrgPath(orgNamePath);
        }

        request.setAttribute("currentAdmin", adminVo);
        //每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.ADMIN.getValue() + adminBo.getAccountName();
        redisComponent.del(userKey);
        redisComponent.set(userKey, adminVo);
        //登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.ADMIN_TOKEN.getValue() + adminBo.getAccountName();
        if (loginSingle) {
            //如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        //设置过期时间
        redisComponent.expire(tokenKey, (expireTime / 1000));

        return adminVo;
    }

    @Override
    public Map<String, Object> getAdminListByCondition(AdminConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<AdminVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            //page为0，则不分页（查询全部）
            list = adminMapper.getAdminListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<AdminVo> adminRoleVos = adminMapper.getAdminListByCondition(conditionBo);
            PageInfo<AdminVo> pageInfo = new PageInfo<>(adminRoleVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addAdmin(AdminBo adminBo) {
        AdminDto adminDto = adminMapper.selectOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getAccountName, adminBo.getAccountName())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null != adminDto) {
            return AjaxResult.fail("账号已存在");
        }

        Date now = new Date();
        AdminDto admin = new AdminDto();
        BeanUtils.copyProperties(adminBo, admin);
        admin.setOid(IdUtil.simpleUUID());
        admin.setPassword(SecureUtil.sha1(adminBo.getAccountName() + initPassword));
        admin.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        admin.setIsLocked(IsLocked.NO.getCode());
        admin.setCreateTime(now);
        save(admin);

        // 更新角色
        AdminRoleDto adminRoleDto = adminRoleService.getOne(new LambdaQueryWrapper<AdminRoleDto>()
                .eq(AdminRoleDto::getAdminOid, adminBo.getOid()));
        if (null == adminRoleDto) {
            adminRoleDto = new AdminRoleDto();
            adminRoleDto.setAdminOid(admin.getOid());
            adminRoleDto.setRoleId(adminBo.getRoleId());
            adminRoleDto.setCreateBy(adminBo.getCreateBy());
            adminRoleDto.setCreateTime(new Date());

            adminRoleService.save(adminRoleDto);
        } else {
            adminRoleDto.setRoleId(adminBo.getRoleId());
            adminRoleDto.setUpdateBy(adminBo.getCreateBy());
            adminRoleDto.setUpdateTime(new Date());

            adminRoleService.updateById(adminRoleDto);
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteAdmin(AdminBo adminBo) {
        // 获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getOid, adminBo.getOid())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == adminDto) {
            return AjaxResult.fail("用户不存在");
        }

        AdminDto admin = new AdminDto();
        admin.setId(adminDto.getId());
        admin.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        admin.setUpdateBy(adminBo.getUpdateBy());
        admin.setUpdateTime(new Date());
        updateById(admin);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateAdmin(AdminBo adminBo) {
        // 获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getOid, adminBo.getOid())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == adminDto) {
            return AjaxResult.fail("用户不存在");
        }

        if (StringUtils.isNotBlank(adminBo.getAccountName()) && !adminBo.getAccountName().equals(adminDto.getAccountName())) {
            // 更新账号
            // 判断账号重复
            AdminDto otherAdminDto = adminMapper.selectOne(new LambdaQueryWrapper<AdminDto>()
                    .eq(AdminDto::getAccountName, adminBo.getAccountName())
                    .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .ne(AdminDto::getOid, adminBo.getOid()));
            if (null != otherAdminDto) {
                return AjaxResult.fail("账号已存在");
            }
        }

        AdminDto admin = new AdminDto();
        BeanUtils.copyProperties(adminBo, admin);
        admin.setId(adminDto.getId());
        admin.setUpdateBy(adminBo.getUpdateBy());
        admin.setUpdateTime(new Date());
        updateById(admin);

        // 更新角色
        AdminRoleDto adminRoleDto = adminRoleService.getOne(new LambdaQueryWrapper<AdminRoleDto>()
                .eq(AdminRoleDto::getAdminOid, adminBo.getOid()));
        if (null == adminRoleDto) {
            adminRoleDto = new AdminRoleDto();
            adminRoleDto.setAdminOid(adminBo.getOid());
            adminRoleDto.setRoleId(adminBo.getRoleId());
            adminRoleDto.setCreateBy(adminBo.getUpdateBy());
            adminRoleDto.setCreateTime(new Date());

            adminRoleService.save(adminRoleDto);
        } else {
            adminRoleDto.setRoleId(adminBo.getRoleId());
            adminRoleDto.setUpdateBy(adminBo.getUpdateBy());
            adminRoleDto.setUpdateTime(new Date());

            adminRoleService.updateById(adminRoleDto);
        }

        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult updateState(AdminBo adminBo) {
        // 获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getOid, adminBo.getOid())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == adminDto) {
            return AjaxResult.fail("用户不存在");
        }

        AdminDto dto = new AdminDto();
        dto.setId(adminDto.getId());
        dto.setIsLocked(adminBo.getIsLocked());
        dto.setUpdateBy(adminBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public Boolean changePassword(AdminBo adminBo) {
        String account = adminBo.getAccountName();
        String oldPassword = adminBo.getOldPassword();
        String password = adminBo.getPassword();

        //首先先检验新密码是否通过正则
        String reg = StringUtils.isEmpty(passwordReg) ? "^(?=.*[0-9])(?=.*[a-z])(?=.*[!@#$%^&*,\\\\.])[0-9a-zA-Z!@#$%^&*,\\\\.]{8,16}$" : passwordReg;
        boolean match = ReUtil.isMatch(reg, password);
        if (!match) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), StringUtils.isEmpty(passwordReg) ? "密码格式不正确,密码长度为8-16位，包含数字、大小写字母及特殊字符" : passwordRegAlert);
        }
        //其次判断旧密码是否与新密码一致
        String oldPsd = generatePsd(account, oldPassword);
        String newPsd = generatePsd(account, password);
        if (newPsd.equals(oldPsd)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "新密码与旧密码不可以一致");
        }
        //最终校验原密码是否与库中密码相同
        AdminDto orgin = baseMapper.selectOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getAccountName, account)
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == orgin) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "用户不存在");
        }

        if (!oldPsd.equals(orgin.getPassword())) {
            throw new BusinessException(HttpStatus.BAD_REQUEST.value(), "原密码输入不正确,请重新输入");
        }

        AdminDto user = new AdminDto();
        user.setId(orgin.getId());
        user.setPassword(newPsd);
        user.setUpdateBy(adminBo.getUpdateBy());
        user.setUpdateTime(new Date());
        boolean update = updateById(user);
        return update;
    }

    @Override
    public AjaxResult resetPassword(AdminBo adminBo) {
        // 获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getOid, adminBo.getOid())
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == adminDto) {
            return AjaxResult.fail("用户不存在");
        }

        String psd = generatePsd(adminDto.getAccountName(), initPassword);
        AdminDto user = new AdminDto();
        user.setId(adminDto.getId());
        user.setPassword(psd);
        user.setUpdateBy(adminBo.getUpdateBy());
        user.setUpdateTime(new Date());
        updateById(user);
        return AjaxResult.success(initPassword);
    }

    @Override
    public AjaxResult getDetail(String oid) {
        // 获取当前用户信息
        AdminDto adminDto = getOne(new LambdaQueryWrapper<AdminDto>()
                .eq(AdminDto::getOid, oid)
                .eq(AdminDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == adminDto) {
            return AjaxResult.fail("用户不存在");
        }
        AdminVo adminVo = new AdminVo();
        BeanUtils.copyProperties(adminDto, adminVo);
        adminVo.setPassword(null);

        // 获取角色、菜单权限
        AdminRoleConditionBo adminRoleConditionBo = new AdminRoleConditionBo();
        adminRoleConditionBo.setAdminOid(adminDto.getOid());
        Map<String, Object> adminRoleListMap = adminRoleService.getAdminRoleListByCondition(adminRoleConditionBo);
        List<AdminRoleVo> adminRoleVos = (List<AdminRoleVo>) adminRoleListMap.get("list");
        if (CollectionUtil.isNotEmpty(adminRoleVos)) {
            // 管理员只有一个角色
            AdminRoleVo adminRoleVo = adminRoleVos.get(0);

            adminVo.setRoleId(adminRoleVo.getRoleId());
            adminVo.setRoleName(adminRoleVo.getRoleName());
            adminVo.setMenuVos(adminRoleVo.getMenuVos());
        }

        return AjaxResult.success(adminVo);
    }

}