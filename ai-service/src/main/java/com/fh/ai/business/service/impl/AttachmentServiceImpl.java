package com.fh.ai.business.service.impl;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jaudiotagger.audio.AudioFile;
import org.jaudiotagger.audio.AudioFileIO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.attachment.AttachmentConditionBo;
import com.fh.ai.business.entity.dto.attachment.AttachmentDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.mapper.AttachmentMapper;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.AttachmentState;
import com.fh.ai.common.enums.AttachmentUploadState;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.qwen.QwenUtil;
import com.fh.ai.common.utils.FileSignUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.utils.RichTextParser;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import net.coobird.thumbnailator.Thumbnails;
import ws.schild.jave.process.ffmpeg.DefaultFFMPEGLocator;

/**
 * 附件表接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
@Service
public class AttachmentServiceImpl extends ServiceImpl<AttachmentMapper, AttachmentDto> implements IAttachmentService {

    @Resource
    private AttachmentMapper attachmentMapper;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    @Value("${ffmpeg.path:ffmpeg}")
    private String ffmpegPath;

    @Value("${filepath.webPrefix}")
    private String webPrefix;

    @Value("${filepath.viewPrefix}")
    private String viewPrefix;

    @Value("${filepath.loadPrefix}")
    private String loadPrefix;

    @Autowired
    private QwenUtil qwenUtil;

    @Autowired
    private RichTextParser richTextParser;

    @Resource
    private RedisComponent redisComponent;

    // 支持的图片后缀
    private static final String[] IMAGE_EXTS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

    @Override
    public Map<String, Object> getAttachmentListByCondition(AttachmentConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<AttachmentVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = attachmentMapper.getAttachmentListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<AttachmentVo> attachmentVos = attachmentMapper.getAttachmentListByCondition(conditionBo);
            PageInfo<AttachmentVo> pageInfo = new PageInfo<>(attachmentVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addAttachment(AttachmentBo attachmentBo) {
        AttachmentDto attachment = new AttachmentDto();
        BeanUtils.copyProperties(attachmentBo, attachment);

        attachment.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        attachment.setCreateTime(new Date());
        save(attachment);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateAttachment(AttachmentBo attachmentBo) {
        AttachmentDto attachment = new AttachmentDto();
        BeanUtils.copyProperties(attachmentBo, attachment);

        attachment.setUpdateTime(new Date());
        updateById(attachment);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(AttachmentDto::getId, id);

        AttachmentDto attachment = getOne(lqw);
        if (null == attachment) {
            return AjaxResult.fail("附件表数据不存在");
        }

        AttachmentVo attachmentVo = new AttachmentVo();
        BeanUtils.copyProperties(attachment, attachmentVo);

        return AjaxResult.success(attachmentVo);
    }

    @Override
    public AttachmentVo getDetail(String fileOid) {
        AttachmentConditionBo condition = new AttachmentConditionBo();
        condition.setOid(fileOid);
        List<AttachmentVo> list = attachmentMapper.getAttachmentListByCondition(condition);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        AttachmentVo attachmentVo = list.get(0);
        return attachmentVo;
    }

    @Override
    public AjaxResult updateState(AttachmentBo attachmentBo) {
        // 更新状态
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(AttachmentDto::getId, attachmentBo.getId());

        AttachmentDto attachment = getOne(lqw);
        if (null == attachment) {
            return AjaxResult.fail("附件表数据不存在");
        }

        AttachmentDto dto = new AttachmentDto();
        dto.setId(attachment.getId());
        dto.setState(attachmentBo.getState());
        dto.setUpdateBy(attachmentBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteAttachment(AttachmentBo attachmentBo) {
        // 删除信息
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(AttachmentDto::getId, attachmentBo.getId());

        AttachmentDto attachment = getOne(lqw);
        if (null == attachment) {
            return AjaxResult.fail("附件表数据不存在");
        }

        AttachmentDto dto = new AttachmentDto();
        dto.setId(attachment.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(attachmentBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public void deleteQwenFiles() {
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode());
        lqw.isNotNull(AttachmentDto::getQwenFileId);
        lqw.eq(AttachmentDto::getQwenDeleteState, IsDeleteEnum.NOTDELETE.getCode());
        List<AttachmentDto> attachmentDtos = attachmentMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(attachmentDtos)) {
            return;
        }

        for (AttachmentDto attachmentDto : attachmentDtos) {
            deleteAttachment(attachmentDto.getId());
        }
    }

    public void deleteAttachment(Long id) {
        // 获取附件
        AttachmentDto attachment = attachmentMapper.selectById(id);
        if (null == attachment) {
            // 找不到
            return;
        }

        if (!IsDeleteEnum.ISDELETE.getCode().equals(attachment.getIsDelete())) {
            // 没有删除
            AttachmentDto dto = new AttachmentDto();
            dto.setId(id);
            dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
            dto.setUpdateTime(new Date());
            updateById(dto);
        }

        if (StringUtils.isNotBlank(attachment.getQwenFileId())
            && !IsDeleteEnum.ISDELETE.getCode().equals(attachment.getQwenDeleteState())) {
            // 含千问文件
            JSONObject jsonObject = qwenUtil.deleteFile(attachment.getQwenFileId());
            if (null != jsonObject && jsonObject.getBoolean("deleted")) {
                // 删除成功
                AttachmentDto dto = new AttachmentDto();
                dto.setId(id);
                dto.setQwenDeleteState(IsDeleteEnum.ISDELETE.getCode());
                dto.setUpdateTime(new Date());
                updateById(dto);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttachmentVo uploadFile(String originalName, String remoteFoldPath,
        ByteArrayOutputStream byteArrayOutputStream, AttachmentBo attachmentBo) throws Exception {
        // 获取文件格式
        String fileExt = Objects.requireNonNull(FileUtil.extName(originalName)).toLowerCase();
        // 格式校验
        if (StringUtils.isBlank(fileExt)) {
            // return AjaxResult.fail("文件格式错误");
            throw new Exception("文件格式错误");
        }

        // 获取文件的大小 KB
        byte[] bytes = byteArrayOutputStream.toByteArray();
        long fileSize = bytes.length / 1024;
        if (fileSize > 300 * 1024) {
            throw new Exception("文件大小不能超过300MB");
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
            + fileExt + File.separator;
        if (StringUtils.isNotBlank(remoteFoldPath)) {
            relativeDir = relativeDir + remoteFoldPath + File.separator;
        }

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 新的文件名
        String newName = IdUtil.simpleUUID() + "." + fileExt;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);

        // 上传
        BufferedOutputStream outputStream = null;
        try {
            outputStream = FileUtil.getOutputStream(tempImageFile);
            outputStream.write(bytes);
        } catch (Exception e) {
            log.error("上传文件异常", e);
            throw new Exception("上传文件失败");
        } finally {
            IoUtil.close(outputStream);
        }

        Long duration = null;
        boolean audioFile = isAudioFile(fileExt);
        if (audioFile) {
            File tempFile = new File(filePath);

            if (fileExt.equals("amr")) {
                duration = getAmrDuration(tempFile);
            } else if (fileExt.equalsIgnoreCase("wav")) {
                duration = getAudioDuration(tempFile);
            } else {
                duration = getAllAudioDuration(tempFile);
            }
        }

        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setOid(IdUtil.simpleUUID());
        attachmentDto.setOriginalName(originalName);
        attachmentDto.setNewName(newName);
        attachmentDto.setSuffix(fileExt);
        attachmentDto.setSize(fileSize);
        attachmentDto.setOriginPath(relativeDir + newName);
        attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
        attachmentDto.setDuration(duration);
        attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
        attachmentDto.setState(AttachmentState.FINISH.getCode());
        attachmentDto.setNote(attachmentBo.getNote());
        attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        attachmentDto.setCreateBy(attachmentBo.getCreateBy());
        attachmentDto.setCreateTime(new Date());
        baseMapper.insert(attachmentDto);

        AttachmentVo vo = new AttachmentVo();
        BeanUtils.copyProperties(attachmentDto, vo);
        // vo.setDownloadUrl(webPrefix + attachmentDto.getOid());

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadFile(MultipartFile file, AttachmentBo attachmentBo) {
        if (null == file) {
            return AjaxResult.fail("文件不能为空");
        }

        // 原始文件名
        String originalName = file.getOriginalFilename();
        if (Objects.equals(originalName, "")) {
            originalName = file.getName();
        }
        // 获取文件格式
        String fileExt = Objects.requireNonNull(FileUtil.extName(originalName)).toLowerCase();
        // 格式校验
        if (StringUtils.isBlank(fileExt)) {
            return AjaxResult.fail("文件格式错误");
        }

        // 获取文件的大小 KB
        long fileSize = file.getSize() / 1024;
        if (fileSize > 500 * 1024) {
            return AjaxResult.fail("文件大小不能超过500MB");
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
            + fileExt + File.separator;

        // 新的文件名
        String uuid = IdUtil.simpleUUID();
        String newName = uuid + "." + fileExt;
        String scaleName = uuid + "_scale." + fileExt;
        if (attachmentBo.isUseOriginName()) {
            newName = originalName;
            relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
                + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + File.separator + fileExt
                + File.separator;
        }

        // 临时文件目录
        if (attachmentBo.getTempFile() != null && attachmentBo.getTempFile()) {
            relativeDir = File.separator + Constants.TEMP_FILE_PATH + relativeDir;
        }

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;
        String scalePath = baseDir + scaleName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        File scaleImageFile = new File(scalePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }
        // 存入
        try (OutputStream outputStream = new FileOutputStream(tempImageFile)) {
            outputStream.write(file.getBytes());
            outputStream.flush();
        } catch (Exception e) {
            log.error("上传文件异常", e);
            return AjaxResult.fail("上传文件失败");
        }

        Long duration = null;
        boolean audioFile = isAudioFile(fileExt);
        if (audioFile) {
            File tempFile = new File(filePath);

            if (fileExt.equals("amr")) {
                duration = getAmrDuration(tempFile);
            } else if (fileExt.equalsIgnoreCase("wav")) {
                duration = getAudioDuration(tempFile);
            } else {
                duration = getAllAudioDuration(tempFile);
            }
        }
        // 存入
        try {
            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOid(IdUtil.simpleUUID());
            attachmentDto.setOriginalName(originalName);
            attachmentDto.setNewName(newName);
            attachmentDto.setSuffix(fileExt);
            attachmentDto.setSize(fileSize);
            attachmentDto.setOriginPath(relativeDir + newName);
            attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
            attachmentDto.setDuration(duration);
            attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
            attachmentDto.setState(AttachmentState.FINISH.getCode());
            attachmentDto.setNote(attachmentBo.getNote());
            attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            attachmentDto.setCreateBy(attachmentBo.getCreateBy());
            attachmentDto.setCreateTime(new Date());
            baseMapper.insert(attachmentDto);

            AttachmentVo vo = new AttachmentVo();
            BeanUtils.copyProperties(attachmentDto, vo);
            vo.setMainName(FileUtil.mainName(vo.getOriginalName()));
            // vo.setDownloadUrl(webPrefix + attachmentDto.getOid());
            if (ObjectUtil.equals(attachmentBo.getScalePath(), Boolean.TRUE)) {
                Thumbnails.of(tempImageFile).scale(0.5).toFile(scaleImageFile);
                vo.setScalePath(viewPrefix + relativeDir + scaleName);
            }
            return AjaxResult.success(vo);

        } catch (Exception e) {
            log.error("上传文件异常", e);
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadVideo(MultipartFile file, AttachmentBo attachmentBo) {
        if (null == file) {
            return AjaxResult.fail("文件不能为空");
        }

        // 原始文件名
        String originalName = file.getOriginalFilename();
        if (Objects.equals(originalName, "")) {
            originalName = file.getName();
        }
        // 获取文件格式
        String fileExt = Objects.requireNonNull(FileUtil.extName(originalName)).toLowerCase();
        // 格式校验
        if (StringUtils.isBlank(fileExt)) {
            return AjaxResult.fail("文件格式错误");
        }

        // 获取文件的大小 KB
        long fileSize = file.getSize() / 1024;
        if (fileSize > 300 * 1024) {
            return AjaxResult.fail("文件大小不能超过300MB");
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
            + fileExt + File.separator;

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 新的文件名
        String UUID = IdUtil.simpleUUID();
        String newName = UUID + "." + fileExt;
        if (attachmentBo.isUseOriginName()) {
            newName = originalName;
        }

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;
        String imageName = UUID + ".jpg";
        String imagePath = baseDir + imageName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }
        // 存入
        try (OutputStream outputStream = new FileOutputStream(tempImageFile)) {
            outputStream.write(file.getBytes());
            outputStream.flush();
        } catch (Exception e) {
            log.error("上传文件异常", e);
            return AjaxResult.fail("上传文件失败");
        }

        try {
            ProcessBuilder processBuilder =
                new ProcessBuilder(ffmpegPath, "-i", filePath, "-ss", "00:00:01", "-vframes", "1", imagePath);
            Process process = processBuilder.start();
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        }

        Long duration = null;
        boolean audioFile = isAudioFile(fileExt);
        if (audioFile) {
            File tempFile = new File(filePath);

            if (fileExt.equals("amr")) {
                duration = getAmrDuration(tempFile);
            } else if (fileExt.equalsIgnoreCase("wav")) {
                duration = getAudioDuration(tempFile);
            } else {
                duration = getAllAudioDuration(tempFile);
            }
        }

        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setOid(IdUtil.simpleUUID());
        attachmentDto.setOriginalName(originalName);
        attachmentDto.setNewName(newName);
        attachmentDto.setSuffix(fileExt);
        attachmentDto.setSize(fileSize);
        attachmentDto.setOriginPath(relativeDir + newName);
        attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
        attachmentDto.setDuration(duration);
        attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
        attachmentDto.setState(AttachmentState.FINISH.getCode());
        attachmentDto.setNote(attachmentBo.getNote());
        attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        attachmentDto.setCreateBy(attachmentBo.getCreateBy());
        attachmentDto.setCreateTime(new Date());
        baseMapper.insert(attachmentDto);

        AttachmentVo vo = new AttachmentVo();
        BeanUtils.copyProperties(attachmentDto, vo);
        // vo.setDownloadUrl(webPrefix + attachmentDto.getOid());
        vo.setFirstImage(viewPrefix + relativeDir + imageName);
        return AjaxResult.success(vo);
    }

    public boolean isAudioFile(String extension) {
        // String extension = fileName.substring(fileName.lastIndexOf('.') + 1);

        // 音频文件常见的扩展名
        String[] audioExtensions = {"mp3", "wav", "ogg", "amr", "m4a"};

        for (String audioExtension : audioExtensions) {
            if (audioExtension.equalsIgnoreCase(extension)) {
                return true;
            }
        }

        return false;
    }

    // public static long getVideoDuration(String videoPath) {
    // long duration = 0;
    // try (FFmpegFrameGrabber grabber = FFmpegFrameGrabber.createDefault(videoPath)) {
    // grabber.start();
    // int numFrames = grabber.getLengthInFrames();
    // double frameRate = grabber.getFrameRate();
    // duration = (long) (numFrames / frameRate);
    // } catch (Exception e) {
    // e.printStackTrace();
    // }
    // return duration;
    // }

    public Long getAmrDuration(File file) {
        long duration = -1;
        int[] packedSize = {12, 13, 15, 17, 19, 20, 26, 31, 5, 0, 0, 0, 0, 0, 0, 0};
        RandomAccessFile randomAccessFile = null;
        try {
            randomAccessFile = new RandomAccessFile(file, "rw");
            long length = file.length();// 文件的长度
            int pos = 6;// 设置初始位置
            int frameCount = 0;// 初始帧数
            int packedPos = -1;

            byte[] datas = new byte[1];// 初始数据值
            while (pos <= length) {
                randomAccessFile.seek(pos);
                if (randomAccessFile.read(datas, 0, 1) != 1) {
                    duration = length > 0 ? ((length - 6) / 650) : 0;
                    break;
                }
                packedPos = (datas[0] >> 3) & 0x0F;
                pos += packedSize[packedPos] + 1;
                frameCount++;
            }

            duration += frameCount * 20;// 帧数*20

            // 单位为秒
            int x = (int)((duration / 1000) + 1);
            int i = 3 * (x - 2) / 11;
            return (long)i;
        } catch (Exception e) {
            log.error("Amr音频文件读取失败", e);
        } finally {
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    public Long getAllAudioDuration(File file) {
        String executablePath = new DefaultFFMPEGLocator().getExecutablePath();

        List<String> commands = new java.util.ArrayList<String>();
        commands.add(executablePath);
        commands.add("-i");
        commands.add(file.getAbsolutePath());
        try {
            ProcessBuilder builder = new ProcessBuilder();
            builder.command(commands);
            final Process p = builder.start();

            // 从输入流中读取视频信息
            BufferedReader br = new BufferedReader(new InputStreamReader(p.getErrorStream()));
            StringBuffer sb = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            br.close();

            // 从视频信息中解析时长
            String regexDuration = "Duration: (.*?), start: (.*?), bitrate: (\\d*) kb\\/s";
            Pattern pattern = Pattern.compile(regexDuration);
            Matcher m = pattern.matcher(sb.toString());
            if (m.find()) {
                String group = m.group(1);
                return getTimelen(group);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Long getTimelen(String timelen) {
        Long min = 0L;
        String strs[] = timelen.split(":");
        if (strs[0].compareTo("0") > 0) {
            min += Long.valueOf(strs[0]) * 60 * 60;// 秒
        }
        if (strs[1].compareTo("0") > 0) {
            min += Long.valueOf(strs[1]) * 60;
        }
        if (strs[2].compareTo("0") > 0) {
            min += Math.round(Float.valueOf(strs[2]));
        }
        return min;
    }

    public Long getAudioDuration(File file) {
        try {
            AudioFile f = AudioFileIO.read(file);
            // 时长（以秒为单位）
            int duration = f.getAudioHeader().getTrackLength();
            return (long)duration;
        } catch (Exception e) {
            log.error("方法getAudioDuration获取音频文件时长失败，文件名：" + file.getName() + ", " + e.getMessage());
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadRichTextToFile(String inputHtml, AttachmentBo attachmentBo) {
        // XWPFDocument document = richTextParser.parseToDocx(inputHtml);

        // 文件相对路径，例如：/temp/20240715112211222/docx/
        String fileExt = "docx";
        String relativeDir =
            File.separator + "temp" + File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT)
                + File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + File.separator
                + fileExt + File.separator;

        // 文件存放目录，例如：D:\file\temp\20240715112211222\docx\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 文件名
        String newName = attachmentBo.getFileName().replace(".", "") + "." + fileExt;

        // 文件存放路径，例如：D:\file\temp\20240715112211222\docx\2ad26e33d9564aebbd2a26f5fcdfc37f.docx
        String filePath = baseDir + newName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }
        // 存入
        try {
            // 生成doc格式的word文档，需要手动改为docx
            XWPFDocument xwpfDocument = richTextParser.parseToDocx(inputHtml);
            OutputStream outputStream = new FileOutputStream(tempImageFile);
            xwpfDocument.write(outputStream);
            xwpfDocument.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.error("上传文件异常", e);
            return AjaxResult.fail("上传文件异常");
        }

        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setOid(IdUtil.simpleUUID());
        attachmentDto.setOriginalName(newName);
        attachmentDto.setNewName(newName);
        attachmentDto.setSuffix(fileExt);
        // 获取文件的大小 KB
        long fileSize = tempImageFile.length() / 1024;
        attachmentDto.setSize(fileSize);
        attachmentDto.setOriginPath(relativeDir + newName);
        attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
        // attachmentDto.setDuration(duration);
        attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
        attachmentDto.setState(AttachmentState.FINISH.getCode());
        attachmentDto.setNote(attachmentBo.getNote());
        attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        attachmentDto.setCreateBy(attachmentBo.getCreateBy());
        attachmentDto.setCreateTime(new Date());
        baseMapper.insert(attachmentDto);

        AttachmentVo vo = new AttachmentVo();
        BeanUtils.copyProperties(attachmentDto, vo);

        // 生成文件token，过期时间5分钟
        String fileToken = IdUtil.simpleUUID();
        String fileTokenKey = fileToken + attachmentDto.getOid();
        redisComponent.set(fileTokenKey, fileToken);

        // 设置过期时间
        redisComponent.expire(fileTokenKey, 300);

        vo.setDownloadUrl(loadPrefix + attachmentDto.getOid() + "/" + fileToken);

        return AjaxResult.success(vo);
    }

    @Override
    public AjaxResult getUrlWithToken(String oid) {
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(AttachmentDto::getOid, oid);

        AttachmentDto attachmentDto = getOne(lqw);
        if (null == attachmentDto) {
            return AjaxResult.fail("附件表数据不存在");
        }

        // 生成文件token，过期时间5分钟
        String fileToken = IdUtil.simpleUUID();
        String fileTokenKey = fileToken + attachmentDto.getOid();
        redisComponent.set(fileTokenKey, fileToken);

        // 设置过期时间
        redisComponent.expire(fileTokenKey, 300);

        Map<String, String> fileMap = new HashMap<>(4);
        fileMap.put("url", loadPrefix + attachmentDto.getOid() + "/" + fileToken);
        fileMap.put("fileName", attachmentDto.getOriginalName());
        return AjaxResult.success(fileMap);
    }

    @Override
    public AjaxResult writeToFile(String text, String originalName) {
        if (StringUtils.isBlank(text)) {
            return AjaxResult.fail("文件不能为空");
        }

        if (StringUtils.isBlank(originalName)) {
            return AjaxResult.fail("文件名不能为空");
        }

        // 获取文件格式
        String fileExt = Objects.requireNonNull(FileUtil.extName(originalName)).toLowerCase();
        // 格式校验
        if (StringUtils.isBlank(fileExt)) {
            return AjaxResult.fail("文件格式错误");
        }

        // 获取文件的大小 KB
        long fileSize = text.getBytes().length / 1024;
        if (fileSize > 300 * 1024) {
            return AjaxResult.fail("文件大小不能超过300MB");
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
            + fileExt + File.separator;

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 新的文件名
        String newName = IdUtil.simpleUUID() + "." + fileExt;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }

        // 存入
        try (BufferedWriter bw = new BufferedWriter(new FileWriter(filePath))) {
            bw.write(text);
            bw.flush();
        } catch (Exception e) {
            log.error("上传文件异常", e);
            return AjaxResult.fail("上传文件失败");
        }

        // AttachmentDto attachmentDto = new AttachmentDto();
        // attachmentDto.setOid(IdUtil.simpleUUID());
        // attachmentDto.setOriginalName(originalName);
        // attachmentDto.setNewName(newName);
        // attachmentDto.setSuffix(fileExt);
        // attachmentDto.setSize(fileSize);
        // attachmentDto.setOriginPath(relativeDir + newName);
        // attachmentDto.setViewPath(webPrefix + attachmentDto.getOid());
        // attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
        // attachmentDto.setState(AttachmentState.FINISH.getCode());
        // attachmentDto.setNote(attachmentBo.getNote());
        // attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        // attachmentDto.setCreateBy(attachmentBo.getCreateBy());
        // attachmentDto.setCreateTime(new Date());
        // baseMapper.insert(attachmentDto);
        //
        // AttachmentVo vo = new AttachmentVo();
        // BeanUtils.copyProperties(attachmentDto, vo);

        return AjaxResult.success();
    }

    @Override
    public File toFile(MultipartFile multipartFile) {
        // 原始文件名
        String originalName = multipartFile.getOriginalFilename();
        if (Objects.equals(originalName, "")) {
            originalName = multipartFile.getName();
        }
        // 获取文件格式
        String fileExt = Objects.requireNonNull(FileUtil.extName(originalName)).toLowerCase();
        // 格式校验
        if (StringUtils.isBlank(fileExt)) {
            return null;
        }

        // 获取文件的大小 KB
        long fileSize = multipartFile.getSize() / 1024;
        if (fileSize > 300 * 1024) {
            return null;
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator
            + fileExt + File.separator;

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 新的文件名
        String newName = IdUtil.simpleUUID() + "." + fileExt;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }
        // 存入
        try (OutputStream outputStream = new FileOutputStream(tempImageFile)) {
            outputStream.write(multipartFile.getBytes());
            outputStream.flush();
        } catch (Exception e) {
            log.error("上传文件异常", e);
            return null;
        }

        return tempImageFile;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttachmentVo uploadImage(String imageUrl, AttachmentBo attachmentBo) {
        // 默认的文件格式
        // 从imageUrl里面提取文件后缀，imageUrl示例：https://obs.mtlab.meitu.com/mtopen/e1dae187d3474ae6a9fb0c57099aa622/e2d1c224-490f-4db6-6ca6-289552cc401b/e2d1c224-490f-4db6-6ca6-289552cc401b.jpeg
        String fileExt = getImageExtOrDefault(imageUrl);

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + "whee" + File.separator
            + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + fileExt + File.separator;

        // 新的文件名
        String uuid = IdUtil.simpleUUID();
        String newName = uuid + "." + fileExt;
        String scaleName = uuid + "_scale." + fileExt;

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;
        String scalePath = baseDir + scaleName;

        // 检查路径是否存在
        File tempImageFile = new File(filePath);
        File scaleImageFile = new File(scalePath);
        if (!tempImageFile.getParentFile().exists()) {
            tempImageFile.getParentFile().mkdirs();
        }

        // 存入
        try {
            URL url = new URL(imageUrl);
            String path = url.getPath();

            String originalName = new File(path).getName();

            // 使用HttpURLConnection并设置请求头来避免防盗链问题
            HttpURLConnection connection = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            // 设置User-Agent模拟浏览器访问
            connection.setRequestProperty("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            // 设置Referer
            connection.setRequestProperty("Referer", imageUrl);
            // 设置Accept
            connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");

            BufferedInputStream bis = new BufferedInputStream(connection.getInputStream());
            FileOutputStream fis = new FileOutputStream(tempImageFile);

            byte[] buffer = new byte[1024];
            int count = 0;
            while ((count = bis.read(buffer, 0, 1024)) != -1) {
                fis.write(buffer, 0, count);
            }

            fis.close();
            bis.close();
            connection.disconnect();

            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOid(IdUtil.simpleUUID());
            attachmentDto.setOriginalName(originalName);
            attachmentDto.setNewName(newName);
            attachmentDto.setSuffix(fileExt);
            // attachmentDto.setSize(fileSize);
            attachmentDto.setOriginPath(relativeDir + newName);
            attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
            // attachmentDto.setDuration(duration);
            attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
            attachmentDto.setState(AttachmentState.FINISH.getCode());
            attachmentDto.setNote(attachmentBo.getNote());
            attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            attachmentDto.setCreateBy(attachmentBo.getCreateBy());
            attachmentDto.setThirdFileUrl(attachmentBo.getThirdFileUrl());
            attachmentDto.setThirdFileUrlMd5(attachmentBo.getThirdFileUrlMd5());
            attachmentDto.setCreateTime(new Date());
            baseMapper.insert(attachmentDto);

            AttachmentVo vo = new AttachmentVo();
            BeanUtils.copyProperties(attachmentDto, vo);
            vo.setMainName(FileUtil.mainName(vo.getOriginalName()));
            vo.setDownloadUrl(webPrefix + attachmentDto.getOid());

            // 添加异常处理和格式检查
            try {
                // 检查是否为支持的图片格式
                if (isImageFile(fileExt)) {
                    Thumbnails.of(tempImageFile).scale(0.5).toFile(scaleImageFile);
                    vo.setScalePath(viewPrefix + relativeDir + scaleName);
                }
            } catch (Exception e) {
                log.warn("生成缩略图失败，文件：" + newName + "，错误：" + e.getMessage());
                // 缩略图生成失败时不影响主流程，不设置scalePath
            }

            return vo;
        } catch (Exception e) {
            log.error("上传文件异常", e);
        }

        return null;
    }

    /**
     *  是否是图片文件
     * @param extension
     * @return
     */
    private boolean isImageFile(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }

        String[] supportedFormats = {"jpg", "jpeg", "png", "bmp", "gif"};
        for (String format : supportedFormats) {
            if (format.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 上传文件扩展名处理：如果imageUrl中包含有效的扩展名（指定图片扩展名范围内），则返回该扩展名，否则返回默认的"png"。
     * 
     * @param imageUrl
     * @return
     */
    private String getImageExtOrDefault(String imageUrl) {
        int lastDot = imageUrl.lastIndexOf('.');
        if (lastDot > 0 && lastDot < imageUrl.length() - 1) {
            String ext = imageUrl.substring(lastDot + 1).toLowerCase();
            for (String valid : IMAGE_EXTS) {
                if (valid.equals(ext)) {
                    return ext;
                }
            }
        }
        return "png";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttachmentVo uploadUrl(String fileUrl, AttachmentBo attachmentBo) {
        // 获取文件格式
        String fileExt = "ppt";
        if (StringUtils.isNotBlank(attachmentBo.getSuffix())) {
            fileExt = attachmentBo.getSuffix();
        }

        // 文件相对路径，例如：/20221015/pdf/
        String relativeDir = File.separator + "ppt" + File.separator
            + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + fileExt + File.separator;

        // 新的文件名
        String uuid = IdUtil.simpleUUID();
        String newName = uuid + "." + fileExt;

        // 文件存放目录，例如：D:\file\20221015\pdf\
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = baseDir + newName;

        // 检查路径是否存在
        File tempPptFile = new File(filePath);
        if (!tempPptFile.getParentFile().exists()) {
            tempPptFile.getParentFile().mkdirs();
        }

        // 存入
        try {
            URL url = new URL(fileUrl);
            String path = url.getPath();

            String originalName = new File(path).getName();

            BufferedInputStream bis = new BufferedInputStream(url.openStream());
            FileOutputStream fis = new FileOutputStream(tempPptFile);

            byte[] buffer = new byte[1024];
            int count = 0;
            while ((count = bis.read(buffer, 0, 1024)) != -1) {
                fis.write(buffer, 0, count);
            }

            fis.close();
            bis.close();

            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOid(IdUtil.simpleUUID());
            attachmentDto.setOriginalName(originalName);
            attachmentDto.setNewName(newName);
            attachmentDto.setSuffix(fileExt);
            attachmentDto.setOriginPath(relativeDir + newName);
            attachmentDto.setViewPath(viewPrefix + relativeDir.replace(File.separator, "/") + newName);
            attachmentDto.setUploadState(AttachmentUploadState.FINISH.getCode());
            attachmentDto.setState(AttachmentState.FINISH.getCode());
            attachmentDto.setNote(attachmentBo.getNote());
            attachmentDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            attachmentDto.setCreateBy(attachmentBo.getCreateBy());
            attachmentDto.setCreateTime(new Date());
            baseMapper.insert(attachmentDto);

            AttachmentVo vo = new AttachmentVo();
            BeanUtils.copyProperties(attachmentDto, vo);
            vo.setMainName(FileUtil.mainName(vo.getOriginalName()));
            vo.setDownloadUrl(webPrefix + attachmentDto.getOid());
            return vo;
        } catch (Exception e) {
            log.error("上传文件异常", e);
        }
        return null;
    }

    @Override
    public AjaxResult getAndUploadImage(AttachmentBo attachmentBo) {
        if (StringUtils.isBlank(attachmentBo.getThirdFileUrl())) {
            return AjaxResult.fail("第三方文件url不能为空");
        }

        String thirdFileUrlMd5 = DigestUtil.md5Hex(attachmentBo.getThirdFileUrl());
        LambdaQueryWrapper<AttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttachmentDto::getThirdFileUrlMd5, thirdFileUrlMd5);
        lqw.eq(AttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.last("limit 1");
        AttachmentDto attachmentDto = getOne(lqw);

        if (attachmentDto != null) {
            AttachmentVo attachmentVo = new AttachmentVo();
            BeanUtils.copyProperties(attachmentDto, attachmentVo);
            if (attachmentBo.getIsSignFileUrl() != null && attachmentBo.getIsSignFileUrl()) {
                attachmentVo.setSignViewPath(FileSignUtil.sign(attachmentVo.getViewPath()));
            }
            return AjaxResult.success(attachmentVo);
        }

        attachmentBo.setThirdFileUrlMd5(thirdFileUrlMd5);
        AttachmentVo attachmentVo = uploadImage(attachmentBo.getThirdFileUrl(), attachmentBo);
        if (attachmentBo.getIsSignFileUrl() != null && attachmentBo.getIsSignFileUrl()) {
            attachmentVo.setSignViewPath(FileSignUtil.sign(attachmentVo.getViewPath()));
        }
        if (attachmentVo == null) {
            return AjaxResult.fail("图片上传失败");
        }
        return AjaxResult.success(attachmentVo);
    }

}