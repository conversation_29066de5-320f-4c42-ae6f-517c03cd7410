package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.behaviorRecord.BehaviorRecordBo;
import com.fh.ai.business.entity.bo.behaviorRecord.BehaviorRecordConditionBo;
import com.fh.ai.business.entity.dto.behaviorRecord.BehaviorRecordDto;
import com.fh.ai.business.entity.vo.behaviorRecord.BehaviorRecordVo;
import com.fh.ai.business.mapper.BehaviorRecordMapper;
import com.fh.ai.business.service.IBehaviorRecordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行为记录表接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-11 14:44:58
 */
@Service
public class BehaviorRecordServiceImpl extends ServiceImpl<BehaviorRecordMapper, BehaviorRecordDto> implements IBehaviorRecordService {

    @Resource
    private BehaviorRecordMapper behaviorRecordMapper;

    @Override
    public Map<String, Object> getBehaviorRecordListByCondition(BehaviorRecordConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<BehaviorRecordVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = behaviorRecordMapper.getBehaviorRecordListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<BehaviorRecordVo> behaviorRecordVos = behaviorRecordMapper.getBehaviorRecordListByCondition(conditionBo);
            PageInfo<BehaviorRecordVo> pageInfo = new PageInfo<>(behaviorRecordVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    public Map<String, Object> getDistinctType(BehaviorRecordConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<BehaviorRecordVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = behaviorRecordMapper.getDistinctType(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<BehaviorRecordVo> behaviorRecordVos = behaviorRecordMapper.getDistinctType(conditionBo);
            PageInfo<BehaviorRecordVo> pageInfo = new PageInfo<>(behaviorRecordVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addBehaviorRecord(BehaviorRecordBo behaviorRecordBo) {
        BehaviorRecordDto behaviorRecord = new BehaviorRecordDto();
        BeanUtils.copyProperties(behaviorRecordBo, behaviorRecord);

        behaviorRecord.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        behaviorRecord.setCreateTime(new Date());
        save(behaviorRecord);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateBehaviorRecord(BehaviorRecordBo behaviorRecordBo) {
        BehaviorRecordDto behaviorRecord = new BehaviorRecordDto();
        BeanUtils.copyProperties(behaviorRecordBo, behaviorRecord);

        behaviorRecord.setUpdateTime(new Date());
        updateById(behaviorRecord);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<BehaviorRecordDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(BehaviorRecordDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(BehaviorRecordDto::getId, id);

        BehaviorRecordDto behaviorRecord = getOne(lqw);
        if (null == behaviorRecord) {
            return AjaxResult.fail("行为记录表数据不存在");
        }

        BehaviorRecordVo behaviorRecordVo = new BehaviorRecordVo();
        BeanUtils.copyProperties(behaviorRecord, behaviorRecordVo);

        return AjaxResult.success(behaviorRecordVo);
    }

}