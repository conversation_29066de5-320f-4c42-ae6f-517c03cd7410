package com.fh.ai.business.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.function.Function;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookBo;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingBookDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo;
import com.fh.ai.business.mapper.BookCopywritingBookMapper;
import com.fh.ai.business.service.IBookCopywritingBookService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;

/**
 * 软文与书籍关系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:03
 */
@Service
public class BookCopywritingBookServiceImpl extends ServiceImpl<BookCopywritingBookMapper, BookCopywritingBookDto>
    implements IBookCopywritingBookService {

    @Resource
    private BookCopywritingBookMapper bookCopywritingBookMapper;

    @Override
    public Map<String, Object> getBookCopywritingBookListByConditionAndPage(BookCopywritingBookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookCopywritingBookVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookCopywritingBookMapper.getBookCopywritingBookListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<BookCopywritingBookVo> bookCopywritingBookVos =
                bookCopywritingBookMapper.getBookCopywritingBookListByCondition(condition);
            PageInfo<BookCopywritingBookVo> pageInfo = new PageInfo<>(bookCopywritingBookVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public List<BookCopywritingBookVo> getBookCopywritingBookListByCondition(BookCopywritingBookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookCopywritingBookMapper.getBookCopywritingBookListByCondition(condition);
    }

    @Override
    public AjaxResult addBookCopywritingBook(BookCopywritingBookBo bookCopywritingBookBo) {
        BookCopywritingBookDto bookCopywritingBook = new BookCopywritingBookDto();
        BeanUtils.copyProperties(bookCopywritingBookBo, bookCopywritingBook);
        bookCopywritingBook.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(bookCopywritingBook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBookCopywritingBook(BookCopywritingBookBo bookCopywritingBookBo) {
        BookCopywritingBookDto bookCopywritingBook = new BookCopywritingBookDto();
        BeanUtils.copyProperties(bookCopywritingBookBo, bookCopywritingBook);
        if (updateById(bookCopywritingBook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public BookCopywritingBookVo getBookCopywritingBookByCondition(BookCopywritingBookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        BookCopywritingBookVo vo = bookCopywritingBookMapper.getBookCopywritingBookByCondition(condition);
        return vo;
    }

    @Override
    public void addBookCopywritingBookBatch(List<BookCopywritingBookBo> bookCopywritingBookBos) {
        if(CollectionUtils.isEmpty(bookCopywritingBookBos)){
            return;
        }

        // 批量新增，使用java8的语法转换对象。然后再调用saveBatch批量新增
        List<BookCopywritingBookDto> bookCopywritingBookDtos =
            bookCopywritingBookBos.stream().map(bookCopywritingBookBo -> {
                BookCopywritingBookDto bookCopywritingBookDto = new BookCopywritingBookDto();
                BeanUtils.copyProperties(bookCopywritingBookBo, bookCopywritingBookDto);
                bookCopywritingBookDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                bookCopywritingBookDto.setCreateTime(new Date());
                return bookCopywritingBookDto;
            }).collect(Collectors.toList());
        saveBatch(bookCopywritingBookDtos);
    }

    @Override
    public void saveOrUpdateBookCopywritingBookBatch(List<BookCopywritingBookBo> bookCopywritingBookBos, Long copywritingId, String updateBy) {
        if (copywritingId == null) {
            return;
        }

        // 查询数据库中现有的子表数据
        LambdaQueryWrapper<BookCopywritingBookDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookCopywritingBookDto::getCopywritingId, copywritingId)
                   .eq(BookCopywritingBookDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        List<BookCopywritingBookDto> existingList = list(queryWrapper);

        List<BookCopywritingBookDto> toSaveOrUpdate = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(bookCopywritingBookBos)) {
            // 处理传入的数据：新增或更新
            for (BookCopywritingBookBo bookCopywritingBookBo : bookCopywritingBookBos) {
                BookCopywritingBookDto bookCopywritingBookDto = new BookCopywritingBookDto();
                BeanUtils.copyProperties(bookCopywritingBookBo, bookCopywritingBookDto);
                bookCopywritingBookDto.setCopywritingId(copywritingId);
                bookCopywritingBookDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

                // 如果有ID，说明是更新；否则是新增
                if (bookCopywritingBookDto.getId() != null && bookCopywritingBookDto.getId() > 0) {
                    bookCopywritingBookDto.setUpdateBy(updateBy);
                    bookCopywritingBookDto.setUpdateTime(new Date());
                } else {
                    bookCopywritingBookDto.setCreateBy(updateBy);
                    bookCopywritingBookDto.setCreateTime(new Date());
                }
                toSaveOrUpdate.add(bookCopywritingBookDto);
            }

            // 找出需要删除的数据（数据库中存在但传入数据中不存在的）
            Map<Long, BookCopywritingBookDto> newDataMap = toSaveOrUpdate.stream()
                .filter(dto -> dto.getId() != null)
                .collect(Collectors.toMap(BookCopywritingBookDto::getId, Function.identity()));

            for (BookCopywritingBookDto existingDto : existingList) {
                if (!newDataMap.containsKey(existingDto.getId())) {
                    // 标记为删除
                    existingDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
                    existingDto.setUpdateBy(updateBy);
                    existingDto.setUpdateTime(new Date());
                    toSaveOrUpdate.add(existingDto);
                }
            }
        } else {
            // 如果传入的数据为空，则删除所有现有数据
            for (BookCopywritingBookDto existingDto : existingList) {
                existingDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
                existingDto.setUpdateBy(updateBy);
                existingDto.setUpdateTime(new Date());
                toSaveOrUpdate.add(existingDto);
            }
        }

        // 批量保存或更新
        if (CollectionUtils.isNotEmpty(toSaveOrUpdate)) {
            saveOrUpdateBatch(toSaveOrUpdate);
        }
    }
}