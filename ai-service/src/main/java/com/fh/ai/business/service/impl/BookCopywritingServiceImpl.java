package com.fh.ai.business.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBo;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingBookConditionBo;
import com.fh.ai.business.entity.bo.bookCopywriting.BookCopywritingConditionBo;
import com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingDto;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo;
import com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingVo;
import com.fh.ai.business.mapper.BookCopywritingMapper;
import com.fh.ai.business.service.IBookCopywritingBookService;
import com.fh.ai.business.service.IBookCopywritingService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;

/**
 * 书籍软文表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25 09:53:02
 */
@Service
public class BookCopywritingServiceImpl extends ServiceImpl<BookCopywritingMapper, BookCopywritingDto>
    implements IBookCopywritingService {

    @Resource
    private BookCopywritingMapper bookCopywritingMapper;
    @Resource
    private IBookCopywritingBookService bookCopywritingBookService;

    @Override
    public Map<String, Object> getBookCopywritingListByConditionAndPage(BookCopywritingConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookCopywritingVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookCopywritingMapper.getBookCopywritingListByCondition(condition);
            convertResult(list);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<BookCopywritingVo> bookCopywritingVos =
                bookCopywritingMapper.getBookCopywritingListByCondition(condition);
            convertResult(bookCopywritingVos);
            PageInfo<BookCopywritingVo> pageInfo = new PageInfo<>(bookCopywritingVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    /**
     * 封装返回结果（并做一些处理）
     *
     * @param list
     */
    private void convertResult(List<BookCopywritingVo> list) {
        // 组装list返回数据
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> copywritingIdList = list.stream().map(BookCopywritingVo::getId).collect(Collectors.toList());
            // 查询每本书详情列表
            BookCopywritingBookConditionBo bookCopywritingBookConditionBo = new BookCopywritingBookConditionBo();
            bookCopywritingBookConditionBo.setCopywritingIdList(copywritingIdList);
            List<BookCopywritingBookVo> bookCopywritingBookListByCondition =
                bookCopywritingBookService.getBookCopywritingBookListByCondition(bookCopywritingBookConditionBo);
            // bookCopywritingBookListByCondition结果转为map结构<copywritingId, List<BookCopywritingBookVo>>
            Map<Long, List<BookCopywritingBookVo>> bookCopywritingBookMap = bookCopywritingBookListByCondition.stream()
                .collect(Collectors.groupingBy(BookCopywritingBookVo::getCopywritingId));
            // 遍历list，将bookCopywritingBookMap中的数据赋值到list中
            for (BookCopywritingVo bookCopywritingVo : list) {
                bookCopywritingVo.setBookCopywritingBookList(bookCopywritingBookMap.get(bookCopywritingVo.getId()));
            }
        }
    }

    /**
     * 封装返回结果（并做一些处理）- 单个对象版本
     *
     * @param vo 单个BookCopywritingVo对象
     */
    private void convertResult(BookCopywritingVo vo) {
        if (vo != null && vo.getId() != null) {
            // 查询该软文对应的书籍详情列表
            BookCopywritingBookConditionBo bookCopywritingBookConditionBo = new BookCopywritingBookConditionBo();
            bookCopywritingBookConditionBo.setCopywritingId(vo.getId());
            List<BookCopywritingBookVo> bookCopywritingBookList =
                bookCopywritingBookService.getBookCopywritingBookListByCondition(bookCopywritingBookConditionBo);
            // 直接设置到对象中
            vo.setBookCopywritingBookList(bookCopywritingBookList);
        }
    }

    @Override
    public List<BookCopywritingVo> getBookCopywritingListByCondition(BookCopywritingConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<BookCopywritingVo> bookCopywritingVoList =
            bookCopywritingMapper.getBookCopywritingListByCondition(condition);
        convertResult(bookCopywritingVoList);
        return bookCopywritingVoList;
    }

    @Override
    public Long addBookCopywriting(BookCopywritingBo bookCopywritingBo) {
        BookCopywritingDto bookCopywriting = new BookCopywritingDto();
        BeanUtils.copyProperties(bookCopywritingBo, bookCopywriting);
        bookCopywriting.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(bookCopywriting)) {
            return bookCopywriting.getId();
        } else {
            return null;
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addBookCopywritingWithDetail(BookCopywritingBo bookCopywritingBo) {
        // 新增主表
        Long bookCopywritingId = addBookCopywriting(bookCopywritingBo);
        // 如果子表不为空则新增子表
        if(bookCopywritingId != null && CollectionUtils.isNotEmpty(bookCopywritingBo.getBookCopywritingBookBos())){
            // 设置copywritingId
            bookCopywritingBo.getBookCopywritingBookBos().forEach(bookCopywritingBookBo -> {
                bookCopywritingBookBo.setCopywritingId(bookCopywritingId);
            });
            // 迭代封装子表
            bookCopywritingBookService.addBookCopywritingBookBatch(bookCopywritingBo.getBookCopywritingBookBos());
        }
        return AjaxResult.success(bookCopywritingId);
    }

    @Override
    public AjaxResult updateBookCopywriting(BookCopywritingBo bookCopywritingBo) {
        BookCopywritingDto bookCopywriting = new BookCopywritingDto();
        BeanUtils.copyProperties(bookCopywritingBo, bookCopywriting);
        if (updateById(bookCopywriting)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult updateBookCopywritingWithDetail(BookCopywritingBo bookCopywritingBo) {
        // 更新主表
        BookCopywritingDto bookCopywriting = new BookCopywritingDto();
        BeanUtils.copyProperties(bookCopywritingBo, bookCopywriting);
        if (!updateById(bookCopywriting)) {
            return AjaxResult.fail("保存失败");
        }

        // 处理子表数据（新增/更新/删除）
        bookCopywritingBookService.saveOrUpdateBookCopywritingBookBatch(
            bookCopywritingBo.getBookCopywritingBookBos(),
            bookCopywritingBo.getId(),
            bookCopywritingBo.getUpdateBy()
        );

        return AjaxResult.success("保存成功");
    }

    @Override
    public BookCopywritingVo getBookCopywritingByCondition(BookCopywritingConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        BookCopywritingVo vo = bookCopywritingMapper.getBookCopywritingByCondition(condition);
        // 使用新的重载方法处理单个对象
        convertResult(vo);
        return vo;
    }

    @Override
    public BookCopywritingVo getBookCopywritingByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        // 使用MyBatis-Plus的方法进行精确查询
        BookCopywritingDto dto = lambdaQuery()
            .eq(BookCopywritingDto::getUuid, uuid)
            .eq(BookCopywritingDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()).last("limit 1")
            .one();

        if (dto == null) {
            return null;
        }

        // 转换为VO
        BookCopywritingVo vo = new BookCopywritingVo();
        BeanUtils.copyProperties(dto, vo);

        // 处理子表数据
        convertResult(vo);
        return vo;
    }

}