package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.BookFeedbackBo;
import com.fh.ai.business.entity.bo.book.BookFeedbackConditionBo;
import com.fh.ai.business.entity.dto.book.BookFeedbackDto;
import com.fh.ai.business.entity.vo.book.BookFeedbackVo;
import com.fh.ai.business.mapper.BookFeedbackMapper;
import com.fh.ai.business.service.IBookFeedbackService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:25
 */
@Service
public class BookFeedbackServiceImpl extends ServiceImpl<BookFeedbackMapper, BookFeedbackDto> implements IBookFeedbackService {
    @Resource
    private BookFeedbackMapper bookFeedbackMapper;

    @Override
    public Map<String, Object> getBookFeedbackPageByCondition(BookFeedbackConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookFeedbackVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookFeedbackMapper.getBookFeedbackListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
            List<BookFeedbackVo> bookListVos = bookFeedbackMapper.getBookFeedbackListByCondition(condition);
            PageInfo<BookFeedbackVo> pageInfo = new PageInfo<>(bookListVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public AjaxResult addBookFeedback(BookFeedbackBo bo) {
        BookFeedbackDto entity = new BookFeedbackDto();
        BeanUtils.copyProperties(bo, entity);
        if (save(entity)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBookFeedback(BookFeedbackBo bo) {
        BookFeedbackDto entity = new BookFeedbackDto();
        BeanUtils.copyProperties(bo, entity);
        if (updateById(entity)) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.fail("更新失败");
        }
    }

    @Override
    public BookFeedbackVo getDetail(BookFeedbackConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookFeedbackMapper.getBookFeedbackByCondition(condition);
    }
}
