package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageBo;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageConditionBo;
import com.fh.ai.business.entity.dto.bookList.BookListJsonTemporaryStorageDto;
import com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo;
import com.fh.ai.business.mapper.BookListJsonTemporaryStorageMapper;
import com.fh.ai.business.service.IBookListJsonTemporaryStorageService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:24
 */
@Service
public class BookListJsonTemporaryStorageServiceImpl extends ServiceImpl<BookListJsonTemporaryStorageMapper, BookListJsonTemporaryStorageDto>
        implements IBookListJsonTemporaryStorageService {
    @Resource
    private BookListJsonTemporaryStorageMapper bookListJsonTemporaryStorageMapper;

    @Override
    public Map<String, Object> getBookListJsonTemporaryStoragePageByCondition(BookListJsonTemporaryStorageConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookListJsonTemporaryStorageVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookListJsonTemporaryStorageMapper.getBookListJsonTemporaryStorageListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
            List<BookListJsonTemporaryStorageVo> bookListVos = bookListJsonTemporaryStorageMapper.getBookListJsonTemporaryStorageListByCondition(condition);
            PageInfo<BookListJsonTemporaryStorageVo> pageInfo = new PageInfo<>(bookListVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public AjaxResult addBookListJsonTemporaryStorage(BookListJsonTemporaryStorageBo bo) {
        BookListJsonTemporaryStorageDto entity = new BookListJsonTemporaryStorageDto();
        BeanUtils.copyProperties(bo, entity);
        if (save(entity)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBookListJsonTemporaryStorage(BookListJsonTemporaryStorageBo bo) {
        BookListJsonTemporaryStorageDto entity = new BookListJsonTemporaryStorageDto();
        BeanUtils.copyProperties(bo, entity);
        if (updateById(entity)) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.fail("更新失败");
        }
    }

    @Override
    public BookListJsonTemporaryStorageVo getDetail(BookListJsonTemporaryStorageConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookListJsonTemporaryStorageMapper.getBookListJsonTemporaryStorageByCondition(condition);
    }
}
