package com.fh.ai.business.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.bookListPoster.BookListPosterBo;
import com.fh.ai.business.entity.bo.bookListPoster.BookListPosterConditionBo;
import com.fh.ai.business.entity.dto.bookListPoster.BookListPosterDto;
import com.fh.ai.business.entity.vo.bookListPoster.BookListPosterVo;
import com.fh.ai.business.mapper.BookListPosterMapper;
import com.fh.ai.business.service.IBookListPosterService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;

/**
 * 书单海报表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-07 10:00:00
 */
@Service
public class BookListPosterServiceImpl extends ServiceImpl<BookListPosterMapper, BookListPosterDto>
    implements IBookListPosterService {

    @Resource
    private BookListPosterMapper bookListPosterMapper;

    @Override
    public Map<String, Object> getBookListPosterListByConditionAndPage(BookListPosterConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookListPosterVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookListPosterMapper.getBookListPosterListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<BookListPosterVo> bookListPosterVos =
                bookListPosterMapper.getBookListPosterListByCondition(condition);
            PageInfo<BookListPosterVo> pageInfo = new PageInfo<>(bookListPosterVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public List<BookListPosterVo> getBookListPosterListByCondition(BookListPosterConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookListPosterMapper.getBookListPosterListByCondition(condition);
    }

    @Override
    public BookListPosterVo getBookListPosterByCondition(BookListPosterConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookListPosterMapper.getBookListPosterByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult addBookListPoster(BookListPosterBo bookListPosterBo) {
        BookListPosterDto bookListPoster = new BookListPosterDto();
        BeanUtils.copyProperties(bookListPosterBo, bookListPoster);
        bookListPoster.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(bookListPoster)) {
            return AjaxResult.success("新增成功");
        } else {
            return AjaxResult.fail("新增失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult updateBookListPoster(BookListPosterBo bookListPosterBo) {
        if (bookListPosterBo.getId() == null) {
            return AjaxResult.fail("ID不能为空");
        }
        BookListPosterDto bookListPoster = new BookListPosterDto();
        BeanUtils.copyProperties(bookListPosterBo, bookListPoster);
        if (updateById(bookListPoster)) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.fail("修改失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult deleteBookListPoster(Long id) {
        if (id == null) {
            return AjaxResult.fail("ID不能为空");
        }
        BookListPosterDto bookListPoster = new BookListPosterDto();
        bookListPoster.setId(id);
        bookListPoster.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        if (updateById(bookListPoster)) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.fail("删除失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult batchDeleteBookListPoster(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return AjaxResult.fail("ID列表不能为空");
        }
        // 批量逻辑删除
        boolean result = lambdaUpdate()
            .in(BookListPosterDto::getId, ids)
            .set(BookListPosterDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
            .update();
        if (result) {
            return AjaxResult.success("批量删除成功");
        } else {
            return AjaxResult.fail("批量删除失败");
        }
    }

    @Override
    public BookListPosterVo getBookListPosterByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        // 使用MyBatis-Plus的方法进行精确查询
        BookListPosterDto dto = lambdaQuery()
            .eq(BookListPosterDto::getUuid, uuid)
            .eq(BookListPosterDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
            .last("limit 1")
            .one();

        if (dto == null) {
            return null;
        }

        // 转换为VO
        BookListPosterVo vo = new BookListPosterVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult updateBookListPosterByUuid(String uuid, BookListPosterBo bookListPosterBo) {
        if (StringUtils.isBlank(uuid)) {
            return AjaxResult.fail("UUID不能为空");
        }

        // 先根据uuid查询记录
        BookListPosterDto existingDto = lambdaQuery()
            .eq(BookListPosterDto::getUuid, uuid)
            .eq(BookListPosterDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
            .last("limit 1")
            .one();

        if (existingDto == null) {
            return AjaxResult.fail("未找到对应的书单海报记录");
        }

        // 复制属性并设置ID
        BookListPosterDto updateDto = new BookListPosterDto();
        BeanUtils.copyProperties(bookListPosterBo, updateDto);
        updateDto.setId(existingDto.getId());

        if (updateById(updateDto)) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.fail("修改失败");
        }
    }

}
