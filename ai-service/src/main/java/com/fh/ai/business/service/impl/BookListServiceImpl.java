package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.bookList.BookListBo;
import com.fh.ai.business.entity.bo.bookList.BookListConditionBo;
import com.fh.ai.business.entity.bo.bookList.BookListJsonTemporaryStorageConditionBo;
import com.fh.ai.business.entity.bo.bookList.ConvertBookListJsonBo;
import com.fh.ai.business.entity.dto.bookList.BookListDto;
import com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo;
import com.fh.ai.business.entity.vo.bookList.BookListJsonVo;
import com.fh.ai.business.entity.vo.bookList.BookListTopicVo;
import com.fh.ai.business.entity.vo.bookList.BookListVo;
import com.fh.ai.business.mapper.BookListJsonTemporaryStorageMapper;
import com.fh.ai.business.mapper.BookListMapper;
import com.fh.ai.business.service.IBookListService;
import com.fh.ai.common.coze.CozeUtil;
import com.fh.ai.common.coze.constants.CozeConstants;
import com.fh.ai.common.coze.vo.CozeWorkflowDataStreamVo;
import com.fh.ai.common.enums.BookListJsonConvertState;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-18  16:24
 */
@Service
@Slf4j
public class BookListServiceImpl extends ServiceImpl<BookListMapper, BookListDto> implements IBookListService {
    @Resource
    private BookListMapper bookListMapper;
    @Resource
    private BookListJsonTemporaryStorageMapper bookListJsonTemporaryStorageMapper;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Override
    public Map<String, Object> getBookListPageByCondition(BookListConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<BookListVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = bookListMapper.getBookListListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<BookListVo> bookListVos = bookListMapper.getBookListListByCondition(condition);
            PageInfo<BookListVo> pageInfo = new PageInfo<>(bookListVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public AjaxResult addBookList(BookListBo bookListBo) {
        BookListDto bookList = new BookListDto();
        BeanUtils.copyProperties(bookListBo, bookList);
        bookList.setCreateBy(bookListBo.getUserOid());
        // 书单json未转换
        bookList.setConvertState(BookListJsonConvertState.NOT_CONVERT.getCode());
        bookList.setCreateTime(new Date());
        bookList.setUpdateBy(bookListBo.getUserOid());
        bookList.setUpdateTime(new Date());
        if (save(bookList)) {
            // 异步将大模型返回结果转换成json
            ThreadUtil.taskCommonExecute(() -> {
                convertBookListJson(bookList);
            });
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBookList(BookListBo bookListBo) {
        LambdaQueryWrapper<BookListDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookListDto::getUuid, bookListBo.getUuid());
        queryWrapper.eq(BookListDto::getUserOid, bookListBo.getUserOid());
        queryWrapper.eq(BookListDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        queryWrapper.last("limit 1");
        BookListDto bookList = getOne(queryWrapper);
        if (bookList == null) {
            return AjaxResult.fail("书单不存在");
        }
        Long bookListId = bookList.getId();
        BeanUtils.copyProperties(bookListBo, bookList);
        bookList.setId(bookListId);
        bookList.setUpdateBy(bookListBo.getUserOid());
        bookList.setUpdateTime(new Date());
        if (updateById(bookList)) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.fail("更新失败");
        }
    }

    @Override
    public BookListVo getDetail(BookListConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookListMapper.getBookListByCondition(condition);
    }

    @Override
    public AjaxResult removeBookList(BookListConditionBo condition) {
        BookListVo bookListVo = bookListMapper.getBookListByCondition(condition);
        if (bookListVo == null) {
            return AjaxResult.fail("书单不存在");
        }
        BookListDto bookList = new BookListDto();
        bookList.setId(bookListVo.getId());
        bookList.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        bookList.setUpdateBy(condition.getUserOid());
        bookList.setUpdateTime(new Date());
        if (updateById(bookList)) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.fail("删除失败");
        }
    }

    @Override
    public void convertBookListJsonBatch() {
        // 查询未处理的记录
        LambdaQueryWrapper<BookListDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookListDto::getConvertState, BookListJsonConvertState.NOT_CONVERT.getCode());
        queryWrapper.eq(BookListDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        List<BookListDto> bookListDtos = list(queryWrapper);
        if (!CollectionUtils.isEmpty(bookListDtos)) {
            // 异步将大模型返回结果转换成json
            bookListDtos.forEach(bookListDto -> {
                ThreadUtil.taskCommonExecute(() -> {
                    convertBookListJson(bookListDto);
                });
            });
        }
    }

    /**
     * 大模型返回结果转换书单json
     *
     * @param bookListDto 包含大模型返回结果的DTO对象
     * @return 转换是否成功
     * <AUTHOR>
     * @date 2025/4/24 15:16
     */
    public boolean convertBookListJson(BookListDto bookListDto) {
        // 查询是否生成结果 若已生成，更新并返回
        String bookListJson = getBookListJson(bookListDto);
        if (StringUtils.isNotBlank(bookListJson)) {
            bookListDto.setBookListJson(bookListJson);
            bookListDto.setConvertState(BookListJsonConvertState.SUCCESS.getCode());
            updateById(bookListDto);
            return true;
        }
        // 构建请求参数
        ConvertBookListJsonBo convertBookListJsonBo = new ConvertBookListJsonBo();
        convertBookListJsonBo.setUuid(bookListDto.getUuid());
        convertBookListJsonBo.setLongInput(bookListDto.getModelResult());
        String environment = "dev";
        if (activeProfile.contains("test")) {
            environment = "test";
        } else if (activeProfile.contains("prod")) {
            environment = "prod";
        }
        convertBookListJsonBo.setEnvironment(environment);

        // 输出流
        ByteArrayOutputStream out = null;

        // 调用工作流工具类获取流式响应
        String parameterJson = JSON.toJSONString(convertBookListJsonBo);
        CozeWorkflowDataStreamVo cozeWorkflowDataStreamVo =
            CozeUtil.workflowStream(parameterJson, bookListDto.getUserOid(), CozeConstants.MODEL_RESULT_TO_JSON_FLOW_ID,
                null, null, null);

        try {
            out = new ByteArrayOutputStream();
            CozeWorkflowDataStreamVo cozeWorkflowDataStreamVoResult =
                CozeUtil.makeWorkflowStreamResponse(cozeWorkflowDataStreamVo.getInputStream(), out);

            // 查询结果并保存
            bookListJson = getBookListJson(bookListDto);
            if (StringUtils.isNotBlank(bookListJson)) {
                BookListJsonVo bookListJsonVo = JSON.parseObject(bookListJson, BookListJsonVo.class);
                String bookListName = bookListJsonVo.getName();
                String recommendTopic = Optional.ofNullable(bookListJsonVo.getTopicList()).map(
                    list -> list.stream().filter(t -> StringUtils.isNotBlank(t.getTopicName()))
                        .map(BookListTopicVo::getTopicName).collect(Collectors.joining(","))).orElse("");
                bookListDto.setBookListJson(bookListJson);
                bookListDto.setName(bookListName);
                bookListDto.setRecommendTopic(recommendTopic);
                bookListDto.setConvertState(BookListJsonConvertState.SUCCESS.getCode());
            } else {
                bookListDto.setConvertState(BookListJsonConvertState.FAIL.getCode());
            }
            updateById(bookListDto);
            return true;
        } catch (Exception e) {
            // 记录详细的异常信息
            log.error("convertBookListJson异常，e: " + e);
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("convertBookListJson 流关闭异常，e: " + e);
                }
            }
        }
    }

    /**
     * 更新bookListJson结果
     *
     * @param bookListDto
     * @return
     */
    private String getBookListJson(BookListDto bookListDto) {
        BookListJsonTemporaryStorageConditionBo bookListJsonTemporaryStorageConditionBo =
            new BookListJsonTemporaryStorageConditionBo();
        bookListJsonTemporaryStorageConditionBo.setUuid(bookListDto.getUuid());
        BookListJsonTemporaryStorageVo bookListJsonTemporaryStorageVo =
            bookListJsonTemporaryStorageMapper.getBookListJsonTemporaryStorageByCondition(
                bookListJsonTemporaryStorageConditionBo);
        if (bookListJsonTemporaryStorageVo != null && StringUtils.isNotBlank(
            bookListJsonTemporaryStorageVo.getBookListJson())) {
            // json数据处理
            String bookListJson = bookListJsonTemporaryStorageVo.getBookListJson();
            if (bookListJson.startsWith("```json")) {
                bookListJson = bookListJson.substring(7);
            }
            if (bookListJson.endsWith("```")) {
                bookListJson = bookListJson.substring(0, bookListJson.length() - 3);
            }
            return bookListJson;
        }
        return null;
    }

}
