package com.fh.ai.business.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.BookBo;
import com.fh.ai.business.entity.bo.book.BookConditionBo;
import com.fh.ai.business.entity.bo.book.BookExcelBo;
import com.fh.ai.business.entity.bo.book.JingDongExcelBo;
import com.fh.ai.business.entity.bo.book.QueryRankAndSaleBo;
import com.fh.ai.business.entity.bo.book.QuerySmartSaleBo;
import com.fh.ai.business.entity.bo.book.RankingSmartConditionBo;
import com.fh.ai.business.entity.dto.book.BookDto;
import com.fh.ai.business.entity.dto.book.RankingDouyinDto;
import com.fh.ai.business.entity.dto.book.RankingJingdongDto;
import com.fh.ai.business.entity.dto.book.RankingSmartDto;
import com.fh.ai.business.entity.vo.book.BookVo;
import com.fh.ai.business.entity.vo.book.RankAndSaleInfo;
import com.fh.ai.business.entity.vo.book.RankingDouyinVo;
import com.fh.ai.business.entity.vo.book.RankingSmartVo;
import com.fh.ai.business.entity.vo.book.SmartSaleInfo;
import com.fh.ai.business.entity.vo.fzBook.FzBookSyncResultVo;
import com.fh.ai.business.entity.vo.fzBook.FzBookVo;
import com.fh.ai.business.mapper.BookMapper;
import com.fh.ai.business.mapper.RankingDouyinMapper;
import com.fh.ai.business.mapper.RankingSmartMapper;
import com.fh.ai.business.service.IBookService;
import com.fh.ai.business.service.IRankingDouyinService;
import com.fh.ai.business.service.IRankingJingdongService;
import com.fh.ai.business.service.IRankingSmartService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.BookInputType;
import com.fh.ai.common.enums.BookSourceType;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.RankingType;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.openbookclient.SelfBestSellerClient;
import com.fh.ai.common.openbookclient.config.ApiConfig;
import com.fh.ai.common.openbookclient.config.SmartClientConfig;
import com.fh.ai.common.openbookclient.entity.HttpResultEntity;
import com.fh.ai.common.openbookclient.entity.PageDataEntity;
import com.fh.ai.common.openbookclient.entity.domain.BestSellerQueryEntity;
import com.fh.ai.common.openbookclient.entity.domain.BestSellerRespForMainChannelEntity;
import com.fh.ai.common.utils.DateKit;
import com.fh.ai.common.utils.DateUtil;
import com.fh.ai.common.utils.HttpUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 书籍信息表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class BookServiceImpl extends ServiceImpl<BookMapper, BookDto> implements IBookService {

    @Resource
    private BookMapper bookMapper;

    /**
     * 远程调用
     */
    @Resource
    private SelfBestSellerClient selfBestSellerClient;
    /**
     * 动态配置
     */
    @Resource
    private SmartClientConfig smartClientConfig;
    /**
     * 排行服务
     */
    @Resource
    private IRankingSmartService iRankingSmartService;

    @Resource
    private RankingSmartMapper rankingSmartMapper;
    @Resource
    private IRankingDouyinService rankingDouyinService;
    @Resource
    private IRankingJingdongService rankingJingdongService;

    @Resource
    private RankingDouyinMapper rankingDouyinMapper;

    @Resource
    private RedissonClient redissonClient;

    private static final Long LOCK_TIME = 10L;

    @Value("${fzBook.sync.url:http://*************:28080/prodb/api/cms-api/third/getBookInfo.do}")
    private String syncUrl;

    @Value("${fzBook.sync.size:200}")
    private Integer syncSize;

    private final Logger log = LoggerFactory.getLogger(BookServiceImpl.class);

    @Override
    public Map<String, Object> getBookListByConditionAndPage(BookConditionBo conditionBo) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(ConstantsInteger.NUM_2);
        List<BookVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = bookMapper.getBookListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<BookVo> bookVos = bookMapper.getBookListByCondition(conditionBo);
            PageInfo<BookVo> pageInfo = new PageInfo<>(bookVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public List<BookVo> getBookListByCondition(BookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return bookMapper.getBookListByCondition(condition);
    }

    @Override
    public AjaxResult addBook(BookBo bookBo) {
        BookDto book = new BookDto();
        BeanUtils.copyProperties(bookBo, book);
        book.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(book)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateBook(BookBo bookBo) {
        BookDto book = new BookDto();
        BeanUtils.copyProperties(bookBo, book);
        if (updateById(book)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public BookVo getBookByCondition(BookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        BookVo vo = bookMapper.getBookByCondition(condition);
        return vo;
    }

    @Override
    public List<String> getCategoryListBySourceType(Integer sourceType) {
        if (sourceType == null) {
            return Lists.newArrayList();
        }
        return bookMapper.getCategoryListBySourceType(sourceType);
    }

    @Override
    public List<String> getShopListBySourceType(Integer sourceType) {
        if (sourceType == null) {
            return Lists.newArrayList();
        }
        return bookMapper.getShopListBySourceType(sourceType);
    }

    @Override
    public List<String> getPublishListBySourceType(Integer sourceType) {
        if (sourceType == null) {
            return Lists.newArrayList();
        }
        return bookMapper.getPublishListBySourceType(sourceType);
    }

    /**
     * 获取 图书榜单和销售信息
     * 
     * @return key：图书渠道名称的拼写，value：（key：榜单时间yyyy-MM-dd，value：排名/销量/评论数量）
     */
    @Override
    public Map<String, Map<String, RankAndSaleInfo>> getBookRankAndSaleInfo(QueryRankAndSaleBo queryRankAndSaleBo) {
        Map<String, Map<String, RankAndSaleInfo>> map = new HashMap<>();
        // 微信
        List<RankAndSaleInfo> weiXinRankInfo = bookMapper.getWeiXinRankInfo(queryRankAndSaleBo);
        if (CollectionUtils.isNotEmpty(weiXinRankInfo)) {
            Map<String, RankAndSaleInfo> weixinMap = handlerRankInfo(weiXinRankInfo);
            map.put(BookSourceType.WEIXIN.getName(), weixinMap);
        }
        // 当当
        List<RankAndSaleInfo> dangDangRankInfo = bookMapper.getDangDangRankInfo(queryRankAndSaleBo);
        if (CollectionUtils.isNotEmpty(dangDangRankInfo)) {
            Map<String, RankAndSaleInfo> dangdangMap = handlerRankInfo(dangDangRankInfo);
            map.put(BookSourceType.DANGDANG.getName(), dangdangMap);
        }

        if (queryRankAndSaleBo.getRankingType() == RankingType.PPMBOOK_CX_MONTH.getValue()
            || queryRankAndSaleBo.getRankingType() == RankingType.NEWBOOK.getValue()) {
            // 抖音
            List<RankAndSaleInfo> douYinSaleInfo = bookMapper.getDouYinSaleInfo(queryRankAndSaleBo);
            if (CollectionUtils.isNotEmpty(douYinSaleInfo)) {
                Map<String, RankAndSaleInfo> douyinMap =
                    handlerSaleInfo(douYinSaleInfo, BookSourceType.DOUYING.getValue());
                map.put(BookSourceType.DOUYING.getName(), douyinMap);
            }
            // 京东
//            List<RankAndSaleInfo> jingDongSaleInfo = bookMapper.getJingDongSaleInfo(queryRankAndSaleBo);
//            if (CollectionUtils.isNotEmpty(jingDongSaleInfo)) {
//                Map<String, RankAndSaleInfo> jingdongMap =
//                    handlerSaleInfo(jingDongSaleInfo, BookSourceType.JINGDONG.getValue());
//                map.put(BookSourceType.JINGDONG.getName(), jingdongMap);
//            }
        }
        // 畅销榜
        if (RankingType.PPMBOOK_CX_MONTH.getValue() == queryRankAndSaleBo.getRankingType()) {
            // 豆瓣
            List<RankAndSaleInfo> douBanRankInfo = bookMapper.getDouBanRankInfo(queryRankAndSaleBo);
            if (CollectionUtils.isNotEmpty(douBanRankInfo)) {
                Map<String, RankAndSaleInfo> doubanMap = handlerRankInfo(douBanRankInfo);
                map.put(BookSourceType.DOUBAN.getName(), doubanMap);
            }
        }

        return map;
    }

    /**
     * 处理排名信息。封装除抖音和京东的排名数值map返回
     *
     * @param rankAndSaleInfoList @return（榜单时间yyyy-MM-dd,排名对象）
     */
    private Map<String, RankAndSaleInfo> handlerRankInfo(List<RankAndSaleInfo> rankAndSaleInfoList) {
        Map<String, RankAndSaleInfo> collect = rankAndSaleInfoList.stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(RankAndSaleInfo::getCreateDay, x -> x, (x, y) -> y));
        return collect;
    }

    /**
     * 处理销售信息：处理抖音和京东的评论数和销量数。对于抖音数据：需要合并相同店铺下相同isbn码书籍的销量；对于京东数据，由于没有销量，本期暂时 只展示第一个元素的评论数量，不做合并处理
     * 
     * @param rankAndSaleInfoList
     * @return key：数据收集时间yyyy-MM-dd，value：抖音/京东的销量和排名信息
     */
    private Map<String, RankAndSaleInfo> handlerSaleInfo(List<RankAndSaleInfo> rankAndSaleInfoList,
        Integer sourceType) {
        // 按照榜单日期分组，yyyy-MM-dd
        Map<String, List<RankAndSaleInfo>> collectMap = rankAndSaleInfoList.stream().filter(Objects::nonNull)
            .collect(Collectors.groupingBy(RankAndSaleInfo::getCollectDay));
        Set<Map.Entry<String, List<RankAndSaleInfo>>> entries = collectMap.entrySet();
        Map<String, RankAndSaleInfo> collect = new HashMap<>();
        // 遍历
        for (Map.Entry<String, List<RankAndSaleInfo>> entry : entries) {
            List<RankAndSaleInfo> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                // 如果某一天抖音书籍在同一渠道下存在多个排行数据，并且bookshop不一样，需处理合并销量。
                if (value.size() > Constants.NUM_ONE && sourceType == BookSourceType.DOUYING.getValue()) {
                    // 待计算销量的列表
                    List<RankAndSaleInfo> accumulate = new ArrayList<>();
                    // 按照bookshop+bookid分组，获取不同bookshop的但是
                    Map<String, List<RankAndSaleInfo>> shopBookIdMap = value.stream()
                        .collect(Collectors.groupingBy(x -> x.getBookShop().concat(String.valueOf(x.getBookId()))));
                    for (Map.Entry<String, List<RankAndSaleInfo>> shopBookIdEntry : shopBookIdMap.entrySet()) {
                        List<RankAndSaleInfo> shopBookIdEntryValue = shopBookIdEntry.getValue();
                        // 如果列表长度大于1说明存在多个重复的排名数据，按照入库时间，取最新的排名数据。
                        if (shopBookIdEntryValue.size() > Constants.NUM_ONE) {
                            List<RankAndSaleInfo> repeatRankAndSaleInfos = shopBookIdEntryValue.stream()
                                .sorted(Comparator.comparing(RankAndSaleInfo::getCreateTime))
                                .collect(Collectors.toList());
                            RankAndSaleInfo latestRepeat =
                                repeatRankAndSaleInfos.get(repeatRankAndSaleInfos.size() - ConstantsInteger.NUM_1);
                            accumulate.add(latestRepeat);
                            // 否则取第一个数据
                        } else {
                            RankAndSaleInfo single = shopBookIdEntryValue.get(ConstantsInteger.NUM_0);
                            accumulate.add(single);
                        }
                    }
                    RankAndSaleInfo rankAndSaleInfo = new RankAndSaleInfo();
                    BeanUtils.copyProperties(accumulate.get(0), rankAndSaleInfo);
                    rankAndSaleInfo.setSalesCount(Constants.NUM_ZERO_STR);
                    rankAndSaleInfo.setCommentCount(Constants.NUM_ZERO_STR);
                    // 遍历累加抖音销量
                    accumulate.stream().forEach(a -> {
                        if (StringUtils.isNotBlank(a.getSalesCount())) {
                            rankAndSaleInfo
                                .setSalesCount(String.valueOf(Integer.parseInt(rankAndSaleInfo.getSalesCount())
                                    + Integer.parseInt(a.getSalesCount())));
                        }
                    });
                    collect.put(entry.getKey(), rankAndSaleInfo);
                } else {
                    collect.put(entry.getKey(), value.get(0));
                }
            }
        }
        return collect;
    }

    /**
     * 远程获取开卷类型的book
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getBookRpcFromSmartClient() {
        try {
            // 找出来自开卷的book
            List<BookVo> bookVos = bookMapper.selectIds(BookSourceType.SMART.getValue());
            // 按照第三方主键分组
            Map<String, BookVo> bookMap =
                bookVos.stream().collect(Collectors.toMap(BookVo::getBookThirdId, bookVo -> bookVo, (a, b) -> b));
            BestSellerQueryEntity req = buildSmartClientBaseQueryParam();
            List<YearMonth> dateScopes = determineDateScopes();
            String uuid = UUID.randomUUID().toString();
            for (YearMonth yearMonth : dateScopes) {
                for (Integer publishId : ApiConfig.publishIDs) {
                    for (Integer channel : ApiConfig.dataChannelFilters) {
                        String format = yearMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
                        req.setCycleValue(Integer.parseInt(format));
                        req.setPublishID(publishId);
                        req.setDataChannelFilter(channel);
                        HttpResultEntity<PageDataEntity<BestSellerRespForMainChannelEntity>> httpResult = null;
                        try {
                            log.info("开始调用selfBestSellerClient.List_M()，时间范围：{}，出版社id：{}，销售渠道：{}。。。。", format,
                                publishId, channel);
                            httpResult = selfBestSellerClient.List_M(req);
                        } catch (Exception e) {
                            Throwable cause = e.getCause();
                            if(Objects.nonNull(cause)){
                                log.error("调用开卷接口失败，失败原因：{}，异常堆栈：",cause.getMessage(),cause);
                            }else {
                                log.error("调用smart接口失败，失败原因：{}，异常堆栈：",e.getMessage(),e);
                            }

                            throw new RuntimeException("调用selfBestSellerClient.List_M()失败");
                        }
                        if (httpResult.getErrorCode() != 0) {
                            log.error("调用smartclient查询书籍信息失败，时间戳{}，入参：{}，失败信息{}：", DateUtil.toYmdhms(new Date()),
                                JSON.toJSONString(req), httpResult.getErrorMsg());
                            continue;
                        }
                        ArrayList<BestSellerRespForMainChannelEntity> pageData = httpResult.getData().getPageData();
                        if (CollectionUtils.isEmpty(pageData)) {
                            log.info("未查到书籍信息，跳过。入参：{}", JSON.toJSONString(req));
                            continue;
                        }
                        // 处理数据
                        smartClientDataHandler(pageData, bookMap, yearMonth, channel, uuid);
                    }
                }

            }
        } catch (Exception e) {
            log.error("运行出错" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 开卷-处理开卷接口返回的数据
     *
     * @param pageData 开卷书籍
     * @param bookMap p_book中已有的开卷书籍
     * @param yearMonth 年月
     * @param channel 查询渠道
     * @param uuid 本次查询的唯一标记
     */
    private void smartClientDataHandler(List<BestSellerRespForMainChannelEntity> pageData, Map<String, BookVo> bookMap,
        YearMonth yearMonth, Integer channel, String uuid) {
        RankingSmartConditionBo conditionBo = new RankingSmartConditionBo();
        // 榜单类型：1月，2年
        conditionBo.setRankingType(1);
        conditionBo.setRankingYear((long)yearMonth.getYear());
        conditionBo.setRankingMonth((long)yearMonth.getMonthValue());
        conditionBo.setRankingChannel(channel);
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<RankingSmartVo> rankingSmartVos = rankingSmartMapper.selectRankInfo(conditionBo);
        Map<Long, List<RankingSmartVo>> rankingMap =
            rankingSmartVos.stream().collect(Collectors.groupingBy(RankingSmartVo::getBookId));
        log.debug("开始处理samartClient返回的数据");
        List<BookDto> total = new ArrayList<>();
        for (BestSellerRespForMainChannelEntity data : pageData) {
            // 存在做更新操作，String.valueOf()包装int，确保可以找到key。
            if (bookMap.containsKey(String.valueOf(data.getBookID()))) {
                BookVo bookVo = bookMap.get(String.valueOf(data.getBookID()));
                if (Objects.nonNull(bookVo)) {
                    BookDto updateBook = buildBookDtoBySmartClientData(data);
                    updateBook.setId(bookVo.getId());
                    // 更新
                    updateById(updateBook);
                    if (!rankingMap.containsKey(bookVo.getId())) {
                        total.add(updateBook);
                    }
                }
            } else {
                // 否则做插入操作
                BookDto newBook = buildBookDtoBySmartClientData(data);
                newBook.setSourceType(BookSourceType.SMART.getValue());
                newBook.setCreateTime(new Date());
                newBook.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                save(newBook);
                // 放入书籍map，保证重复书籍不会重复插入数据库
                BookVo bookVo = new BookVo();
                bookVo.setId(newBook.getId());
                bookVo.setBookThirdId(newBook.getBookThirdId());
                bookMap.put(newBook.getBookThirdId(), bookVo);
                total.add(newBook);
            }
        }
        if (CollectionUtils.isNotEmpty(total)) {
            List<RankingSmartDto> rankingSmarts = buildRankingSmart(total, yearMonth, uuid, channel);
            iRankingSmartService.saveBatch(rankingSmarts);
        }
    }

    /**
     * 开卷-构建开卷接口基础查询条件
     *
     * @return
     */
    private BestSellerQueryEntity buildSmartClientBaseQueryParam() {
        BestSellerQueryEntity req = new BestSellerQueryEntity();
        // 固定按月查询，1- 月维度，2- 周维度，3-年维度，4-当前1-N维度,5- 日维度
        req.setCycleType(1);
        // 固定查询本版书频道，本版书频道ID：3020；市场竞争频道ID：4010；图书排行榜频道ID：1020
        req.setChannelID(3020);
        // 固定查询所有种类图书，总体图书：-1000
        req.setKindID("-1000");
        // 固定查询包含当前单位下的副牌社图书； boolean 是否包含当前单位下的副牌社图书
        req.setWithChildPublishers(true);
        // 固定查询包含单省单店数据； integer or null 是否包含单省单店数据
        req.setIncludePrvData(1);
        // 固定查询包含一号多书； boolean 是否包含一号多书
        req.setIncludeMultiBook(true);
        req.setPageNo(1);
        req.setPageSize(smartClientConfig.getPageSize());
        return req;
    }

    /**
     * 根据配置计算查询范围
     *
     * @return
     */
    private List<YearMonth> determineDateScopes() {
        List<YearMonth> dateScopes = new ArrayList<>();
        if (StringUtils.isNotBlank(smartClientConfig.getCycleValue())) {
            try {
                YearMonth queryMonthYear =
                    YearMonth.parse(smartClientConfig.getCycleValue(), DateTimeFormatter.ofPattern("yyyyMM"));
                if (Constants.QUERY_TYPE_YEAR.equals(smartClientConfig.getQueryType())) {
                    for (int i = 1; i <= 12; i++) {
                        YearMonth yearMonth = YearMonth.of(queryMonthYear.getYear(), i);
                        dateScopes.add(yearMonth);
                    }
                } else if (Constants.QUERY_TYPE_MONTH.equals(smartClientConfig.getQueryType())) {
                    dateScopes.add(YearMonth.of(queryMonthYear.getYear(), queryMonthYear.getMonth()));
                }
            } catch (Exception e) {
                log.error("解析cycleValue失败，cycleValue：{}，异常信息：{}", smartClientConfig.getCycleValue(), e.getMessage(), e);
                throw e;
            }
        } else {
            dateScopes.add(YearMonth.now().minusMonths(1));
        }
        return dateScopes;
    }

    /**
     * 根据开卷接口返回的数据构建p_book的pojo对象。
     *
     * @param data
     * @return
     */
    private BookDto buildBookDtoBySmartClientData(BestSellerRespForMainChannelEntity data) {
        BookDto newBook = new BookDto();
        newBook.setSortIndex((long)data.getRowID());
        newBook.setBookName(data.getDisplayTitle());
        newBook.setBookAuthor(data.getAuthor());
        List<String> kinds =
            Arrays.asList(data.getKind1Name(), data.getKind2Name(), data.getKind3Name(), data.getKind4Name());
        String category = kinds.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        newBook.setBookCategory(data.getKind1Name());
        newBook.setBookCategoryAll(category);
        newBook.setBookLabel(category);
        newBook.setPublishName(data.getPublishName());
        newBook.setBookIsbn(data.getIsbn());
        newBook.setBookCover(data.getBookCover());
        newBook.setBookThirdId(String.valueOf(data.getBookID()));
        newBook.setUpdateTime(new Date());
        newBook.setSalesCount(String.valueOf(data.getSales()));
        return newBook;
    }

    /**
     * 开卷-构建排名信息pojo
     *
     * @param bookDtoList
     * @param yearMonth
     * @param uuid
     * @param channel
     * @return
     */
    private List<RankingSmartDto> buildRankingSmart(List<BookDto> bookDtoList, YearMonth yearMonth, String uuid,
        Integer channel) {
        List<RankingSmartDto> rankingSmartDtos = bookDtoList.stream().map(x -> {
            RankingSmartDto rankingSmartDto = new RankingSmartDto();
            // 榜单类型，按月查询
            rankingSmartDto.setRankingType(1);
            rankingSmartDto.setSortIndex(x.getSortIndex());
            rankingSmartDto.setBookId(x.getId());
            rankingSmartDto.setRankingYear((long)yearMonth.getYear());
            rankingSmartDto.setRankingMonth((long)yearMonth.getMonthValue());
            rankingSmartDto.setRankingQuarter((long)DateKit.calculateQuarter(yearMonth.getMonthValue()));
            rankingSmartDto.setUuid(uuid);
            rankingSmartDto.setCreateTime(new Date());
            rankingSmartDto.setUpdateTime(new Date());
            rankingSmartDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            rankingSmartDto.setRankingChannel(channel);
            rankingSmartDto.setSalesCount(Integer.valueOf(x.getSalesCount()));
            return rankingSmartDto;
        }).collect(Collectors.toList());

        return rankingSmartDtos;
    }

    /**
     * 导入书籍信息，同时计算抖音书籍的飙升指数
     * 
     * @param sourceType 渠道类型 8 抖音 9 京东
     * @param bookExcelBos 待导入数据
     * @param collectTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void importBooksFromExcel(Integer sourceType, List<BookExcelBo> bookExcelBos, Date collectTime) {
        // 根据渠道查出，已有书籍，并根据书籍isbn码分组
        List<BookVo> bookVos = bookMapper.selectIds(sourceType);
        Map<String, BookVo> bookVoMap =
            bookVos.stream().collect(Collectors.toMap(BookVo::getBookIsbn, x -> x, (x, y) -> y));
        // 待新增书籍列表
        List<BookDto> newBooks = new ArrayList<>();
        // 待更新书籍列表
        List<BookDto> updateBooks = new ArrayList<>();
        Date date = new Date();
        // 遍历待导入数据
        for (BookExcelBo bookExcelBo : bookExcelBos) {
            // 已经存在做更新
            if (bookVoMap.containsKey(bookExcelBo.getBookIsbn())) {
                BookVo bookVo = bookVoMap.get(bookExcelBo.getBookIsbn());
                BookDto updateBookDto = buildImportBookDTO(bookExcelBo, sourceType, bookVo, date);
                updateBooks.add(updateBookDto);
                // 否则插入
            } else {
                BookDto newBookDto = buildImportBookDTO(bookExcelBo, sourceType, null, date);
                newBooks.add(newBookDto);
            }
        }
        // 批量插入，注意，集合中可能存在bookShop不同但isbn码相同的对象，插入前需对集合中重复的isbn码进行过滤。同时保证重复数据也获取主键id。
        List<BookDto> newBooksHaveId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newBooks)) {
            // 待插入数据
            List<BookDto> insertBooks = new ArrayList<>();
            // 重复数据
            List<BookDto> repeatList = new ArrayList<>();
            // set去重
            Set<String> isbnSet = new HashSet<>();
            // 分离待插入和重复书籍
            for (BookDto bookDto : newBooks) {
                if (isbnSet.add(bookDto.getBookIsbn())) {
                    insertBooks.add(bookDto);
                } else {
                    repeatList.add(bookDto);
                }
            }
            // 批量插入
            saveBatch(insertBooks);
            // 分组
            Map<String, BookDto> insertMap =
                insertBooks.stream().collect(Collectors.toMap(BookDto::getBookIsbn, Function.identity()));
            // 为重复书籍数据获取相同的主键id
            for (BookDto bookDto : repeatList) {
                BookDto insertBookDtoHaveId = insertMap.get(bookDto.getBookIsbn());
                bookDto.setId(insertBookDtoHaveId.getId());
            }
            // 将所有存在id的数据放入集合。
            newBooksHaveId.addAll(insertBooks);
            newBooksHaveId.addAll(repeatList);
        }
        // 批量更新
        if (CollectionUtils.isNotEmpty(updateBooks)) {
            updateBatchById(updateBooks);
        }
        List<BookDto> all = new ArrayList<>();
        all.addAll(newBooksHaveId);
        all.addAll(updateBooks);
        String uuid = UUID.randomUUID().toString();
        try {
            // 抖音批量插入销量信息
            if (sourceType == BookSourceType.DOUYING.getValue()) {
                // 查询前一天收集的数据，计算飙升指数
                List<RankingDouyinVo> lastDayRankInfo = rankingDouyinMapper
                    .selectLastDayRankInfo(DateUtil.DateToLocalDate(collectTime).minusDays(Constants.NUM_ONE));
                Map<String, List<RankingDouyinVo>> lastDayRankMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(lastDayRankInfo)) {
                    lastDayRankMap = lastDayRankInfo.stream()
                        .collect(Collectors.groupingBy(x -> x.getBookShop().concat(String.valueOf(x.getBookId()))));
                }
                List<RankingDouyinDto> rankingDouyinDtos =
                    buildRankInfo(RankingDouyinDto.class, all, null, uuid, date, collectTime, lastDayRankMap);
                rankingDouyinService.saveBatch(rankingDouyinDtos);
                // 京东批量插入销量信息
            } else if (sourceType == BookSourceType.JINGDONG.getValue()) {
                // 将待导入数据，根据书籍isbn码分组
                Map<String, BookExcelBo> excelBoMap =
                    bookExcelBos.stream().collect(Collectors.toMap(BookExcelBo::getBookIsbn, x -> x, (x, y) -> y));
                List<RankingJingdongDto> rankingJingdongDtos =
                    buildRankInfo(RankingJingdongDto.class, all, excelBoMap, uuid, date, collectTime, null);
                rankingJingdongService.saveBatch(rankingJingdongDtos);
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 根据isbn获取凤凰本版书销量数据
     * 
     * @param querySmartSaleBo
     * @return 返回数据的格式(key：月份yyyy-MM, value：销量)
     */
    @Override
    public Map<String, Integer> getMonthSalesByIsbnOfSmart(QuerySmartSaleBo querySmartSaleBo) {
        List<SmartSaleInfo> salesCountByIsbns = bookMapper.getSalesCountByIsbnOfSmart(querySmartSaleBo);
        Map<String, Integer> smartSalesMap = Maps.newHashMapWithExpectedSize(ConstantsInteger.NUM_12);
        if (CollectionUtils.isNotEmpty(salesCountByIsbns)) {
            for (SmartSaleInfo smartSaleInfo : salesCountByIsbns) {
                YearMonth yearMonth = YearMonth.of(smartSaleInfo.getRankingYear(), smartSaleInfo.getRankingMonth());
                String yearMonthStr = yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                smartSalesMap.put(yearMonthStr, smartSaleInfo.getMonthSalesCount());
            }
        }
        return smartSalesMap;
    }

    @Override
    public void fzBookSync(String startTime, String endTime) {
        RLock lock = redissonClient.getLock("fzBook:sync");
        try {
            lock.lock(LOCK_TIME, TimeUnit.MINUTES);
            int page = 1;
            BookConditionBo bookConditionBo = new BookConditionBo();
            bookConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            bookConditionBo.setSourceType(BookSourceType.FZBOOK.getValue());
            List<BookVo> existsFzBookVos = getBookListByCondition(bookConditionBo);
            Map<String, BookVo> existsFzBookVoMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(existsFzBookVos)) {
                existsFzBookVoMap = existsFzBookVos.stream().collect(Collectors.toMap(BookVo::getBookIsbn, f -> f, (v1, v2) -> v1));
            }
            // 查询第一页数据
            String url = syncUrl.concat("?startTime=").concat(startTime)
                    .concat("&endTime=").concat(endTime)
                    .concat("&page=").concat(String.valueOf(page))
                    .concat("&size=").concat(String.valueOf(syncSize));
            JSONObject result = HttpUtil.doGet(url);
            FzBookSyncResultVo syncResultVo = JSON.parseObject(result.toJSONString(), FzBookSyncResultVo.class);
            if (syncResultVo == null || syncResultVo.getNums() == null || syncResultVo.getNums() <= 0) {
                return;
            }
            // 计算页数
            Integer pageSize = syncResultVo.getNums() / syncSize;
            if (syncResultVo.getNums() % syncSize > 0) {
                pageSize += 1;
            }
            for (page = 1; page <= pageSize; page ++) {
                // 第一页不重新获取数据
                if (page > 1) {
                    url = syncUrl.concat("?startTime=").concat(startTime)
                            .concat("&endTime=").concat(endTime)
                            .concat("&page=").concat(String.valueOf(page))
                            .concat("&size=").concat(String.valueOf(syncSize));
                    result = HttpUtil.doGet(url);
                    syncResultVo = JSON.parseObject(result.toJSONString(), FzBookSyncResultVo.class);
                }
                List<FzBookVo> fzBookVos = syncResultVo.getData();
                if (CollectionUtil.isEmpty(fzBookVos)) {
                    return;
                }
                // 保存、更新
                List<BookDto> addList = Lists.newArrayList();
                List<BookDto> updateList = Lists.newArrayList();
                Map<String, BookDto> addBooksMap = Maps.newHashMap();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                for (FzBookVo fzBookVo : fzBookVos) {
                    BookDto entity = new BookDto();
                    // 参数转换
                    entity.setBookName(fzBookVo.getSysTopic());
                    entity.setBookAuthor(fzBookVo.getSysAuthors());
                    String category = fzBookVo.getBookCat();
                    if (StringUtils.isNotBlank(category) && category.contains("~")) {
                        category = category.substring(category.lastIndexOf("~") + 1);
                    }
                    entity.setBookCategory(category);
                    entity.setBookCategoryAll(fzBookVo.getBookCat());
                    entity.setBookLabel(fzBookVo.getBookCat());
                    entity.setPublishName(fzBookVo.getPressName());
                    if (fzBookVo.getPubdate() != null) {
                        entity.setPublishTimeStr(sdf.format(fzBookVo.getPubdate()));
                    }
                    entity.setBookIsbn(fzBookVo.getIsbn());
                    String bookCover = fzBookVo.getPicurl();
                    if (StringUtils.isNotBlank(bookCover)) {
                        if (bookCover.contains(",")) {
                            bookCover = bookCover.split(",")[ConstantsInteger.NUM_0];
                        }
                        // 图片IP->域名
                        bookCover = bookCover.replaceAll("http://*************:28080", "https://chat.ppm.cn/proxy_tsk_img");
                    }
                    entity.setBookCover(bookCover);
                    entity.setBookThirdId(fzBookVo.getSysDocumentid());
                    entity.setSourceType(BookSourceType.FZBOOK.getValue());
                    entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                    entity.setUpdateTime(new Date());
                    if (existsFzBookVoMap.containsKey(entity.getBookIsbn())) {
                        entity.setId(existsFzBookVoMap.get(entity.getBookIsbn()).getId());
                        updateList.add(entity);
                    } else {
                        entity.setCreateTime(new Date());
                        addBooksMap.put(entity.getBookIsbn(), entity);
                    }
                }
                if (CollectionUtil.isNotEmpty(addBooksMap)) {
                    for (String bookIsbn : addBooksMap.keySet()) {
                        addList.add(addBooksMap.get(bookIsbn));
                    }
                    saveBatch(addList);
                }
                if (CollectionUtil.isNotEmpty(updateList)) {
                    updateBatchById(updateList);
                }
            }
        } catch (Exception e) {
            log.error("fzBookSync error:" + e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<BookVo> getCozeBookListByCondition(BookConditionBo conditionBo) {
        return bookMapper.getCozeBookListByCondition(conditionBo);
    }

    /**
     * 构建书籍信息
     *
     * @param bookExcelBo excel对象
     * @param sourceType 渠道 8 抖音 9 京东
     * @param bookVo 原书籍（更新时才会传）
     * @return
     */
    private BookDto buildImportBookDTO(BookExcelBo bookExcelBo, Integer sourceType, BookVo bookVo, Date date) {
        BookDto bookDto = new BookDto();
        BeanUtils.copyProperties(bookExcelBo, bookDto);
        // 更新
        if (Objects.nonNull(bookVo)) {
            bookDto.setId(bookVo.getId());
            bookDto.setUpdateTime(date);
            // 插入
        } else {
            bookDto.setCreateTime(date);
            bookDto.setUpdateTime(date);
            bookDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        }
        // 抖音数据：处理销量字符串
        if (sourceType == BookSourceType.DOUYING.getValue()) {
            String handlerNumStr = handlerNumStr(bookDto.getSalesCount());
            bookDto.setSalesCount(handlerNumStr);
        }
        // 手动导入
        bookDto.setInputType(BookInputType.MANUAL.getValue());
        bookDto.setSourceType(sourceType);
        return bookDto;
    }

    /**
     * @param tClass 类型，抖音/京东
     * @param bookDtoList
     * @param excelBoMap
     * @param collectTime
     * @param <T>
     * @param lastDayRankMap 前一天的排名数据
     * @return
     */
    private <T> List<T> buildRankInfo(Class<T> tClass, List<BookDto> bookDtoList, Map<String, BookExcelBo> excelBoMap,
        String uuid, Date date, Date collectTime, Map<String, List<RankingDouyinVo>> lastDayRankMap)
        throws InstantiationException, IllegalAccessException {
        if (CollectionUtils.isEmpty(bookDtoList)) {
            throw new BusinessException("bookDtoList不能为空");
        }
        List<T> list = new ArrayList<>();
        for (BookDto bookDto : bookDtoList) {
            T t = tClass.newInstance();
            if (t instanceof RankingDouyinDto) {
                RankingDouyinDto rankingDouyinDto = (RankingDouyinDto)t;
                List<RankingDouyinVo> rankingDouyinVos =
                    lastDayRankMap.get(bookDto.getBookShop().concat(String.valueOf(bookDto.getId())));
                if (CollectionUtils.isNotEmpty(rankingDouyinVos)) {
                    RankingDouyinVo lastDayRankingDouyinVo = null;
                    if (rankingDouyinVos.size() > Constants.NUM_ONE) {
                        List<RankingDouyinVo> sortedList = rankingDouyinVos.stream()
                            .sorted(Comparator.comparing(RankingDouyinVo::getCreateTime)).collect(Collectors.toList());
                        lastDayRankingDouyinVo = sortedList.get(sortedList.size() - Constants.NUM_ONE);
                    } else {
                        lastDayRankingDouyinVo = rankingDouyinVos.get(ConstantsInteger.NUM_0);
                    }
                    // 昨日销量
                    BigDecimal lastDaySalesCount = new BigDecimal(lastDayRankingDouyinVo.getSalesCount());
                    // 今日销量
                    BigDecimal todaySalesCount = new BigDecimal(bookDto.getSalesCount());
                    // 计算飙升指数，四舍五入保留1位小数，，如果前一天销量为0，则为0。
                    BigDecimal risingIndex =
                        lastDaySalesCount.compareTo(BigDecimal.ZERO) == Constants.ZERO ? BigDecimal.ZERO
                            : todaySalesCount.divide(lastDaySalesCount, ConstantsInteger.NUM_1, RoundingMode.HALF_UP);
                    rankingDouyinDto.setRisingIndex(risingIndex);

                }

                rankingDouyinDto.setBookId(bookDto.getId());
                rankingDouyinDto.setBookShop(bookDto.getBookShop());
                rankingDouyinDto.setSortIndex(Constants.ZERO);
                rankingDouyinDto.setRankingUp(Constants.ZERO);
                rankingDouyinDto.setRankingDown(Constants.ZERO);
                rankingDouyinDto.setRemark(bookDto.getRemark());
                rankingDouyinDto.setSalesCount(bookDto.getSalesCount());
                rankingDouyinDto.setUuid(uuid);
                rankingDouyinDto.setCreateTime(date);
                rankingDouyinDto.setUpdateTime(date);
                rankingDouyinDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                rankingDouyinDto.setCollectTime(collectTime);
            } else if (t instanceof RankingJingdongDto) {
                BookExcelBo bookExcelBo = excelBoMap.get(bookDto.getBookIsbn());
                RankingJingdongDto rankingJingdongDto = (RankingJingdongDto)t;
                rankingJingdongDto.setBookId(bookDto.getId());
                rankingJingdongDto.setBookShop(bookDto.getBookShop());
                rankingJingdongDto.setSortIndex(Constants.ZERO);
                rankingJingdongDto.setRankingUp(Constants.ZERO);
                rankingJingdongDto.setRankingDown(Constants.ZERO);
                rankingJingdongDto.setUuid(uuid);
                rankingJingdongDto.setRemark(bookDto.getRemark());
                if (bookExcelBo instanceof JingDongExcelBo) {
                    String commentCountStr = ((JingDongExcelBo)bookExcelBo).getCommentCount();
                    rankingJingdongDto.setCommentCount(handlerNumStr(commentCountStr));
                }
                rankingJingdongDto.setCreateTime(date);
                rankingJingdongDto.setUpdateTime(date);
                rankingJingdongDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                rankingJingdongDto.setCollectTime(collectTime);
            }
            list.add(t);
        }

        return list;
    }

    private String handlerNumStr(String numStr) {
        if (StringUtils.isNotBlank(numStr)) {
            if (numStr.endsWith("万")) {
                String num = numStr.trim();
                BigDecimal sales = new BigDecimal(num.substring(0, num.length() - 1));
                return sales.multiply(new BigDecimal(10000)).toString();
            } else
                return numStr;
        } else {
            throw new BusinessException("数量格式错误");
        }
    }

}