package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.dto.BookSumEsEntity;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.vo.PageVo;
import com.fh.ai.business.mapper.BookSumEsRepository;
import com.fh.ai.business.mapper.UserPrizeMapper;
import com.fh.ai.business.service.IBookSumEsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 图书接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
@Slf4j
@Service
public class BookSumEsServiceImpl extends ServiceImpl<UserPrizeMapper, UserPrizeDto> implements IBookSumEsService {

    @Resource
    private BookSumEsRepository bookSumEsRepository;

    @Override
    public PageVo<BookSumEsEntity> page(UserPrizeConditionBo conditionBo) {

        BookSumEsEntity bookSumEsEntity = new BookSumEsEntity();

        PageVo<BookSumEsEntity> bookSumEsEntityPageVo = null;
        try {
            bookSumEsEntityPageVo = bookSumEsRepository.page(bookSumEsEntity, conditionBo.getLimit().intValue(), conditionBo.getLastRow());
        } catch (Exception e) {
            log.error("查询失败! ", e);
        }
        return bookSumEsEntityPageVo;
    }
}