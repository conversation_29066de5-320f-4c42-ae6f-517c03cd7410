package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.business.entity.bo.calendarEvent.CalendarEventBo;
import com.fh.ai.business.entity.bo.calendarEvent.CalendarEventConditionBo;
import com.fh.ai.business.entity.dto.calendarEvent.CalendarEventDto;
import com.fh.ai.business.entity.vo.calendarEvent.CalendarEventVo;
import com.fh.ai.business.mapper.CalendarEventMapper;
import com.fh.ai.business.service.ICalendarEventService;
import com.fh.ai.common.enums.CalendarEventType;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日历事件表接口实现类
 *
 * <AUTHOR>
 * @date 2024-07-02 14:21:19
 */
@Service
public class CalendarEventServiceImpl extends ServiceImpl<CalendarEventMapper, CalendarEventDto> implements ICalendarEventService {

	@Resource
	private CalendarEventMapper calendarEventMapper;
	
    @Override
	public Map<String, Object> getCalendarEventListByCondition(CalendarEventConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<CalendarEventVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = calendarEventMapper.getCalendarEventListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<CalendarEventVo> calendarEventVos = calendarEventMapper.getCalendarEventListByCondition(conditionBo);
            PageInfo<CalendarEventVo> pageInfo = new PageInfo<>(calendarEventVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
	}

	@Override
	public AjaxResult addCalendarEvent(CalendarEventBo calendarEventBo) {
		CalendarEventDto calendarEvent = new CalendarEventDto();
		BeanUtils.copyProperties(calendarEventBo, calendarEvent);

		calendarEvent.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		calendarEvent.setCreateTime(new Date());
        save(calendarEvent);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult updateCalendarEvent(CalendarEventBo calendarEventBo) {
		CalendarEventDto calendarEvent = new CalendarEventDto();
		BeanUtils.copyProperties(calendarEventBo, calendarEvent);

		calendarEvent.setUpdateTime(new Date());
        updateById(calendarEvent);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<CalendarEventDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(CalendarEventDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(CalendarEventDto::getId, id);

        CalendarEventDto calendarEvent = getOne(lqw);
        if(null == calendarEvent) {
            return AjaxResult.fail("日历事件表数据不存在");
        }

		CalendarEventVo calendarEventVo = new CalendarEventVo();
		BeanUtils.copyProperties(calendarEvent, calendarEventVo);

		return AjaxResult.success(calendarEventVo);
	}

    @Override
    public AjaxResult deleteCalendarEvent(CalendarEventBo calendarEventBo) {
        // 删除信息
        LambdaQueryWrapper<CalendarEventDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CalendarEventDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(CalendarEventDto::getId, calendarEventBo.getId());

        CalendarEventDto calendarEvent = getOne(lqw);
        if(null == calendarEvent) {
            return AjaxResult.fail("日历事件表数据不存在");
        }

        CalendarEventDto dto = new CalendarEventDto();
        dto.setId(calendarEvent.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(calendarEventBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public Map<String, Object> getCalendarEventListWithHistoryEvents(CalendarEventConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<CalendarEventVo> list = null;

        // 不分页（查询全部）
        list = calendarEventMapper.getCalendarEventListByCondition(conditionBo);

        // 查询历史上的今天事件
        List<Integer> types = Lists.newArrayList();
        if (conditionBo.getType() != null) {
            types.add(conditionBo.getType());
        }
        if (CollectionUtil.isNotEmpty(conditionBo.getTypes())) {
            types.addAll(conditionBo.getTypes());
        }
        // 不查询历史上的今天 直接返回
        if (CollectionUtil.isNotEmpty(types) && !types.contains(CalendarEventType.HISTORY.getValue())) {
            map.put("list", list);
            return map;
        }

        list.addAll(getHistoryEventsList(conditionBo));
        map.put("list", list);
        return map;
    }

    /**
     * 查询历史上的今天事件
     *
     * @param conditionBo
     * @return java.util.List<com.fh.ai.business.entity.vo.calendarEvent.CalendarEventVo>
     * <AUTHOR>
     * @date 2024/11/18 14:46
     **/
    private List<CalendarEventVo> getHistoryEventsList(CalendarEventConditionBo conditionBo) {
        if (conditionBo.getStartTime() != null && conditionBo.getEndTime() != null) {
            conditionBo.setHistoryQueryDays(getHistoryQueryDays(conditionBo.getStartTime(), conditionBo.getEndTime()));
            conditionBo.setStartTime(null);
            conditionBo.setEndTime(null);
        }
        conditionBo.setType(CalendarEventType.HISTORY.getValue());
        List<CalendarEventVo> historyList = calendarEventMapper.getCalendarEventListByCondition(conditionBo);

        // 查询为空 直接返回
        if (CollectionUtil.isEmpty(historyList)) {
            return new ArrayList<>();
        }

        // 查询全部 直接返回
        if (CollectionUtil.isEmpty(conditionBo.getHistoryQueryDays())) {
            return historyList;
        }

        List<CalendarEventVo> historyEvents = Lists.newArrayList();
        for (Date day : conditionBo.getHistoryQueryDays()) {
            SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
            List<CalendarEventVo> dayHistoryList = historyList.stream()
                    .filter(x -> x.getYear().toString().concat("-").concat(sdf.format(day))
                            .equals(x.getDay()))
                    .sorted(Comparator.comparing(CalendarEventVo::getYear)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dayHistoryList)) {
                CalendarEventVo calendarEventVo = new CalendarEventVo();
                calendarEventVo.setDay(DateUtil.format(day, "yyyy-MM-dd"));
                calendarEventVo.setType(CalendarEventType.HISTORY.getValue());
                calendarEventVo.setHistoryEventList(dayHistoryList);
                historyEvents.add(calendarEventVo);
            }
        }

        return historyEvents;
    }

    /**
     * 获取查询区间每一天
     *
     * @param startDay
     * @param endDay
     * @return java.util.List<java.util.Date>
     * <AUTHOR>
     * @date 2024/11/18 14:23
     **/
    private List<Date> getHistoryQueryDays(Date startDay, Date endDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDay);

        List<Date> dates = new ArrayList<>();
        while (calendar.getTime().compareTo(endDay) <= 0) {
            dates.add(calendar.getTime());
            calendar.add(Calendar.DATE, 1);
        }

        return dates;
    }

}