package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.ai.business.entity.bo.config.ConfigBo;
import com.fh.ai.business.entity.bo.config.ConfigConditionBo;
import com.fh.ai.business.entity.dto.config.ConfigDto;
import com.fh.ai.business.entity.vo.config.ConfigVo;
import com.fh.ai.business.mapper.ConfigMapper;
import com.fh.ai.business.service.IConfigService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.RedisKeyEnum;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 配置表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-02 09:40:20
 */
@Slf4j
@Service
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigDto> implements IConfigService {

	@Resource
	private ConfigMapper configMapper;

	@Autowired
	private RedisComponent redisComponent;


	@PostConstruct
	public void initConfig(){
		boolean lock = redisComponent.lockKey(RedisKeyEnum.CONFIG_LOCK_KEY.getValue());
		try{
			// 查询所有系统配置
			List<ConfigDto> list = list();
			Map<String, Object> map = list.stream().collect(Collectors.toMap(ConfigDto::getConfigKey, ConfigDto::returnOwn));
			//删除key
			redisComponent.del(RedisKeyEnum.CONFIG_KEY.getValue());
			// 数据放入redis
			redisComponent.hmset(RedisKeyEnum.CONFIG_KEY.getValue(), map);
		}catch (Exception e){
			log.info("系统参数缓存存入失败｛｝：", e.getMessage());
		}finally {
			redisComponent.releaseLock(RedisKeyEnum.CONFIG_LOCK_KEY.getValue());
		}
	}

	public boolean refreshSingleConfig(String configKey) {
		boolean result = false;
		try{
			// 查询参数配置
			ConfigDto config = getConfigByKeyFromDB(configKey);
			if(null!=config) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put(configKey, config.returnOwn());
				result = redisComponent.hmset(RedisKeyEnum.CONFIG_KEY.getValue(), map);
			}
		}catch (Exception e) {
			log.info("刷新系统参数配置失败｛｝：", configKey);
		}finally {
			return result;
		}
	}


	@Override
	public List<ConfigVo> getConfigListByCondition(ConfigConditionBo condition) {
		return configMapper.getConfigListByCondition(condition);
	}

	@Override
	@Transactional
	public AjaxResult addConfig(ConfigBo configBo) {
		ConfigDto config = new ConfigDto();
		BeanUtils.copyProperties(configBo, config);
		// 校验key是否唯一
		LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<ConfigDto>();
		lqw.eq(ConfigDto::getConfigKey, configBo.getConfigKey()).eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		if(null!=configMapper.selectOne(lqw)) {
			return AjaxResult.fail("配置参数key已存在，无法保存");
		}
		if(save(config)){
			// 新增数据之后刷新缓存
			if(refreshSingleConfig(config.getConfigKey())) {
				return AjaxResult.success("保存成功");
			}else{
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return AjaxResult.fail("刷新缓存失败");
			}
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	@Transactional
	public AjaxResult updateConfig(ConfigBo configBo) {
		ConfigDto config = new ConfigDto();
		BeanUtils.copyProperties(configBo, config);
		// 校验key是否唯一
		LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<ConfigDto>();
		lqw.eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(ConfigDto::getId, configBo.getId());
		ConfigDto configDto = configMapper.selectOne(lqw);
		if(Objects.isNull(configDto)) {
			return AjaxResult.fail("配置参数不存在或已删除");
		}
		if(updateById(config)){
			// 修改数据之后刷新缓存
			if(refreshSingleConfig(configDto.getConfigKey())) {
				return AjaxResult.success("保存成功");
			}else{
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return AjaxResult.fail("刷新缓存失败");
			}
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long configId) {
		LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ConfigDto::getId, configId);
		lqw.eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		ConfigDto config = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("configVo", config==null?new ConfigVo():config);
		return reuslt;
	}

	@Override
	public ConfigDto getConfigByKey(String configKey) {
		ConfigDto config = null;
		//从缓存中获取
		Object hget = null;
		try {
			hget = redisComponent.hget(RedisKeyEnum.CONFIG_KEY.getValue(), configKey);
		}catch (Exception e){
			throw new BusinessException(e.getMessage());
		}
		Map<String, Object> map = (Map<String, Object>)hget;
		if(null != map ) {
			config = BeanUtil.mapToBean(map, ConfigDto.class, new CopyOptions());
		}
		if(null == config) {//从数据库查询
			LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<>();
			lqw.eq(ConfigDto::getConfigKey, configKey);
			lqw.eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
			config = configMapper.selectOne(lqw);
			if(null != config) {
				return config;
			}
			return null;
		}else{
			return config;
		}
	}


	@Override
	public ConfigDto getConfigFromRedis(String configKey) {
		Map<String, Object> map = (Map<String, Object>)redisComponent.hget(RedisKeyEnum.CONFIG_KEY.getValue(), configKey);
		if(null != map) {
			ConfigDto config = BeanUtil.mapToBean(map, ConfigDto.class, new CopyOptions());
			return config;
		}else {
			return null;
		}
	}

	@Override
	public AjaxResult refresh() {
		try{
			initConfig();
			return AjaxResult.fail("刷新成功");
		}catch (Exception e) {
			log.error("刷新配置信息缓存失败{}");
		}
		return AjaxResult.fail("刷新失败");
	}

	@Override
	public AjaxResult getConfigValue(String configKey) {
		ConfigDto config = null;
		//从缓存中获取
		Map<String, Object> map = (Map<String, Object>)redisComponent.hget(RedisKeyEnum.CONFIG_KEY.getValue(), configKey);
		if(null != map ) {
			config = BeanUtil.mapToBean(map, ConfigDto.class, new CopyOptions());
		}
		if(null == config) {
			LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<>();
			lqw.eq(ConfigDto::getConfigKey, configKey);
			config = configMapper.selectOne(lqw);
			if(null != config) {
				return AjaxResult.success(config.getConfigValue());
			}
			return AjaxResult.success(null);
		}else{
			return AjaxResult.success(config.getConfigValue());
		}
	}

	@Override
	public AjaxResult updateByConfigKey(ConfigBo configBo) {
		final String configKey = configBo.getConfigKey();
		final ConfigDto configDto = getByConfigKey(configKey);
		if(configDto == null){
			return AjaxResult.fail("数据不存在");
		}
		configDto.setConfigValue(configBo.getConfigValue());
		configDto.setConfigDescription(configBo.getConfigDescription());

		if(updateById(configDto)){
			// 修改数据之后刷新缓存
			if(refreshSingleConfig(configKey)) {
				return AjaxResult.success("保存成功");
			}else{
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return AjaxResult.fail("刷新缓存失败");
			}
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ConfigDto getConfigByKeyFromDB(String configKey) {
		LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ConfigDto::getConfigKey,configKey)
				.eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		ConfigDto config = configMapper.selectOne(lqw);
		if(null != config) {
			return config;
		}
		return null;
	}


	public ConfigDto getByConfigKey(String configKey){
		LambdaQueryWrapper<ConfigDto> lqw = new LambdaQueryWrapper<ConfigDto>();
		lqw.eq(ConfigDto::getConfigKey, configKey);
		lqw.eq(ConfigDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		return configMapper.selectOne(lqw);
	}



}