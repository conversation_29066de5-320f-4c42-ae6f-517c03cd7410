package com.fh.ai.business.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.ai.common.enums.*;
import com.fh.ai.common.ffmpeg.FfmpegService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.conversation.ConversationBo;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileBo;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileConditionBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.dto.attachment.AttachmentDto;
import com.fh.ai.business.entity.dto.conversation.ConversationDto;
import com.fh.ai.business.entity.dto.conversationFile.ConversationFileDto;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.dto.tip.TipDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.business.entity.vo.conversationFile.AudioConvertVo;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.business.mapper.ConversationFileMapper;
import com.fh.ai.business.mapper.ConversationMapper;
import com.fh.ai.business.mapper.HistoryAppMapper;
import com.fh.ai.business.mapper.TipMapper;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IConversationFileService;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IFileContentCacheService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.aippt.AipptRequestCallableTask;
import com.fh.ai.common.aippt.AipptUtil;
import com.fh.ai.common.aippt.bo.AipptBo;
import com.fh.ai.common.aippt.vo.AipptDetailVo;
import com.fh.ai.common.aippt.vo.AipptFileVo;
import com.fh.ai.common.ark.ArkUtil;
import com.fh.ai.common.coze.CozeUtil;
import com.fh.ai.common.enums.ConversationStateTypeEnum;
import com.fh.ai.common.enums.ConversationTaskTypeEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.QueryTaskResultFlagType;
import com.fh.ai.common.enums.QwenStateEnum;
import com.fh.ai.common.enums.SubTypeEnum;
import com.fh.ai.common.enums.TypeEnum;
import com.fh.ai.common.enums.XfStateEnum;
import com.fh.ai.common.fileextract.FileextractUtil;
import com.fh.ai.common.kimi.KimiUtil;
import com.fh.ai.common.qwen.QwenUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.xfyun.XfyunUtil;
import com.fh.ai.common.xfyun.bo.XfyunBo;
import com.fh.ai.common.xfyun.vo.Content;
import com.fh.ai.common.xfyun.vo.Cw;
import com.fh.ai.common.xfyun.vo.Lattice;
import com.fh.ai.common.xfyun.vo.Rt;
import com.fh.ai.common.xfyun.vo.St;
import com.fh.ai.common.xfyun.vo.TransferInfo;
import com.fh.ai.common.xfyun.vo.UploadContent;
import com.fh.ai.common.xfyun.vo.UploadInfo;
import com.fh.ai.common.xfyun.vo.Ws;
import com.fh.ai.common.zhipuai.ContextBo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话文件表接口实现类
 *
 * <AUTHOR>
 * @date 2024-06-17 14:42:54
 */
@Slf4j
@Service
public class ConversationFileServiceImpl extends ServiceImpl<ConversationFileMapper, ConversationFileDto>
    implements IConversationFileService {
    @Value("${filepath.windows}")
    private String windowsPath;
    @Value("${filepath.linux}")
    private String linuxPath;

    @Resource
    private ConversationFileMapper conversationFileMapper;
    @Resource
    private HistoryAppMapper historyAppMapper;
    @Autowired
    private IAttachmentService attachmentService;
    @Autowired
    private QwenUtil qwenUtil;
    @Autowired
    private KimiUtil kimiUtil;
    @Resource
    private ConversationMapper conversationMapper;
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private XfyunUtil xfyunUtil;
    @Autowired
    private IOrganizationService organizationService;
    @Resource
    private AipptUtil aipptUtil;
    @Resource
    private CozeUtil cozeUtil;
    @Resource
    private FileextractUtil fileextractUtil;
    @Resource
    private IFileContentCacheService fileContentCacheService;

    /**
     * 企业套餐额度校验/扣减开关 默认false关闭
     */
    @Value("${organization.quota.reduce.switch:false}")
    public Boolean ORGANIZATION_QUOTA_REDUCE_SWITCH;

    @Resource
    private FfmpegService ffmpegService;

    @Value("${ffmpeg.switch:false}")
    private boolean ffmpegSwitch;

    @Override
    public Map<String, Object> getConversationFileListByCondition(ConversationFileConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ConversationFileVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = conversationFileMapper.getConversationFileListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ConversationFileVo> conversationFileVos =
                conversationFileMapper.getConversationFileListByCondition(conditionBo);
            PageInfo<ConversationFileVo> pageInfo = new PageInfo<>(conversationFileVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        // 返回结果处理
        if (CollectionUtils.isNotEmpty(list)
            && Objects.equals(conditionBo.getQueryTaskResultFlag(), QueryTaskResultFlagType.QUERY_NO.getCode())) {
            list.stream().forEach(conversationFileVo -> conversationFileVo.setTaskResult(null));
        }
        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public List<ConversationFileVo> getConversationFileListByConditionNoPage(ConversationFileConditionBo conditionBo) {
        List<ConversationFileVo> list = conversationFileMapper.getConversationFileListByCondition(conditionBo);
        return list;
    }

    @Override
    public AjaxResult uploadFile(MultipartFile multipartFile, ConversationFileBo conversationFileBo) {
        // 先将文件上传到智灵服务器
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(conversationFileBo.getCreateBy());
        attachmentBo.setUseOriginName(true);
        AjaxResult ajaxResult = attachmentService.uploadFile(multipartFile, attachmentBo);
        if (ajaxResult.failed()) {
            return ajaxResult;
        }

        AttachmentVo attachmentVo = (AttachmentVo)ajaxResult.getData();

        RLock lock = redissonClient.getLock("conversation:uploadFile:" + conversationFileBo.getConversationCode());
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");
            // 接口调用的幂等性：无论接口被调用多少次，以下业务执行一次
            // 保存对话及文件
            LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(ConversationDto::getConversationCode, conversationFileBo.getConversationCode());
            if (null != conversationFileBo.getType()) {
                lqw.eq(ConversationDto::getType, conversationFileBo.getType());
            }
            ConversationDto conversationDto = conversationMapper.selectOne(lqw);
            if (null == conversationDto) {
                conversationDto = new ConversationDto();
                conversationDto.setConversationCode(conversationFileBo.getConversationCode());
                conversationDto.setUserOid(conversationFileBo.getUserOid());
                conversationDto.setOrganizationId(conversationFileBo.getOrganizationId());
                conversationDto.setType(conversationFileBo.getType());
                conversationDto.setChannel(conversationFileBo.getChannel());
                conversationDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                conversationDto.setCreateTime(new Date());
                conversationDto.setCreateBy(conversationFileBo.getCreateBy());
                conversationDto.setCustomParameterJson(conversationFileBo.getCustomParameterJson());
                // 设置标题
                if (StringUtils.isNotBlank(conversationFileBo.getTitle())) {
                    conversationDto.setTitle(conversationFileBo.getTitle());
                }
                conversationMapper.insert(conversationDto);
            }

            ConversationFileDto conversationFileDto = new ConversationFileDto();
            conversationFileDto.setConversationCode(conversationFileBo.getConversationCode());
            conversationFileDto.setType(conversationFileBo.getType());
            conversationFileDto.setUserOid(conversationFileBo.getUserOid());
            conversationFileDto.setOrganizationId(conversationFileBo.getOrganizationId());
            conversationFileDto.setFileName(attachmentVo.getOriginalName());
            conversationFileDto.setFileOid(attachmentVo.getOid());
            conversationFileDto.setChannel(conversationFileBo.getChannel());
            conversationFileDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            conversationFileDto.setCreateTime(new Date());
            conversationFileDto.setCreateBy(conversationFileBo.getCreateBy());
            conversationFileDto.setTaskType(conversationFileBo.getTaskType());

            conversationFileMapper.insert(conversationFileDto);

            // 根据appType判断（314文档解读助手或205智能PPT）改为根据taskType判断，方便切换类型。
            if (conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())
                || conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_COZE.getCode())
                || conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_KIMI.getCode())
                || conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_LOCAL.getCode())) {
                // 文档解读助手上传后就传到千问解析
                String originPath = attachmentVo.getOriginPath();
                // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
                String filePath = (SystemUtil.isWindows() ? windowsPath : linuxPath) + originPath;

                // 传到千问
                ThreadUtil.taskSplitExecute(new Runnable() {
                    @Override
                    public void run() {
                        Date now = new Date();
                        // 更新千问状态
                        ConversationFileDto conversationFile = new ConversationFileDto();
                        conversationFile.setId(conversationFileDto.getId());
                        if (conversationFileBo.getTaskType()
                            .equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())) {
                            conversationFile.setQwenState(QwenStateEnum.DEALING.getCode());
                        } else {
                            conversationFile.setTaskState(ConversationStateTypeEnum.DEALING.getCode());
                        }
                        conversationFile.setUpdateTime(now);
                        conversationFileMapper.updateById(conversationFile);

                        File file = new File(filePath);

                        // 千问
                        String fileCallBackId = "";
                        if (conversationFileBo.getTaskType()
                            .equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())) {
                            fileCallBackId = uploadQwenFile(file);
                        }
                        // coze
                        if (conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_COZE.getCode())) {
                            fileCallBackId = uploadCozeFile(file);
                        }
                        // kimi
                        if (conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_KIMI.getCode())) {
                            fileCallBackId = uploadKimiFile(file);
                        }
                        // local
                        if (conversationFileBo.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_LOCAL.getCode())) {
                            // 本地的第三方id就使用文件oid
                            fileCallBackId = conversationFileDto.getFileOid();
                        }
                        // 第三方文件id，放在taskId返回
                        attachmentVo.setTaskId(fileCallBackId);

                        if (StringUtils.isNotBlank(fileCallBackId)) {
                            // 成功
                            // 更新附件表
                            AttachmentDto attachmentDto = new AttachmentDto();
                            attachmentDto.setId(attachmentVo.getId());
                            if (conversationFileBo.getTaskType()
                                .equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())) {
                                attachmentDto.setQwenFileId(fileCallBackId);
                            } else {
                                attachmentDto.setTaskId(fileCallBackId);
                                attachmentDto.setTaskType(conversationFileBo.getTaskType());
                            }
                            attachmentDto.setUpdateTime(now);
                            attachmentService.updateById(attachmentDto);

                            // 更新千问处理结果表
                            ConversationFileDto conversationfile = new ConversationFileDto();
                            conversationfile.setId(conversationFileDto.getId());
                            if (conversationFileBo.getTaskType()
                                .equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())) {
                                conversationfile.setQwenFileId(fileCallBackId);
                                conversationfile.setQwenState(QwenStateEnum.FINISH.getCode());
                            } else {
                                conversationfile.setTaskId(fileCallBackId);
                                conversationfile.setTaskState(ConversationStateTypeEnum.FINISH.getCode());
                            }
                            // 如果是kimi则需要获取内容并更新
                            if (conversationFileBo.getTaskType()
                                .equals(ConversationTaskTypeEnum.FILE_QA_KIMI.getCode())) {
                                String content = kimiUtil.fileContent(fileCallBackId);
                                Long fileContentCacheId = fileContentCacheService.addFileContentCacheReturnId(
                                    conversationFileDto.getFileOid(), fileCallBackId, content);
                                // conversationfile.setTaskResult(content);
                                conversationfile.setFileContentCacheId(fileContentCacheId);
                            }
                            // 如果是本地则需要获取内容并更新
                            if (conversationFileBo.getTaskType()
                                .equals(ConversationTaskTypeEnum.FILE_QA_LOCAL.getCode())) {
                                String content = "";
                                try {
                                    content = fileextractUtil.extractFromLocalFile(filePath);
                                } catch (Exception e) {
                                    log.error("读取local文件[" + filePath + "]内容异常", e);
                                }
                                if (StringUtils.isBlank(content)) {
                                    // 本地获取内容失败，设置状态为失败
                                    conversationfile.setTaskState(ConversationStateTypeEnum.FAIL.getCode());
                                }
                                Long fileContentCacheId = fileContentCacheService.addFileContentCacheReturnId(
                                    conversationFileDto.getFileOid(), fileCallBackId, content);
                                // conversationfile.setTaskResult(content);
                                conversationfile.setFileContentCacheId(fileContentCacheId);
                            }
                            conversationfile.setUpdateTime(now);
                            conversationFileMapper.updateById(conversationfile);
                        } else {
                            // 失败
                            // 更新千问处理结果表
                            ConversationFileDto conversationfile = new ConversationFileDto();
                            conversationfile.setId(conversationFileDto.getId());
                            // conversationfile.setQwenFileId(fileCallBackId);
                            if (conversationFileBo.getTaskType()
                                .equals(ConversationTaskTypeEnum.FILE_QA_QIANWEN.getCode())) {
                                conversationfile.setQwenState(QwenStateEnum.FAIL.getCode());
                            } else {
                                conversationfile.setTaskId(fileCallBackId);
                                conversationfile.setTaskState(ConversationStateTypeEnum.FAIL.getCode());
                            }
                            conversationfile.setUpdateTime(now);
                            conversationFileMapper.updateById(conversationfile);
                        }
                    }
                });
            }
        } catch (Exception e) {
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }

        // JSONObject jsonObject = qwenUtil.uploadFile(file);
        //
        // String qwenFileId = null;
        // if (null != jsonObject && jsonObject.containsKey("id")) {
        // qwenFileId = jsonObject.getString("id");
        // }
        //
        // if (StringUtils.isBlank(qwenFileId)) {
        // return AjaxResult.fail("文件上传失败");
        // }

        // attachmentVo.setQwenFileId(qwenFileId);
        return AjaxResult.success(attachmentVo);
    }

    /**
     * 上传文件到千问并返回千问的文件id(或者叫taskId)
     * 
     * @param file
     * @return
     */
    public String uploadQwenFile(File file) {
        String qwenFileId = null;
        int time = 0;
        while (StringUtils.isBlank(qwenFileId)) {
            JSONObject jsonObject = qwenUtil.uploadFile(file);

            time++;

            if (null != jsonObject && jsonObject.containsKey("id")) {
                qwenFileId = jsonObject.getString("id");
            } else {
                if (time == 30) {
                    // 超过30次
                    break;
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("Thread.sleep(2000)异常", e);
                }
            }
        }
        return qwenFileId;
    }

    /**
     * 上传文件到coze并返回coze的文件id(或者叫taskId)
     * 
     * @param file
     * @return
     */
    public String uploadCozeFile(File file) {
        String cozeFileId = null;
        int time = 0;
        while (StringUtils.isBlank(cozeFileId)) {
            String fileId = cozeUtil.uploadFile(file);

            time++;

            if (null != fileId) {
                cozeFileId = fileId;
            } else {
                if (time == 30) {
                    // 超过30次
                    break;
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("Thread.sleep(2000)异常", e);
                }
            }
        }
        return cozeFileId;
    }

    /**
     * 上传文件到kimi并返回kimi的文件id(或者叫taskId)
     * 
     * @param file
     * @return
     */
    public String uploadKimiFile(File file) {
        String kimiFileId = null;
        int time = 0;
        while (StringUtils.isBlank(kimiFileId)) {
            JSONObject jsonObject = kimiUtil.uploadFile(file);

            time++;

            if (null != jsonObject && jsonObject.containsKey("id")) {
                kimiFileId = jsonObject.getString("id");
            } else {
                if (time == 30) {
                    // 超过30次
                    break;
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("Thread.sleep(2000)异常", e);
                }
            }
        }
        return kimiFileId;
    }

    @Override
    public AjaxResult changeState(ConversationFileBo conversationFileBo) {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getConversationCode, conversationFileBo.getConversationCode());
        if (null != conversationFileBo.getType()) {
            lqw.eq(ConversationFileDto::getType, conversationFileBo.getType());
        }
        lqw.eq(ConversationFileDto::getFileOid, conversationFileBo.getFileOid());
        ConversationFileDto conversationFile = conversationFileMapper.selectOne(lqw);
        if (null == conversationFile) {
            return AjaxResult.fail("转换文件不存在");
        }

        AttachmentVo attachmentVo = attachmentService.getDetail(conversationFileBo.getFileOid());
        if (null == attachmentVo) {
            return AjaxResult.fail("文件不存在");
        }
        ConversationFileDto conversationFileDto = new ConversationFileDto();
        conversationFileDto.setId(conversationFile.getId());
        conversationFileDto.setXfState(XfStateEnum.DEALING.getCode());
        conversationFileDto.setChannel(conversationFileBo.getChannel());
        conversationFileDto.setUpdateTime(new Date());
        conversationFileDto.setUpdateBy(conversationFileBo.getUpdateBy());

        conversationFileMapper.updateById(conversationFileDto);

        return AjaxResult.success();

    }

    @Override
    public AjaxResult xfConvert(ConversationFileBo conversationFileBo) {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getConversationCode, conversationFileBo.getConversationCode());
        if (null != conversationFileBo.getType()) {
            lqw.eq(ConversationFileDto::getType, conversationFileBo.getType());
        }
        lqw.eq(ConversationFileDto::getFileOid, conversationFileBo.getFileOid());
        ConversationFileDto conversationFile = conversationFileMapper.selectOne(lqw);
        if (null == conversationFile) {
            return AjaxResult.fail("转换文件不存在");
        }

        AttachmentVo attachmentVo = attachmentService.getDetail(conversationFileBo.getFileOid());
        if (null == attachmentVo) {
            return AjaxResult.fail("文件不存在");
        }

        String originPath = attachmentVo.getOriginPath();

        if (ffmpegSwitch) {
            long ffmpegStart = System.currentTimeMillis();
            String fileOri = (SystemUtil.isWindows() ? windowsPath : linuxPath) + originPath;
            String newFilePath;
            Integer ffmpegState;
            if (ffmpegService.checkNeedConvert(fileOri)) {
                String newFileName = attachmentVo.getNewName().replace("." + attachmentVo.getSuffix(), "")
                    .concat("_new.").concat(attachmentVo.getSuffix());
                newFilePath = attachmentVo.getOriginPath().replace(attachmentVo.getNewName(), newFileName);
                String newFileOri = (SystemUtil.isWindows() ? windowsPath : linuxPath) + newFilePath;
                boolean result = ffmpegService.convert(fileOri, newFileOri);
                ffmpegState = result ? FfmpegState.SUCCESS.getState() : FfmpegState.FAIL.getState();
                originPath = result ? newFilePath : originPath;
            } else {
                newFilePath = null;
                ffmpegState = FfmpegState.NOT_NEED_CONVERT.getState();
            }

            long ffmpegEnd = System.currentTimeMillis();
            log.info("fileOid:{}, ffmpegEnd use {} ms", attachmentVo.getOid(), ffmpegEnd - ffmpegStart);

            conversationFile.setFfmpegConvertResult(newFilePath);
            conversationFile.setFfmpegState(ffmpegState);
            conversationFileMapper.updateById(conversationFile);
        }

        // 文件存放路径，例如：D:\file\20221015\pdf\2ad26e33d9564aebbd2a26f5fcdfc37f.pdf
        String filePath = (SystemUtil.isWindows() ? windowsPath : linuxPath) + originPath;
        File file = new File(filePath);
        XfyunBo xfyunBo = new XfyunBo();
        xfyunBo.setHotWord(conversationFileBo.getHotWord());

        try {
            // 如果上传超时，则xf状态先是2--> 超时后更新为1
            UploadInfo uploadInfo = xfyunUtil.upload(file, xfyunBo);
            if (null == uploadInfo || null == uploadInfo.getContent()
                || StringUtils.isBlank(uploadInfo.getContent().getOrderId())) {
                ConversationFileDto conversationFileDto = new ConversationFileDto();
                conversationFileDto.setId(conversationFile.getId());
                conversationFileDto.setXfState(XfStateEnum.UPLOADED.getCode());
                conversationFileDto.setChannel(conversationFileBo.getChannel());
                conversationFileDto.setUpdateTime(new Date());
                conversationFileDto.setUpdateBy(conversationFileBo.getUpdateBy());
                conversationFileMapper.updateById(conversationFileDto);
                return AjaxResult.fail("转换失败");
            }

            UploadContent content = uploadInfo.getContent();
            String orderId = content.getOrderId();
            // 保存对话文件表
            ConversationFileDto conversationFileDto = new ConversationFileDto();
            conversationFileDto.setId(conversationFile.getId());
            conversationFileDto.setXfOrderId(orderId);
            conversationFileDto.setXfState(XfStateEnum.DEALING.getCode());
            conversationFileDto.setChannel(conversationFileBo.getChannel());
            conversationFileDto.setUpdateTime(new Date());
            conversationFileDto.setUpdateBy(conversationFileBo.getUpdateBy());
            conversationFileMapper.updateById(conversationFileDto);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("转换失败更新状态为上传中", e);
            ConversationFileDto conversationFileDto = new ConversationFileDto();
            conversationFileDto.setId(conversationFile.getId());
            conversationFileDto.setXfState(XfStateEnum.UPLOADED.getCode());
            conversationFileDto.setChannel(conversationFileBo.getChannel());
            conversationFileDto.setUpdateTime(new Date());
            conversationFileDto.setUpdateBy(conversationFileBo.getUpdateBy());
            conversationFileMapper.updateById(conversationFileDto);
            return AjaxResult.fail("转换失败");
        }
    }

    @Override
    public void getXfConvertResult() {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getXfState, XfStateEnum.DEALING.getCode());
        lqw.isNotNull(ConversationFileDto::getXfOrderId);

        List<ConversationFileDto> fileDtos = conversationFileMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(fileDtos)) {
            return;
        }

        Date now = new Date();
        for (ConversationFileDto conversationFileDto : fileDtos) {
            RLock lock = redissonClient.getLock("lock:XfConvertResult:" + conversationFileDto.getXfOrderId());
            lock.lock();
            try {
                ThreadUtil.taskXfExecute(new Runnable() {
                    @Override
                    public void run() {
                        ConversationFileDto cfd = conversationFileMapper.selectById(conversationFileDto.getId());
                        if (cfd.getXfState().equals(XfStateEnum.FINISH.getCode())) {
                            return;
                        }
                        // 这个接口有获取次数限制，同一个订单获取结果次数最多100，此时content为空
                        TransferInfo transferInfo = xfyunUtil.getResult(conversationFileDto.getXfOrderId());
                        String code = transferInfo.getCode();
                        Content content = transferInfo.getContent();
                        if(content == null){
                            log.error("讯飞转写失败：fileOid：" + conversationFileDto.getFileOid() + ";返回值："
                                + JSON.toJSONString(transferInfo));
                            // 更新附件表
                            ConversationFileDto conversationFile = new ConversationFileDto();
                            conversationFile.setId(conversationFileDto.getId());
                            conversationFile.setXfState(XfStateEnum.FAIL.getCode());
                            conversationFile.setUpdateTime(now);
                            conversationFileMapper.updateById(conversationFile);
                            return;
                        }

                        // 订单流程状态
                        // 0：订单已创建
                        // 3：订单处理中
                        // 4：订单已完成
                        // -1：订单失败
                        int status = transferInfo.getContent().getOrderInfo().getStatus();
                        if (status == 4) {
                            List<AudioConvertVo> convertList = getConvertList(transferInfo);

                            // 存入历史
                            HistoryAppDto historyAppDto = new HistoryAppDto();
                            historyAppDto.setConversationCode(conversationFileDto.getConversationCode());
                            historyAppDto.setUserOid(conversationFileDto.getUserOid());
                            historyAppDto.setOrganizationId(conversationFileDto.getOrganizationId());
                            historyAppDto.setType(conversationFileDto.getType());
                            historyAppDto.setSubType(SubTypeEnum.MEETING_TEXT.getCode());
                            // historyAppDto.setAskType(conversationVo.getAskType());
                            // historyAppDto.setOriginalQuestion(conversationVo.getOriginalQuestion());
                            // historyAppDto.setQuestion(conversationVo.getMessage());
                            // historyAppDto.setParameterJson(conversationVo.getParameterJson());
                            historyAppDto.setResult(JSON.toJSONString(convertList));
                            historyAppDto.setChannel(conversationFileDto.getChannel());
                            historyAppDto.setCreateBy(conversationFileDto.getUserOid());
                            historyAppDto.setCreateTime(now);
                            historyAppMapper.insert(historyAppDto);

                            // 处理会议推理
                            dealMeeting(conversationFileDto.getConversationCode(), conversationFileDto.getUserOid(),
                                conversationFileDto.getOrganizationId());

                            // 更新附件表
                            ConversationFileDto conversationFile = new ConversationFileDto();
                            conversationFile.setId(conversationFileDto.getId());
                            conversationFile.setXfState(XfStateEnum.FINISH.getCode());
                            conversationFile.setUpdateTime(now);
                            conversationFileMapper.updateById(conversationFile);
                        } else if (status == -1) {
                            log.error("讯飞转写失败：fileOid：" + conversationFileDto.getFileOid() + ";返回值："
                                + JSON.toJSONString(transferInfo));

                            // 更新附件表
                            ConversationFileDto conversationFile = new ConversationFileDto();
                            conversationFile.setId(conversationFileDto.getId());
                            conversationFile.setXfState(XfStateEnum.FAIL.getCode());
                            conversationFile.setUpdateTime(now);
                            conversationFileMapper.updateById(conversationFile);
                        }
                    }
                });
            } catch (Exception e) {

            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        }
    }

    public void dealMeeting(String conversationCode, String userOid, Long organizationId) {
        Integer type = TypeEnum.MEETING.getCode();

        TipMapper tipMapper = SpringUtil.getBean(TipMapper.class);
        List<TipDto> tipDtos = tipMapper.selectList(new LambdaQueryWrapper<TipDto>().eq(TipDto::getAppType, type)
            .eq(TipDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(tipDtos)) {
            TipDto tipDto = tipDtos.get(0);

            String remark = tipDto.getRemark();
            IConversationService conversationService = SpringUtil.getBean(IConversationService.class);
            ConversationVo conversationVo = conversationService.getDetail(conversationCode, type);
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(remark);
            cn.hutool.json.JSONArray tabList = jsonObject.getJSONArray("tabList");

            for (int i = 0; i < tabList.size(); i++) {
                cn.hutool.json.JSONObject info = (cn.hutool.json.JSONObject)tabList.get(i);
                String key = info.getStr("key");
                if (StringUtils.isNotEmpty(key)) {
                    String content = info.getStr("content");
                    if (key.equals("gjc")) {
                        String gjc = content;
                        conversationVo.setAskType(1);
                        conversationVo.setSubType(SubTypeEnum.MEETING_KEYWORD.getCode());
                        conversationVo.setMessage(gjc);
                        meetingTextChatARK(conversationVo, userOid, organizationId);
                    } else if (key.equals("hyzy")) {
                        String hyzy = content;
                        conversationVo.setAskType(1);
                        conversationVo.setSubType(SubTypeEnum.MEETING_ABSTRACT.getCode());
                        conversationVo.setMessage(hyzy);
                        meetingTextChatARK(conversationVo, userOid, organizationId);
                    } else if (key.equals("fyzj")) {
                        String fyzj = content;
                        conversationVo.setAskType(1);
                        conversationVo.setSubType(SubTypeEnum.MEETING_SUMMARY.getCode());
                        conversationVo.setMessage(fyzj);
                        meetingTextChatARK(conversationVo, userOid, organizationId);
                    } else if (key.equals("dbsx")) {
                        String dbsx = content;
                        conversationVo.setAskType(1);
                        conversationVo.setSubType(SubTypeEnum.MEETING_TODO.getCode());
                        conversationVo.setMessage(dbsx);
                        meetingTextChatARK(conversationVo, userOid, organizationId);
                    }
                }
            }
        }
    }

    public void meetingTextChatARK(ConversationVo conversationVo, String userOid, Long organizationId) {
        String conversationCode = conversationVo.getConversationCode();

        if (null == conversationVo) {
            return;
        }

        if (StringUtils.isBlank(conversationVo.getMessage())) {
            return;
        }

        if (null == conversationVo.getSubType()) {
            return;
        }
        IHistoryAppService historyAppService = SpringUtil.getBean(IHistoryAppService.class);
        ArkUtil arkUtil = SpringUtil.getBean(ArkUtil.class);
        RedisComponent redisComponent = SpringUtil.getBean(RedisComponent.class);
        // 获取会议的文字记录
        HistoryAppConditionBo condition = new HistoryAppConditionBo();
        condition.setUserOid(userOid);
        condition.setConversationCode(conversationCode);
        HistoryAppVo meetingConvertText = historyAppService.getMeetingConvertText(condition);
        if (null == meetingConvertText || StringUtils.isBlank(meetingConvertText.getResult())) {
            return;
        }

        String convertText = historyAppService.getMeetingConvertText(meetingConvertText.getResult());
        if (StringUtils.isBlank(convertText)) {
            return;
        }

        // 上下文
        String question = conversationVo.getMessage();

        List<ContextBo> arr = new ArrayList();
        boolean hasKey = redisComponent.hasKey("system_context_ark");
        String systemContext = "";
        if (hasKey) {
            systemContext = (String)redisComponent.get("system_context_ark");
        }

        ChatCompletionResult chatCompletionResult;
        if (StringUtils.isNotBlank(conversationVo.getContext())) {
            List<ContextBo> contextBos = JSONArray.parseArray(conversationVo.getContext(), ContextBo.class);
            arr.addAll(contextBos);
            chatCompletionResult = arkUtil.textChat(null, question, null, arr, systemContext, convertText);
        } else {
            chatCompletionResult = arkUtil.textChat(null, question, null, arr, systemContext, convertText);
        }

        String responseRes = arkUtil.makeStreamResponse(chatCompletionResult);

        HistoryAppBo historyAppBo = new HistoryAppBo();
        historyAppBo.setConversationCode(conversationVo.getConversationCode());
        historyAppBo.setUserOid(userOid);
        historyAppBo.setOrganizationId(organizationId);
        historyAppBo.setType(conversationVo.getType());
        historyAppBo.setSubType(conversationVo.getSubType());
        historyAppBo.setAskType(conversationVo.getAskType());
        historyAppBo.setPrompt(conversationVo.getPrompt());
        historyAppBo.setOriginalQuestion(conversationVo.getOriginalQuestion());
        historyAppBo.setQuestion(conversationVo.getMessage());
        historyAppBo.setParameterJson(chatCompletionResult.getModel());
        historyAppBo.setRequestId(chatCompletionResult.getId());
        historyAppBo.setResult(responseRes);
        historyAppBo.setResponseData(JSONUtil.toJsonStr(chatCompletionResult));
        historyAppBo.setChannel(conversationVo.getChannel());
        historyAppBo.setCreateBy(userOid);
        if (chatCompletionResult.getUsage() != null) {
            historyAppBo.setUsageTotal(chatCompletionResult.getUsage().getTotalTokens());
            historyAppBo.setUsageIn(chatCompletionResult.getUsage().getPromptTokens());
            historyAppBo.setUsageOut(chatCompletionResult.getUsage().getCompletionTokens());
        }
        historyAppService.addHistoryApp(historyAppBo);

        // 扣减余额
        if (ORGANIZATION_QUOTA_REDUCE_SWITCH) {
            ThreadUtil.taskSyncExecute(new Runnable() {
                @Override
                public void run() {
                    if (historyAppBo.getUsageTotal() != null) {
                        organizationService.reduceQuota(new OrganizationReduceQuotaBo(userOid, organizationId,
                            historyAppBo.getUsageTotal(), historyAppBo.getType()));
                    }
                }
            });
        }
    }

    @Override
    public AjaxResult addConversationFile(ConversationFileBo conversationFileBo) {
        ConversationFileDto conversationFile = new ConversationFileDto();
        BeanUtils.copyProperties(conversationFileBo, conversationFile);

        conversationFile.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        conversationFile.setCreateTime(new Date());
        save(conversationFile);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateConversationFile(ConversationFileBo conversationFileBo) {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getId, conversationFileBo.getId());
        ConversationFileDto conversationFileDto = conversationFileMapper.selectOne(lqw);
        if (null == conversationFileDto) {
            return AjaxResult.fail("对话文件不存在");
        }

        ConversationFileDto conversationFile = new ConversationFileDto();
        BeanUtils.copyProperties(conversationFileBo, conversationFile);

        conversationFile.setUpdateTime(new Date());
        updateById(conversationFile);

        if (StringUtils.isNotBlank(conversationFileDto.getFileOid())
            && StringUtils.isNotBlank(conversationFileBo.getFileName())) {
            // 更新文件名称
            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOriginalName(conversationFileBo.getFileName());
            attachmentService.update(attachmentDto,
                new LambdaUpdateWrapper<AttachmentDto>().eq(AttachmentDto::getOid, conversationFileDto.getFileOid()));
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateConversationFileByConversationCode(ConversationFileBo conversationFileBo) {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getConversationCode, conversationFileBo.getConversationCode());
        lqw.last("limit 1");
        ConversationFileDto conversationFileDto = conversationFileMapper.selectOne(lqw);
        if (null == conversationFileDto) {
            return AjaxResult.fail("对话文件不存在");
        }

        // 执行更新
        ConversationFileDto conversationFile = new ConversationFileDto();
        BeanUtils.copyProperties(conversationFileBo, conversationFile);
        conversationFile.setUpdateTime(new Date());
        conversationFile.setId(conversationFileDto.getId());
        updateById(conversationFile);

        // 更新文件名称
        if (StringUtils.isNotBlank(conversationFileDto.getFileOid())
            && StringUtils.isNotBlank(conversationFileBo.getFileName())) {
            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOriginalName(conversationFileBo.getFileName());
            attachmentService.update(attachmentDto,
                new LambdaUpdateWrapper<AttachmentDto>().eq(AttachmentDto::getOid, conversationFileDto.getFileOid()));
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult saveConversationFileByConversationCode(ConversationFileBo conversationFileBo) {
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getConversationCode, conversationFileBo.getConversationCode());
        if (conversationFileBo.getHistoryId() != null) {
            log.info("saveConversationFileByConversationCode historyId is null, code:"
                + conversationFileBo.getConversationCode());
            lqw.eq(ConversationFileDto::getHistoryId, conversationFileBo.getHistoryId());
        }
        lqw.last("limit 1");
        ConversationFileDto conversationFileDto = conversationFileMapper.selectOne(lqw);
        if (null == conversationFileDto) {
            conversationFileDto = new ConversationFileDto();
            BeanUtils.copyProperties(conversationFileBo, conversationFileDto);
            conversationFileDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            conversationFileDto.setCreateTime(new Date());
            conversationFileDto.setUpdateTime(new Date());
            conversationFileDto.setConversationCode(conversationFileBo.getConversationCode());
            // 查询会话信息
            LambdaQueryWrapper<ConversationDto> lqwConversation = new LambdaQueryWrapper<>();
            lqwConversation.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqwConversation.eq(ConversationDto::getConversationCode, conversationFileBo.getConversationCode());
            lqwConversation.last("limit 1");
            ConversationDto conversationDto = conversationMapper.selectOne(lqwConversation);
            if (conversationDto != null) {
                conversationFileDto.setUserOid(conversationDto.getUserOid());
                conversationFileDto.setOrganizationId(conversationDto.getOrganizationId());
                conversationFileDto.setType(conversationDto.getType());
                conversationFileDto.setChannel(conversationDto.getChannel());
            }
            saveOrUpdate(conversationFileDto);
        } else {
            ConversationFileDto conversationFile = new ConversationFileDto();
            BeanUtils.copyProperties(conversationFileBo, conversationFile);
            conversationFile.setId(conversationFileDto.getId());
            conversationFile.setUpdateTime(new Date());
            saveOrUpdate(conversationFile);
        }

        // 更新文件名称
        if (StringUtils.isNotBlank(conversationFileDto.getFileOid())
            && StringUtils.isNotBlank(conversationFileBo.getFileName())) {
            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setOriginalName(conversationFileBo.getFileName());
            attachmentService.update(attachmentDto,
                new LambdaUpdateWrapper<AttachmentDto>().eq(AttachmentDto::getOid, conversationFileDto.getFileOid()));
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        ConversationFileConditionBo condition = new ConversationFileConditionBo();
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        condition.setId(id);
        List<ConversationFileVo> conversationFileVos =
            conversationFileMapper.getConversationFileListByCondition(condition);
        if (CollectionUtil.isEmpty(conversationFileVos)) {
            return AjaxResult.fail("会话文件表数据不存在");
        }

        return AjaxResult.success(conversationFileVos.get(0));
    }

    @Override
    public ConversationFileVo getDetail(ConversationFileBo conversationFileBo) {
        ConversationFileConditionBo condition = new ConversationFileConditionBo();
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        condition.setId(conversationFileBo.getId());
        condition.setConversationCode(conversationFileBo.getConversationCode());
        condition.setFileOid(conversationFileBo.getFileOid());
        condition.setType(conversationFileBo.getType());
        condition.setHistoryId(conversationFileBo.getHistoryId());
        condition.setTaskType(conversationFileBo.getTaskType());

        List<ConversationFileVo> conversationFileVos =
            conversationFileMapper.getConversationFileListByCondition(condition);
        if (CollectionUtil.isEmpty(conversationFileVos)) {
            return null;
        }

        return conversationFileVos.get(0);
    }

    @Override
    public AjaxResult deleteConversationFile(ConversationFileBo conversationFileBo) {
        // 删除信息
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        if (null != conversationFileBo.getId()) {
            lqw.eq(ConversationFileDto::getId, conversationFileBo.getId());
        }

        if (StringUtils.isNotBlank(conversationFileBo.getConversationCode())) {
            lqw.eq(ConversationFileDto::getConversationCode, conversationFileBo.getConversationCode());
        }

        if (StringUtils.isNotBlank(conversationFileBo.getFileOid())) {
            lqw.eq(ConversationFileDto::getFileOid, conversationFileBo.getFileOid());
        }

        List<ConversationFileDto> fileDtos = conversationFileMapper.selectList(lqw);
        if (CollectionUtil.isNotEmpty(fileDtos)) {
            // 含有对话文件，删除关联关联，再删附件表
            List<Long> ids = fileDtos.stream().map(ConversationFileDto::getId).collect(Collectors.toList());

            ConversationFileDto dto = new ConversationFileDto();
            dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
            dto.setUpdateBy(conversationFileBo.getUpdateBy());
            dto.setUpdateTime(new Date());
            conversationFileMapper.update(dto,
                new LambdaUpdateWrapper<ConversationFileDto>().in(ConversationFileDto::getId, ids));

            // 删除附件表
            List<String> fileOids = fileDtos.stream().map(ConversationFileDto::getFileOid).collect(Collectors.toList());
            AttachmentDto attachmentDto = new AttachmentDto();
            attachmentDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
            attachmentDto.setUpdateBy(conversationFileBo.getUpdateBy());
            attachmentDto.setUpdateTime(new Date());
            attachmentService.update(attachmentDto,
                new LambdaUpdateWrapper<AttachmentDto>().in(AttachmentDto::getOid, fileOids));
        } else {
            // 不含有对话文件，直接删除附件表
            if (StringUtils.isNotBlank(conversationFileBo.getFileOid())) {
                AttachmentDto attachmentDto = new AttachmentDto();
                attachmentDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
                attachmentDto.setUpdateBy(conversationFileBo.getUpdateBy());
                attachmentDto.setUpdateTime(new Date());
                attachmentService.update(attachmentDto, new LambdaUpdateWrapper<AttachmentDto>()
                    .eq(AttachmentDto::getOid, conversationFileBo.getFileOid()));
            }
        }

        return AjaxResult.success();
    }

    public List<AudioConvertVo> getConvertList(TransferInfo transferInfo) {
        List<AudioConvertVo> audioConvertVos = Lists.newArrayList();
        // 转换数据
        List<Lattice> lattices = transferInfo.getContent().getOrderResult().getLattice();
        if (CollectionUtil.isEmpty(lattices)) {
            return audioConvertVos;
        }

        for (int i = 0; i < lattices.size(); i++) {
            // 做顺滑功能的识别结果
            Lattice lattice = lattices.get(i);

            // 单个句子的结果对象
            St st = lattice.getJson_1best().getSt();

            // 说话人
            String speaker = "说话人" + st.getRl();

            // 单个句子的开始时间
            String bg = convertMillisToTime(Long.parseLong(st.getBg()));

            // 句子内容
            StringBuilder sentenceSb = new StringBuilder();

            // 词语识别结果集合
            List<Rt> rts = st.getRt();
            if (CollectionUtil.isEmpty(rts)) {
                continue;
            }

            for (Rt rt : rts) {
                List<Ws> wsList = rt.getWs();
                if (CollectionUtil.isEmpty(wsList)) {
                    continue;
                }

                for (int k = 0; k < wsList.size(); k++) {
                    Ws ws = wsList.get(k);
                    List<Cw> cwList = ws.getCw();
                    if (CollectionUtil.isEmpty(cwList)) {
                        continue;
                    }

                    for (int l = 0; l < cwList.size(); l++) {
                        Cw cw = cwList.get(l);
                        String w = cw.getW();

                        sentenceSb.append(w);

                        String wp = cw.getWp();
                        if ("g".equals(wp)) {
                            sentenceSb.append("\n");
                        }
                    }
                }
            }

            if (i == 0) {
                // 第一句话
                AudioConvertVo audioConvertVo = new AudioConvertVo();
                audioConvertVo.setSpeaker(speaker);
                audioConvertVo.setStartTime(bg);
                audioConvertVo.setContent(sentenceSb.toString());

                audioConvertVos.add(audioConvertVo);
            } else {
                // 从第二句话开始
                AudioConvertVo lastVo = audioConvertVos.get(audioConvertVos.size() - 1);
                if (speaker.equals(lastVo.getSpeaker())) {
                    // 相同说话人，合并句子
                    String contentSb = lastVo.getContent() + sentenceSb;
                    lastVo.setContent(contentSb);
                } else {
                    // 说话人不同
                    AudioConvertVo audioConvertVo = new AudioConvertVo();
                    audioConvertVo.setSpeaker(speaker);
                    audioConvertVo.setStartTime(bg);
                    audioConvertVo.setContent(sentenceSb.toString());

                    audioConvertVos.add(audioConvertVo);
                }
            }
        }

        return audioConvertVos;
    }

    public static String convertMillisToTime(long millis) {
        // 计算总秒数
        long totalSeconds = millis / 1000;

        // 计算小时数
        long hours = totalSeconds / 3600;

        // 计算剩余秒数
        long remainingSeconds = totalSeconds % 3600;

        // 计算分钟数
        long minutes = remainingSeconds / 60;

        // 计算剩余秒数
        long seconds = remainingSeconds % 60;

        // 格式化时间并返回
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    @Override
    public void getAipptDownloadResult(List<Integer> taskStates) {
        if (CollectionUtils.isEmpty(taskStates)) {
            taskStates = Lists.newArrayList(ConversationStateTypeEnum.UPLOADED.getCode(),
                ConversationStateTypeEnum.DEALING.getCode());
        }

        // 查询所有的创建下载任务成功的记录
        LambdaQueryWrapper<ConversationFileDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationFileDto::getTaskType, ConversationTaskTypeEnum.AIPPT_DOWNLOAD.getCode());
        lqw.in(ConversationFileDto::getTaskState, taskStates);
        // 这里注释掉，因为其他任务也会产生aippt的下载任务
        // lqw.eq(ConversationFileDto::getType, TypeEnum.PPT.getCode());
        // 只查询今天的失败数据，历史失败不作重试
        if (taskStates.contains(ConversationStateTypeEnum.FAIL.getCode())) {
            Date startDate = DateUtil.beginOfDay(new Date());
            Date endDate = DateUtil.endOfDay(new Date());
            lqw.ge(ConversationFileDto::getUpdateTime, startDate).le(ConversationFileDto::getUpdateTime, endDate);
        }
        List<ConversationFileDto> conversationFileDtos = conversationFileMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(conversationFileDtos)) {
            return;
        }

        // 有数据需要处理
        Date now = new Date();
        for (ConversationFileDto conversationFileDto : conversationFileDtos) {
            if (StringUtils.isBlank(conversationFileDto.getTaskId())) {
                continue;
            }
            conversationFileDto.setUpdateTime(now);

            // 开始加分布式锁处理
            RLock lock = redissonClient.getLock("lock:AipptDownloadResult:" + conversationFileDto.getTaskId());
            lock.lock();
            try {
                ThreadUtil.taskExecute(new Runnable() {
                    @Override
                    public void run() {
                        log.info("getAipptDownloadResult start, id:{}", conversationFileDto.getId());
                        // 如果是失败的任务，则重新提交一次该作品下载任务
                        if (conversationFileDto.getTaskState().equals(ConversationStateTypeEnum.FAIL.getCode())
                            && StringUtils.isNotBlank(conversationFileDto.getBusinessId())) {
                            IConversationFileService conversationFileService =
                                SpringUtil.getBean(IConversationFileService.class);
                            String userOid = conversationFileDto.getUserOid();
                            String taskKey = aipptUtil.submitDownloadTask(userOid,
                                Long.valueOf(conversationFileDto.getBusinessId()));
                            if (StringUtils.isBlank(taskKey)) {
                                log.error("getAipptDownloadResult submitDownloadTask error, id:{}",
                                    conversationFileDto.getId());
                            }
                            // 更新任务到数据库，conversionFile表
                            ConversationFileBo conversationFileBo = new ConversationFileBo();
                            conversationFileBo.setConversationCode(conversationFileDto.getConversationCode());
                            conversationFileBo.setBusinessId(conversationFileDto.getBusinessId());
                            conversationFileBo.setTaskId(taskKey);
                            conversationFileBo.setTaskType(ConversationTaskTypeEnum.AIPPT_DOWNLOAD.getCode());
                            conversationFileBo.setTaskState(ConversationStateTypeEnum.UPLOADED.getCode());
                            conversationFileBo.setHistoryId(conversationFileDto.getHistoryId());
                            conversationFileService.saveConversationFileByConversationCode(conversationFileBo);
                            return;
                        }

                        // 查询下载状态
                        AipptBo aipptBo = new AipptBo();
                        aipptBo.setConversationCode(conversationFileDto.getConversationCode());
                        aipptBo.setTaskKey(conversationFileDto.getTaskId());
                        aipptBo.setUserOid(conversationFileDto.getUserOid());
                        AipptRequestCallableTask task = new AipptRequestCallableTask(aipptBo,
                            (AipptBo param) -> aipptUtil.queryDownloadTask(param.getUserOid(), param.getTaskKey()));
                        List<String> resultLists = aipptUtil.queryTaskRetryAndRun(task);
                        if (resultLists == null) {
                            conversationFileDto.setTaskState(ConversationStateTypeEnum.FAIL.getCode());
                            updateById(conversationFileDto);
                            return;
                        }
                        if (CollectionUtils.isEmpty(resultLists)) {
                            conversationFileDto.setTaskState(ConversationStateTypeEnum.DEALING.getCode());
                            updateById(conversationFileDto);
                            return;
                        }
                        // 查询封面图
                        AttachmentBo attachmentBoCover = new AttachmentBo();
                        AttachmentVo attachmentVoCover = null;
                        if (StringUtils.isNotBlank(conversationFileDto.getBusinessId())) {
                            AipptBo aipptCoverBo = new AipptBo();
                            aipptCoverBo.setId(Long.valueOf(conversationFileDto.getBusinessId()));
                            aipptCoverBo.setUserOid(conversationFileDto.getUserOid());
                            AipptDetailVo aipptDetailVo = aipptUtil.queryDetail(aipptCoverBo);
                            if (aipptDetailVo != null) {
                                String coverUrl = aipptDetailVo.getCoverUrl();
                                if (StringUtils.isNotBlank(coverUrl)) {
                                    attachmentBoCover.setSuffix("jpg");
                                    attachmentBoCover.setCreateBy(conversationFileDto.getUserOid());
                                    attachmentBoCover.setFileName("封面图");
                                    attachmentVoCover = attachmentService.uploadUrl(coverUrl, attachmentBoCover);
                                }
                            }
                        }

                        // 上传文件到我们的服务器
                        List<AipptFileVo> aipptFileVos = Lists.newArrayList();
                        AttachmentBo attachmentBo = new AttachmentBo();
                        attachmentBo.setSuffix("ppt");
                        attachmentBo.setCreateBy(conversationFileDto.getUserOid());
                        for (String result : resultLists) {
                            String aipptNameFromUrl = AipptUtil.getAipptNameFromUrl(result);
                            attachmentBo.setFileName(aipptNameFromUrl);
                            AttachmentVo attachmentVo = attachmentService.uploadUrl(result, attachmentBo);
                            if (null != attachmentVo) {
                                AipptFileVo aipptFileVo = new AipptFileVo();
                                aipptFileVo.setAipptUrl(result);
                                aipptFileVo.setDownloadUrl(attachmentVo.getDownloadUrl());
                                aipptFileVo.setFileName(aipptNameFromUrl);
                                aipptFileVo.setViewPath(attachmentVo.getViewPath());
                                aipptFileVo.setFileOid(attachmentVo.getOid());
                                if (attachmentVoCover != null) {
                                    aipptFileVo.setCoverDownloadUrl(attachmentVoCover.getDownloadUrl());
                                    aipptFileVo.setCoverViewPath(attachmentVoCover.getViewPath());
                                }
                                aipptFileVos.add(aipptFileVo);
                            }
                        }

                        // 更新conversation【必然更新】和historyApp【可选更新】
                        IConversationService conversationService = SpringUtil.getBean(IConversationService.class);
                        IHistoryAppService historyAppService = SpringUtil.getBean(IHistoryAppService.class);
                        String aipptFileVoJson = JSON.toJSONString(aipptFileVos);
                        ConversationBo conversationBo = new ConversationBo();
                        conversationBo.setConversationCode(conversationFileDto.getConversationCode());
                        conversationBo.setBusinessJson(aipptFileVoJson);
                        conversationBo.setBusinessJsonBak(aipptFileVoJson);
                        conversationService.updateConversation(conversationBo);
                        if (conversationFileDto.getHistoryId() != null) {
                            HistoryAppBo historyAppBo = new HistoryAppBo();
                            historyAppBo.setId(conversationFileDto.getHistoryId());
                            historyAppBo.setBusinessJson(aipptFileVoJson);
                            historyAppBo.setBusinessJsonBak(aipptFileVoJson);
                            historyAppService.updateHistoryApp(historyAppBo);
                        }

                        // 已经下载完成且上传到我们服务器则更新数据库ConversationFile
                        conversationFileDto.setTaskState(ConversationStateTypeEnum.FINISH.getCode());
                        conversationFileDto.setContent(JSONObject.toJSONString(resultLists));
                        updateById(conversationFileDto);
                        log.info("getAipptDownloadResult end, id:{}", conversationFileDto.getId());
                    }
                });
            } catch (Exception e) {
                log.error("getAipptDownloadResult error", e);
                conversationFileDto.setTaskState(ConversationStateTypeEnum.FAIL.getCode());
                updateById(conversationFileDto);
            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Map<String, String> taskIdResultMap(List<String> taskIds, Integer taskType) {
        Map<String, String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(taskIds) || taskType == null) {
            return resultMap;
        }
        ConversationFileConditionBo conditionBo = new ConversationFileConditionBo();
        conditionBo.setTaskIds(taskIds);
        conditionBo.setTaskType(taskType);
        List<ConversationFileVo> conversationFileVos = getConversationFileListByConditionNoPage(conditionBo);
        if (CollectionUtils.isEmpty(conversationFileVos)) {
            return resultMap;
        }
        conversationFileVos.stream()
            .filter(conversationFileVo -> StringUtils.isNotBlank(conversationFileVo.getTaskId()))
            .forEach(conversationFileVo -> {
                resultMap.put(conversationFileVo.getTaskId(), conversationFileVo.getContent());
            });
        return resultMap;
    }
}