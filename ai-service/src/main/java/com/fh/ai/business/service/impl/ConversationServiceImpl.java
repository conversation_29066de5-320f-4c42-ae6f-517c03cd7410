package com.fh.ai.business.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.conversation.ConversationBo;
import com.fh.ai.business.entity.bo.conversation.ConversationConditionBo;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileBo;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileConditionBo;
import com.fh.ai.business.entity.dto.conversation.ConversationDto;
import com.fh.ai.business.entity.dto.conversationFile.ConversationFileDto;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;
import com.fh.ai.business.mapper.ConversationFileMapper;
import com.fh.ai.business.mapper.ConversationMapper;
import com.fh.ai.business.service.IConversationFileService;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IFileContentCacheService;
import com.fh.ai.common.aippt.vo.AipptFileVo;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.QueryTaskResultFlagType;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;

/**
 * 会话表接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-17 15:40:02
 */
@Service
public class ConversationServiceImpl extends ServiceImpl<ConversationMapper, ConversationDto>
    implements IConversationService {

    @Resource
    private ConversationMapper conversationMapper;
    @Resource
    private ConversationFileMapper conversationFileMapper;
    @Resource
    private IConversationFileService conversationFileService;
    @Resource
    private IFileContentCacheService fileContentCacheService;

    @Override
    public Map<String, Object> getConversationListByCondition(ConversationConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ConversationVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = conversationMapper.getConversationListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ConversationVo> conversationVos = conversationMapper.getConversationListByCondition(conditionBo);
            PageInfo<ConversationVo> pageInfo = new PageInfo<>(conversationVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取对话文档
            List<String> codes = list.stream().map(ConversationVo::getConversationCode).collect(Collectors.toList());
            ConversationFileConditionBo condition = new ConversationFileConditionBo();
            condition.setConversationCodes(codes);
            condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            List<ConversationFileVo> conversationFileVos =
                conversationFileMapper.getConversationFileListByCondition(condition);
            // 如果queryTaskResultFlag 传2 则说明不需要返回taskResult，手动置为null。
            if (CollectionUtils.isNotEmpty(conversationFileVos)
                && Objects.equals(conditionBo.getQueryTaskResultFlag(), QueryTaskResultFlagType.QUERY_NO.getCode())) {
                for (ConversationFileVo conversationFileVo : conversationFileVos) {
                    conversationFileVo.setTaskResult(null);
                }
            }
            if (CollectionUtil.isNotEmpty(conversationFileVos)) {
                Map<String, List<ConversationFileVo>> fileMap = conversationFileVos.stream()
                    .collect(Collectors.groupingBy(t -> t.getConversationCode() + "_" + t.getType()));
                for (ConversationVo vo : list) {
                    List<ConversationFileVo> fileVos = fileMap.get(vo.getConversationCode() + "_" + vo.getType());

                    vo.setFileVos(fileVos);
                }
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addConversation(ConversationBo conversationBo) {
        ConversationDto conversation = new ConversationDto();
        BeanUtils.copyProperties(conversationBo, conversation);

        Date now = new Date();
        conversation.setConversationCode(IdUtil.simpleUUID());
        if (StringUtils.isBlank(conversation.getTitle())) {
            conversation.setTitle(conversationBo.getMessage());
        }
        conversation.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        conversation.setCreateTime(now);
        save(conversation);

        if (CollectionUtil.isNotEmpty(conversationBo.getFileList())) {
            // 保存对话文件
            List<ConversationFileDto> fileDtos = Lists.newArrayList();
            for (ConversationFileBo fileBo : conversationBo.getFileList()) {
                ConversationFileDto fileDto = new ConversationFileDto();
                fileDto.setUserOid(conversationBo.getUserOid());
                fileDto.setOrganizationId(conversationBo.getOrganizationId());
                fileDto.setConversationCode(conversation.getConversationCode());
                fileDto.setType(conversation.getType());
                fileDto.setFileOid(fileBo.getFileOid());
                fileDto.setQwenFileId(fileBo.getQwenFileId());
                fileDto.setChannel(conversationBo.getChannel());
                fileDto.setCreateBy(conversationBo.getCreateBy());
                fileDto.setCreateTime(now);

                fileDtos.add(fileDto);
            }

            conversationFileService.saveBatch(fileDtos);
        }

        return AjaxResult.success(conversation.getConversationCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateConversation(ConversationBo conversationBo) {
        LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationDto::getConversationCode, conversationBo.getConversationCode());
        if (null != conversationBo.getType()) {
            lqw.eq(ConversationDto::getType, conversationBo.getType());
        }
        lqw.last("limit 1");
        ConversationDto conversationDto = getOne(lqw);
        if (null == conversationDto) {
            return AjaxResult.fail("会话表数据不存在");
        }

        ConversationDto conversation = new ConversationDto();
        BeanUtils.copyProperties(conversationBo, conversation);
        conversation.setId(conversationDto.getId());
        conversation.setUpdateTime(new Date());
        updateById(conversation);

        if (CollectionUtil.isNotEmpty(conversationBo.getFileList())) {
            // 查询对话文件
            LambdaQueryWrapper<ConversationFileDto> lqwList = new LambdaQueryWrapper<>();
            lqwList.eq(ConversationFileDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqwList.eq(ConversationFileDto::getConversationCode, conversationBo.getConversationCode());
            if (null != conversationBo.getType()) {
                lqwList.eq(ConversationFileDto::getType, conversationBo.getType());
            }

            List<ConversationFileDto> fileDtos = conversationFileMapper.selectList(lqwList);
            Map<String, ConversationFileDto> fileDtoMap = null;
            if (CollectionUtil.isNotEmpty(fileDtos)) {
                fileDtoMap = fileDtos.stream().collect(Collectors.toMap(u -> u.getFileOid(), u -> u, (v1, v2) -> v2));
            }

            Date now = new Date();
            List<ConversationFileDto> fileDtoList = Lists.newArrayList();
            for (ConversationFileBo fileBo : conversationBo.getFileList()) {
                ConversationFileDto fileDto = null != fileDtoMap ? fileDtoMap.get(fileBo.getFileOid()) : null;
                if (null == fileDto) {
                    fileDto = new ConversationFileDto();
                    fileDto.setConversationCode(conversationBo.getConversationCode());
                    fileDto.setType(conversationBo.getType());
                    fileDto.setUserOid(conversationBo.getUserOid());
                    fileDto.setOrganizationId(conversationBo.getOrganizationId());
                    fileDto.setFileOid(fileBo.getFileOid());
                    fileDto.setQwenFileId(fileBo.getQwenFileId());
                    fileDto.setChannel(conversationBo.getChannel());
                    fileDto.setCreateTime(now);
                    fileDto.setCreateBy(conversationBo.getCreateBy());
                    fileDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

                    fileDtoList.add(fileDto);
                }
            }

            if (CollectionUtil.isNotEmpty(fileDtoList)) {
                conversationFileService.saveBatch(fileDtoList);
            }

        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationDto::getId, id);

        ConversationDto conversation = getOne(lqw);
        if (null == conversation) {
            return AjaxResult.fail("会话表数据不存在");
        }

        ConversationVo conversationVo = new ConversationVo();
        BeanUtils.copyProperties(conversation, conversationVo);

        return AjaxResult.success(conversationVo);
    }

    @Override
    public ConversationVo getDetail(String conversationCode, Integer type) {
        LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ConversationDto::getConversationCode, conversationCode);
        if (null != type) {
            lqw.eq(ConversationDto::getType, type);
        }

        ConversationDto conversation = getOne(lqw);
        if (null == conversation) {
            return null;
        }

        ConversationVo conversationVo = new ConversationVo();
        BeanUtils.copyProperties(conversation, conversationVo);

        // 获取对话文档
        ConversationFileConditionBo condition = new ConversationFileConditionBo();
        condition.setConversationCode(conversationCode);
        condition.setType(type);
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        condition.setQueryTaskResultFlag(QueryTaskResultFlagType.QUERY_YES.getCode());
        List<ConversationFileVo> conversationFileVos =
            conversationFileMapper.getConversationFileListByCondition(condition);
        // 查询文件内容数据塞入到对话对象中
        convertFileContentCache2TaskResult(conversationFileVos);
        conversationVo.setFileVos(conversationFileVos);

        return conversationVo;
    }

    @Override
    public AjaxResult deleteConversation(ConversationBo conversationBo) {
        // 删除信息
        LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());

        if (null != conversationBo.getId()) {
            lqw.eq(ConversationDto::getId, conversationBo.getId());
        }

        if (StringUtils.isNotBlank(conversationBo.getConversationCode())) {
            lqw.eq(ConversationDto::getConversationCode, conversationBo.getConversationCode());
        }

        ConversationDto conversation = getOne(lqw);
        if (null == conversation) {
            return AjaxResult.fail("会话表数据不存在");
        }

        ConversationDto dto = new ConversationDto();
        dto.setId(conversation.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(conversationBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getConversationStatistic(ConversationBo conversationBo) {
        return AjaxResult.success(conversationMapper.getConversationStatistics(conversationBo));
    }

    @Override
    public AjaxResult removeBusinessJson(ConversationBo conversationBo) {
        // 删除信息
        LambdaQueryWrapper<ConversationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        if (null != conversationBo.getId()) {
            lqw.eq(ConversationDto::getId, conversationBo.getId());
        }
        if (StringUtils.isNotBlank(conversationBo.getConversationCode())) {
            lqw.eq(ConversationDto::getConversationCode, conversationBo.getConversationCode());
        }
        lqw.eq(ConversationDto::getUserOid, conversationBo.getUserOid());
        ConversationDto conversation = getOne(lqw);
        if (null == conversation) {
            return AjaxResult.fail("会话表数据不存在");
        }

        if (StringUtils.isBlank(conversation.getBusinessJson())) {
            return AjaxResult.fail();
        }

        List<AipptFileVo> arr = new ArrayList<>();
        if (StringUtils.isNotBlank(conversationBo.getFileOid())) {
            List<AipptFileVo> aipptFileVos = JSON.parseArray(conversation.getBusinessJson(), AipptFileVo.class);
            for (AipptFileVo aipptFileVo : aipptFileVos) {
                if (conversationBo.getFileOid().equals(aipptFileVo.getFileOid())) {
                    continue;
                }
                arr.add(aipptFileVo);
            }
        } else if (StringUtils.isNotBlank(conversationBo.getBusinessJson())) {
            // 删除数据和当前会话数据不一致，直接返回
            if (!conversationBo.getBusinessJson().equals(conversation.getBusinessJson())) {
                return AjaxResult.success();
            }
        }

        conversation.setBusinessJson(JSON.toJSONString(arr));
        conversation.setUpdateTime(new Date());
        conversation.setUpdateBy(conversationBo.getUpdateBy());
        if (updateById(conversation)) {
            return AjaxResult.success();
        }

        return AjaxResult.fail();
    }

    /**
     * 查询文件内容数据塞入到对话对象中
     * 
     * @param conversationFileVos 对话文件列表
     */
    private void convertFileContentCache2TaskResult(List<ConversationFileVo> conversationFileVos) {
        // 查询文件内容数据
        if (CollectionUtil.isNotEmpty(conversationFileVos)) {
            List<Long> fileContentCacheIds = conversationFileVos.stream().map(ConversationFileVo::getFileContentCacheId)
                .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, String> fileContentCacheMap =
                fileContentCacheService.getFileContentCacheMapByIdsWithRedisCache(fileContentCacheIds);
            for (ConversationFileVo conversationFileVo : conversationFileVos) {
                if (null != conversationFileVo.getFileContentCacheId()) {
                    conversationFileVo
                        .setTaskResult(fileContentCacheMap.get(conversationFileVo.getFileContentCacheId()));
                }
            }
        }
    }

}