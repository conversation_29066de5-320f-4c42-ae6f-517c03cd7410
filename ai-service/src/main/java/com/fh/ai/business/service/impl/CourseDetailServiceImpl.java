package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.courseDetail.CourseDetailBo;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailConditionBo;
import com.fh.ai.business.entity.dto.courseDetail.CourseDetailDto;
import com.fh.ai.business.entity.vo.courseDetail.CourseDetailVo;
import com.fh.ai.business.mapper.CourseDetailMapper;
import com.fh.ai.business.service.ICourseDetailService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

/**
 * 课程详情接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class CourseDetailServiceImpl extends ServiceImpl<CourseDetailMapper, CourseDetailDto> implements ICourseDetailService {

	@Resource
	private CourseDetailMapper courseDetailMapper;
	
    @Override
	public List<CourseDetailVo> getCourseDetailListByCondition(CourseDetailConditionBo condition) {
        return courseDetailMapper.getCourseDetailListByCondition(condition);
	}

	@Override
	public AjaxResult addCourseDetail(CourseDetailBo courseDetailBo) {
		CourseDetailDto courseDetail = new CourseDetailDto();
		BeanUtils.copyProperties(courseDetailBo, courseDetail);
		courseDetail.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(courseDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCourseDetail(CourseDetailBo courseDetailBo) {
		CourseDetailDto courseDetail = new CourseDetailDto();
		BeanUtils.copyProperties(courseDetailBo, courseDetail);
		if(updateById(courseDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<CourseDetailDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(CourseDetailDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(CourseDetailDto::getId, id);
		CourseDetailDto courseDetail = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("courseDetailVo", courseDetail==null?new CourseDetailVo():courseDetail);
		return reuslt;
	}

}