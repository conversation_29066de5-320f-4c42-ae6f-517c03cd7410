package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.ai.business.entity.bo.course.CourseBo;
import com.fh.ai.business.entity.bo.course.CourseConditionBo;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailBo;
import com.fh.ai.business.entity.bo.courseDetail.CourseDetailConditionBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.dto.course.CourseDto;
import com.fh.ai.business.entity.dto.courseDetail.CourseDetailDto;
import com.fh.ai.business.entity.dto.videoPlay.VideoPlayDto;
import com.fh.ai.business.entity.vo.course.CourseVo;
import com.fh.ai.business.entity.vo.courseDetail.CourseDetailVo;
import com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo;
import com.fh.ai.business.mapper.CourseDetailMapper;
import com.fh.ai.business.mapper.CourseMapper;
import com.fh.ai.business.mapper.VideoPlayMapper;
import com.fh.ai.business.service.ICourseDetailService;
import com.fh.ai.business.service.ICourseService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 课程接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, CourseDto> implements ICourseService {

    @Resource
    private CourseMapper courseMapper;

    @Resource
    private CourseDetailMapper courseDetailMapper;

    @Resource
    private ICourseDetailService courseDetailService;

    @Resource
    private VideoPlayMapper videoPlayMapper;


    @Override
    public Map<String, Object> getCourseListByCondition(CourseConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<CourseVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = courseMapper.getCourseListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<CourseVo> prizeVos = courseMapper.getCourseListByCondition(conditionBo);
            PageInfo<CourseVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    public Map<String, Object> myList(ExamAnswerConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ExamAnswerVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = courseMapper.myList(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ExamAnswerVo> prizeVos = courseMapper.myList(conditionBo);
            PageInfo<ExamAnswerVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }


    @Override
    public AjaxResult addOrEdit(CourseBo courseBo) {
        Date date = new Date();
        CourseDto course = new CourseDto();
        BeanUtils.copyProperties(courseBo, course);
        course.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        course.setCreateTime(date);
        course.setUpdateTime(date);
        if (saveOrUpdate(course)) {
            if(CollUtil.isNotEmpty(courseBo.getCourseDetailList())){
                List<CourseDetailDto> arr = new ArrayList();
                List<Long> notDeleteArr = new ArrayList();
                for (CourseDetailBo bo : courseBo.getCourseDetailList()) {
                    CourseDetailDto courseDetailDto = new CourseDetailDto();
                    BeanUtils.copyProperties(bo, courseDetailDto);
                    bo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                    courseDetailDto.setCourseId(course.getId());
                    courseDetailDto.setCreateTime(new Date());
                    arr.add(courseDetailDto);
                    //已经入库数据，任然存在的记录
                    if (bo.getId() != null && bo.getId() > 0) {
                        notDeleteArr.add(bo.getId());
                    }
                }
                //编辑时删除数据
                if (courseBo.getId() != null && courseBo.getId() > 0 ) {
                    LambdaUpdateWrapper<CourseDetailDto> uw = new LambdaUpdateWrapper<>();
                    uw.eq(CourseDetailDto::getCourseId,courseBo.getId());
                    uw.eq(CourseDetailDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
                    if(CollUtil.isNotEmpty(notDeleteArr)){
                        uw.notIn(CourseDetailDto::getId,notDeleteArr);
                    }
                    uw.set(CourseDetailDto::getIsDelete,IsDeleteEnum.ISDELETE.getCode());
                    courseDetailService.update(null,uw);
                }
                courseDetailService.saveOrUpdateBatch(arr);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult addCourse(CourseBo courseBo) {
        CourseDto course = new CourseDto();
        BeanUtils.copyProperties(courseBo, course);
        course.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(course)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCourse(CourseBo courseBo) {
        CourseDto course = new CourseDto();
        BeanUtils.copyProperties(courseBo, course);
        if (course.getState() != null) {
            course.setUpdateTime(null);
        }
        if (updateById(course)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id,Integer state) {
        LambdaQueryWrapper<CourseDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CourseDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(CourseDto::getId, id);
        CourseDto course = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        CourseVo courseVo = new CourseVo();
        if (course != null) {
            BeanUtils.copyProperties(course, courseVo);
            CourseDetailConditionBo bo = new CourseDetailConditionBo();
            bo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            bo.setCourseId(course.getId());
            bo.setPage(1);
            bo.setLimit(10000);
            bo.setState(state);
            bo.setOrderBy(" sort asc");
            List<CourseDetailVo> courseDetailVos = courseDetailMapper.getCourseDetailListByCondition(bo);
            courseVo.setDetailVoList(courseDetailVos);
            courseVo.setUserAnswerCount(courseMapper.userAnswerCount(course.getId()));
            courseVo.setVideoNum(videoPlayMapper.selectCount(new LambdaQueryWrapper<VideoPlayDto>()
                    .eq(VideoPlayDto::getCourseId,course.getId())
                    .eq(VideoPlayDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode())));
        }
        reuslt.put("courseVo", courseVo);
        return reuslt;
    }

    @Override
    public List<Map> getType() {
        return courseDetailMapper.getType();
    }

    @Override
    public List<Map> statistics(CourseBo coursebo) {
        return courseDetailMapper.statistics(coursebo);
    }

    @Override
    public Map amountStatistics(CourseBo coursebo) {
        Map<String, Integer> reuslt = new HashMap();
        Integer videoUserCount = courseDetailMapper.videoUserCount(coursebo);
        Integer ansUserCount =  courseDetailMapper.ansUserCount(coursebo);
        reuslt.put("videoUserCount",videoUserCount);
        reuslt.put("ansUserCount",ansUserCount);
        return reuslt;
    }

    @Override
    public AjaxResult otherCourse(Long id) {
        CourseDto byId = getById(id);
        List<CourseVo> courseVos = courseDetailMapper.otherCourse(id,byId.getType());
        return AjaxResult.success(courseVos);
    }

}