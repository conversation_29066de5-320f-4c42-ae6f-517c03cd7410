package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.evaluatingRecord.EvaluatingRecordBo;
import com.fh.ai.business.entity.bo.evaluatingRecord.EvaluatingRecordConditionBo;
import com.fh.ai.business.entity.dto.evaluatingRecord.EvaluatingRecordDto;
import com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo;
import com.fh.ai.business.mapper.EvaluatingRecordMapper;
import com.fh.ai.business.service.IEvaluatingRecordService;
import com.fh.ai.common.enums.EvaluatingRecordState;
import com.fh.ai.common.tingwu.TingwuUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帮助中心接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class EvaluatingRecordServiceImpl extends ServiceImpl<EvaluatingRecordMapper, EvaluatingRecordDto> implements IEvaluatingRecordService {
	@Resource
	private EvaluatingRecordMapper evaluatingRecordMapper;

	@Override
	public Map<String, Object> getEvaluatingRecordListByCondition(EvaluatingRecordConditionBo condition) {
		Map<String, Object> map = new HashMap<>(4);
		List<EvaluatingRecordVo> list = null;
		long count = 0;
		if (null == condition.getPage() || null == condition.getLimit()) {
			// 不分页（查询全部）
			list = evaluatingRecordMapper.getEvaluatingRecordList(condition);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
			List<EvaluatingRecordVo> prizeVos = evaluatingRecordMapper.getEvaluatingRecordList(condition);
			PageInfo<EvaluatingRecordVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

	@Override
	public AjaxResult addEvaluatingRecord(EvaluatingRecordBo evaluatingRecordBo) {
		EvaluatingRecordDto evaluatingRecord = new EvaluatingRecordDto();
		BeanUtils.copyProperties(evaluatingRecordBo, evaluatingRecord);
		evaluatingRecord.setCreateTime(new Date());
		if (save(evaluatingRecord)) {
            return AjaxResult.success("保存成功");
        }
		return AjaxResult.fail("保存失败");
	}

	@Override
	public AjaxResult updateEvaluatingRecord(EvaluatingRecordBo evaluatingRecordBo) {
		EvaluatingRecordDto evaluatingRecord = new EvaluatingRecordDto();
		BeanUtils.copyProperties(evaluatingRecordBo, evaluatingRecord);
		if (updateById(evaluatingRecord)) {
            return AjaxResult.success("保存成功");
        }
		return AjaxResult.fail("保存失败");
	}

	@Override
	public EvaluatingRecordVo getByTaskUid(String taskUid) {
		return evaluatingRecordMapper.getEvaluatingRecordByTaskUid(taskUid);
	}

	@Override
	public String audioToText(String taskUid) {
		EvaluatingRecordVo evaluatingRecordVo = evaluatingRecordMapper.getEvaluatingRecordByTaskUid(taskUid);
		if (evaluatingRecordVo.getState() > EvaluatingRecordState.WAIT_AUDIO_TO_TEXT.getCode()) {
			return evaluatingRecordVo.getContents();
		}
		String contents = TingwuUtil.audioToTextResult(evaluatingRecordVo.getAudioToTextTaskUid());
		if (contents != null) {
			EvaluatingRecordDto evaluatingRecordDto = new EvaluatingRecordDto();
			evaluatingRecordDto.setState(EvaluatingRecordState.WAIT_EVALUATING.getCode());
			evaluatingRecordDto.setContents(contents);
			evaluatingRecordDto.setUpdateTime(new Date());
			updateById(evaluatingRecordDto);
		}
		return contents;
	}


}