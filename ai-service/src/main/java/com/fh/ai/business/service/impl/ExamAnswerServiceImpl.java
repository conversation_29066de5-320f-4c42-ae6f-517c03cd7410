package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerBo;
import com.fh.ai.business.entity.bo.examAnswer.ExamAnswerConditionBo;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDetailDto;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDto;
import com.fh.ai.business.entity.dto.examPaper.ExamPaperDto;
import com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import com.fh.ai.business.mapper.ExamAnswerMapper;
import com.fh.ai.business.mapper.ExamPaperMapper;
import com.fh.ai.business.mapper.QuestionMapper;
import com.fh.ai.business.service.IExamAnswerService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.annotation.Resource;

/**
 * 题库表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Service
public class ExamAnswerServiceImpl extends ServiceImpl<ExamAnswerMapper, ExamAnswerDto> implements IExamAnswerService {

    @Resource
    private ExamAnswerMapper examAnswerMapper;
    @Resource
    private ExamPaperMapper examPaperMapper;
    @Resource
    private QuestionMapper questionMapper;

    @Override
    public Map<String, Object> getExamAnswerListByCondition(ExamAnswerConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ExamAnswerVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = examAnswerMapper.getExamAnswerListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ExamAnswerVo> prizeVos = examAnswerMapper.getExamAnswerListByCondition(conditionBo);
            PageInfo<ExamAnswerVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addExamAnswer(ExamAnswerBo examAnswerBo) {
        ExamPaperDto examPaperDto = examPaperMapper.selectById(examAnswerBo.getExamPaperId());
        if (examPaperDto == null || examPaperDto.getIsDelete().equals(IsDeleteEnum.ISDELETE.getCode())) {
            return AjaxResult.fail("试卷不存在");
        }
        if (examPaperDto.getType().equals(2)) {
            long count = count(new LambdaQueryWrapper<ExamAnswerDto>()
                    .eq(ExamAnswerDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .eq(ExamAnswerDto::getUserOid, examAnswerBo.getUserOid())
                    .eq(ExamAnswerDto::getExamPaperId, examAnswerBo.getExamPaperId())
            );
            if (examPaperDto.getAnsTime() == count) {
                return AjaxResult.fail("已经超出作答上限，不可作答");
            }
        }

        ExamAnswerDto examAnswer = new ExamAnswerDto();
        BeanUtils.copyProperties(examAnswerBo, examAnswer);
        examAnswer.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<ExamAnswerBo> answerDetailList = examAnswerBo.getAnswerDetailList();
        List<ExamAnswerDetailDto> arr = new ArrayList();
        int right = 0;
        int wrong = 0;
        save(examAnswer);
        if (CollUtil.isNotEmpty(answerDetailList)) {
            List<QuestionVo> questionListByIds = questionMapper.getQuestionListByIds(examPaperDto.getQuestionIds());
            Map<Long, QuestionVo> collect = questionListByIds.stream().collect(Collectors.toMap(QuestionVo::getId, o -> o, (o1, o2) -> o2));
            for (ExamAnswerBo bo : answerDetailList) {
                ExamAnswerDetailDto examAnswerDetail = new ExamAnswerDetailDto();
                BeanUtils.copyProperties(examAnswerBo, examAnswerDetail);
                examAnswerDetail.setContent(bo.getContent());
                examAnswerDetail.setQuestionId(bo.getQuestionId());
                examAnswerDetail.setExamAnswerId(examAnswer.getId());
                examAnswerDetail.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                if (collect.containsKey(examAnswerDetail.getQuestionId())) {
                    QuestionVo questionVo = collect.get(examAnswerDetail.getQuestionId());
                    if (StringUtils.isNotEmpty(questionVo.getRightAnswer())) {
                        examAnswerDetail.setIsRight(2);
                        wrong = wrong+1;
                        if (bo.getContent() != null) {
                            if (questionVo.getRightAnswer().equals(bo.getContent())) {
                                examAnswerDetail.setIsRight(1);
                                right = right+1;
                                wrong = wrong-1;
                            } else {
                                List<String> ans = Arrays.asList(bo.getContent().split(","));
                                boolean b = Stream.of(questionVo.getRightAnswer().split(",")).allMatch(ans::contains);
                                if (b) {
                                    examAnswerDetail.setIsRight(1);
                                    right = right+1;
                                    wrong = wrong-1;
                                }
                            }
                        }
                    }
                }
                arr.add(examAnswerDetail);
            }
        }
        if ( examAnswerMapper.batchInsert(arr) > 0) {
            HashMap<String, Integer> res = new HashMap();
            res.put("right",right);
            res.put("wrong",wrong);
            return AjaxResult.success(res);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamAnswer(ExamAnswerBo examAnswerBo) {
        ExamAnswerDto examAnswer = new ExamAnswerDto();
        BeanUtils.copyProperties(examAnswerBo, examAnswer);
        if (updateById(examAnswer)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<ExamAnswerDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ExamAnswerDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ExamAnswerDto::getId, id);
        ExamAnswerDto examAnswer = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("examAnswerVo", examAnswer == null ? new ExamAnswerVo() : examAnswer);
        return reuslt;
    }

}