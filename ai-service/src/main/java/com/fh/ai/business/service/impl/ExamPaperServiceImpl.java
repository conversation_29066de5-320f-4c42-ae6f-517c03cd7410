package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperBo;
import com.fh.ai.business.entity.bo.examPaper.ExamPaperConditionBo;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDetailDto;
import com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDto;
import com.fh.ai.business.entity.dto.examPaper.ExamPaperDto;
import com.fh.ai.business.entity.vo.examPaper.ExamPaperVo;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import com.fh.ai.business.mapper.ExamAnswerMapper;
import com.fh.ai.business.mapper.ExamPaperMapper;
import com.fh.ai.business.mapper.QuestionMapper;
import com.fh.ai.business.service.IExamPaperService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 题库表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Service
public class ExamPaperServiceImpl extends ServiceImpl<ExamPaperMapper, ExamPaperDto> implements IExamPaperService {

    @Resource
    private ExamPaperMapper examPaperMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private ExamAnswerMapper examAnswerMapper;

    @Override
    public Map<String, Object> getExamPaperListByCondition(ExamPaperConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ExamPaperVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = examPaperMapper.getExamPaperListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<ExamPaperVo> prizeVos = examPaperMapper.getExamPaperListByCondition(conditionBo);
            PageInfo<ExamPaperVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addExamPaper(ExamPaperBo examPaperBo) {
        ExamPaperDto examPaper = new ExamPaperDto();
        BeanUtils.copyProperties(examPaperBo, examPaper);
        examPaper.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(examPaper)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamPaper(ExamPaperBo examPaperBo) {
        ExamPaperDto examPaper = new ExamPaperDto();
        BeanUtils.copyProperties(examPaperBo, examPaper);
        if (examPaper.getState() != null) {
            examPaper.setUpdateTime(null);
        }
        if (updateById(examPaper)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<ExamPaperDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ExamPaperDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ExamPaperDto::getId, id);
        ExamPaperDto examPaper = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        ExamPaperVo examPaperVo = new ExamPaperVo();
        if (examPaper != null) {
            BeanUtils.copyProperties(examPaper, examPaperVo);
            List<QuestionVo> questionListByIds = questionMapper.getQuestionListByIds(examPaper.getQuestionIds());
            examPaperVo.setQuestionList(questionListByIds);
        }
        reuslt.put("examPaperVo", examPaperVo);
        return reuslt;
    }

    @Override
    public Map<String, Object> getDetailNoRight(Long id, String oid) {
        LambdaQueryWrapper<ExamPaperDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ExamPaperDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ExamPaperDto::getId, id);
        ExamPaperDto examPaper = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        ExamPaperVo examPaperVo = new ExamPaperVo();
        if (examPaper != null) {
            BeanUtils.copyProperties(examPaper, examPaperVo);
            List<QuestionVo> questionListByIds = questionMapper.getQuestionListByIds(examPaper.getQuestionIds());
            for (QuestionVo vo : questionListByIds) {
                vo.setRightAnswer(null);
            }
            examPaperVo.setQuestionList(questionListByIds);
            long count = examAnswerMapper.selectCount(new LambdaQueryWrapper<ExamAnswerDto>()
                    .eq(ExamAnswerDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .eq(ExamAnswerDto::getUserOid, oid)
                    .eq(ExamAnswerDto::getExamPaperId, examPaper.getId())
            );
            examPaperVo.setHasAnsTime(count);
        }
        reuslt.put("examPaperVo", examPaperVo);
        return reuslt;
    }

    @Override
    public Map<String, Object> answerDetail(Long examPaperId, Long courseDetailId, String oid) {
        LambdaQueryWrapper<ExamPaperDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ExamPaperDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ExamPaperDto::getId, examPaperId);
        ExamPaperDto examPaper = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        ExamPaperVo examPaperVo = new ExamPaperVo();
        if (examPaper != null) {
            BeanUtils.copyProperties(examPaper, examPaperVo);
            List<QuestionVo> questionListByIds = questionMapper.getQuestionListByIds(examPaper.getQuestionIds());
            LambdaQueryWrapper<ExamAnswerDto> examAnswerQuery = new LambdaQueryWrapper<>();
            examAnswerQuery.eq(ExamAnswerDto::getUserOid, oid);
            examAnswerQuery.eq(ExamAnswerDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            examAnswerQuery.eq(ExamAnswerDto::getExamPaperId, examPaperId);
            if (courseDetailId != null) {
                examAnswerQuery.eq(ExamAnswerDto::getCourseDetailId, courseDetailId);
            }
            examAnswerQuery.orderByDesc(ExamAnswerDto::getId);
            examAnswerQuery.last(" limit 1");
            ExamAnswerDto examAnswerDto = examAnswerMapper.selectOne(examAnswerQuery);
            if (examAnswerDto != null) {
                List<ExamAnswerDetailDto> examAnswerDetail = examAnswerMapper.getExamAnswerDetail(examAnswerDto.getId());
                Map<Long, ExamAnswerDetailDto> collect = examAnswerDetail.stream().collect(Collectors.toMap(ExamAnswerDetailDto::getQuestionId, o -> o, (o1, o2) -> o2));
                for (QuestionVo vo : questionListByIds) {
                    if (collect.containsKey(vo.getId())) {
                        ExamAnswerDetailDto dto = collect.get(vo.getId());
                        vo.setIsRight(dto.getIsRight());
                        vo.setUserAnswer(dto.getContent());
                    }
                }
            }
            examPaperVo.setQuestionList(questionListByIds);
        }
        reuslt.put("examPaperVo", examPaperVo);
        return reuslt;
    }

    @Override
    public AjaxResult statistics(ExamPaperBo examPaperBo) {
        //统计概览
        List<Map> statistics = examPaperMapper.statistics(examPaperBo);
        //统计单位
        List<Map> orgStaList = examPaperMapper.orgStatistics(examPaperBo);
        List<Map> orgUserStaList = examPaperMapper.orgUserStatistics(examPaperBo);
        Map<String, Object> result = new HashMap<String, Object>(4);
        Map<String, String> orgUserMap = new HashMap<String, String>();


        if (CollectionUtil.isEmpty(statistics)) {
            result.put("statistics", new HashMap<>());
            result.put("orgStatistics", new ArrayList<>());
            return AjaxResult.success(result);
        }
        if (CollectionUtil.isNotEmpty(orgUserStaList)) {
            for (Map orgUser:orgUserStaList){
                Object count = orgUser.get("count");
                Object parent_id = orgUser.get("parent_id");
                if(count != null && parent_id != null && !count.toString().equals("0")){
                    orgUserMap.put(parent_id.toString(),count.toString());
                }
            }
        }
        Map statisticsMap = statistics.get(0);
        Object allCount = statisticsMap.get("allCount");
        Object rightCount = statisticsMap.get("rightCount");
        Object userCount = statisticsMap.get("ansUserCount");
        if (rightCount != null && rightCount.toString().equals("0")) {
            statisticsMap.put("rightRatio", "0%");
        } else {
            BigDecimal bdDenominator = new BigDecimal(allCount.toString());
            BigDecimal bdNumerator = new BigDecimal(rightCount.toString());
            BigDecimal res = bdNumerator.multiply(new BigDecimal(100)).divide(bdDenominator, 1, RoundingMode.HALF_UP);
            statisticsMap.put("rightRatio", res.toString().replace(".0", "") + "%");
        }

        for (Map org : orgStaList) {
            Object ansUserCountTmp = org.get("ansUserCount");
            Object allCountTmp = org.get("allCount");
            Object orgParentId = org.get("parent_id");
            Object rightCountTmp = org.get("rightCount");
            if (rightCountTmp != null && rightCountTmp.toString().equals("0")) {
                org.put("rightRatio", "0%");
            } else {
                BigDecimal bdDenominator = new BigDecimal(allCountTmp.toString());
                BigDecimal bdNumerator = new BigDecimal(rightCountTmp.toString());
                BigDecimal resTmp = bdNumerator.multiply(new BigDecimal(100)).divide(bdDenominator, 1, RoundingMode.HALF_UP);
                org.put("rightRatio", resTmp.toString().replace(".0", "") + "%");
            }
            String ansUserCount = orgUserMap.get(orgParentId.toString());
            if(StringUtils.isEmpty(ansUserCount)){
                org.put("joinRatio", "0%");
            }else{
                BigDecimal bdDenominatorTmp1 = new BigDecimal(ansUserCount);
                BigDecimal bdNumeratorTmp1 = new BigDecimal(ansUserCountTmp.toString());
                BigDecimal resTmp1 = bdNumeratorTmp1.multiply(new BigDecimal(100)).divide(bdDenominatorTmp1, 1, RoundingMode.HALF_UP);
                org.put("joinRatio",  resTmp1.toString().replace(".0", "") + "%");
            }

            if (userCount != null && userCount.toString().equals("0")) {
                org.put("orgJoinRatio", "0%");
            } else {
                BigDecimal bdDenominator2 = new BigDecimal(userCount.toString());
                BigDecimal bdNumerator2 = new BigDecimal(ansUserCountTmp.toString());
                BigDecimal res2 = bdNumerator2.multiply(new BigDecimal(100)).divide(bdDenominator2, 1, RoundingMode.HALF_UP);
                org.put("orgJoinRatio", res2.toString().replace(".0", "") + "%");
            }
        }

        result.put("statistics", statisticsMap);
        result.put("orgStatistics", orgStaList);
        return AjaxResult.success(result);
    }

    @Override
    public List<Map> orgNumStatistics(ExamPaperBo examPaperBo) {
        ExamPaperDto examPaperDto = examPaperMapper.selectById(examPaperBo.getId());
        String questionIds = examPaperDto.getQuestionIds();
        if (StringUtils.isEmpty(questionIds)) {
            return new ArrayList();
        }
        examPaperBo.setQuestionIds(questionIds);
        return examPaperMapper.orgNumStatistics(examPaperBo);
    }
}