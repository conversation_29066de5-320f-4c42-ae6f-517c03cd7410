package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailBo;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailConditionBo;
import com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto;
import com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo;
import com.fh.ai.business.mapper.ExcellentWorksDetailMapper;
import com.fh.ai.business.service.IExcellentWorksDetailService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-14  14:29
 */
@Service
public class ExcellentWorksDetailServiceImpl extends ServiceImpl<ExcellentWorksDetailMapper, ExcellentWorksDetailDto> implements IExcellentWorksDetailService {
    @Resource
    private ExcellentWorksDetailMapper excellentWorksDetailMapper;

    @Override
    public AjaxResult addExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo) {
        ExcellentWorksDetailDto entity = new ExcellentWorksDetailDto();
        BeanUtils.copyProperties(excellentWorksDetailBo, entity);
        entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        entity.setCreateTime(new Date());
        if (save(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo) {
        ExcellentWorksDetailDto entity = new ExcellentWorksDetailDto();
        BeanUtils.copyProperties(excellentWorksDetailBo, entity);
        entity.setUpdateTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public Map<String, Object> getExcellentWorksDetailListByCondition(ExcellentWorksDetailConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ExcellentWorksDetailVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = excellentWorksDetailMapper.getExcellentWorksDetailListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit());
            List<ExcellentWorksDetailVo> prizeVos = excellentWorksDetailMapper.getExcellentWorksDetailListByCondition(conditionBo);
            PageInfo<ExcellentWorksDetailVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult deleteExcellentWorksDetail(ExcellentWorksDetailBo excellentWorksDetailBo) {
        ExcellentWorksDetailDto entity = new ExcellentWorksDetailDto();
        BeanUtils.copyProperties(excellentWorksDetailBo, entity);
        entity.setUpdateTime(new Date());
        entity.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }
}
