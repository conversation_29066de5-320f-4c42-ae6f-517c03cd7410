package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeBo;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeConditionBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksUserDto;
import com.fh.ai.business.entity.dto.excellentWorksPrize.ExcellentWorksPrizeDto;
import com.fh.ai.business.entity.dto.worksActive.WorksActiveDto;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import com.fh.ai.business.mapper.ExcellentWorksMapper;
import com.fh.ai.business.mapper.ExcellentWorksPrizeMapper;
import com.fh.ai.business.mapper.WorksActiveMapper;
import com.fh.ai.business.service.IExcellentWorksPrizeService;
import com.fh.ai.business.service.IExcellentWorksUserService;
import com.fh.ai.business.service.IUserScoreRecordService;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.fh.ai.common.enums.ScoreTypeEnum.EXCELLENT_WORKS;

/**
 * 用户作品获奖记录（和奖品表没关系）接口实现类
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 17:59:13
 */
@Service
public class ExcellentWorksPrizeServiceImpl extends ServiceImpl<ExcellentWorksPrizeMapper, ExcellentWorksPrizeDto> implements IExcellentWorksPrizeService {

	@Resource
	private ExcellentWorksPrizeMapper excellentWorksPrizeMapper;

	@Resource
	private ExcellentWorksMapper excellentWorksMapper;

	@Resource
	private WorksActiveMapper worksActiveMapper;

	@Resource
	private IExcellentWorksUserService excellentWorksUserService;

	@Resource
	private IUserScoreRecordService userScoreRecordService;

	@Override
	public List<ExcellentWorksPrizeVo> getExcellentWorksPrizeListByCondition(ExcellentWorksPrizeBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return excellentWorksPrizeMapper.getExcellentWorksPrizeListByCondition(condition);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult addExcellentWorksPrize(ExcellentWorksPrizeBo excellentWorksPrizeBo) {
		ExcellentWorksPrizeDto excellentWorksPrize = new ExcellentWorksPrizeDto();
		BeanUtils.copyProperties(excellentWorksPrizeBo, excellentWorksPrize);
		excellentWorksPrize.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		excellentWorksPrize.setCreateBy(excellentWorksPrizeBo.getCreateBy());
		excellentWorksPrize.setCreateTime(new Date());
		save(excellentWorksPrize);
		List<String> userOidList = StrUtil.split(excellentWorksPrize.getUserOid(), ",");
		//查询作品信息
		ExcellentWorksDto excellentWorksDto = excellentWorksMapper.selectById(excellentWorksPrize.getExcellentWorksId());
		//保存关联表
		saveExcellentWorksUserOid(excellentWorksPrizeBo.getUserOid(), excellentWorksDto, excellentWorksPrizeBo.getCreateBy(),excellentWorksPrize.getId());
		//查询活动信息
		WorksActiveDto worksActiveDto = worksActiveMapper.selectById(excellentWorksDto.getWorksActiveId());
		//发送通知
		for (String userOid : userOidList) {
			NoticeUtil.addExcellentWorks(excellentWorksPrize.getExcellentWorksId(),
					userOid,
					worksActiveDto.getActiveName());
			//增加用戶积分
			if(!excellentWorksPrizeBo.getPrizeType().equals(2)){
				UserScoreRecordBo userScoreRecordBo = setUserScoreRecordBo(excellentWorksPrizeBo, userOid, excellentWorksPrize);
				userScoreRecordService.addUserScoreRecord(userScoreRecordBo);
			}
		}

		return AjaxResult.success("保存成功");
	}

	private static UserScoreRecordBo setUserScoreRecordBo(ExcellentWorksPrizeBo excellentWorksPrizeBo, String userOid, ExcellentWorksPrizeDto excellentWorksPrize) {
		UserScoreRecordBo userScoreRecordBo = new UserScoreRecordBo();
		userScoreRecordBo.setUserOid(userOid);
		userScoreRecordBo.setOrganizationId(excellentWorksPrizeBo.getOrganizationId());
		userScoreRecordBo.setScore(excellentWorksPrize.getPrizeScore());
		userScoreRecordBo.setRelationId(excellentWorksPrize.getId());
		userScoreRecordBo.setType(EXCELLENT_WORKS.getCode());
		userScoreRecordBo.setChannel(1);
		userScoreRecordBo.setCreateTime(new Date());
		userScoreRecordBo.setReceiveTime(new Date());
		userScoreRecordBo.setCreateBy(excellentWorksPrizeBo.getCreateBy());
		return userScoreRecordBo;
	}

	@Override
	public AjaxResult updateExcellentWorksPrize(ExcellentWorksPrizeBo excellentWorksPrizeBo) {
		ExcellentWorksPrizeDto excellentWorksPrize = new ExcellentWorksPrizeDto();
		BeanUtils.copyProperties(excellentWorksPrizeBo, excellentWorksPrize);
		if(updateById(excellentWorksPrize)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ExcellentWorksPrizeVo getExcellentWorksPrizeByCondition(ExcellentWorksPrizeConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return excellentWorksPrizeMapper.getExcellentWorksPrizeByCondition(condition);
	}

	private void saveExcellentWorksUserOid(String userOidStr, ExcellentWorksDto entity,String createBy,Long prizeId) {
		//全删
		excellentWorksUserService.lambdaUpdate()
				.eq(ExcellentWorksUserDto::getExcellentWorksId, entity.getId())
				.set(ExcellentWorksUserDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
				.update();
		List<String> userOidList = StrUtil.split(userOidStr, ",");
		if (CollUtil.isNotEmpty(userOidList)){
			ArrayList<ExcellentWorksUserDto> excellentWorksUserDtoList = new ArrayList<>();
			for (String userOid : userOidList) {
				ExcellentWorksUserDto excellentWorksUserDto = new ExcellentWorksUserDto();
				excellentWorksUserDto.setExcellentWorksId(entity.getId());
				excellentWorksUserDto.setUserOid(userOid);
				excellentWorksUserDto.setExcellentWorksPrizeId(prizeId);
				excellentWorksUserDto.setCreateBy(createBy);
				excellentWorksUserDto.setCreateTime(new Date());
				excellentWorksUserDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
				excellentWorksUserDtoList.add(excellentWorksUserDto);
			}

			excellentWorksUserService.saveBatch(excellentWorksUserDtoList);
		}
	}

}