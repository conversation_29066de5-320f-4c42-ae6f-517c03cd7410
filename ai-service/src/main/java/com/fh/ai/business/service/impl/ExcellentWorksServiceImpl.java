package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Maps;
import com.beust.jcommander.internal.Sets;
import com.fh.ai.business.entity.bo.excellentWorks.*;
import com.fh.ai.business.entity.bo.excellentWorksDetail.ExcellentWorksDetailBo;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeBo;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksUserDto;
import com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.prize.PrizeDto;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksExcelVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksUserStatisticsVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo;
import com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeCountVo;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRankVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.IExcellentWorksDetailService;
import com.fh.ai.business.service.IExcellentWorksService;
import com.fh.ai.business.service.IExcellentWorksUserService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.utils.DateTimeUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  14:40
 */
@Slf4j
@Service
public class ExcellentWorksServiceImpl extends ServiceImpl<ExcellentWorksMapper, ExcellentWorksDto> implements IExcellentWorksService {
    @Resource
    private ExcellentWorksMapper excellentWorksMapper;

    @Resource
    private IExcellentWorksUserService excellentWorksUserService;

    @Resource
    private ExcellentWorksDetailMapper excellentWorksDetailMapper;

    @Resource
    private ExcellentWorksPrizeMapper excellentWorksPrizeMapper;

    @Resource
    private IExcellentWorksDetailService excellentWorksDetailService;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private IOrganizationService organizationService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private WorksActiveVoteRecordMapper worksActiveVoteRecordMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addExcellentWorks(ExcellentWorksAddOrUpdateBo excellentWorksAddOrUpdateBo) {
        //设置统计组织机构
        OrganizationConditionBo organizationConditionBo = new OrganizationConditionBo();
        organizationConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<OrganizationVo> allOrganizationList = organizationMapper.getOrganizationListByCondition(organizationConditionBo);
        Map<Long, OrganizationVo> organizationVoMap = allOrganizationList
                .stream()
                .collect(Collectors.toMap(OrganizationVo::getId, Function.identity()));


        ExcellentWorksDto entity = new ExcellentWorksDto();
        BeanUtils.copyProperties(excellentWorksAddOrUpdateBo, entity);
        entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        entity.setCreateTime(new Date());
        entity.setCreateBy(excellentWorksAddOrUpdateBo.getCreateBy());

        OrganizationVo organizationVo = organizationVoMap.get(excellentWorksAddOrUpdateBo.getOrganizationId());
        //设置统计组织机构
        if (ObjectUtil.equals(organizationVo.getIsStatistics(),IsStatisticsEnumEnum.STATISTICS.getCode())){
            entity.setStatisticsOrganizationId(organizationVo.getId());
        }else{
            //获取上级组织机构
            String superiorIds = organizationVo.getSuperiorIds();
            List<String> superiorIdList = StrUtil.split(superiorIds, ",",true,true);
            for (String superiorId : superiorIdList) {
                OrganizationVo superiorOrganizationVo = organizationVoMap.get(Convert.toLong(superiorId));
                if (superiorId.equals("0")){
                    continue;
                }
                //设置统计组织机构
                if (ObjectUtil.equals(superiorOrganizationVo.getIsStatistics(),IsStatisticsEnumEnum.STATISTICS.getCode())){
                    entity.setStatisticsOrganizationId(superiorOrganizationVo.getId());
                    break;
                }
            }
        }

        save(entity);

//        entity.setSort(entity.getId().intValue());
//        updateById(entity);

        //保存用户ID
        saveExcellentWorksUserOid(excellentWorksAddOrUpdateBo.getUserOid(), entity, excellentWorksAddOrUpdateBo.getCreateBy());

        if (CollUtil.isNotEmpty(excellentWorksAddOrUpdateBo.getExplainWorksDetails())) {
            List<ExcellentWorksDetailDto> excellentWorksDetailDtos = new ArrayList<>();
            for (ExcellentWorksDetailBo explainWorksDetail : excellentWorksAddOrUpdateBo.getExplainWorksDetails()) {
                ExcellentWorksDetailDto excellentWorksDetailDto = new ExcellentWorksDetailDto();
                BeanUtil.copyProperties(explainWorksDetail, excellentWorksDetailDto);
                excellentWorksDetailDto.setExcellentWorksId(entity.getId());
                excellentWorksDetailDtos.add(excellentWorksDetailDto);
                excellentWorksDetailDto.setCreateTime(new Date());
                excellentWorksDetailDto.setCreateBy(excellentWorksAddOrUpdateBo.getCreateBy());
            }
            excellentWorksDetailService.saveBatch(excellentWorksDetailDtos);
        }

        return AjaxResult.success();
    }

    private void saveExcellentWorksUserOid(String userOidStr, ExcellentWorksDto entity, String createBy) {
        //全删
        excellentWorksUserService.lambdaUpdate()
                .eq(ExcellentWorksUserDto::getExcellentWorksId, entity.getId())
                .set(ExcellentWorksUserDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
                .update();
        List<String> userOidList = StrUtil.split(userOidStr, ",");
        if (CollUtil.isNotEmpty(userOidList)) {
            ArrayList<ExcellentWorksUserDto> excellentWorksUserDtoList = new ArrayList<>();
            for (String userOid : userOidList) {
                ExcellentWorksUserDto excellentWorksUserDto = new ExcellentWorksUserDto();
                excellentWorksUserDto.setExcellentWorksId(entity.getId());
                excellentWorksUserDto.setUserOid(userOid);
                excellentWorksUserDto.setCreateBy(createBy);
                excellentWorksUserDto.setCreateTime(new Date());
                excellentWorksUserDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                excellentWorksUserDtoList.add(excellentWorksUserDto);
            }
            excellentWorksUserService.saveBatch(excellentWorksUserDtoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateExcellentWorks(ExcellentWorksAddOrUpdateBo excellentWorksAddOrUpdateBo) {
        ExcellentWorksDto entity = new ExcellentWorksDto();
        BeanUtils.copyProperties(excellentWorksAddOrUpdateBo, entity);
        entity.setUpdateBy(excellentWorksAddOrUpdateBo.getUpdateBy());
        entity.setUpdateTime(new Date());
        updateById(entity);

        //保存用户ID
        saveExcellentWorksUserOid(excellentWorksAddOrUpdateBo.getUserOid(), entity, excellentWorksAddOrUpdateBo.getCreateBy());

        if (CollUtil.isNotEmpty(excellentWorksAddOrUpdateBo.getExplainWorksDetails())) {
            List<ExcellentWorksDetailDto> excellentWorksDetailDtos = BeanUtil.copyToList(excellentWorksAddOrUpdateBo.getExplainWorksDetails(),
                    ExcellentWorksDetailDto.class);
            for (ExcellentWorksDetailDto excellentWorksDetailDto : excellentWorksDetailDtos) {
                //id存在设置修改信息，不存在设置新增信息
                if (ObjectUtil.isNotNull(excellentWorksDetailDto.getId())) {
                    excellentWorksDetailDto.setUpdateBy(excellentWorksAddOrUpdateBo.getUpdateBy());
                    excellentWorksDetailDto.setUpdateTime(new Date());
                    excellentWorksDetailService.updateById(excellentWorksDetailDto);
                } else {
                    excellentWorksDetailDto.setExcellentWorksId(entity.getId());
                    excellentWorksDetailDto.setCreateBy(excellentWorksAddOrUpdateBo.getUpdateBy());
                    excellentWorksDetailDto.setCreateTime(new Date());
                    excellentWorksDetailService.save(excellentWorksDetailDto);
                }
            }
            //获取哪些数据要新增，哪些数据要修改
            LambdaQueryWrapper<ExcellentWorksDetailDto> exprWrapper = new LambdaQueryWrapper<ExcellentWorksDetailDto>()
                    .eq(ExcellentWorksDetailDto::getExcellentWorksId, entity.getId())
                    .eq(ExcellentWorksDetailDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            List<ExcellentWorksDetailDto> list = excellentWorksDetailService.list(exprWrapper);
            //判断查询出来的数据在excellentWorksDetailDtos里存不存在，不存在则放入一个新的集合

            Map<Long, ExcellentWorksDetailDto> excellentWorksDetailDtoMap = excellentWorksDetailDtos.stream()
                    .collect(Collectors.toMap(ExcellentWorksDetailDto::getId, Function.identity()));

            List<ExcellentWorksDetailDto> delList = new ArrayList<>();
            for (ExcellentWorksDetailDto detailDto : list) {
                boolean flag = excellentWorksDetailDtoMap.containsKey(detailDto.getId());
                if (!flag) {
                    detailDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
                    detailDto.setUpdateBy(excellentWorksAddOrUpdateBo.getUpdateBy());
                    detailDto.setUpdateTime(new Date());
                    delList.add(detailDto);
                }
            }
            excellentWorksDetailDtos.addAll(delList);

            excellentWorksDetailService.saveOrUpdateBatch(excellentWorksDetailDtos);
        }

        return AjaxResult.success();
    }

    @Override
    public Map<String, Object> getExcellentWorksListByCondition(ExcellentWorksConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<ExcellentWorksVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = excellentWorksMapper.getExcellentWorksListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit());
            List<ExcellentWorksVo> prizeVos = excellentWorksMapper.getExcellentWorksListByCondition(conditionBo);
            PageInfo<ExcellentWorksVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult deleteExcellentWorks(ExcellentWorksBo excellentWorksBo) {
        ExcellentWorksDto entity = new ExcellentWorksDto();
        entity.setId(excellentWorksBo.getId());
        entity.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        entity.setUpdateTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult deleteExcellentWorks(ExcellentWorksDetailBo excellentWorksDetail) {
        ExcellentWorksDetailDto entity = new ExcellentWorksDetailDto();
        entity.setId(excellentWorksDetail.getId());
        entity.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        entity.setUpdateTime(new Date());
        if (excellentWorksDetailService.updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult releaseCancelExcellentWorks(ExcellentWorksBo excellentWorksBo) {
        log.info("releaseCancelExcellentWorks start");
        ExcellentWorksDto excellentWorks = getById(excellentWorksBo.getId());
        if (excellentWorks == null) {
            return AjaxResult.fail("数据不存在");
        }
        RLock lock = redissonClient.getLock("lock:excellentWorksRelease:" + excellentWorks.getWorksActiveId());
        try {
            lock.lock(5, TimeUnit.SECONDS);
            log.info("get lock");
            ExcellentWorksDto entity = new ExcellentWorksDto();
            entity.setId(excellentWorksBo.getId());
            entity.setHoldType(excellentWorksBo.getHoldType());
            entity.setUpdateBy(excellentWorksBo.getUpdateBy());
            entity.setUpdateTime(new Date());
            if (ExcellentWorksHoldTypeEnum.ANSWER.getCode().equals(excellentWorksBo.getHoldType())
                    && excellentWorks.getExcellentWorksNumber() == ConstantsInteger.NUM_0) {
                Integer excellentWorksNumber = excellentWorksMapper.getExcellentWorksNumber(excellentWorks.getWorksActiveId());
                entity.setExcellentWorksNumber(excellentWorksNumber + 1);
            }
            if (updateById(entity)) {
                return AjaxResult.success();
            }
        } catch (Exception e) {
            log.error("releaseCancelExcellentWorks error:" + e);
        } finally {
            lock.unlock();
        }
        return AjaxResult.fail();
    }

    /**
     * 获取优秀作品列表
     * <p>
     * 传入参数: @param excellentWorksBo 优秀作品页面请求对象，包含分页信息和查询条件
     * 返回值: @return {@link Map }<{@link String }, {@link Object }>
     * 创建人: 杨圣君
     * 创建时间: 2024/11/15
     */
    @Override
    public Map<String, Object> getExcellentWorksListPage(ExcellentWorksConditionBo excellentWorksBo) {
        // 启动分页查询
        PageHelper.startPage(excellentWorksBo.getPage(), excellentWorksBo.getLimit());
        // 初始化结果映射，用于存储作品列表和总数量
        Map<String, Object> map = new HashMap<>(4);
        // 初始化作品列表和总数量
        List<ExcellentWorksVo> list = null;
        long count = 0;

        // 将请求对象转换为查询条件对象，便于后续查询使用
        excellentWorksBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

        // 执行查询，获取优秀作品列表
        list = excellentWorksMapper.getExcellentWorksListByCondition(excellentWorksBo);

        // 创建 PageInfo 对象以获取分页信息
        PageInfo<ExcellentWorksVo> pageInfo = new PageInfo<>(list);

        // 更新列表和总数量
        list = pageInfo.getList();
        count = pageInfo.getTotal();

        // 如果列表不为空，则进一步处理每个作品的详情信息
        if (CollUtil.isNotEmpty(list)) {
            // 获取所有作品的 ID
            Set<Long> idSet = list.stream()
                    .map(ExcellentWorksVo::getId)
                    .collect(Collectors.toSet());
            // 准备查询作品详情的条件
            LambdaQueryWrapper<ExcellentWorksDetailDto> exampleQueryWrapper = new LambdaQueryWrapper<ExcellentWorksDetailDto>()
                    .eq(ExcellentWorksDetailDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .in(ExcellentWorksDetailDto::getExcellentWorksId, idSet);
            // 执行批量查询，获取所有相关作品详情
            List<ExcellentWorksDetailDto> excellentWorksDetailDtos = excellentWorksDetailMapper.selectList(exampleQueryWrapper);
            // 将查询结果转换为目标对象列表
            List<ExcellentWorksDetailVo> excellentWorksDetailVos = BeanUtil.copyToList(excellentWorksDetailDtos, ExcellentWorksDetailVo.class);
            // 根据作品 ID 对详情信息进行分组
            Map<Long, List<ExcellentWorksDetailVo>> excellentWorksDetailVoMap = excellentWorksDetailVos.stream()
                    .collect(Collectors.groupingBy(ExcellentWorksDetailVo::getExcellentWorksId));
            //查询获奖次数
            List<ExcellentWorksPrizeCountVo> excellentWorksPrizeCountVo = excellentWorksPrizeMapper
                    .getExcellentWorksPrizeCountVo(ListUtil.toList(idSet));
            Map<Long, ExcellentWorksPrizeCountVo> excellentWorksPrizeMap = excellentWorksPrizeCountVo.stream()
                    .collect(Collectors.toMap(
                            ExcellentWorksPrizeCountVo::getExcellentWorksId,
                            Function.identity())
                    );
            //设置组织单位
            // 获取单位信息
            List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            List<OrgTreeNodeVo> orgVos = null;
            List<Long> statisticOrgIds = null;
            if (CollectionUtil.isNotEmpty(orgDtos)) {
                orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
                List<OrganizationDto> statisticOrgList = orgDtos.stream()
                        .filter(o -> OrganizationIsStatistics.IS_STATISTICS.getValue().equals(o.getIsStatistics()))
                        .collect(Collectors.toList());
                statisticOrgIds = statisticOrgList.stream().map(OrganizationDto::getId).collect(Collectors.toList());
            }

            // 获取排名
            Map<Long, WorksActiveVoteRankVo> rankMap = Maps.newHashMap();
            if (excellentWorksBo.getWorksActiveId() != null && excellentWorksBo.getQueryVoteAndRank()) {
                List<WorksActiveVoteRankVo> rankVos = worksActiveVoteRecordMapper
                        .getWorksActiveVoteRank(excellentWorksBo.getWorksActiveId(),
                                excellentWorksBo.getStartDay(),
                                excellentWorksBo.getEndDay());
                rankMap = getRankMap(rankVos);
            }

            // 为每个作品对象设置其详情列表
            for (ExcellentWorksVo excellentWorksVo : list) {
                List<ExcellentWorksDetailVo> excellentWorksDetailVoList = excellentWorksDetailVoMap.getOrDefault(excellentWorksVo.getId(),
                        Collections.emptyList());
                ExcellentWorksPrizeCountVo prizeMapOrDefault = excellentWorksPrizeMap.getOrDefault(excellentWorksVo.getId(),
                        new ExcellentWorksPrizeCountVo());
                excellentWorksVo.setExcellentWorksDetailList(excellentWorksDetailVoList);
                excellentWorksVo.setAwardsCumulativeNumber(prizeMapOrDefault.getAwardsNumber());
                excellentWorksVo.setLatestAwardTime(prizeMapOrDefault.getLatestAwardTime());

                String orgNamePath = organizationService.findOrgNamePath(orgVos, excellentWorksVo.getOrganizationId());
                excellentWorksVo.setOrgPath(orgNamePath);

                OrgTreeNodeVo org = organizationService.findStatisticOrg(orgVos, statisticOrgIds, excellentWorksVo.getOrganizationId());
                excellentWorksVo.setStatisticOrgId(org.getId());
                excellentWorksVo.setStatisticOrgName(org.getName());

                if (CollectionUtil.isNotEmpty(rankMap)) {
                    excellentWorksVo.setVoteNumber(rankMap.getOrDefault(excellentWorksVo.getId(), rankMap.get(0L)).getVoteNumber());
                    excellentWorksVo.setRank(rankMap.getOrDefault(excellentWorksVo.getId(), rankMap.get(0L)).getRank());
                }
            }

        }

        // 将处理后的列表和总数量放入结果映射中
        map.put("list", list);
        map.put("count", count);

        // 返回结果映射
        return map;
    }

    /**
     * 获取优秀作品列表
     * <p>
     * 传入参数: @param excellentWorksBo 优秀作品页面请求对象，包含分页信息和查询条件
     * 返回值: @return {@link Map }<{@link String }, {@link Object }>
     * 创建人: 杨圣君
     * 创建时间: 2024/11/15
     */
    @Override
    public List<ExcellentWorksVo> getExcellentWorksList(ExcellentWorksConditionBo excellentWorksBo) {
        // 初始化作品列表和总数量
        List<ExcellentWorksVo> list = null;

        // 将请求对象转换为查询条件对象，便于后续查询使用
        excellentWorksBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

        // 执行查询，获取优秀作品列表
        list = excellentWorksMapper.getExcellentWorksListByCondition(excellentWorksBo);

        // 如果列表不为空，则进一步处理每个作品的详情信息
        if (CollUtil.isNotEmpty(list)) {
            // 获取所有作品的 ID
            Set<Long> idSet = list.stream()
                    .map(ExcellentWorksVo::getId)
                    .collect(Collectors.toSet());
            // 准备查询作品详情的条件
            LambdaQueryWrapper<ExcellentWorksDetailDto> exampleQueryWrapper = new LambdaQueryWrapper<ExcellentWorksDetailDto>()
                    .eq(ExcellentWorksDetailDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .in(ExcellentWorksDetailDto::getExcellentWorksId, idSet);
            // 执行批量查询，获取所有相关作品详情
            List<ExcellentWorksDetailDto> excellentWorksDetailDtos = excellentWorksDetailMapper.selectList(exampleQueryWrapper);
            // 将查询结果转换为目标对象列表
            List<ExcellentWorksDetailVo> excellentWorksDetailVos = BeanUtil.copyToList(excellentWorksDetailDtos, ExcellentWorksDetailVo.class);
            // 根据作品 ID 对详情信息进行分组
            Map<Long, List<ExcellentWorksDetailVo>> excellentWorksDetailVoMap = excellentWorksDetailVos.stream()
                    .collect(Collectors.groupingBy(ExcellentWorksDetailVo::getExcellentWorksId));
            //查询获奖次数
            List<ExcellentWorksPrizeCountVo> excellentWorksPrizeCountVo = excellentWorksPrizeMapper
                    .getExcellentWorksPrizeCountVo(ListUtil.toList(idSet));
            Map<Long, ExcellentWorksPrizeCountVo> excellentWorksPrizeMap = excellentWorksPrizeCountVo.stream()
                    .collect(Collectors.toMap(
                            ExcellentWorksPrizeCountVo::getExcellentWorksId,
                            Function.identity())
                    );
            // 为每个作品对象设置其详情列表
            list.forEach(excellentWorksVo -> {
                List<ExcellentWorksDetailVo> excellentWorksDetailVoList = excellentWorksDetailVoMap.getOrDefault(excellentWorksVo.getId(),
                        Collections.emptyList());
                ExcellentWorksPrizeCountVo prizeMapOrDefault = excellentWorksPrizeMap.getOrDefault(excellentWorksVo.getId(),
                        new ExcellentWorksPrizeCountVo());
                excellentWorksVo.setExcellentWorksDetailList(excellentWorksDetailVoList);
                excellentWorksVo.setAwardsCumulativeNumber(prizeMapOrDefault.getAwardsNumber());
                excellentWorksVo.setLatestAwardTime(prizeMapOrDefault.getLatestAwardTime());
            });
        }

        // 返回结果映射
        return list;
    }

    @Override
    public List<ExcellentWorksExcelVo> getExcellentWorksExcelList(ExcellentWorksConditionBo excellentWorksBo) {
        List<ExcellentWorksExcelVo> excellentWorksExcelVos = new ArrayList<>();
        // 将请求对象转换为查询条件对象，便于后续查询使用
        excellentWorksBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

        // 执行查询，获取优秀作品列表
        List<ExcellentWorksVo> excellentWorksListByCondition = excellentWorksMapper.getExcellentWorksListByCondition(excellentWorksBo);

        //设置组织单位
        // 获取单位信息
        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        List<OrgTreeNodeVo> orgVos = null;
        List<Long> statisticOrgIds = null;
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            List<OrganizationDto> statisticOrgList = orgDtos.stream()
                    .filter(o -> OrganizationIsStatistics.IS_STATISTICS.getValue().equals(o.getIsStatistics()))
                    .collect(Collectors.toList());
            statisticOrgIds = statisticOrgList.stream().map(OrganizationDto::getId).collect(Collectors.toList());
        }


        if (CollUtil.isNotEmpty(excellentWorksListByCondition)) {
            // 获取所有作品的 ID
            Set<Long> idSet = excellentWorksListByCondition.stream()
                    .map(ExcellentWorksVo::getId)
                    .collect(Collectors.toSet());
            //查询获奖次数
            List<ExcellentWorksPrizeCountVo> excellentWorksPrizeCountVo = excellentWorksPrizeMapper
                    .getExcellentWorksPrizeCountVo(ListUtil.toList(idSet));
            Map<Long, ExcellentWorksPrizeCountVo> excellentWorksPrizeMap = excellentWorksPrizeCountVo.stream()
                    .collect(Collectors.toMap(
                            ExcellentWorksPrizeCountVo::getExcellentWorksId,
                            Function.identity())
                    );
            //查询获奖信息
            ExcellentWorksPrizeBo excellentWorksPrizeBo = new ExcellentWorksPrizeBo();
            excellentWorksPrizeBo.setExcellentWorksIdList(ListUtil.toList(idSet));
            List<ExcellentWorksPrizeVo> excellentWorksPrizeVo = excellentWorksPrizeMapper.getExcellentWorksPrizeListByCondition(excellentWorksPrizeBo);
            Map<Long, List<ExcellentWorksPrizeVo>> excellentWorksPrizeVoMap = excellentWorksPrizeVo.stream().collect(Collectors.groupingBy(
                    ExcellentWorksPrizeVo::getExcellentWorksId
            ));

            for (ExcellentWorksVo excellentWorksVo : excellentWorksListByCondition) {
                ExcellentWorksPrizeCountVo prizeMapOrDefault = excellentWorksPrizeMap.getOrDefault(excellentWorksVo.getId(),
                        new ExcellentWorksPrizeCountVo());

                List<ExcellentWorksPrizeVo> worksPrizeVoMapOrDefault = excellentWorksPrizeVoMap.getOrDefault(excellentWorksVo.getId(),
                        new ArrayList<>());

                OrgTreeNodeVo org = organizationService.findStatisticOrg(orgVos, statisticOrgIds, excellentWorksVo.getOrganizationId());

                ExcellentWorksExcelVo excellentWorksExcelVo = new ExcellentWorksExcelVo();
                excellentWorksExcelVo.setSourceType(ExcellentWorksSourceTypeEnum.getMsg(excellentWorksVo.getSourceType()));
                String appType = excellentWorksVo.getAppType() == 0 ? "其他" : TypeEnum.getMsg(excellentWorksVo.getAppType());
                excellentWorksExcelVo.setAppType(appType);
                excellentWorksExcelVo.setWorksName(excellentWorksVo.getWorksName());
                excellentWorksExcelVo.setUserName(excellentWorksVo.getUserName());
                excellentWorksExcelVo.setOrganizationName(org.getName());
                excellentWorksExcelVo.setHoldType(ExcellentWorksHoldTypeEnum.getMsg(excellentWorksVo.getHoldType()));
                excellentWorksExcelVo.setAwardsCumulativeNumber(Convert.toStr(prizeMapOrDefault.getAwardsNumber()));
                if (ObjectUtil.isNotNull(excellentWorksVo.getCreateTime())) {
                    excellentWorksExcelVo.setCreateTime(DateTimeUtil.formatDatetime(excellentWorksVo.getCreateTime()));
                }
                if (CollUtil.isNotEmpty(worksPrizeVoMapOrDefault)) {
                    for (ExcellentWorksPrizeVo worksPrizeVo : worksPrizeVoMapOrDefault) {
                        ExcellentWorksExcelVo excellentWorksExcelVoNew = new ExcellentWorksExcelVo();
                        BeanUtil.copyProperties(excellentWorksExcelVo, excellentWorksExcelVoNew);
                        excellentWorksExcelVoNew.setLatestAwardTime(DateTimeUtil.formatDatetime(worksPrizeVo.getCreateTime()));
                        List<String> prizeCardInfo=new ArrayList<>();
                        if (ObjectUtil.isNotNull(worksPrizeVo.getPrizeScore())) {
                            prizeCardInfo.add("积分：" + worksPrizeVo.getPrizeScore());
                        }
                        if (StrUtil.isNotBlank(worksPrizeVo.getPrizeCardInfo())) {
                            prizeCardInfo.add(worksPrizeVo.getPrizeCardInfo());
                        }
                        excellentWorksExcelVoNew.setPrizeCardInfo(StrUtil.join(",",prizeCardInfo));
                        excellentWorksExcelVos.add(excellentWorksExcelVoNew);
                    }
                    continue;
                }
                excellentWorksExcelVos.add(excellentWorksExcelVo);
            }

        }


        return excellentWorksExcelVos;
    }


    /**
     * 根据ID获取优秀作品的详细信息
     * <p>
     * 传入参数: @param id 优秀作品的ID，用于查询特定作品的详细信息
     * 返回值: @return {@link ExcellentWorksVo }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/15
     */
    @Override
    public ExcellentWorksVo getExcellentWorksDetails(Long id, Boolean queryVoteAndRank) {
        ExcellentWorksVo excellentWorksVo = excellentWorksMapper.getExcellentWorksWithDetailsById(id);
        // 获取所有作品的 ID
        Set<Long> idSet = Sets.newHashSet();
        idSet.add(excellentWorksVo.getId());
        // 准备查询作品详情的条件
        LambdaQueryWrapper<ExcellentWorksDetailDto> exampleQueryWrapper = new LambdaQueryWrapper<ExcellentWorksDetailDto>()
                .eq(ExcellentWorksDetailDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                .in(ExcellentWorksDetailDto::getExcellentWorksId, idSet);
        // 执行批量查询，获取所有相关作品详情
        List<ExcellentWorksDetailDto> excellentWorksDetailDtos = excellentWorksDetailMapper.selectList(exampleQueryWrapper);
        // 将查询结果转换为目标对象列表
        List<ExcellentWorksDetailVo> excellentWorksDetailVos = BeanUtil.copyToList(excellentWorksDetailDtos, ExcellentWorksDetailVo.class);
        // 根据作品 ID 对详情信息进行分组
        Map<Long, List<ExcellentWorksDetailVo>> excellentWorksDetailVoMap = excellentWorksDetailVos.stream()
                .collect(Collectors.groupingBy(ExcellentWorksDetailVo::getExcellentWorksId));
        //查询获奖次数
        List<ExcellentWorksPrizeCountVo> excellentWorksPrizeCountVo = excellentWorksPrizeMapper
                .getExcellentWorksPrizeCountVo(ListUtil.toList(idSet));
        Map<Long, ExcellentWorksPrizeCountVo> excellentWorksPrizeMap = excellentWorksPrizeCountVo.stream()
                .collect(Collectors.toMap(
                        ExcellentWorksPrizeCountVo::getExcellentWorksId,
                        Function.identity())
                );
        //设置组织单位
        // 获取单位信息
        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        List<OrgTreeNodeVo> orgVos = null;
        List<Long> statisticOrgIds = null;
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            List<OrganizationDto> statisticOrgList = orgDtos.stream()
                    .filter(o -> OrganizationIsStatistics.IS_STATISTICS.getValue().equals(o.getIsStatistics()))
                    .collect(Collectors.toList());
            statisticOrgIds = statisticOrgList.stream().map(OrganizationDto::getId).collect(Collectors.toList());
        }

        // 为每个作品对象设置其详情列表
        List<ExcellentWorksDetailVo> excellentWorksDetailVoList = excellentWorksDetailVoMap.getOrDefault(excellentWorksVo.getId(),
                Collections.emptyList());
        ExcellentWorksPrizeCountVo prizeMapOrDefault = excellentWorksPrizeMap.getOrDefault(excellentWorksVo.getId(),
                new ExcellentWorksPrizeCountVo());
        excellentWorksVo.setExcellentWorksDetailList(excellentWorksDetailVoList);
        excellentWorksVo.setAwardsCumulativeNumber(prizeMapOrDefault.getAwardsNumber());
        excellentWorksVo.setLatestAwardTime(prizeMapOrDefault.getLatestAwardTime());

        String orgNamePath = organizationService.findOrgNamePath(orgVos, excellentWorksVo.getOrganizationId());
        excellentWorksVo.setOrgPath(orgNamePath);

        OrgTreeNodeVo org = organizationService.findStatisticOrg(orgVos, statisticOrgIds, excellentWorksVo.getOrganizationId());
        excellentWorksVo.setStatisticOrgId(org.getId());
        excellentWorksVo.setStatisticOrgName(org.getName());
        excellentWorksVo.setOrganizationName(org.getName());

        // 查询排名
        if (queryVoteAndRank != null && queryVoteAndRank) {
            List<WorksActiveVoteRankVo> rankVos = worksActiveVoteRecordMapper
                    .getWorksActiveVoteRank(excellentWorksVo.getWorksActiveId(), null, null);
            Map<Long, WorksActiveVoteRankVo> rankMap = getRankMap(rankVos);
            excellentWorksVo.setRank(rankMap.getOrDefault(id, rankMap.get(0L)).getRank());
            excellentWorksVo.setVoteNumber(rankMap.getOrDefault(id, rankMap.get(0L)).getVoteNumber());
        }
        return excellentWorksVo;
    }

    /**
     * 获取排名
     *
     * @param rankVos
     * @return java.util.Map<java.lang.Long,com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRankVo>
     * <AUTHOR>
     * @date 2025/3/5 14:54
     **/
    private Map<Long, WorksActiveVoteRankVo> getRankMap(List<WorksActiveVoteRankVo> rankVos) {
        Map<Long, WorksActiveVoteRankVo> rankMap = new HashMap<>(rankVos.size());  // 指定初始化容量

        int previousVote = Integer.MIN_VALUE;  // 使用基本类型避免装箱
        int currentRank = 0;

        for (int i = 0; i < rankVos.size(); i++) {
            WorksActiveVoteRankVo vo = rankVos.get(i);
            int currentVote = vo.getVoteNumber();

            // 仅当票数变化时更新排名（支持并列排名）
            if (currentVote != previousVote) {
                currentRank = currentRank + 1;  // 排名从1开始
                previousVote = currentVote;
            }
            vo.setRank(currentRank);
            rankMap.put(vo.getExcellentWorksId(), vo);
        }
        // 设置0票名次入map
        WorksActiveVoteRankVo rankVo = new WorksActiveVoteRankVo();
        rankVo.setVoteNumber(0);
        rankVo.setRank(currentRank + 1);
        rankMap.put(0L, rankVo);
        return rankMap;
    }


    @Override
    public AjaxResult updateSort(ExcellentWorksBo excellentWorksBo) {
        if (excellentWorksBo.getSort() != null) {
            if (excellentWorksBo.getSort() > 0) {
                update(null, new LambdaUpdateWrapper<ExcellentWorksDto>()
                        .eq(ExcellentWorksDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(ExcellentWorksDto::getSort, excellentWorksBo.getSort())
                        .set(ExcellentWorksDto::getSort, null)
                );
            } else {
                update(null, new LambdaUpdateWrapper<ExcellentWorksDto>()
                        .eq(ExcellentWorksDto::getId, excellentWorksBo.getId())
                        .set(ExcellentWorksDto::getSort, null)
                );
                return AjaxResult.success();
            }
        }
        ExcellentWorksDto excellentWorksDto = new ExcellentWorksDto();
        BeanUtils.copyProperties(excellentWorksBo, excellentWorksDto);

        excellentWorksDto.setUpdateTime(new Date());
        updateById(excellentWorksDto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult checkUserAdd(ExcellentWorksBo excellentWorksBo) {
        //查询是否投稿过
        Long count = excellentWorksMapper.selectCount(new LambdaQueryWrapper<ExcellentWorksDto>()
                .eq(ExcellentWorksDto::getBusinessId, excellentWorksBo.getBusinessId())
                .eq(ExcellentWorksDto::getWorksActiveId, excellentWorksBo.getWorksActiveId())
                .eq(ExcellentWorksDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
        );
        //查询到数据，证明投稿过，返回true
        return AjaxResult.success(count > 0);
    }

    /**
     * 用户统计
     * <p>
     * 传入参数: @param excellentWorksUserStatisticsConditionBo 优秀作品 用户统计 状况 bo
     * 返回值: @return {@link Map }<{@link String }, {@link Object }>
     * 创建人: 杨圣君
     * 创建时间: 2024/12/02
     */
    public List<ExcellentWorksUserStatisticsVo> userStatistics(ExcellentWorksUserStatisticsConditionBo excellentWorksUserStatisticsConditionBo) {
        ArrayList<ExcellentWorksUserStatisticsVo> excellentWorksUserStatisticsVos = new ArrayList<>();
        List<ExcellentWorksUserStatisticsVo> statisticsByOrganizationList = excellentWorksMapper
                .getStatisticsByOrganization(excellentWorksUserStatisticsConditionBo);

        Map<Long, ExcellentWorksUserStatisticsVo> excellentWorksUserStatisticsVoMap = statisticsByOrganizationList.stream().collect(Collectors.toMap(
                ExcellentWorksUserStatisticsVo::getOrganizationId,
                Function.identity()
        ));
        //查询相应时间范围内优秀作品
        List<OrganizationVo> statisticsOrganizationList = organizationMapper.getStatisticsOrganizationList();
        statisticsOrganizationList.forEach(organizationVo -> {
            ExcellentWorksUserStatisticsVo orDefault = excellentWorksUserStatisticsVoMap.getOrDefault(organizationVo.getId(),
                    new ExcellentWorksUserStatisticsVo());
            ExcellentWorksUserStatisticsVo excellentWorksUserStatisticsVo = new ExcellentWorksUserStatisticsVo();
            excellentWorksUserStatisticsVo.setOrganizationId(organizationVo.getId());
            excellentWorksUserStatisticsVo.setOrganizationName(organizationVo.getName());
            excellentWorksUserStatisticsVo.setTotalWorksCount(orDefault.getTotalWorksCount());
            excellentWorksUserStatisticsVo.setSelectedWorksCount(orDefault.getSelectedWorksCount());
            excellentWorksUserStatisticsVo.setTotalUsersCount(orDefault.getTotalUsersCount());
            excellentWorksUserStatisticsVo.setSelectedUsersCount(orDefault.getSelectedUsersCount());
            excellentWorksUserStatisticsVos.add(excellentWorksUserStatisticsVo);
        });
        return excellentWorksUserStatisticsVos;
    }

    @Override
    public AjaxResult releaseCheck(String userOid, Long worksActiveId) {
        List<String> userOids = Arrays.asList(userOid.split(","));
        boolean oneUser = userOids.size() == ConstantsInteger.NUM_1;
        // 查询已发布作品
        ExcellentWorksConditionBo conditionBo = new ExcellentWorksConditionBo();
        conditionBo.setHoldType(ExcellentWorksHoldTypeEnum.ANSWER.getCode());
        conditionBo.setWorksActiveId(worksActiveId);
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (oneUser) {
            conditionBo.setUserOid(userOid);
        }
        List<ExcellentWorksVo> list = excellentWorksMapper.getExcellentWorksListByCondition(conditionBo);
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.success(ConstantsInteger.NUM_0);
        } else if (oneUser) {
            return AjaxResult.success(list.size());
        }

        // 筛选作者数量相等的数据
        list = list.stream()
                .filter(x -> StringUtils.isNotBlank(x.getUserOid()) && userOids.size() == x.getUserOid().split(",").length)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.success(ConstantsInteger.NUM_0);
        }

        // 判断作者是否相同
        int holdCount = 0;
        for (ExcellentWorksVo excellentWorksVo : list) {
            List<String> worksUserOids = Arrays.asList(excellentWorksVo.getUserOid().split(","));
            // 比较两个list元素是否完全相同
            if (CollectionUtils.isEqualCollection(userOids, worksUserOids)) {
                holdCount++;
            }
        }
        return AjaxResult.success(holdCount);
    }
}
