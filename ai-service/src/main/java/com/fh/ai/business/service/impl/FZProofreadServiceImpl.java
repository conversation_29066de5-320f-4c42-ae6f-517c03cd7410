package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.business.entity.vo.proofreading.FZExecuteParam;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import com.fh.ai.business.mapper.ProofreadingTaskMapper;
import com.fh.ai.business.service.IFZProofreadService;
import com.fh.ai.business.service.IProofreadingRecordService;
import com.fh.ai.business.service.IProofreadingTaskService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.fileextract.FileextractUtil;
import com.fh.ai.common.proofreading.fz.FZProofreadingUtil;
import com.fh.ai.common.proofreading.fz.req.*;
import com.fh.ai.common.proofreading.fz.vo.*;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Classname FZProofreadServiceImpl
 * @Description 方正审校业务实现类
 * @Date 2025/1/22 9:32
 * @Created by dyj
 */
@Slf4j
@Service
public class FZProofreadServiceImpl implements IFZProofreadService {

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;
    /**
     * 查询异步任务状态最大重试次数
     */
    private static final Integer MAX_ATTEMPTS = 90;
    /**
     * 查询异步任务状态线程休眠时间
     */
    private static final Integer SLEEP_INTERVAL_MS = 10000;

    /**
     * 方正审校工具类
     */
    @Resource
    private FZProofreadingUtil fzProofreadingUtil;
    /**
     * 文件内容抽取
     */
    @Resource
    private FileextractUtil fileextractUtil;

    /**
     * 审校任务业务类
     */
    @Resource
    IProofreadingTaskService proofreadingTaskService;
    /**
     * 审校任务mapper
     */
    @Resource
    ProofreadingTaskMapper proofreadingTaskMapper;
    /**
     * 审校记录业务类
     */
    @Resource
    IProofreadingRecordService proofreadingRecordService;

    /***
     * 方正在线同步审校
     * 
     * @param fzSetting 选项
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createFZOnlineSyncTask(FZSetting fzSetting) {
        Long recordId = fzSetting.getRecordId();
        // 根据审校记录id找到审校记录
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setId(fzSetting.getRecordId());
        ProofreadingRecordVo record = proofreadingRecordService.getProofreadingRecordByCondition(conditionBo);
        // 校验审校记录
        AjaxResult checkRecordLegal = proofreadingRecordService.checkRecordLegal(record, fzSetting.getType());
        if (!checkRecordLegal.getSuccess()) {
            return checkRecordLegal;
        }

        // 校验审校记录的待执行列表
        List<Integer> excutedList = JSON.parseArray(record.getExecutedTaskInfo(), Integer.class);
        if (!excutedList.contains(ProofreadingExcutedEnum.ONLINE_FZ_WORD.getCode())) {
            return AjaxResult.fail("审校记录执行列表未找到方正在线文本审校任务");
        }

        Map<Integer, ProofreadingTaskVo> fzRecordTaskMap = getFZRecordTaskMap(record.getId());
        if (fzRecordTaskMap.containsKey(ProofreadingTaskType.WORDS_CHECK.getCode())) {
            return AjaxResult.fail("当前审校记录已经存在处理中或处理成功的在线文本审校任务，请重新发起审校记录");
        }
        // 构造方正文本审校传参
        FZReviewTxtReq fzReviewTxtReq = buildFZTxtReq(fzSetting);
        ProofreadingTaskBo proofreadingTaskBo = new ProofreadingTaskBo();
        // 来源：方正
        proofreadingTaskBo.setSourceType(ProofreadingTaskSourceType.FZ.getCode());
        // 任务类型：字词字符审校
        proofreadingTaskBo.setTaskType(ProofreadingTaskType.WORDS_CHECK.getCode());
        // 审校项参数
        proofreadingTaskBo.setSettingOptionInfo(JSON.toJSONString(fzSetting.getOption()));
        // p_proofreading_record主键
        proofreadingTaskBo.setProofreadingRecordId(recordId);
        // 构造其他参数
        buildProofreadingTask(fzReviewTxtReq, null, null, proofreadingTaskBo);
        // 保存审校任务
        AjaxResult saveTaskResult = proofreadingTaskService.createProofreadingTask(proofreadingTaskBo);
        ProofreadingTaskDto proofreadingTask = (ProofreadingTaskDto)saveTaskResult.getData();
        // 执行审校请求
        AjaxResult onlineSyncTask = fzProofreadingUtil.createOnlineSyncTask(fzReviewTxtReq);
        // 请求返回失败
        if (!onlineSyncTask.getSuccess()) {
            proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), JSON.toJSONString(onlineSyncTask),
                onlineSyncTask.getMsg());
            return onlineSyncTask;
        }
        // 请求成功获取结果
        TxtReviewSync txtReviewSync = (TxtReviewSync)onlineSyncTask.getData();
        ProofreadingTaskDto updateTask = new ProofreadingTaskDto();
        updateTask.setId(proofreadingTask.getId());
        // 状态成功
        updateTask.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        // 接口返回体
        updateTask.setResponseInfo(JSON.toJSONString(txtReviewSync));
        // 同步任务id
        updateTask.setThirdTaskId(txtReviewSync.getJobId());
        updateTask.setFinishTime(new Date());
        updateTask.setUpdateTime(new Date());
        proofreadingTaskService.updateById(updateTask);

        return onlineSyncTask;
    }

    /**
     * 方正在线内容审校
     * 
     * @param fzSetting
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createFZContentSyncTask(FZSetting fzSetting) {
        // 根据审校记录id找到审校记录
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setId(fzSetting.getRecordId());
        ProofreadingRecordVo record = proofreadingRecordService.getProofreadingRecordByCondition(conditionBo);
        // 校验审校记录
        AjaxResult checkRecordLegal = proofreadingRecordService.checkRecordLegal(record, fzSetting.getType());
        if (!checkRecordLegal.getSuccess()) {
            return checkRecordLegal;
        }
        // 校验审校记录的待执行列表
        List<Integer> excutedList = JSON.parseArray(record.getExecutedTaskInfo(), Integer.class);
        if (!excutedList.contains(ProofreadingExcutedEnum.ONLINE_FZ_CONTENT.getCode())) {
            return AjaxResult.fail("审校记录执行列表未找到方正在线重要讲话审校任务");
        }
        // 校验是否存在已执行成功的任务任务
        Map<Integer, ProofreadingTaskVo> fzRecordTaskMap = getFZRecordTaskMap(record.getId());
        if (fzRecordTaskMap.containsKey(ProofreadingTaskType.CONTENT_CHECK.getCode())) {
            return AjaxResult.fail("当前审校记录已经存在处理中或处理成功的在线内容审校任务，请重新发起审校记录");
        }
        // 构建方正内容审校请求入参
        FZReviewContentReq fzReviewContentReq = buildFZContentReq(fzSetting, null);
        if (Objects.isNull(fzReviewContentReq)) {
            return AjaxResult.success("构造方正在线内容审校失败入参");
        }
        ProofreadingTaskBo proofreadingTaskBo = new ProofreadingTaskBo();
        proofreadingTaskBo.setSourceType(ProofreadingTaskSourceType.FZ.getCode());
        // 任务类型：重要讲话审校
        proofreadingTaskBo.setTaskType(ProofreadingTaskType.CONTENT_CHECK.getCode());
        // // 审校项参数
        // proofreadingTaskBo.setSettingOptionInfo(JSON.toJSONString(fzSetting.getOption()));
        // p_proofreading_record主键
        proofreadingTaskBo.setProofreadingRecordId(fzSetting.getRecordId());
        // 构造其他参数
        buildProofreadingTask(null, fzReviewContentReq, null, proofreadingTaskBo);
        // 保存审校任务
        AjaxResult saveTaskResult = proofreadingTaskService.createProofreadingTask(proofreadingTaskBo);
        ProofreadingTaskDto proofreadingTask = (ProofreadingTaskDto)saveTaskResult.getData();
        // 执行内容同步审校
        AjaxResult contentASyncTask = fzProofreadingUtil.createContentSyncTask(fzReviewContentReq);
        // 请求返回失败
        if (!contentASyncTask.getSuccess()) {
            proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), JSON.toJSONString(contentASyncTask),
                contentASyncTask.getMsg());
            return contentASyncTask;
        }
        // 获取审校结果
        ContentverifyTaskOutPut contentverifyTaskOutPut = (ContentverifyTaskOutPut)contentASyncTask.getData();
        // 更新审校任务信息
        ProofreadingTaskBo updateTask = new ProofreadingTaskBo();
        updateTask.setId(proofreadingTask.getId());
        updateTask.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        updateTask.setResponseInfo(JSON.toJSONString(contentverifyTaskOutPut));
        updateTask.setThirdTaskId(contentverifyTaskOutPut.getJobId());
        updateTask.setFinishTime(new Date());
        proofreadingTaskService.updateProofreadingTask(updateTask);
        return contentASyncTask;
    }

    /***
     * 方正文档审校
     * 
     * @param fzSetting
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createFZFileTask(FZSetting fzSetting) {
        // 根据审校记录id找到审校记录
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setId(fzSetting.getRecordId());
        // 校验审校记录是否可以发起审校任务
        ProofreadingRecordVo record = proofreadingRecordService.getProofreadingRecordByCondition(conditionBo);
        AjaxResult checkRecordLegal = proofreadingRecordService.checkRecordLegal(record, fzSetting.getType());
        if (!checkRecordLegal.getSuccess()) {
            return checkRecordLegal;
        }
        // 校验审校记录的待执行列表
        List<Integer> excutedList = JSON.parseArray(record.getExecutedTaskInfo(), Integer.class);
        // 获取本地审校文件路径
        String originalFileUrl = record.getOriginalFileUrl();
        // 获取原始文件名
        String originalFileName = record.getOriginalFileName();
        // 构造原始文件路径
        String filePath = SystemUtil.isWindows() ? windowsPath + originalFileUrl : linuxPath + originalFileUrl;
        // 获取该审校记录下的所有审校任务
        Map<Integer, ProofreadingTaskVo> fzRecordTaskMap = getFZRecordTaskMap(record.getId());

        List<FZExecuteParam> params = new ArrayList<>();
        FZExecuteParam fileTaskParam = null;
        // 根据任务开启标记、当前记录是否存在已完成的审校任务，来判断是否需要发起对应审校请求
        if (fzSetting.getStartFileTaskFlag() && excutedList.contains(ProofreadingExcutedEnum.FILE_FZ_WORD.getCode())
            && !fzRecordTaskMap.containsKey(ProofreadingTaskType.WORDS_CHECK.getCode())) {
            // 构造并保存文件审校任务和审校请求。
            FZReviewTxtReq fzReviewFileReq = buildFZTxtReq(fzSetting);
            fileTaskParam = buildExecuteParam(fzSetting, fzReviewFileReq, ProofreadingTaskType.WORDS_CHECK.getCode());
        }

        if (fzSetting.getStartContentTaskFlag()
            && excutedList.contains(ProofreadingExcutedEnum.FILE_FZ_CONTENT.getCode())
            && !fzRecordTaskMap.containsKey(ProofreadingTaskType.CONTENT_CHECK.getCode())) {
            // 构造内容审校入参
            FZReviewContentReq contentReq = buildFZContentReq(fzSetting, ProofreadingTaskType.CONTENT_CHECK.getCode());
            if (Objects.nonNull(contentReq)) {
                FZExecuteParam contentParam =
                    buildExecuteParam(fzSetting, contentReq, ProofreadingTaskType.CONTENT_CHECK.getCode());
                params.add(contentParam);
            }
        }

        if (fzSetting.getStartDocDupTaskFlag() && excutedList.contains(ProofreadingExcutedEnum.FILE_FZ_DUP.getCode())
            && !fzRecordTaskMap.containsKey(ProofreadingTaskType.DUPLICATE_CHECK.getCode())) {
            // 构造上下文查重审校入参
            FZReviewDocsimReq docsimReq = buildFZDocDupReq(fzSetting);
            if (Objects.nonNull(docsimReq)) {
                FZExecuteParam docsimParam =
                    buildExecuteParam(fzSetting, docsimReq, ProofreadingTaskType.DUPLICATE_CHECK.getCode());
                params.add(docsimParam);
            }
        }

        if (fzSetting.getStartReferenceTask()
            && excutedList.contains(ProofreadingExcutedEnum.FILE_FZ_REFERENCE.getCode())
            && !fzRecordTaskMap.containsKey(ProofreadingTaskType.REFERENCES.getCode())) {
            // 构造参考文献审校入参
            FZReviewContentReq referenceReq = buildFZContentReq(fzSetting, ProofreadingTaskType.REFERENCES.getCode());
            if (Objects.nonNull(referenceReq)) {
                FZExecuteParam referenceParam =
                    buildExecuteParam(fzSetting, referenceReq, ProofreadingTaskType.REFERENCES.getCode());
                params.add(referenceParam);
            }
        }

        // 执行文件审校异步任务
        if (Objects.nonNull(fileTaskParam)) {
            executeFileTask(fileTaskParam, filePath, record, originalFileName);
        }

        // 执行内容审校、参考文献审校、上下文查重审校异步任务
        if (CollectionUtils.isNotEmpty(params)) {
            executeContentAndDupTask(params, filePath);
        }
        return AjaxResult.success("发起方正文件审校成功，请等待处理结果....");
    }

    /**
     * 构建并保存审校任务，返回执行审校异步任务的入参
     * 
     * @param fzSetting 方正审校设置
     * @param req 审校请求
     * @param taskType 审校类型
     * @return
     */
    private FZExecuteParam buildExecuteParam(FZSetting fzSetting, Object req, Integer taskType) {
        FZExecuteParam executeParam = new FZExecuteParam();
        // 构建
        ProofreadingTaskBo proofreadingTaskBo = buildProofreadTaskByTaskType(fzSetting, taskType);
        executeParam.setType(taskType);
        // 文件审校，即字词字符审校
        if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(taskType)) {
            FZReviewTxtReq fzReviewTxtReq = (FZReviewTxtReq)req;
            buildProofreadingTask(fzReviewTxtReq, null, null, proofreadingTaskBo);
            executeParam.setFzReviewTxtReq(fzReviewTxtReq);
            // 内容审校和参考文献审校
        } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(taskType)
            || ProofreadingTaskType.REFERENCES.getCode().equals(taskType)) {
            FZReviewContentReq fzReviewContentReq = (FZReviewContentReq)req;
            buildProofreadingTask(null, fzReviewContentReq, null, proofreadingTaskBo);
            executeParam.setFzReviewContentReq(fzReviewContentReq);
            // 上下文查重审校
        } else if (ProofreadingTaskType.DUPLICATE_CHECK.getCode().equals(taskType)) {
            FZReviewDocsimReq fzReviewDocsimReq = (FZReviewDocsimReq)req;
            buildProofreadingTask(null, null, fzReviewDocsimReq, proofreadingTaskBo);
            executeParam.setFzReviewDocsimReq(fzReviewDocsimReq);
        }
        // 保存
        AjaxResult saveTaskResult = proofreadingTaskService.createProofreadingTask(proofreadingTaskBo);
        executeParam.setTaskDto((ProofreadingTaskDto)saveTaskResult.getData());
        return executeParam;
    }

    /**
     * 执行方正文件审校
     * 
     * @param param
     * @param filePath
     * @param record
     */
    private void executeFileTask(FZExecuteParam param, String filePath, ProofreadingRecordVo record,
        String originalFileName) {
        FZReviewTxtReq fzReviewFileReq = param.getFzReviewTxtReq();
        ProofreadingTaskDto proofreadingTask = param.getTaskDto();
        ProofreadingTaskDto updateTask = new ProofreadingTaskDto();
        updateTask.setId(proofreadingTask.getId());

        // 时间戳
        long millis = System.currentTimeMillis();
        // 创建文件
        File file = new File(filePath);
        // 获取文件名和文件后缀
        String[] fileNameList = StringKit.getFileNameList(originalFileName);
        // 创建新的文件路径
        Path sourcePath = file.toPath();
        Path targetPath = Paths.get(file.getParent(), fileNameList[ConstantsInteger.NUM_0] + "_" + millis
            + record.getId() + fileNameList[ConstantsInteger.NUM_1]);
        Path copyed = null;
        try {
            copyed = Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error("重命名文件失败，{}，错误堆栈：", e.getMessage(), e);
            proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), null, "方正文件审校，复制文件失败");
            return;
        }
        // 获取重命名后的文件
        File newFile = copyed.toFile();

        // 执行文件审校任务编排
        CompletableFuture
            // 上传文件到方正，得到第三方文件id
            .supplyAsync(() -> fzProofreadingUtil.uploadFile(newFile), ThreadUtil.fzFilePool)
            // 通过第三方文件id发起文件审校，得到jobId
            .thenCompose((ajaxResult) -> {
                if (!ajaxResult.getSuccess()) {
                    return CompletableFuture.completedFuture(ajaxResult);
                }
                FileInfo fileInfo = (FileInfo)ajaxResult.getData();
                fzReviewFileReq.setId(fileInfo.getId());
                updateTask.setThirdFileId(fileInfo.getId());
                AjaxResult fileTask = fzProofreadingUtil.createFileTask(fzReviewFileReq);
                return CompletableFuture.completedFuture(fileTask);
            })
            // 通过jobId获取任务状态
            .thenCompose(fileTask -> {
                if (!fileTask.getSuccess()) {
                    return CompletableFuture.completedFuture(fileTask);
                }
                String jobId = (String)fileTask.getData();
                // 自旋等待
                AjaxResult fileTaskState = checkJobStateAndSpin(jobId);
                return CompletableFuture.completedFuture(fileTaskState);
            })
            // 通过jobId获取任务结果
            .thenCompose((fileTaskState) -> {
                if (!fileTaskState.getSuccess()) {
                    return CompletableFuture.completedFuture(fileTaskState);
                }
                JobState job = (JobState)fileTaskState.getData();
                String jobId = job.getJobId();
                updateTask.setThirdTaskId(jobId);
                AjaxResult fileTaskResult = fzProofreadingUtil.queryFileTaskResult(jobId);
                return CompletableFuture.completedFuture(fileTaskResult);
            })
            // 通过任务结果获取审校结果资源下载地址
            .thenCompose(fileTaskResult -> {
                if (!fileTaskResult.getSuccess()) {
                    return CompletableFuture.completedFuture(fileTaskResult);
                }
                // 审校结果
                TxtReviewASync txtReviewASync = (TxtReviewASync)fileTaskResult.getData();
                // 记录任务结果
                updateTask.setResponseInfo(JSON.toJSONString(txtReviewASync));
                // 获取审校报告对象
                BookReviewReport report = txtReviewASync.getReport();
                // 资源列表
                List<FileResource> fileResources = new ArrayList<>();
                // 存在审校成功，但是无错误，不返回审校结果文档的情况，需做判断。
                if (Objects.nonNull(report)) {
                    // word批注文件id
                    String pdfMarkId = report.getPdfMarkId();
                    // 审校报告id
                    String reportId = report.getReportId();
                    if (StringUtils.isNotBlank(pdfMarkId)) {
                        // 获取批注文件
                        AjaxResult pdfMarkResult = fzProofreadingUtil.queryFileUrl(pdfMarkId);
                        if (!pdfMarkResult.getSuccess()) {
                            return CompletableFuture.completedFuture(pdfMarkResult);
                        }
                        // 批注文件
                        FileResource markFileResource = (FileResource)pdfMarkResult.getData();
                        markFileResource.setFileType(FileResourceFileTypeEnum.MARK_FILE.getCode());
                        if (StringUtils.isBlank(markFileResource.getFileName())){
                            markFileResource.setFileName(record.getOriginalFileName());
                        }
                        fileResources.add(markFileResource);
                    }
                    if (StringUtils.isNotBlank(reportId)) {
                        // 获取审校报告文件
                        AjaxResult reportResult = fzProofreadingUtil.queryFileUrl(reportId);
                        if (!reportResult.getSuccess()) {
                            return CompletableFuture.completedFuture(reportResult);
                        }
                        // 审校报告
                        FileResource reportFileResource = (FileResource)reportResult.getData();
                        reportFileResource.setFileType(FileResourceFileTypeEnum.REPORT_FILE.getCode());
                        if (StringUtils.isBlank(reportFileResource.getFileName())){
                            reportFileResource.setFileName(record.getOriginalFileName());
                        }
                        fileResources.add(reportFileResource);
                    }
                }
                return CompletableFuture.completedFuture(AjaxResult.success(fileResources));
            })
            // 处理结果
            .handle((result, e) -> {
                // 发生异常
                if (e != null) {
                    String msg = handleException(e, proofreadingTask);
                    return AjaxResult.fail("执行方正文件审校发生异常：" + msg);
                }
                // 执行失败
                if (!result.getSuccess()) {
                    proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), JSON.toJSONString(result),
                        result.getMsg());
                    // 执行成功
                } else {
                    List<FileResource> fileResources = (List<FileResource>)result.getData();
                    // 上传文件至服务器，并保存到审校任务中。
                    proofreadingTaskService.uploadFileProofreadingTask(fileResources, updateTask,
                        record.getOriginalFileName(), "方案二");
                }

                return result;
            });
    }

    /**
     * 执行方正内容审校、参考文献审校、上下文查重审校
     * 
     * @param params
     * @param filePath
     */
    private void executeContentAndDupTask(List<FZExecuteParam> params, String filePath) {
        CompletableFuture
            // 抽取文件内容
            .supplyAsync(() -> getFileExtractResult(filePath), ThreadUtil.getFileContentPool)
            // 并将文件内容放入审校请求入参，保存请求参数
            .thenApply(result -> {
                if (!result.getSuccess()) {
                    return result;
                }
                String fileContent = (String)result.getData();
                if (StringUtils.isBlank(fileContent)) {
                    return AjaxResult.fail("抽取文件内容失败，未获取到文件内容");
                }
                // 保存文件内容到审校任务
                for (FZExecuteParam param : params) {
                    ProofreadingTaskDto taskDto = param.getTaskDto();
                    FZReviewDocsimReq fzReviewDocsimReq = param.getFzReviewDocsimReq();
                    FZReviewContentReq fzReviewContentReq = param.getFzReviewContentReq();
                    String requestStr = null;
                    if (Objects.nonNull(fzReviewDocsimReq)) {
                        requestStr = JSON.toJSONString(fzReviewDocsimReq);
                        fzReviewDocsimReq.setText(fileContent);
                    } else if (Objects.nonNull(fzReviewContentReq)) {
                        requestStr = JSON.toJSONString(fzReviewContentReq);
                        fzReviewContentReq.setText(fileContent);
                    }
                    ProofreadingTaskDto update = new ProofreadingTaskDto();
                    update.setId(taskDto.getId());
                    update.setUpdateTime(new Date());
                    update.setRequestInfo(requestStr);
                    proofreadingTaskService.updateById(update);
                }
                return AjaxResult.success(params);
                // 根据请求入参 ，分别发起内容审校、上下文查重审校和参考文献审校
            }).thenCompose(result -> {
                if (!result.getSuccess()) {
                    return CompletableFuture.completedFuture(result);
                }
                List<FZExecuteParam> fzExecuteParams = (List<FZExecuteParam>)result.getData();
                Map<Integer, FZExecuteParam> collect =
                    fzExecuteParams.stream().collect(Collectors.toMap(FZExecuteParam::getType, x -> x, (x, y) -> y));
                FZExecuteParam contentParam = collect.get(ProofreadingTaskType.CONTENT_CHECK.getCode());
                FZExecuteParam docsimParam = collect.get(ProofreadingTaskType.DUPLICATE_CHECK.getCode());
                FZExecuteParam referenceParam = collect.get(ProofreadingTaskType.REFERENCES.getCode());
                // 发起内容审校
                if (Objects.nonNull(contentParam)) {
                    CompletableFuture
                        // 创建内容审校异步任务，获取任务id
                        .supplyAsync(
                            () -> fzProofreadingUtil.createContentASyncTask(contentParam.getFzReviewContentReq()),
                            ThreadUtil.fzContentPool)
                        // 通过任务id获取审校结果
                        .thenApply(ajaxResult -> {
                            if (!ajaxResult.getSuccess()) {
                                return ajaxResult;
                            }
                            String jobId = (String)ajaxResult.getData();
                            return checkContentJobState(jobId);
                        })
                        // 处理结果
                        .handle((checkContentJobStateResult,
                            e) -> handleContentOrReferenceResult(checkContentJobStateResult, e, contentParam));
                }

                // 参考文献审校
                if (Objects.nonNull(referenceParam)) {
                    CompletableFuture
                        // 创建参考文献审校异步任务，获取任务id。
                        .supplyAsync(
                            () -> fzProofreadingUtil.createReferenceASyncTask(referenceParam.getFzReviewContentReq()),
                            ThreadUtil.fzReferencePool)
                        // 通过任务id，获取任务状态和结果，若任务未完成，需自旋等待
                        .thenApply(ajaxResult -> {
                            if (!ajaxResult.getSuccess()) {
                                return ajaxResult;
                            }
                            String jobId = (String)ajaxResult.getData();
                            return checkContentJobState(jobId);
                        })
                        // 处理结果
                        .handle((checkReferenceJobStateResult,
                            e) -> handleContentOrReferenceResult(checkReferenceJobStateResult, e, referenceParam));
                }

                // 上下文查重审校
                if (Objects.nonNull(docsimParam)) {
                    CompletableFuture
                        // 发起上下文查重审校，获取任务id
                        .supplyAsync(() -> fzProofreadingUtil.createDocDumASyncTask(docsimParam.getFzReviewDocsimReq()),
                            ThreadUtil.fzDupPool)
                        // 通过任务id获取任务状态，未完成需自旋等待。
                        .thenCompose(ajaxResult -> {
                            if (!ajaxResult.getSuccess()) {
                                return CompletableFuture.completedFuture(ajaxResult);
                            }
                            String jobId = (String)ajaxResult.getData();
                            AjaxResult jobStateResult = checkJobStateAndSpin(jobId);
                            return CompletableFuture.completedFuture(jobStateResult);
                        })
                        // 通过任务id获取任务结果
                        .thenCompose(jobStateResult -> {
                            if (!jobStateResult.getSuccess()) {
                                return CompletableFuture.completedFuture(jobStateResult);
                            }
                            JobState jobState = (JobState)jobStateResult.getData();
                            String jobId = jobState.getJobId();
                            AjaxResult docsimTaskResult = fzProofreadingUtil.queryFileTaskResult(jobId);
                            return CompletableFuture.completedFuture(docsimTaskResult);
                        })
                        // 处理结果
                        .handle((docsimTaskResult, e) -> {
                            ProofreadingTaskDto taskDto = docsimParam.getTaskDto();
                            // 发生异常
                            if (e != null) {
                                String msg = handleException(e, taskDto);
                                return AjaxResult.fail(
                                    "执行方正" + ProofreadingTaskType.getValue(taskDto.getTaskType()) + "发生异常：" + msg);
                            }
                            // 处理失败
                            if (!docsimTaskResult.getSuccess()) {
                                proofreadingTaskService.failProofreadingTask(taskDto.getId(),
                                    JSON.toJSONString(docsimTaskResult), docsimTaskResult.getMsg());
                                // 处理成功
                            } else {
                                TxtReviewASync docsimTxtReviewASync = (TxtReviewASync)docsimTaskResult.getData();
                                buildAndSaveSuccessTask(taskDto, docsimTxtReviewASync.getJobId(),
                                    JSON.toJSONString(docsimTxtReviewASync));
                            }
                            return CompletableFuture.completedFuture(docsimTaskResult);
                        });
                }

                return CompletableFuture.completedFuture(result);
            })
            // 处理结果
            .handle((result, e) -> {
                if (e != null) {
                    for (FZExecuteParam param : params) {
                        handleException(e, param.getTaskDto());
                    }
                    return AjaxResult.fail("抽取文件时，发生异常，异常信息：" + e.getMessage());
                }
                if (!result.getSuccess()) {
                    for (FZExecuteParam param : params) {
                        proofreadingTaskService.failProofreadingTask(param.getTaskDto().getId(),
                            JSON.toJSONString(result), result.getMsg());
                    }
                }
                return result;
            });
    }

    private AjaxResult handleContentOrReferenceResult(AjaxResult result, Throwable e, FZExecuteParam param) {
        ProofreadingTaskDto taskDto = param.getTaskDto();
        // 发生异常
        if (e != null) {
            String msg = handleException(e, taskDto);
            return AjaxResult.fail("执行方正" + ProofreadingTaskType.getValue(taskDto.getTaskType()) + "发生异常：" + msg);
        }
        // 失败
        if (!result.getSuccess()) {
            proofreadingTaskService.failProofreadingTask(taskDto.getId(), JSON.toJSONString(result), result.getMsg());
            // 成功
        } else {
            // 获取结果
            ContentverifyTaskOutPut taskOutPut = (ContentverifyTaskOutPut)result.getData();
            buildAndSaveSuccessTask(taskDto, taskOutPut.getJobId(), JSON.toJSONString(taskOutPut));
        }
        return result;
    }

    /**
     * 自旋调用 获取方正 文件审校/上下文查重审校 异步任务状态
     * 
     * @param jobId
     * @return
     */
    private AjaxResult checkJobStateAndSpin(String jobId) {
        JobState jobState = null;
        int attemptCount = 0;
        boolean isCompleted = Boolean.FALSE;
        while (!isCompleted && attemptCount < MAX_ATTEMPTS) {
            AjaxResult result = fzProofreadingUtil.queryFileTaskState(jobId);
            if (!result.getSuccess()) {
                return result;
            }
            jobState = (JobState)result.getData();
            FZJobStateEnum fzJobStateEnumResult = FZJobStateEnum.getFZJobStateEnumResult(jobState.getJobState());
            // 处理失败，直接退出
            if (fzJobStateEnumResult.getCode().equals(FZJobStateEnum.FAILURE.getCode())) {
                return result;
            }
            // 处理成功，isCompleted设为true
            if (fzJobStateEnumResult.getCode().equals(FZJobStateEnum.REVIEW_COMPLETED.getCode())) {
                isCompleted = Boolean.TRUE;
                // 处理中
            } else {
                // 如果任务未完成，等待一段时间后再进行查询 处理中，10秒后调用
                try {
                    Thread.sleep(SLEEP_INTERVAL_MS); // 自旋等待一段时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                // 计数
                attemptCount++;
            }
        }
        // 检查任务状态是否已完成
        if (!isCompleted) {
            // 如果超过最大尝试次数仍未完成，直接返回
            return AjaxResult.fail("查询方正审校任务状态失败，已超过最大重试次数，任务id: " + jobId);
        }
        return AjaxResult.success(jobState);
    }

    /**
     * 自旋调用 获取方正 内容审校/参考文献审校 异步任务状态
     *
     * @param jobId
     * @return
     */
    private AjaxResult checkContentJobState(String jobId) {
        ContentverifyTaskOutPut taskOutPut = null;
        int attemptCount = 0;
        boolean isCompleted = Boolean.FALSE;
        while (!isCompleted && attemptCount < MAX_ATTEMPTS) {
            AjaxResult result = fzProofreadingUtil.queryContentTaskResult(jobId);
            if (!result.getSuccess()) {
                return result;
            }
            taskOutPut = (ContentverifyTaskOutPut)result.getData();
            FZContentJobStateEnum fzContentJobStateEnumResult =
                FZContentJobStateEnum.getFZContentJobStateEnumResult(taskOutPut.getState());
            // 处理失败，直接退出
            if (fzContentJobStateEnumResult.getCode().equals(FZContentJobStateEnum.TASK_FAILED.getCode())) {
                return result;
            }
            // 处理成功，isCompleted设为true
            if (fzContentJobStateEnumResult.getCode().equals(FZContentJobStateEnum.TASK_COMPLETED.getCode())) {
                isCompleted = Boolean.TRUE;
                // 处理中
            } else {
                // 如果任务未完成，等待一段时间后再进行查询 处理中，10秒后调用
                try {
                    Thread.sleep(SLEEP_INTERVAL_MS); // 自旋等待一段时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                // 计数
                attemptCount++;
            }
        }
        // 检查任务状态是否已完成
        if (!isCompleted) {
            // 如果超过最大尝试次数仍未完成，直接返回
            return AjaxResult.fail("查询方正内容审校任务状态失败，已超过最大重试次数，任务id: " + jobId);
        }
        return AjaxResult.success(taskOutPut);
    }

    /**
     * 根据传入的具体审校类型，构造审校任务
     * 
     * @param fzSetting
     * @param taskType
     * @return
     */
    private ProofreadingTaskBo buildProofreadTaskByTaskType(FZSetting fzSetting, Integer taskType) {
        ProofreadingTaskBo proofreadingTaskBo = new ProofreadingTaskBo();
        // 来源：方正
        proofreadingTaskBo.setSourceType(ProofreadingTaskSourceType.FZ.getCode());
        // 任务类型
        proofreadingTaskBo.setTaskType(taskType);
        // 审校项参数
        proofreadingTaskBo.setSettingOptionInfo(JSON.toJSONString(fzSetting.getOption()));
        // p_proofreading_record主键
        proofreadingTaskBo.setProofreadingRecordId(fzSetting.getRecordId());
        return proofreadingTaskBo;
    }

    /**
     * 构造并保存处理成功的任务
     * 
     * @param taskDto
     * @param jobId
     * @param jsonString
     */
    private void buildAndSaveSuccessTask(ProofreadingTaskDto taskDto, String jobId, String jsonString) {
        ProofreadingTaskDto updateTask = new ProofreadingTaskDto();
        updateTask.setId(taskDto.getId());
        updateTask.setThirdTaskId(jobId);
        updateTask.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        updateTask.setResponseInfo(jsonString);
        updateTask.setFinishTime(new Date());
        updateTask.setUpdateTime(new Date());
        proofreadingTaskService.updateById(updateTask);
    }

    /**
     * 根据前端传参构建方正文本审校入参
     * 
     * @param fzSetting
     * @return
     */
    private FZReviewTxtReq buildFZTxtReq(FZSetting fzSetting) {
        FZReviewTxtReq fzReviewTxtReq = new FZReviewTxtReq();
        // 审校文本内容
        if (ProofreadEnum.FZ_TXT_PROOFREAD.getCode().equals(fzSetting.getType())) {
            String content = fzSetting.getContent();
            fzReviewTxtReq.setText(content);
        }
        FZSettingOption option = fzSetting.getOption();
        // 可选项设置
        FZSelectOption selectOption = option.getSelectOption();
        // 专项设置
        FZSpecialOption specialOption = option.getSpecialOption();
        List<String> selects = new ArrayList<>();
        if (Objects.nonNull(selectOption)) {
            // 字符符号检查项
            if (MapUtils.isNotEmpty(selectOption.getWordsCheckMap())) {
                List<String> words = getSelectOption(selectOption.getWordsCheckMap());
                selects.addAll(words);
            }
            // 敏感内容检查
            if (MapUtils.isNotEmpty(selectOption.getSensitiveCheckMap())) {
                List<String> sensitives = getSelectOption(selectOption.getSensitiveCheckMap());
                selects.addAll(sensitives);
            }
            // 文件审校
            if (ProofreadEnum.FZ_FILE_PROOFREAD.getCode().equals(fzSetting.getType())) {
                // 知识检查选项
                if (MapUtils.isNotEmpty(selectOption.getKnowledgeCheckMap())) {
                    List<String> sensitives = getSelectOption(selectOption.getKnowledgeCheckMap());
                    selects.addAll(sensitives);
                }
                // 格式检查选项
                if (MapUtils.isNotEmpty(selectOption.getFormatCheckMap())) {
                    List<String> sensitives = getSelectOption(selectOption.getFormatCheckMap());
                    selects.addAll(sensitives);
                }
            }
            // 拼接审校类型
            if (CollectionUtils.isNotEmpty(selects)) {
                String etype = String.join("|", selects);
                fzReviewTxtReq.setEtype(etype);
            }
        }
        // 易错词检查模式
        if (StringUtils.isNotBlank(specialOption.getPrecisionMode())) {
            String precision = FZPrecisionModeType.getCode(specialOption.getPrecisionMode());
            fzReviewTxtReq.setPrecision(precision);
        }
        // 敏感内容严格程度
        if (StringUtils.isNotBlank(specialOption.getSensitiveLevel())) {
            String strictness = FZSensitiveLevelType.getCode(specialOption.getSensitiveLevel());
            fzReviewTxtReq.setStrictness(strictness);
        }
        return fzReviewTxtReq;
    }

    /**
     * 构造上下文查重入参，不包括查重文本
     * 
     * @param fzSetting
     * @return
     */
    private FZReviewDocsimReq buildFZDocDupReq(FZSetting fzSetting) {
        FZSettingOption option = fzSetting.getOption();
        if (Objects.nonNull(option)) {
            FZSelectOption selectOption = option.getSelectOption();
            FZSpecialOption specialOption = option.getSpecialOption();
            if (Objects.nonNull(selectOption)
                && ProofreadingOptionType.CHOOSE.getCode().equals(selectOption.getDuplicationFlag())) {
                FZReviewDocsimReq docsimReq = new FZReviewDocsimReq();
                Map<String, Integer> duplicationSetMap = specialOption.getDuplicationSetMap();
                if (MapUtils.isNotEmpty(duplicationSetMap)) {
                    docsimReq.setSenLengthLimit(String.valueOf(duplicationSetMap.get("num")));
                }
                return docsimReq;
            }

        }

        return null;
    }

    /**
     * 构建内容审校入参
     * 
     * @param fzSetting
     * @return
     */
    private FZReviewContentReq buildFZContentReq(FZSetting fzSetting, Integer taskType) {
        // 方正在线审校
        if (ProofreadEnum.FZ_TXT_PROOFREAD.getCode().equals(fzSetting.getType())) {
            FZReviewContentReq fzReviewContentReq = new FZReviewContentReq();
            // 审校文本内容
            fzReviewContentReq.setText(fzSetting.getContent());
            // 内容审校设置项：固定 importantspeech
            fzReviewContentReq.setReviewType(FZContentSettingEnum.IMPORTANT_SPEECH.getCode());
            return fzReviewContentReq;
            // 方正文本审校
        } else if (ProofreadEnum.FZ_FILE_PROOFREAD.getCode().equals(fzSetting.getType())) {
            FZSettingOption option = fzSetting.getOption();
            if (Objects.nonNull(option)) {
                // 内容审校
                if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(taskType)) {
                    // 可选项
                    FZSelectOption selectOption = option.getSelectOption();
                    // 内容审校设置项，包括重要讲话(importantspeech)、法律法规(lawlegal)、党内法规（innerlaw），多个审校类型之间用“|”分割。示例：importantspeech|lawlegal。
                    if (Objects.nonNull(selectOption)) {
                        // 拼接入参审校项
                        String reviewType = concatContentReviewType(selectOption);
                        if (StringUtils.isNotBlank(reviewType)) {
                            FZReviewContentReq fzReviewContentReq = new FZReviewContentReq();
                            fzReviewContentReq.setReviewType(reviewType);
                            return fzReviewContentReq;
                        }
                    }
                    // 参考文献审校
                } else if (ProofreadingTaskType.REFERENCES.getCode().equals(taskType)) {
                    // 参考文献审校设置项 设置参考文献核查标准，五类标准分别为国标、 APA 、 NLM 、 MLA 、AMA，默认为国标
                    FZReferencesOption referencesOption = option.getReferencesOption();
                    if (Objects.nonNull(referencesOption) && StringUtils.isNotBlank(referencesOption.getStandard())) {
                        FZReviewContentReq fzReviewContentReq = new FZReviewContentReq();
                        String standard = referencesOption.getStandard();
                        fzReviewContentReq.setRefentryMode(standard);
                        return fzReviewContentReq;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 拼接内容审校的审校项，包括重要讲话(importantspeech)、法律法规(lawlegal)、党内法规（innerlaw），多个审校类型之间用“|”分割。示例：importantspeech|lawlegal。
     * 
     * @param selectOption
     * @return
     */
    private String concatContentReviewType(FZSelectOption selectOption) {
        List<String> selects = new ArrayList<>();
        if (ProofreadingOptionType.CHOOSE.getCode().equals(selectOption.getImportSpeechFlag())) {
            selects.add(FZContentSettingEnum.IMPORTANT_SPEECH.getCode());
        }
        if (ProofreadingOptionType.CHOOSE.getCode().equals(selectOption.getLawFlag())) {
            selects.add(FZContentSettingEnum.LAW_LEGAL.getCode());
        }
        if (ProofreadingOptionType.CHOOSE.getCode().equals(selectOption.getInnerLawFlag())) {
            selects.add(FZContentSettingEnum.INNER_LAW.getCode());
        }
        if (CollectionUtils.isNotEmpty(selects)) {
            return String.join("|", selects);
        }

        return null;
    }

    /**
     * 根据选项获取key
     * 
     * @param selectOption
     * @return
     */
    private List<String> getSelectOption(Map<String, Integer> selectOption) {
        List<String> selectList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : selectOption.entrySet()) {
            if (ProofreadingOptionType.CHOOSE.getCode().equals(entry.getValue())) {
                selectList.add(entry.getKey());
            }
        }
        return selectList;
    }

    /**
     * 构造公共审校任务参数
     * 
     * @param fzReviewTxtReq 文本审校入参
     * @param fzReviewContentReq 内容审校入参
     * @param proofreadingTaskBo
     * @return
     */
    private void buildProofreadingTask(FZReviewTxtReq fzReviewTxtReq, FZReviewContentReq fzReviewContentReq,
        FZReviewDocsimReq fzReviewDocsimReq, ProofreadingTaskBo proofreadingTaskBo) {
        // 审校中
        proofreadingTaskBo.setTaskState(ProofreadingTaskState.HANDLER_ING.getCode());
        if (Objects.nonNull(fzReviewTxtReq)) {
            proofreadingTaskBo.setRequestInfo(JSON.toJSONString(fzReviewTxtReq));
        } else if (Objects.nonNull(fzReviewContentReq)) {
            proofreadingTaskBo.setRequestInfo(JSON.toJSONString(fzReviewContentReq));
        } else if (Objects.nonNull(fzReviewDocsimReq)) {
            proofreadingTaskBo.setRequestInfo(JSON.toJSONString(fzReviewDocsimReq));
        }
        proofreadingTaskBo.setSubmitTime(new Date());
    }

    /**
     * 处理异常
     * 
     * @param e
     * @param proofreadingTask
     * @return
     */
    private String handleException(Throwable e, ProofreadingTaskDto proofreadingTask) {
        String msg = null;
        // 发生异常
        Throwable cause = e.getCause();
        if (Objects.nonNull(cause)) {
            msg = cause.getMessage();
        } else {
            msg = e.getMessage();
        }
        log.error("执行{}发生异常，异常信息：{}，异常堆栈", ProofreadingTaskType.getValue(proofreadingTask.getTaskType()), msg, e);
        // 将任务置为失败
        proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), null, msg);

        return msg;
    }

    /**
     * 查询当前审校记录下，正在处理或者已经处理完成的审校任务。
     * 
     * @param recordId
     * @return
     */
    private Map<Integer, ProofreadingTaskVo> getFZRecordTaskMap(Long recordId) {
        ProofreadingTaskConditionBo taskConditionBo = new ProofreadingTaskConditionBo();
        taskConditionBo.setProofreadingRecordId(recordId);
        taskConditionBo.setSourceType(ProofreadingTaskSourceType.FZ.getCode());
        List<Integer> list =
            Arrays.asList(ProofreadingTaskState.HANDLER_ING.getCode(), ProofreadingTaskState.SUCCESS.getCode());
        taskConditionBo.setTaskStates(list);
        List<ProofreadingTaskVo> oldTasks = proofreadingTaskMapper.getTaskByRecordIdExcludeLongColumn(taskConditionBo);
        return oldTasks.stream().collect(Collectors.toMap(ProofreadingTaskVo::getTaskType, x -> x, (x, y) -> x));
    }

    /**
     * 抽取文件内容
     * 
     * @param filePath
     * @return
     */
    public AjaxResult getFileExtractResult(String filePath) {
        String content = fileextractUtil.extractFromLocalFile(filePath);
        if (StringUtils.isBlank(content)) {
            return AjaxResult.fail("抽取文件失败，抽取文件内容为空");
        }
        return AjaxResult.success(content);

    }

}
