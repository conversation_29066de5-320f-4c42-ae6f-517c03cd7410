package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.festival.FestivalBo;
import com.fh.ai.business.entity.bo.festival.FestivalConditionBo;
import com.fh.ai.business.entity.dto.festival.FestivalDto;
import com.fh.ai.business.entity.vo.festival.FestivalVo;
import com.fh.ai.business.mapper.FestivalMapper;
import com.fh.ai.business.service.IFestivalService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 节假日表接口实现类
 *
 * <AUTHOR>
 * @date 2024-07-02 14:28:17
 */
@Service
public class FestivalServiceImpl extends ServiceImpl<FestivalMapper, FestivalDto> implements IFestivalService {

	@Resource
	private FestivalMapper festivalMapper;
	
    @Override
	public Map<String, Object> getFestivalListByCondition(FestivalConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<FestivalVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = festivalMapper.getFestivalListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<FestivalVo> festivalVos = festivalMapper.getFestivalListByCondition(conditionBo);
            PageInfo<FestivalVo> pageInfo = new PageInfo<>(festivalVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
	}

	@Override
	public AjaxResult addFestival(FestivalBo festivalBo) {
		FestivalDto festival = new FestivalDto();
		BeanUtils.copyProperties(festivalBo, festival);

		festival.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		festival.setCreateTime(new Date());
        save(festival);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult updateFestival(FestivalBo festivalBo) {
		FestivalDto festival = new FestivalDto();
		BeanUtils.copyProperties(festivalBo, festival);

		festival.setUpdateTime(new Date());
        updateById(festival);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<FestivalDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(FestivalDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(FestivalDto::getId, id);

        FestivalDto festival = getOne(lqw);
        if(null == festival) {
            return AjaxResult.fail("节假日表数据不存在");
        }

		FestivalVo festivalVo = new FestivalVo();
		BeanUtils.copyProperties(festival, festivalVo);

		return AjaxResult.success(festivalVo);
	}

    @Override
    public AjaxResult deleteFestival(FestivalBo festivalBo) {
        // 删除信息
        LambdaQueryWrapper<FestivalDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(FestivalDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(FestivalDto::getId, festivalBo.getId());

        FestivalDto festival = getOne(lqw);
        if(null == festival) {
            return AjaxResult.fail("节假日表数据不存在");
        }

        FestivalDto dto = new FestivalDto();
        dto.setId(festival.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(festivalBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}