package com.fh.ai.business.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.conversationFile.FileContentCacheBo;
import com.fh.ai.business.entity.bo.conversationFile.FileContentCacheConditionBo;
import com.fh.ai.business.entity.dto.conversationFile.FileContentCacheDto;
import com.fh.ai.business.entity.vo.conversationFile.FileContentCacheVo;
import com.fh.ai.business.mapper.FileContentCacheMapper;
import com.fh.ai.business.service.IFileContentCacheService;
import com.fh.ai.common.constants.ConstantsRedis;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 文件内容缓存表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-27 14:52:45
 */
@Service
public class FileContentCacheServiceImpl extends ServiceImpl<FileContentCacheMapper, FileContentCacheDto>
    implements IFileContentCacheService {

    @Resource
    private FileContentCacheMapper fileContentCacheMapper;
    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<FileContentCacheVo> getFileContentCacheListByCondition(FileContentCacheConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return fileContentCacheMapper.getFileContentCacheListByCondition(condition);
    }

    @Override
    public AjaxResult addFileContentCache(FileContentCacheBo fileContentCacheBo) {
        FileContentCacheDto fileContentCache = new FileContentCacheDto();
        BeanUtils.copyProperties(fileContentCacheBo, fileContentCache);
        fileContentCache.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(fileContentCache)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateFileContentCache(FileContentCacheBo fileContentCacheBo) {
        FileContentCacheDto fileContentCache = new FileContentCacheDto();
        BeanUtils.copyProperties(fileContentCacheBo, fileContentCache);
        if (updateById(fileContentCache)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public FileContentCacheVo getFileContentCacheByCondition(FileContentCacheConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        FileContentCacheVo vo = fileContentCacheMapper.getFileContentCacheByCondition(condition);
        return vo;
    }

    @Override
    public Map<Long, String> getFileContentCacheMapByIds(List<Long> ids) {
        FileContentCacheConditionBo condition = new FileContentCacheConditionBo();
        condition.setIds(ids);
        List<FileContentCacheVo> list = getFileContentCacheListByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream()
            .collect(Collectors.toMap(FileContentCacheVo::getId, FileContentCacheVo::getTaskResult, (k1, k2) -> k1));
    }

    @Override
    public Map<Long, String> getFileContentCacheMapByIdsWithRedisCache(List<Long> ids) {
        Map<Long, String> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return resultMap;
        }
        List<Long> noCacheIds = Lists.newArrayList();
        // 先从缓存中查询
        for (Long id : ids) {
            String cacheKey = ConstantsRedis.FILE_CONTENT_CACHE_PREFIX + id;
            if (redisComponent.hasKey(cacheKey)) {
                Object value = redisComponent.get(cacheKey);
                if (value != null) {
                   String valueStr = (String)value;
                    resultMap.put(id, valueStr);
                }
            } else {
                noCacheIds.add(id);
            }
        }
        // 从数据库查询未缓存的数据并设置缓存
        if (CollectionUtils.isNotEmpty(noCacheIds)) {
            Map<Long, String> noCacheMap = getFileContentCacheMapByIds(noCacheIds);
            if (MapUtils.isNotEmpty(noCacheMap)) {
                noCacheMap.forEach((k, v) -> {
                    if (k == null || StringUtils.isBlank(v)) {
                        return;
                    }
                    String cacheKey = ConstantsRedis.FILE_CONTENT_CACHE_PREFIX + k;
                    redisComponent.set(cacheKey, v, ConstantsRedis.FILE_CONTENT_CACHE_PREFIX_EXPIRE_IN);
                    resultMap.put(k, v);
                });
            }
        }
        return resultMap;
    }

    @Override
    public Long addFileContentCacheReturnId(String fileOid, String taskId, String taskResult) {
        if (taskResult == null) {
            return null;
        }
        FileContentCacheDto fileContentCacheDto = new FileContentCacheDto();
        fileContentCacheDto.setFileOid(fileOid);
        fileContentCacheDto.setTaskId(taskId);
        fileContentCacheDto.setTaskResult(taskResult);
        fileContentCacheDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        fileContentCacheMapper.insert(fileContentCacheDto);
        return fileContentCacheDto.getId();
    }
}