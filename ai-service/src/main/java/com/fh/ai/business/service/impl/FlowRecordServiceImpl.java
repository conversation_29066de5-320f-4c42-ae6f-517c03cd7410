package com.fh.ai.business.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.flowRecord.FlowRecordBo;
import com.fh.ai.business.entity.bo.flowRecord.FlowRecordConditionBo;
import com.fh.ai.business.entity.dto.flowRecord.FlowRecordDto;
import com.fh.ai.business.entity.vo.flowRecord.FlowRecordVo;
import com.fh.ai.business.mapper.FlowRecordMapper;
import com.fh.ai.business.service.IFlowRecordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-12  10:54
 */
@Service
@Slf4j
public class FlowRecordServiceImpl extends ServiceImpl<FlowRecordMapper, FlowRecordDto> implements IFlowRecordService {
    @Resource
    private FlowRecordMapper flowRecordMapper;

    @Override
    public AjaxResult addFlowRecord(FlowRecordBo flowRecordBo) {
        FlowRecordDto entity = new FlowRecordDto();
        BeanUtils.copyProperties(flowRecordBo, entity);
        if (StringUtils.isBlank(flowRecordBo.getFlowCode())) {
            entity.setFlowCode(IdUtil.simpleUUID());
        } else {
            FlowRecordConditionBo conditionBo = new FlowRecordConditionBo();
            conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            conditionBo.setFlowCode(flowRecordBo.getFlowCode());
            conditionBo.setParentId(0L);
            FlowRecordVo flowRecordVo = flowRecordMapper.getFlowRecordByCondition(conditionBo);
            entity.setTopId(flowRecordVo.getId());
        }
        entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        entity.setCreateTime(new Date());
        if (save(entity)) {
            FlowRecordVo flowRecordVo = new FlowRecordVo();
            BeanUtils.copyProperties(entity, flowRecordVo);
            return AjaxResult.success(flowRecordVo);
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateFlowRecord(FlowRecordBo flowRecordBo) {
        FlowRecordDto entity = new FlowRecordDto();
        BeanUtils.copyProperties(flowRecordBo, entity);
        entity.setUpdateTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public Map<String, Object> getFlowRecordList(FlowRecordConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<FlowRecordVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = flowRecordMapper.getFlowRecordListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<FlowRecordVo> flowRecordVos = flowRecordMapper.getFlowRecordListByCondition(conditionBo);
            PageInfo<FlowRecordVo> pageInfo = new PageInfo<>(flowRecordVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }
}
