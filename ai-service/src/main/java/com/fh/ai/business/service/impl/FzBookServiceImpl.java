package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fh.ai.business.entity.bo.fzBook.FzBookBo;
import com.fh.ai.business.entity.bo.fzBook.FzBookConditionBo;
import com.fh.ai.business.entity.dto.fzBook.FzBookDto;
import com.fh.ai.business.entity.vo.fzBook.FzBookSyncResultVo;
import com.fh.ai.business.entity.vo.fzBook.FzBookVo;
import com.fh.ai.business.mapper.FzBookMapper;
import com.fh.ai.business.service.IFzBookService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.HttpUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

/**
 * 图书库表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:00:52
 */
@Service
public class FzBookServiceImpl extends ServiceImpl<FzBookMapper, FzBookDto> implements IFzBookService {

	@Resource
	private FzBookMapper fzBookMapper;

	@Value("${fzBook.sync.url:http://221.226.15.42:28080/prodb/api/cms-api/third/getBookInfo.do}")
	private String syncUrl;

	@Value("${fzBook.sync.size:200}")
	private Integer syncSize;

	@Resource
	private RedissonClient redissonClient;

	private static final Long LOCK_TIME = 10L;

    @Override
	public List<FzBookVo> getFzBookListByCondition(FzBookConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return fzBookMapper.getFzBookListByCondition(condition);
	}

	@Override
	public AjaxResult addFzBook(FzBookBo fzBookBo) {
		FzBookDto fzBook = new FzBookDto();
		BeanUtils.copyProperties(fzBookBo, fzBook);
		fzBook.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(fzBook)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateFzBook(FzBookBo fzBookBo) {
		FzBookDto fzBook = new FzBookDto();
		BeanUtils.copyProperties(fzBookBo, fzBook);
		if(updateById(fzBook)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public FzBookVo getFzBookByCondition(FzBookConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return fzBookMapper.getFzBookByCondition(condition);
	}

	@Override
	public void fzBookSync(String startTime, String endTime) {
		RLock lock = redissonClient.getLock("fzBook:sync");
		try {
			lock.lock(LOCK_TIME, TimeUnit.MINUTES);
			int page = 1;
			FzBookConditionBo conditionBo = new FzBookConditionBo();
			List<FzBookVo> existsFzBookVos = getFzBookListByCondition(conditionBo);
			Map<String, FzBookVo> existsFzBookVoMap = Maps.newHashMap();
			if (CollectionUtil.isNotEmpty(existsFzBookVos)) {
				existsFzBookVoMap = existsFzBookVos.stream().collect(Collectors.toMap(FzBookVo::getIsbn, f -> f, (v1, v2) -> v1));
			}
            // 查询第一页数据
			String url = syncUrl.concat("?startTime=").concat(startTime)
					.concat("&endTime=").concat(endTime)
					.concat("&page=").concat(String.valueOf(page))
					.concat("&size=").concat(String.valueOf(syncSize));
			JSONObject result = HttpUtil.doGet(url);
			FzBookSyncResultVo syncResultVo = JSON.parseObject(result.toJSONString(), FzBookSyncResultVo.class);
			if (syncResultVo == null || syncResultVo.getNums() == null || syncResultVo.getNums() <= 0) {
				return;
			}
			// 计算页数
			Integer pageSize = syncResultVo.getNums() / syncSize;
			if (syncResultVo.getNums() % syncSize > 0) {
				pageSize += 1;
			}
			for (page = 1; page <= pageSize; page ++) {
				// 第一页不重新获取数据
				if (page > 1) {
					url = syncUrl.concat("?startTime=").concat(startTime)
							.concat("&endTime=").concat(endTime)
							.concat("&page=").concat(String.valueOf(page))
							.concat("&size=").concat(String.valueOf(syncSize));
					result = HttpUtil.doGet(url);
					syncResultVo = JSON.parseObject(result.toJSONString(), FzBookSyncResultVo.class);
				}
				List<FzBookVo> fzBookVos = syncResultVo.getData();
				if (CollectionUtil.isEmpty(fzBookVos)) {
					return;
				}
				// 保存、更新
				List<FzBookDto> addList = Lists.newArrayList();
				List<FzBookDto> updateList = Lists.newArrayList();
				for (FzBookVo fzBookVo : fzBookVos) {
					FzBookDto entity = new FzBookDto();
					BeanUtils.copyProperties(fzBookVo, entity);
					entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
					entity.setUpdateTime(new Date());
					if (existsFzBookVoMap.containsKey(entity.getIsbn())) {
						entity.setId(existsFzBookVoMap.get(entity.getIsbn()).getId());
						updateList.add(entity);
					} else {
						entity.setCreateTime(new Date());
						addList.add(entity);
					}
				}
				if (CollectionUtil.isNotEmpty(addList)) {
					saveBatch(addList);
				}
				if (CollectionUtil.isNotEmpty(updateList)) {
					updateBatchById(updateList);
				}
			}
		} catch (Exception e) {
			log.error("fzBookSync error:" + e);
		} finally {
			lock.unlock();
		}
	}

}