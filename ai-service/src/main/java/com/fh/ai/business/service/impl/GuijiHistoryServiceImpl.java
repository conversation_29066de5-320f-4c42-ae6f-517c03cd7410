package com.fh.ai.business.service.impl;

import java.io.File;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.guiji.GuijiHistoryBo;
import com.fh.ai.business.entity.bo.guiji.GuijiHistoryConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiHistoryDto;
import com.fh.ai.business.entity.dto.guiji.GuijiUserDto;
import com.fh.ai.business.entity.vo.guiji.GuijiHistoryVo;
import com.fh.ai.business.mapper.GuijiHistoryMapper;
import com.fh.ai.business.service.IGuijiHistoryService;
import com.fh.ai.business.service.IGuijiUserService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ZegoStateEnum;
import com.fh.ai.common.guiji.GuijiHumanVideoBo;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.vo.AjaxResult;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;

/**
 * 硅基数智人生成记录表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
@Service
public class GuijiHistoryServiceImpl extends ServiceImpl<GuijiHistoryMapper, GuijiHistoryDto>
    implements IGuijiHistoryService {

    @Resource
    private GuijiHistoryMapper guijiHistoryMapper;
    @Resource
    private IGuijiUserService guijiUserService;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    @Value("${filepath.viewPrefix}")
    private String viewPrefix;

    @Override
    public List<GuijiHistoryVo> getGuijiHistoryListByCondition(GuijiHistoryConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return guijiHistoryMapper.getGuijiHistoryListByCondition(condition);
    }

    @Override
    public AjaxResult addGuijiHistory(GuijiHistoryBo guijiHistoryBo) {
        GuijiHistoryDto guijiHistory = new GuijiHistoryDto();
        BeanUtils.copyProperties(guijiHistoryBo, guijiHistory);
        guijiHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(guijiHistory)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateGuijiHistory(GuijiHistoryBo guijiHistoryBo) {
        GuijiHistoryDto guijiHistory = new GuijiHistoryDto();
        BeanUtils.copyProperties(guijiHistoryBo, guijiHistory);
        if (updateById(guijiHistory)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public GuijiHistoryVo getGuijiHistoryByCondition(GuijiHistoryConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        GuijiHistoryVo vo = guijiHistoryMapper.getGuijiHistoryByCondition(condition);
        return vo;
    }

    @Override
    public void addCreateImageMetaHumanVideoHistoryApp(GuijiHumanVideoBo guijiHumanVideoBo) {
        Date now = new Date();
        GuijiUserDto guijiUser = new GuijiUserDto();
        if (guijiHumanVideoBo.getId() != null) {
            GuijiUserDto byId = guijiUserService.getById(guijiHumanVideoBo.getId());
            if (byId.getState().equals(ZegoStateEnum.FAIL.getCode())) {
                guijiUser.setTaskId("");
                guijiUser.setResult("");
            }
        } else {
            guijiUser.setCreateTime(now);
        }
        guijiUser.setState(ZegoStateEnum.DEALING.getCode());
        guijiUser.setId(guijiHumanVideoBo.getId());

        GuijiHistoryDto guijiHistory = new GuijiHistoryDto();
        guijiHistory.setUserOid(guijiHumanVideoBo.getOid());
        guijiHistory.setOrganizationId(guijiHumanVideoBo.getOrgId());
        guijiHistory
            .setTaskId(guijiHumanVideoBo.getVideoId() == null ? "" : String.valueOf(guijiHumanVideoBo.getVideoId()));
        guijiHistory.setParameterJson(guijiHumanVideoBo.getParams());
        guijiHistory.setResponseData(guijiHumanVideoBo.getResult());
        guijiHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        guijiHistory.setCreateTime(now);
        guijiHistory.setId(null);
        save(guijiHistory);

        guijiUser.setUserOid(guijiHistory.getUserOid());
        guijiUser.setOrganizationId(guijiHistory.getOrganizationId());
        guijiUser.setTaskId(guijiHistory.getTaskId());
        guijiUser.setName(guijiHumanVideoBo.getVideoName());
        guijiUser.setCover(guijiHumanVideoBo.getCover());
        if(StringUtils.isNotEmpty(guijiUser.getCover()) ) {
            String uuid = IdUtil.simpleUUID();
            String cover = guijiUser.getCover();
            String relativeDir = File.separator + "zego" + File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
            String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
            String coverName = uuid + "." + FileUtil.getSuffix(cover);
            HttpUtil.downloadFile(cover, baseDir + coverName);
            guijiUser.setCover(viewPrefix + relativeDir + coverName);
        }
        // guijiUser.setState(zegoHistory.getState());
        if (guijiHistory.getResult() != null) {
            guijiUser.setResult(guijiHistory.getResult());
        }
        guijiUser.setChannel(guijiHistory.getChannel());
        guijiUser.setHistoryId(guijiHistory.getId());
        guijiUser.setAppType(guijiHistory.getAppType());
        guijiUser.setParams(guijiHumanVideoBo.getParams());
        guijiUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        guijiUser.setCreateBy(guijiHistory.getCreateBy());

        guijiUser.setUpdateBy(guijiHistory.getUpdateBy());
        guijiUser.setUpdateTime(guijiHistory.getUpdateTime());

        guijiUserService.saveOrUpdate(guijiUser);
    }
}