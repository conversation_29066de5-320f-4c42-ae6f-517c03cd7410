package com.fh.ai.business.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.guiji.GuijiTimbreBo;
import com.fh.ai.business.entity.bo.guiji.GuijiTimbreConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiTimbreDto;
import com.fh.ai.business.entity.vo.guiji.GuijiTimbreVo;
import com.fh.ai.business.mapper.GuijiTimbreMapper;
import com.fh.ai.business.service.IGuijiTimbreService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.google.common.collect.Maps;

/**
 * 硅基数智人音色表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
@Service
public class GuijiTimbreServiceImpl extends ServiceImpl<GuijiTimbreMapper, GuijiTimbreDto> implements IGuijiTimbreService {

	@Resource
	private GuijiTimbreMapper guijiTimbreMapper;
	
    @Override
	public List<GuijiTimbreVo> getGuijiTimbreListByCondition(GuijiTimbreConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return guijiTimbreMapper.getGuijiTimbreListByCondition(condition);
	}

	@Override
	public AjaxResult addGuijiTimbre(GuijiTimbreBo guijiTimbreBo) {
		GuijiTimbreDto guijiTimbre = new GuijiTimbreDto();
		BeanUtils.copyProperties(guijiTimbreBo, guijiTimbre);
		guijiTimbre.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(guijiTimbre)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateGuijiTimbre(GuijiTimbreBo guijiTimbreBo) {
		GuijiTimbreDto guijiTimbre = new GuijiTimbreDto();
		BeanUtils.copyProperties(guijiTimbreBo, guijiTimbre);
		if(updateById(guijiTimbre)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public GuijiTimbreVo getGuijiTimbreByCondition(GuijiTimbreConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		GuijiTimbreVo vo = guijiTimbreMapper.getGuijiTimbreByCondition(condition);
		return vo;
	}

	@Override
	public Map<String, String> getGuijiTimbreMapByCondition(GuijiTimbreConditionBo condition) {
		List<GuijiTimbreVo> guijiTimbreVos = getGuijiTimbreListByCondition(condition);
		Map<String,String> resultMap = Maps.newHashMap();
		// 遍历音色列表，将音色id和音色音频地址放入map中，使用java8
		guijiTimbreVos.forEach(guijiTimbreVo -> resultMap.put(guijiTimbreVo.getTimbreId(), guijiTimbreVo.getTimbreUrl()));
		return resultMap;
	}
}