package com.fh.ai.business.service.impl;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.guiji.GuijiUserBo;
import com.fh.ai.business.entity.bo.guiji.GuijiUserConditionBo;
import com.fh.ai.business.entity.dto.guiji.GuijiHistoryDto;
import com.fh.ai.business.entity.dto.guiji.GuijiUserDto;
import com.fh.ai.business.entity.vo.guiji.GuijiUserVo;
import com.fh.ai.business.entity.vo.zegoUser.ZegoUserVo;
import com.fh.ai.business.mapper.GuijiHistoryMapper;
import com.fh.ai.business.mapper.GuijiUserMapper;
import com.fh.ai.business.service.IGuijiUserService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ZegoStateEnum;
import com.fh.ai.common.guiji.GuijiHumanVo;
import com.fh.ai.common.guiji.GuijiImageMetaHumanUtil;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;

/**
 * 硅基数智人用户使用表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-30 13:50:10
 */
@Service
public class GuijiUserServiceImpl extends ServiceImpl<GuijiUserMapper, GuijiUserDto> implements IGuijiUserService {

    @Resource
    private GuijiUserMapper guijiUserMapper;
    @Resource
    private GuijiHistoryMapper guijiHistoryMapper;
    @Resource
    private GuijiImageMetaHumanUtil guijiImageMetaHumanUtil;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    @Value("${filepath.viewPrefix}")
    private String viewPrefix;

    @Override
    public List<GuijiUserVo> getGuijiUserListByCondition(GuijiUserConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return guijiUserMapper.getGuijiUserListByCondition(condition);
    }

    @Override
    public List<GuijiUserVo> getZegoGuijiUserListByCondition(GuijiUserConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return guijiUserMapper.getZegoGuijiUserListByCondition(condition);
    }

    @Override
    public AjaxResult addGuijiUser(GuijiUserBo guijiUserBo) {
        GuijiUserDto guijiUser = new GuijiUserDto();
        BeanUtils.copyProperties(guijiUserBo, guijiUser);
        guijiUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(guijiUser)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateGuijiUser(GuijiUserBo guijiUserBo) {
        GuijiUserDto guijiUser = new GuijiUserDto();
        BeanUtils.copyProperties(guijiUserBo, guijiUser);
        if (guijiUserBo.getId() == null) {
            guijiUser.setCreateTime(new Date());
        }
        guijiUser.setUpdateTime(new Date());
        // 更新不修改处理状态
        // guijiUser.setState(ZegoStateEnum.DEALING.getCode());
        if (guijiUserBo.getId() != null) {
            GuijiUserDto byId = getById(guijiUserBo.getId());
            if (byId.getState().equals(ZegoStateEnum.FAIL.getCode())) {
                guijiUser.setTaskId("");
                guijiUser.setResult("");
            }
        }
        if (StringUtils.isNotEmpty(guijiUser.getCover())) {
            String uuid = IdUtil.simpleUUID();
            String cover = guijiUser.getCover();
            String relativeDir = File.separator + "zego" + File.separator
                + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
            String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
            String coverName = uuid + "." + FileUtil.getSuffix(cover);
            HttpUtil.downloadFile(cover, baseDir + coverName);
            guijiUser.setCover(viewPrefix + relativeDir + coverName);
        }
        if (saveOrUpdate(guijiUser)) {
            return AjaxResult.success(guijiUser);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<GuijiUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(GuijiUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(GuijiUserDto::getId, id);
        GuijiUserDto guijiUser = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("guijiUserVo", guijiUser == null ? new ZegoUserVo() : guijiUser);
        return reuslt;
    }

    @Override
    public GuijiUserVo getGuijiUserByCondition(GuijiUserConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        GuijiUserVo vo = guijiUserMapper.getGuijiUserByCondition(condition);
        return vo;
    }

    @Override
    public void performTask() {
        LambdaQueryWrapper<GuijiHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(GuijiHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(GuijiHistoryDto::getState, ZegoStateEnum.DEALING.getCode());
        lqw.isNotNull(GuijiHistoryDto::getTaskId);
        List<GuijiHistoryDto> guijiHistoryList = guijiHistoryMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(guijiHistoryList)) {
            return;
        }

        Date now = new Date();
        for (GuijiHistoryDto zhd : guijiHistoryList) {
            ThreadUtil.taskZegoSyncExecute(new Runnable() {
                @Override
                public void run() {
                    GuijiHumanVo guijiHumanVo = guijiImageMetaHumanUtil.describeMetaHumanVideoStr(zhd.getTaskId());
                    // 状态码，合成状态：-1. 编辑中 0.准备中 1. 排队中 2. 合成中 3：合成成功
                    // 4：合成失败 5. 归档 6. 任务取消 7. 任务失败
                    if (guijiHumanVo != null) {
                        String result = guijiHumanVo.getResult();
                        Long status = guijiHumanVo.getSynthesisStatus();
                        if (status != null && (status.equals(4L) || status.equals(6L) || status.equals(7L))) {
                            log.error("硅基生成失败：taskId：" + zhd.getTaskId() + ";返回值：" + JSON.toJSONString(guijiHumanVo));
                            GuijiHistoryDto updateDto = new GuijiHistoryDto();
                            updateDto.setId(zhd.getId());
                            updateDto.setState(ZegoStateEnum.FAIL.getCode());
                            updateDto.setUpdateTime(now);
                            updateDto.setResult(result);
                            updateDto.setResponseData(result);
                            guijiHistoryMapper.updateById(updateDto);
                            // 更新使用结果
                            LambdaQueryWrapper<GuijiUserDto> lqw = new LambdaQueryWrapper<>();
                            lqw.eq(GuijiUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                            lqw.eq(GuijiUserDto::getHistoryId, zhd.getId());
                            lqw.last("limit 1");

                            GuijiUserDto mtUser = guijiUserMapper.selectOne(lqw);
                            GuijiUserDto updateGuijiUserDto = new GuijiUserDto();
                            updateGuijiUserDto.setId(mtUser.getId());
                            updateGuijiUserDto.setResult(updateDto.getResult());
                            updateGuijiUserDto.setState(updateDto.getState());
                            updateGuijiUserDto.setUpdateTime(now);
                            updateGuijiUserDto.setCover(guijiHumanVo.getCoverUrl());

                            guijiUserMapper.updateById(updateGuijiUserDto);
                        } else if (status != null && status.equals(3L)) {
                            guijiHumanVo.setVideoUrl(
                                guijiImageMetaHumanUtil.handleEscapeCharacters(guijiHumanVo.getVideoUrl()));
                            guijiHumanVo.setCoverUrl(
                                guijiImageMetaHumanUtil.handleEscapeCharacters(guijiHumanVo.getCoverUrl()));
                            // 更新结果
                            GuijiHistoryDto updateDto = new GuijiHistoryDto();
                            updateDto.setId(zhd.getId());
                            updateDto.setState(ZegoStateEnum.FINISH.getCode());

                            updateDto.setResult(result);
                            updateDto.setUpdateTime(now);
                            updateDto.setResponseData(result);
                            guijiHistoryMapper.updateById(updateDto);
                            // 更新使用结果
                            LambdaQueryWrapper<GuijiUserDto> lqw = new LambdaQueryWrapper<>();
                            lqw.eq(GuijiUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                            lqw.eq(GuijiUserDto::getHistoryId, zhd.getId());
                            lqw.last("limit 1");

                            GuijiUserDto guijiUser = guijiUserMapper.selectOne(lqw);
                            GuijiUserDto updateGuijiUserDto = new GuijiUserDto();
                            updateGuijiUserDto.setId(guijiUser.getId());
                            String dataRes = dealData(guijiHumanVo);
                            updateGuijiUserDto.setResult(dataRes);
                            updateGuijiUserDto.setState(updateDto.getState());
                            updateGuijiUserDto.setCover(guijiHumanVo.getCoverUrl());
                            updateGuijiUserDto.setUpdateTime(now);

                            guijiUserMapper.updateById(updateGuijiUserDto);
                        }
                    }
                }
            });
        }
    }

    /**
     * 处理返回资源媒体数据
     *
     * @param guijiHumanVo the guiji human vo
     * @return string
     * <AUTHOR>
     * @date 2024 -10-31 16:51:50
     */
    public String dealData(GuijiHumanVo guijiHumanVo) {
        String uuid = IdUtil.simpleUUID();
        String relativeDir = File.separator + "zego" + File.separator
            + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
        String coverName = uuid + "." + FileUtil.getSuffix(guijiHumanVo.getCoverUrl());
        String videoName = uuid + "." + FileUtil.getSuffix(guijiHumanVo.getVideoUrl());
        HttpUtil.downloadFile(guijiHumanVo.getCoverUrl(), baseDir + coverName);
        HttpUtil.downloadFile(guijiHumanVo.getVideoUrl(), baseDir + videoName);
        guijiHumanVo.setCoverUrl(viewPrefix + relativeDir + coverName);
        guijiHumanVo.setVideoUrl(viewPrefix + relativeDir + videoName);
        return JSON.toJSONString(guijiHumanVo);
    }
}