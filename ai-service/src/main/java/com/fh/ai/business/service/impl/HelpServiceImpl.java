package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.help.HelpBo;
import com.fh.ai.business.entity.bo.help.HelpConditionBo;
import com.fh.ai.business.entity.dto.help.HelpDto;
import com.fh.ai.business.entity.vo.help.HelpVo;
import com.fh.ai.business.mapper.HelpMapper;
import com.fh.ai.business.service.IHelpService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 帮助中心接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class HelpServiceImpl extends ServiceImpl<HelpMapper, HelpDto> implements IHelpService {

	@Resource
	private HelpMapper helpMapper;
	
    @Override
	public Map<String, Object> getHelpListByCondition(HelpConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<HelpVo> list = null;
		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = helpMapper.getHelpListByCondition(conditionBo);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<HelpVo> prizeVos = helpMapper.getHelpListByCondition(conditionBo);
			PageInfo<HelpVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}

		map.put("list", list);
		map.put("count", count);

		return map;
	}

	@Override
	public AjaxResult addHelp(HelpBo helpBo) {
		HelpDto help = new HelpDto();
		BeanUtils.copyProperties(helpBo, help);
		help.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(help)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHelp(HelpBo helpBo) {
		HelpDto help = new HelpDto();
		BeanUtils.copyProperties(helpBo, help);
		if(updateById(help)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<HelpDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HelpDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(HelpDto::getId, id);
		HelpDto help = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("helpVo", help==null?new HelpVo():help);
		return reuslt;
	}

}