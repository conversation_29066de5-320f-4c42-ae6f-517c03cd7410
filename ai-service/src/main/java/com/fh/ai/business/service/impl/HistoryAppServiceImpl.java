package com.fh.ai.business.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.conversation.ConversationBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryBo;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.vo.conversationFile.AudioConvertVo;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import com.fh.ai.business.mapper.HistoryAppMapper;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.business.service.IModelUseHistoryService;
import com.fh.ai.common.aippt.vo.AipptFileVo;
import com.fh.ai.common.coze.CozeUtil;
import com.fh.ai.common.coze.vo.CozeWorkflowResultVo;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ModelUseType;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zhipuai.ContextBo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 应用历史表接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-06 10:21:35
 */
@Service
@Slf4j
public class HistoryAppServiceImpl extends ServiceImpl<HistoryAppMapper, HistoryAppDto> implements IHistoryAppService {

    @Resource
    private HistoryAppMapper historyAppMapper;
    @Resource
    private RedissonClient redissonClient;
    @Lazy
    @Resource
    private IConversationService conversationService;
    @Lazy
    @Resource
    private IModelUseHistoryService modelUseHistoryService;

    @Override
    public Map<String, Object> getHistoryAppListByCondition(HistoryAppConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<HistoryAppVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = historyAppMapper.getHistoryAppListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<HistoryAppVo> historyAppVos = historyAppMapper.getHistoryAppListByCondition(conditionBo);
            PageInfo<HistoryAppVo> pageInfo = new PageInfo<>(historyAppVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addHistoryApp(HistoryAppBo historyAppBo) {
        HistoryAppDto historyApp = new HistoryAppDto();
        BeanUtils.copyProperties(historyAppBo, historyApp);

        historyApp.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        historyApp.setCreateTime(new Date());
        save(historyApp);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateHistoryApp(HistoryAppBo historyAppBo) {
        HistoryAppDto historyApp = new HistoryAppDto();
        BeanUtils.copyProperties(historyAppBo, historyApp);

        historyApp.setUpdateTime(new Date());
        updateById(historyApp);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<HistoryAppDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HistoryAppDto::getId, id);

        HistoryAppDto historyApp = getOne(lqw);
        if (null == historyApp) {
            return AjaxResult.fail("应用历史表数据不存在");
        }

        HistoryAppVo historyAppVo = new HistoryAppVo();
        BeanUtils.copyProperties(historyApp, historyAppVo);

        return AjaxResult.success(historyAppVo);
    }

    @Override
    public HistoryAppVo getById(Long id) {
        LambdaQueryWrapper<HistoryAppDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HistoryAppDto::getId, id);

        HistoryAppDto historyApp = getOne(lqw);
        if (null == historyApp) {
            return null;
        }

        HistoryAppVo historyAppVo = new HistoryAppVo();
        BeanUtils.copyProperties(historyApp, historyAppVo);

        return historyAppVo;
    }

    @Override
    public AjaxResult deleteHistoryApp(HistoryAppBo historyAppBo) {
        // 删除信息
        LambdaQueryWrapper<HistoryAppDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HistoryAppDto::getId, historyAppBo.getId());

        HistoryAppDto historyApp = getOne(lqw);
        if (null == historyApp) {
            return AjaxResult.fail("应用历史表数据不存在");
        }

        HistoryAppDto dto = new HistoryAppDto();
        dto.setId(historyApp.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(historyAppBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public List<UsageStatisticsTotalVo> getActualTimeTop10(HistoryAppConditionBo condition) {
        List<UsageStatisticsTotalVo> totalVoList = baseMapper.getActualTimeTop10(condition);
        if (CollectionUtil.isNotEmpty(totalVoList)) {
            totalVoList.stream().forEach(t -> t.setAppType(t.getType() / 100));
        }
        return totalVoList;
    }

    @Override
    public HistoryAppVo getMeetingConvertText(HistoryAppConditionBo condition) {
        return baseMapper.getMeetingConvertText(condition);
    }

    @Override
    public String getMeetingConvertText(String result) {
        List<AudioConvertVo> audioConvertVos = JSONArray.parseArray(result, AudioConvertVo.class);
        if (CollectionUtil.isEmpty(audioConvertVos)) {
            return null;
        }

        StringBuilder resultSb = new StringBuilder();
        for (AudioConvertVo vo : audioConvertVos) {
            resultSb.append(vo.getSpeaker()).append("(").append(vo.getStartTime()).append("):").append(vo.getContent());
            resultSb.append("\n");
        }

        return resultSb.toString();
    }

    public static void main(String[] args) {
        String result =
            "[{\"content\":\"嗯嗯嗯嗯嗯嗯高一多。呗你不。\",\"speaker\":\"说话人1\",\"startTime\":\"00:00:00\"},{\"content\":\"我离婚。好一。\",\"speaker\":\"说话人2\",\"startTime\":\"00:00:20\"},{\"content\":\"朵美丽的姑。\",\"speaker\":\"说话人2\",\"startTime\":\"00:00:23\"},{\"content\":\"梨花。\",\"speaker\":\"说话人2\",\"startTime\":\"00:00:25\"},{\"content\":\"东北泥巴7。\",\"speaker\":\"说话人1\",\"startTime\":\"00:00:28\"},{\"content\":\"嗯嗯\",\"speaker\":\"说话人3\",\"startTime\":\"00:00:32\"},{\"content\":\"又香。\",\"speaker\":\"说话人2\",\"startTime\":\"00:00:33\"},{\"content\":\"又白的阳光。\",\"speaker\":\"说话人1\",\"startTime\":\"00:00:34\"},{\"content\":\"有多难将你摘下送给别人家。\\n茉莉花，茉莉花。\",\"speaker\":\"说话人2\",\"startTime\":\"00:00:39\"},{\"content\":\"好，一朵美丽的茉莉花，\",\"speaker\":\"说话人1\",\"startTime\":\"00:00:54\"},{\"content\":\"嗯嗯\",\"speaker\":\"说话人2\",\"startTime\":\"00:01:00\"},{\"content\":\"好，一朵美丽的玻璃花，三方美丽满枝芽。\",\"speaker\":\"说话人1\",\"startTime\":\"00:01:01\"},{\"content\":\"又想。\",\"speaker\":\"说话人2\",\"startTime\":\"00:01:12\"},{\"content\":\"又卖人人花，\",\"speaker\":\"说话人1\",\"startTime\":\"00:01:13\"},{\"content\":\"让我再将。\",\"speaker\":\"说话人3\",\"startTime\":\"00:01:17\"},{\"content\":\"你摘下。\",\"speaker\":\"说话人2\",\"startTime\":\"00:01:20\"},{\"content\":\"送给别人家。\\n\",\"speaker\":\"说话人3\",\"startTime\":\"00:01:23\"},{\"content\":\"嗯哇嗯嗯\",\"speaker\":\"说话人1\",\"startTime\":\"00:01:28\"}]";
        List<AudioConvertVo> audioConvertVos = JSONArray.parseArray(result, AudioConvertVo.class);
        if (CollectionUtil.isEmpty(audioConvertVos)) {
            return;
        }

        StringBuilder resultSb = new StringBuilder();
        for (AudioConvertVo vo : audioConvertVos) {
            resultSb.append(vo.getSpeaker()).append("(").append(vo.getStartTime()).append("):").append(vo.getContent());
            resultSb.append("\n");
        }

        System.out.println(resultSb.toString());
    }

    @Override
    public String getMeetingConvertRichText(String result) {
        List<AudioConvertVo> audioConvertVos = JSONArray.parseArray(result, AudioConvertVo.class);
        if (CollectionUtil.isEmpty(audioConvertVos)) {
            return null;
        }

        StringBuilder resultSb = new StringBuilder();
        for (AudioConvertVo vo : audioConvertVos) {
            resultSb.append("<p>");
            resultSb.append(vo.getSpeaker()).append("(").append(vo.getStartTime()).append("):").append(vo.getContent());
            resultSb.append("</p>");
        }

        return resultSb.toString();
    }

    @Override
    public List<HistoryAppVo> getMeetingAssistantList(HistoryAppConditionBo condition) {
        // if (CollectionUtil.isEmpty(condition.getSubTypeList())) {
        // List<Integer> subTypes = Lists.newArrayList();
        // subTypes.add(SubTypeEnum.MEETING_KEYWORD.getCode());
        // subTypes.add(SubTypeEnum.MEETING_ABSTRACT.getCode());
        // subTypes.add(SubTypeEnum.MEETING_SUMMARY.getCode());
        // subTypes.add(SubTypeEnum.MEETING_TODO.getCode());
        // condition.setSubTypeList(subTypes);
        // }
        return baseMapper.getMeetingAssistantList(condition);
    }

    @Override
    public List<ContextBo> getConversation(String conversationCode, Integer type, Integer num) {
        LambdaQueryWrapper<HistoryAppDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HistoryAppDto::getConversationCode, conversationCode);
        lqw.eq(HistoryAppDto::getType, type);
        lqw.orderByDesc(HistoryAppDto::getId);
        lqw.last(" limit " + num);
        List<HistoryAppDto> list = list(lqw);
        List<ContextBo> arr = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (HistoryAppDto dto : list) {
                ContextBo bo = new ContextBo();
                bo.setAnswer(dto.getResult());
                bo.setQuestion(dto.getOriginalQuestion());
                arr.add(bo);
            }
        }
        // 倒序查询，需反过来
        Collections.reverse(arr);
        return arr;
    }

    @Override
    public AjaxResult saveMarketHistory(HistoryAppBo historyAppBo) {
        RLock lock = redissonClient.getLock("lock:saveMarketHistory:" + historyAppBo.getGaodingId());
        try {
            lock.lock(9, TimeUnit.SECONDS);

            HistoryAppDto historyAppDto = new HistoryAppDto();
            BeanUtils.copyProperties(historyAppBo, historyAppDto);
            historyAppDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            HistoryAppConditionBo conditionBo = new HistoryAppConditionBo();
            conditionBo.setGaodingId(historyAppBo.getGaodingId());
            conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            List<HistoryAppVo> historyAppVos = historyAppMapper.getHistoryAppListByCondition(conditionBo);
            if (CollectionUtil.isNotEmpty(historyAppVos)) {
                HistoryAppVo historyAppVo = historyAppVos.get(0);
                historyAppDto.setId(historyAppVo.getId());
                historyAppDto.setUpdateTime(new Date());
            } else {
                historyAppDto.setCreateTime(new Date());
            }
            if (saveOrUpdate(historyAppDto)) {
                return AjaxResult.success();
            }
        } catch (Exception e) {
            log.error("saveMarketHistory error, e:" + e);
        } finally {
            lock.unlock();
            log.info("release lock");
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult removeBusinessJson(HistoryAppBo historyAppBo) {
        // 删除信息
        LambdaQueryWrapper<HistoryAppDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HistoryAppDto::getId, historyAppBo.getId());
        lqw.eq(HistoryAppDto::getUserOid, historyAppBo.getUserOid());

        HistoryAppDto historyApp = getOne(lqw);
        if (null == historyApp) {
            return AjaxResult.fail("应用历史表数据不存在");
        }
        List<AipptFileVo> arr = new ArrayList<>();
        String businessJson = historyApp.getBusinessJson();
        if (StringUtils.isNotBlank(historyAppBo.getFileOid())) {
            List<AipptFileVo> aipptFileVos = JSON.parseArray(historyApp.getBusinessJson(), AipptFileVo.class);
            for (AipptFileVo aipptFileVo : aipptFileVos) {
                if (historyAppBo.getFileOid().equals(aipptFileVo.getFileOid())) {
                    continue;
                }
                arr.add(aipptFileVo);
            }
        }

        historyApp.setBusinessJson(JSON.toJSONString(arr));
        historyApp.setUpdateTime(new Date());
        historyApp.setUpdateBy(historyAppBo.getUpdateBy());
        if (updateById(historyApp)) {
            ConversationBo conversationBo = new ConversationBo();
            conversationBo.setConversationCode(historyApp.getConversationCode());
            conversationBo.setUserOid(historyAppBo.getUserOid());
            conversationBo.setFileOid(historyAppBo.getFileOid());
            conversationBo.setBusinessJson(businessJson);
            conversationService.removeBusinessJson(conversationBo);
            return AjaxResult.success();
        }

        return AjaxResult.fail();
    }

    @Override
    public AjaxResult useful(HistoryAppBo historyAppBo) {
        HistoryAppVo historyAppVo =
            historyAppMapper.getByIdOrMessageUUID(historyAppBo.getId(), historyAppBo.getMessageUUID());
        if (historyAppVo == null) {
            return AjaxResult.fail("数据不存在");
        }
        if (!historyAppBo.getUserOid().equals(historyAppVo.getUserOid())) {
            return AjaxResult.fail("无权限");
        }
        HistoryAppDto historyAppDto = new HistoryAppDto();
        historyAppDto.setId(historyAppVo.getId());
        historyAppDto.setUseful(historyAppBo.getUseful());
        historyAppDto.setUpdateBy(historyAppBo.getUserOid());
        historyAppDto.setUpdateTime(new Date());
        updateById(historyAppDto);
        // 保存是否有用变更记录
        addModelUseHistory(historyAppVo, historyAppBo);
        return AjaxResult.success();
    }

    /**
     * 保存使用记录
     *
     * @param historyAppVo
     * @param historyAppBo
     */
    private void addModelUseHistory(HistoryAppVo historyAppVo, HistoryAppBo historyAppBo) {
        ModelUseHistoryBo modelUseHistoryBo = new ModelUseHistoryBo();
        modelUseHistoryBo.setUserOid(historyAppBo.getUserOid());
        modelUseHistoryBo.setHistoryAppId(historyAppVo.getId());
        modelUseHistoryBo.setConversationCode(historyAppVo.getConversationCode());
        modelUseHistoryBo.setMessageUUID(historyAppVo.getMessageUUID());
        modelUseHistoryBo.setUseType(ModelUseType.USEFUL.getCode());
        modelUseHistoryBo.setUseValue(historyAppBo.getUseful().toString());
        modelUseHistoryService.addModelUseHistory(modelUseHistoryBo);
    }

    @Override
    public List<HistoryAppVo> getRunningCozeWorkflowRecords() {
        return historyAppMapper.getRunningCozeWorkflowRecords();
    }

    @Override
    public Map<String, Integer> batchUpdateCozeWorkflowResults() {
        Map<String, Integer> result = new HashMap<>();
        result.put("total", 0);
        result.put("success", 0);
        result.put("fail", 0);

        try {
            // 1. 查询状态为Running的Coze工作流记录
            List<HistoryAppVo> historyAppList = getRunningCozeWorkflowRecords();

            if (historyAppList == null || historyAppList.isEmpty()) {
                log.info("batchUpdateCozeWorkflowResults - 没有找到状态为Running的Coze工作流记录");
                return result;
            }

            result.put("total", historyAppList.size());
            log.info("batchUpdateCozeWorkflowResults - 找到 {} 条状态为Running的Coze工作流记录", historyAppList.size());

            // 2. 遍历记录并处理
            int successCount = 0;
            int failCount = 0;

            for (HistoryAppVo historyAppVo : historyAppList) {
                try {
                    // 检查是否有coze工作流相关信息
                    if (StringUtils.isBlank(historyAppVo.getCozeWorkflowId())
                        || StringUtils.isBlank(historyAppVo.getCozeExecuteId())) {
                        log.warn("batchUpdateCozeWorkflowResults - 记录ID: {} 缺少coze工作流信息，跳过处理", historyAppVo.getId());
                        failCount++;
                        continue;
                    }

                    // 处理单个记录
                    boolean success = updateSingleCozeWorkflowResult(historyAppVo);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }

                } catch (Exception e) {
                    log.error("batchUpdateCozeWorkflowResults - 处理记录ID: {} 时发生异常: {}", historyAppVo.getId(),
                        e.getMessage(), e);
                    failCount++;
                }
            }

            result.put("success", successCount);
            result.put("fail", failCount);
            log.info("batchUpdateCozeWorkflowResults - 处理完成，成功: {} 条，失败: {} 条", successCount, failCount);

        } catch (Exception e) {
            log.error("batchUpdateCozeWorkflowResults - 批量更新异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 更新单个HistoryApp记录的Coze工作流执行结果
     *
     * @param historyAppVo HistoryApp记录
     * @return 是否处理成功
     */
    private boolean updateSingleCozeWorkflowResult(HistoryAppVo historyAppVo) {
        try {
            log.debug("updateSingleCozeWorkflowResult - 开始处理记录ID: {}, workflowId: {}, executeId: {}",
                historyAppVo.getId(), historyAppVo.getCozeWorkflowId(), historyAppVo.getCozeExecuteId());

            // 调用公共方法处理Coze工作流结果
            AjaxResult updateResult = processCozeWorkflowResult(historyAppVo, null);

            if (updateResult.getSuccess()) {
                log.info("updateSingleCozeWorkflowResult - 记录ID: {} 更新成功", historyAppVo.getId());
                return true;
            } else {
                log.warn("updateSingleCozeWorkflowResult - 记录ID: {} 处理失败: {}", historyAppVo.getId(),
                    updateResult.getMsg());
                return false;
            }

        } catch (Exception e) {
            log.error("updateSingleCozeWorkflowResult - 处理记录ID: {} 时发生异常: {}", historyAppVo.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public AjaxResult processCozeWorkflowResult(HistoryAppVo historyAppVo, String updateBy) {
        try {
            // 1. 调用CozeUtil查询工作流执行历史
            CozeWorkflowResultVo cozeWorkflowResultVo =
                CozeUtil.workflowHistory(historyAppVo.getCozeWorkflowId(), historyAppVo.getCozeExecuteId());

            if (cozeWorkflowResultVo == null) {
                return AjaxResult.fail("查询coze工作流执行历史失败");
            }

            // 2. 解析返回结果
            String newResult = null;
            String newExecuteStatus = null;

            if (cozeWorkflowResultVo.getData() != null) {
                try {
                    // 将data转换为JSON字符串
                    String dataJsonStr = JSON.toJSONString(cozeWorkflowResultVo.getData());

                    JSONArray dataArray = null;

                    // 尝试解析为数组格式 {"data": [...]}
                    try {
                        dataArray = JSON.parseArray(dataJsonStr);
                    } catch (Exception e1) {
                        // 如果解析数组失败，尝试解析为对象格式 {"data": {"data": [...]}}
                        try {
                            JSONObject dataObj = JSON.parseObject(dataJsonStr);
                            dataArray = dataObj.getJSONArray("data");
                        } catch (Exception e2) {
                            log.error("processCozeWorkflowResult - 记录ID: {} 解析coze工作流返回结果失败，既不是数组也不是包含data字段的对象: {}",
                                historyAppVo.getId(), e2.getMessage(), e2);
                        }
                    }

                    // 提取第一个元素的数据
                    if (dataArray != null && dataArray.size() > 0) {
                        JSONObject firstItem = dataArray.getJSONObject(0);
                        if (firstItem != null) {
                            // 提取output和execute_status
                            newResult = firstItem.getString("output");
                            newExecuteStatus = firstItem.getString("execute_status");
                        }
                    }
                } catch (Exception e) {
                    log.error("processCozeWorkflowResult - 记录ID: {} 解析coze工作流返回结果失败: {}", historyAppVo.getId(),
                        e.getMessage(), e);
                    return AjaxResult.fail("解析coze工作流返回结果失败");
                }
            }

            // 3. 更新HistoryApp记录
            HistoryAppBo updateBo = new HistoryAppBo();
            updateBo.setId(historyAppVo.getId());
            updateBo.setUpdateBy(updateBy);

            boolean hasUpdate = false;
            if (StringUtils.isNotBlank(newResult)) {
                updateBo.setResult(newResult);
                hasUpdate = true;
            }
            if (StringUtils.isNotBlank(newExecuteStatus)) {
                updateBo.setCozeExecuteStatus(newExecuteStatus);
                hasUpdate = true;
            }

            // 存储原始响应数据
            boolean hasResponseData = false;
            if (cozeWorkflowResultVo.getData() != null) {
                String dataJsonStr = JSON.toJSONString(cozeWorkflowResultVo.getData());
                updateBo.setResponseData(dataJsonStr);
                hasResponseData = true;
            }

            // 只有当有新数据时才更新
            if (hasUpdate || hasResponseData) {
                AjaxResult updateResult = updateHistoryApp(updateBo);
                if (updateResult.getSuccess()) {
                    return AjaxResult.success("更新成功");
                } else {
                    return AjaxResult.fail("更新历史记录失败");
                }
            } else {
                return AjaxResult.success("没有新数据需要更新");
            }

        } catch (Exception e) {
            log.error("processCozeWorkflowResult - 处理记录ID: {} 时发生异常: {}", historyAppVo.getId(), e.getMessage(), e);
            return AjaxResult.fail("处理coze工作流执行结果异常: " + e.getMessage());
        }
    }

}