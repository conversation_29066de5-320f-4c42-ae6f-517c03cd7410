package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.hotspot.HotspotBo;
import com.fh.ai.business.entity.bo.hotspot.HotspotConditionBo;
import com.fh.ai.business.entity.dto.hotspot.HotspotDto;
import com.fh.ai.business.entity.vo.hotspot.HotspotVo;
import com.fh.ai.business.mapper.HotspotMapper;
import com.fh.ai.business.service.IHotspotService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 热点表接口实现类
 *
 * <AUTHOR>
 * @date 2024-07-02 15:15:14
 */
@Slf4j
@Service
public class HotspotServiceImpl extends ServiceImpl<HotspotMapper, HotspotDto> implements IHotspotService {

    @Resource
    private HotspotMapper hotspotMapper;

    private static final String URL = "https://www.kaolamedia.com/hot";

    @Override
    public Map<String, Object> getHotspotListByCondition(HotspotConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<HotspotVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = hotspotMapper.getHotspotListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<HotspotVo> hotspotVos = hotspotMapper.getHotspotListByCondition(conditionBo);
            PageInfo<HotspotVo> pageInfo = new PageInfo<>(hotspotVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addHotspot(HotspotBo hotspotBo) {
        HotspotDto hotspot = new HotspotDto();
        BeanUtils.copyProperties(hotspotBo, hotspot);

        hotspot.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        hotspot.setCreateTime(new Date());
        save(hotspot);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateHotspot(HotspotBo hotspotBo) {
        HotspotDto hotspot = new HotspotDto();
        BeanUtils.copyProperties(hotspotBo, hotspot);

        hotspot.setUpdateTime(new Date());
        updateById(hotspot);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<HotspotDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HotspotDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HotspotDto::getId, id);

        HotspotDto hotspot = getOne(lqw);
        if (null == hotspot) {
            return AjaxResult.fail("热点表数据不存在");
        }

        HotspotVo hotspotVo = new HotspotVo();
        BeanUtils.copyProperties(hotspot, hotspotVo);

        return AjaxResult.success(hotspotVo);
    }

    @Override
    public AjaxResult deleteHotspot(HotspotBo hotspotBo) {
        // 删除信息
        LambdaQueryWrapper<HotspotDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HotspotDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(HotspotDto::getId, hotspotBo.getId());

        HotspotDto hotspot = getOne(lqw);
        if (null == hotspot) {
            return AjaxResult.fail("热点表数据不存在");
        }

        HotspotDto dto = new HotspotDto();
        dto.setId(hotspot.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(hotspotBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public void updateData() {
        LambdaQueryWrapper<HotspotDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HotspotDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        List<HotspotDto> hotspotDtos = hotspotMapper.selectList(lqw);
        Map<String, HotspotDto> hotspotDtoMap = null;
        if (CollectionUtil.isNotEmpty(hotspotDtos)) {
            hotspotDtoMap = hotspotDtos.stream().collect(Collectors.toMap(u -> u.getTitle() + "_" + u.getSort(), u -> u, (value1, value2) -> value1));
        }

        List<HotspotDto> addDtos = Lists.newArrayList();
        List<HotspotDto> updateDtos = Lists.newArrayList();

        try {
            // 使用Jsoup连接到目标网页
            Document doc = Jsoup.connect(URL).get();

            // 解析网页内容，这里假设热点内容被包含在一个class为"hotspot"的div标签内
            Elements elements = doc.select("div.hot-block");

            for (Element element : elements) {
                // 提取热点标题
                String title = null;
                Elements titleDiv = element.select("div.title");
                for (Element titleEl : titleDiv) {
                    // 提取热点标题或其他信息
                    Elements children = titleEl.children();
                    for (Element child : children) {
                        Elements div = child.select("div");

                        for (Element titleDEl : div) {
                            // 提取热点标题或其他信息
                            title = titleDEl.text();
//                            System.out.println(title);
                        }
                    }
                }

                Date now = new Date();
//                Elements rowDiv = element.select("div.row");
                Elements rowDiv = element.select("div[class=row]");
                for (Element el : rowDiv) {
                    // 序号
                    String num = el.select("div.num").text();

                    String href = null;
                    String name = null;
                    Elements links = el.select("a");
                    for (Element link : links) {
                        href = link.attr("href");
                        name = link.text();
                    }

                    String value = el.select("div.value").text();

                    HotspotDto hotspotDto = null == hotspotDtoMap ? null : hotspotDtoMap.get(title + "_" + num);
                    if (null == hotspotDto) {
                        HotspotDto addDto = new HotspotDto();
                        addDto.setTitle(title);
                        addDto.setName(name);
                        addDto.setUrl(href);
                        addDto.setHeat(value);
                        addDto.setSort(Long.parseLong(num));
                        addDto.setCreateTime(now);

                        addDtos.add(addDto);
                    } else {
                        hotspotDto.setTitle(title);
                        hotspotDto.setName(name);
                        hotspotDto.setUrl(href);
                        hotspotDto.setHeat(value);
                        hotspotDto.setSort(Long.parseLong(num));
                        hotspotDto.setUpdateTime(now);

                        updateDtos.add(hotspotDto);
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(addDtos)) {
                saveBatch(addDtos);
            }

            if (CollectionUtil.isNotEmpty(updateDtos)) {
                updateBatchById(updateDtos);
            }

        } catch (Exception e) {
            log.error("更新热点数据失败：", e.getMessage());
        }
    }
}