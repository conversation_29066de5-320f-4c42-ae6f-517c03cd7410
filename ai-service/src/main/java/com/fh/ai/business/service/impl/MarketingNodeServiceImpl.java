package com.fh.ai.business.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.ai.business.entity.vo.hotspot.HotspotVo;
import com.fh.ai.common.utils.BeanCopyUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.calendarEvent.MarketingNodeBo;
import com.fh.ai.business.entity.bo.calendarEvent.MarketingNodeConditionBo;
import com.fh.ai.business.entity.dto.calendarEvent.MarketingNodeDto;
import com.fh.ai.business.entity.vo.calendarEvent.MarketingNodeVo;
import com.fh.ai.business.mapper.MarketingNodeMapper;
import com.fh.ai.business.service.IMarketingNodeService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 营销节点接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-09 11:42:41
 */
@Service
public class MarketingNodeServiceImpl extends ServiceImpl<MarketingNodeMapper, MarketingNodeDto> implements IMarketingNodeService {

	@Resource
	private MarketingNodeMapper marketingNodeMapper;

	/**
	 * 列表/分页查询
	 * @param condition
	 * @return
	 */
    @Override
	public Map<String, Object> getMarketingNodeListByCondition(MarketingNodeConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		Map<String, Object> map = new HashMap<>(16);
		List<MarketingNodeVo> list = null;
		long count = 0;
		if (null == condition.getPage() || null == condition.getLimit()) {
			// 不分页（查询全部）
			list = marketingNodeMapper.getMarketingNodeListByCondition(condition);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
			List<MarketingNodeVo> marketingNodeVos = marketingNodeMapper.getMarketingNodeListByCondition(condition);
			PageInfo<MarketingNodeVo> pageInfo = new PageInfo<>(marketingNodeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}

		map.put("list", list);
		map.put("count", count);

		return map;
	}

	/**
	 * 营销节点详情查询
	 * @param id
	 * @return
	 */
	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<MarketingNodeDto> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(MarketingNodeDto::getId,id).eq(MarketingNodeDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode());
		MarketingNodeDto marketingNodeDto = getOne(lambdaQueryWrapper);
		if (null == marketingNodeDto) {
			return AjaxResult.fail("营销节点不存在或已经删除");
		}
		MarketingNodeVo marketingNodeVo = BeanCopyUtils.copy(marketingNodeDto, MarketingNodeVo.class);

		return AjaxResult.success(marketingNodeVo,"营销节点详情查询成功");
	}

	/**
	 * 新增节点
	 * @param marketingNodeBo
	 * @return
	 */
	@Override
	public AjaxResult addMarketingNode(MarketingNodeBo marketingNodeBo) {
		MarketingNodeDto marketingNode = new MarketingNodeDto();
		BeanUtils.copyProperties(marketingNodeBo, marketingNode);
		marketingNode.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(marketingNode)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	/**
	 * 更新营销节点
	 * @param marketingNodeBo
	 * @return
	 */
	@Override
	public AjaxResult updateMarketingNode(MarketingNodeBo marketingNodeBo) {
		MarketingNodeDto marketingNode = new MarketingNodeDto();
		BeanUtils.copyProperties(marketingNodeBo, marketingNode);
		marketingNode.setUpdateTime(new Date());
		if(updateById(marketingNode)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	/**
	 * 删除某个营销节点
	 * @param id
	 * @return
	 */
	@Override
	public AjaxResult deleteMarketingNode(Long id,String oid) {

		MarketingNodeDto marketingNodeDto = getOne(new LambdaQueryWrapper<MarketingNodeDto>()
				.eq(MarketingNodeDto::getId,id)
				.eq(MarketingNodeDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode()));
		if (null == marketingNodeDto) {
			return AjaxResult.fail("营销节点不存在或已经删除");
		}
		MarketingNodeDto newMarket = new MarketingNodeDto();
		newMarket.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		newMarket.setId(id);
		newMarket.setUpdateTime(new Date());
		newMarket.setUpdateBy(oid);
		updateById(newMarket);
		return AjaxResult.success("删除成功");
	}

	@Override
	public MarketingNodeVo getMarketingNodeByCondition(MarketingNodeConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		MarketingNodeVo vo = marketingNodeMapper.getMarketingNodeByCondition(condition);
		return vo;
	}

}