package com.fh.ai.business.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.material.MaterialBo;
import com.fh.ai.business.entity.bo.material.MaterialConditionBo;
import com.fh.ai.business.entity.dto.material.MaterialDto;
import com.fh.ai.business.entity.vo.material.MaterialVo;
import com.fh.ai.business.mapper.MaterialMapper;
import com.fh.ai.business.service.IMaterialService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * 用户上传素材表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-07 11:15:45
 */
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, MaterialDto> implements IMaterialService {

	@Resource
	private MaterialMapper materialMapper;
	
    @Override
	public Map<String, Object> getMaterialListByCondition(MaterialConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		Map<String, Object> map = new HashMap<>();
		List<MaterialVo> list = null;
		long count = 0;
		// 列表
		if (null == condition.getPage() || null == condition.getLimit()) {
			list = materialMapper.getMaterialListByCondition(condition);
			count = list.size();
		}else {
			// 分页查询
			PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
			list = materialMapper.getMaterialListByCondition(condition);
			PageInfo<MaterialVo> pageInfo = new PageInfo<>(list);
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;

	}

	/**
	 * 上传保存素材
	 * @param materialBo
	 * @return
	 */
	@Override
	public AjaxResult addMaterial(MaterialBo materialBo) {
		MaterialDto material = new MaterialDto();
		BeanUtils.copyProperties(materialBo, material);
		material.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		material.setCreateTime(new Date());
		material.setUpdateTime(new Date());
		if(save(material)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	/**
	 * 修改素材
	 * @param materialBo
	 * @return
	 */
	@Override
	public AjaxResult updateMaterial(MaterialBo materialBo) {
		MaterialDto material = new MaterialDto();
		BeanUtils.copyProperties(materialBo, material);
		material.setUpdateTime(new Date());
		if(updateById(material)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	/**
	 * 素材详情
	 * @param condition
	 * @return
	 */
	@Override
	public MaterialVo getMaterialByCondition(MaterialConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		MaterialVo vo = materialMapper.getMaterialByCondition(condition);
		return vo;
	}

	@Override
	public AjaxResult deleteMaterial(Long id) {
		MaterialVo materialVo = materialMapper.selectIdByIdAndDeleteFlag(id);
		if (Objects.isNull(materialVo)){
			return AjaxResult.fail("素材不存在或已经删除");
		}
		materialMapper.updateDeleteFlag(id);
		return AjaxResult.success("删除成功");
	}


}