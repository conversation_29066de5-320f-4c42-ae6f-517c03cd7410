package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.menu.MenuBo;
import com.fh.ai.business.entity.bo.menu.MenuConditionBo;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.business.mapper.MenuMapper;
import com.fh.ai.business.entity.dto.menu.MenuDto;
import com.fh.ai.business.service.IMenuService;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 接口实现类
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, MenuDto> implements IMenuService {

    @Resource
    private MenuMapper menuMapper;

    @Override
    public Map<String, Object> getMenuListByCondition(MenuConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MenuVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            //page为0，则不分页（查询全部）
            list = menuMapper.getMenuListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MenuVo> adminRoleVos = menuMapper.getMenuListByCondition(conditionBo);
            PageInfo<MenuVo> pageInfo = new PageInfo<>(adminRoleVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public List<MenuVo> getMenuListByRole(Long roleId) {
        List<MenuVo> menuVos = menuMapper.getMenuListByRole(roleId);
        return menuVos;
    }

    @Override
    public AjaxResult addMenu(MenuBo menuBo) {
        MenuDto menu = new MenuDto();
        BeanUtils.copyProperties(menuBo, menu);
        if (save(menu)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateMenu(MenuBo menuBo) {
        MenuDto menu = new MenuDto();
        BeanUtils.copyProperties(menuBo, menu);
        if (updateById(menu)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<MenuDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MenuDto::getId, id);
        MenuDto menu = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("menuVo", menu == null ? new MenuVo() : menu);

        MenuVo menuVo = new MenuVo();
        BeanUtils.copyProperties(menu, menuVo);

        return AjaxResult.success(menuVo);
    }

}