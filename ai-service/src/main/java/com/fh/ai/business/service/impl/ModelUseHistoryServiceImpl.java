package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryBo;
import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryConditionBo;
import com.fh.ai.business.entity.dto.modelUseHistory.ModelUseHistoryDto;
import com.fh.ai.business.entity.vo.modelUseHistory.ModelUseHistoryVo;
import com.fh.ai.business.mapper.ModelUseHistoryMapper;
import com.fh.ai.business.service.IModelUseHistoryService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:37
 */
@Service
public class ModelUseHistoryServiceImpl extends ServiceImpl<ModelUseHistoryMapper, ModelUseHistoryDto> implements IModelUseHistoryService {
    @Resource
    private ModelUseHistoryMapper modelUseHistoryMapper;

    @Override
    public AjaxResult addModelUseHistory(ModelUseHistoryBo modelUseHistoryBo) {
        ModelUseHistoryDto modelUseHistoryDto = new ModelUseHistoryDto();
        BeanUtils.copyProperties(modelUseHistoryBo, modelUseHistoryDto);
        modelUseHistoryDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        modelUseHistoryDto.setCreateBy(modelUseHistoryBo.getUserOid());
        modelUseHistoryDto.setCreateTime(new Date());
        if (save(modelUseHistoryDto)) {
            return AjaxResult.success("保存成功");
        }
        return AjaxResult.fail("保存失败");
    }

    @Override
    public AjaxResult updateModelUseHistory(ModelUseHistoryBo modelUseHistoryBo) {
        ModelUseHistoryDto modelUseHistoryDto = new ModelUseHistoryDto();
        BeanUtils.copyProperties(modelUseHistoryBo, modelUseHistoryDto);
        if (updateById(modelUseHistoryDto)) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail("修改失败");
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<ModelUseHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ModelUseHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ModelUseHistoryDto::getId, id);
        ModelUseHistoryDto modelUseHistoryDto = getOne(lqw);
        if (null == modelUseHistoryDto) {
            return AjaxResult.fail("美图用户使用表数据不存在");
        }
        ModelUseHistoryVo modelUseHistoryVo = new ModelUseHistoryVo();
        BeanUtils.copyProperties(modelUseHistoryDto, modelUseHistoryVo);
        return AjaxResult.success(modelUseHistoryVo);
    }

    @Override
    public Map<String, Object> getModelUseHistoryListByCondition(ModelUseHistoryConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>();
        List<ModelUseHistoryVo> list = Lists.newArrayList();
        long count = 0;
        if (conditionBo.getPage() == null || conditionBo.getLimit() == null) {
            list = modelUseHistoryMapper.getModelUseHistoryListByCondition(conditionBo);
            count = list.size();
        } else {
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit());
            List<ModelUseHistoryVo> modelUseHistoryVos = modelUseHistoryMapper.getModelUseHistoryListByCondition(conditionBo);
            PageInfo<ModelUseHistoryVo> pageInfo = new PageInfo<>(modelUseHistoryVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }
}
