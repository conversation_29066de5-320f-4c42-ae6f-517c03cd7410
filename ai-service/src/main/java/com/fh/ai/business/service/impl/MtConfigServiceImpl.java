package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.mtConfig.MtConfigBo;
import com.fh.ai.business.entity.bo.mtConfig.MtConfigConditionBo;
import com.fh.ai.business.entity.dto.mtConfig.MtConfigDto;
import com.fh.ai.business.entity.vo.mtConfig.MtCategoryVo;
import com.fh.ai.business.entity.vo.mtConfig.MtConfigVo;
import com.fh.ai.business.mapper.MtConfigMapper;
import com.fh.ai.business.service.IMtConfigService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.MtConfigTypeEnum;
import com.fh.ai.common.meitu.vo.*;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 美图模型配置表接口实现类
 *
 * <AUTHOR>
 * @date 2024-08-13 16:06:29
 */
@Service
public class MtConfigServiceImpl extends ServiceImpl<MtConfigMapper, MtConfigDto> implements IMtConfigService {

    @Resource
    private MtConfigMapper mtConfigMapper;

    @Override
    public Map<String, Object> getMtConfigListByCondition(MtConfigConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MtConfigVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = mtConfigMapper.getMtConfigListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MtConfigVo> mtConfigVos = mtConfigMapper.getMtConfigListByCondition(conditionBo);
            PageInfo<MtConfigVo> pageInfo = new PageInfo<>(mtConfigVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addMtConfig(MtConfigBo mtConfigBo) {
        MtConfigDto mtConfig = new MtConfigDto();
        BeanUtils.copyProperties(mtConfigBo, mtConfig);

        mtConfig.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtConfig.setCreateTime(new Date());
        save(mtConfig);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateMtConfig(MtConfigBo mtConfigBo) {
        MtConfigDto mtConfig = new MtConfigDto();
        BeanUtils.copyProperties(mtConfigBo, mtConfig);

        mtConfig.setUpdateTime(new Date());
        updateById(mtConfig);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<MtConfigDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtConfigDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtConfigDto::getId, id);

        MtConfigDto mtConfig = getOne(lqw);
        if (null == mtConfig) {
            return AjaxResult.fail("美图模型配置表数据不存在");
        }

        MtConfigVo mtConfigVo = new MtConfigVo();
        BeanUtils.copyProperties(mtConfig, mtConfigVo);

        return AjaxResult.success(mtConfigVo);
    }

    @Override
    public AjaxResult deleteMtConfig(MtConfigBo mtConfigBo) {
        // 删除信息
        LambdaQueryWrapper<MtConfigDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtConfigDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtConfigDto::getId, mtConfigBo.getId());

        MtConfigDto mtConfig = getOne(lqw);
        if (null == mtConfig) {
            return AjaxResult.fail("美图模型配置表数据不存在");
        }

        MtConfigDto dto = new MtConfigDto();
        dto.setId(mtConfig.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(mtConfigBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMtConfigDate(ConfigData configData) {
        if (null == configData) {
            return;
        }

        Date now = new Date();
        List<MtConfigDto> addList = Lists.newArrayList();

        if (CollectionUtil.isNotEmpty(configData.getModel_list())) {
            // 模型数据
            List<ModelData> modelList = configData.getModel_list();
            for (ModelData modelData : modelList) {
                MtConfigDto mtConfigDto = new MtConfigDto();
                mtConfigDto.setType(MtConfigTypeEnum.MODEL.getCode());
                mtConfigDto.setMtId(modelData.getId());
                mtConfigDto.setName(modelData.getName());
                mtConfigDto.setDescription(modelData.getDesc());
                mtConfigDto.setModel(modelData.getModel());

                if (CollectionUtil.isNotEmpty(modelData.getImages())) {
                    List<String> imagesList = modelData.getImages();
                    String images = String.join(", ", imagesList);
                    mtConfigDto.setImages(images);
                }
                mtConfigDto.setDataJson(JSON.toJSONString(modelData));
                mtConfigDto.setCreateTime(now);

                addList.add(mtConfigDto);
            }
        }

        if (CollectionUtil.isNotEmpty(configData.getLora_list())) {
            // 风格模型数据
            List<LoraData> loraList = configData.getLora_list();
            for (LoraData loraData : loraList) {
                MtConfigDto mtConfigDto = new MtConfigDto();
                mtConfigDto.setType(MtConfigTypeEnum.STYLE_MODEL.getCode());
                mtConfigDto.setMtId(loraData.getId());
                mtConfigDto.setName(loraData.getName());
                mtConfigDto.setDescription(loraData.getDesc());
                mtConfigDto.setModel(loraData.getModel());

                if (CollectionUtil.isNotEmpty(loraData.getImages())) {
                    List<String> imagesList = loraData.getImages();
                    String images = String.join(", ", imagesList);
                    mtConfigDto.setImages(images);
                }
                mtConfigDto.setDataJson(JSON.toJSONString(loraData));
                mtConfigDto.setCreateTime(now);

                addList.add(mtConfigDto);
            }
        }

        if (CollectionUtil.isNotEmpty(configData.getModule_list())) {
            // 参考模型数据
            List<ModuleData> moduleList = configData.getModule_list();
            for (ModuleData moduleData : moduleList) {
                MtConfigDto mtConfigDto = new MtConfigDto();
                mtConfigDto.setType(MtConfigTypeEnum.REFERENCE_MODEL.getCode());
                mtConfigDto.setName(moduleData.getName());
                mtConfigDto.setDescription(moduleData.getDesc());
                mtConfigDto.setModel(moduleData.getModule());

                mtConfigDto.setDataJson(JSON.toJSONString(moduleData));
                mtConfigDto.setCreateTime(now);

                addList.add(mtConfigDto);
            }
        }

        if (CollectionUtil.isNotEmpty(configData.getPic_size_list())) {
            // 图片尺寸数据
            List<PicSize> picSizeList = configData.getPic_size_list();
            for (PicSize picSize : picSizeList) {
                MtConfigDto mtConfigDto = new MtConfigDto();
                mtConfigDto.setType(MtConfigTypeEnum.IMAGE_SIZE.getCode());
                mtConfigDto.setWidth(picSize.getWidth());
                mtConfigDto.setHeight(picSize.getHeight());
                mtConfigDto.setRatio(picSize.getRatio());

                mtConfigDto.setDataJson(JSON.toJSONString(picSize));
                mtConfigDto.setCreateTime(now);

                addList.add(mtConfigDto);
            }
        }

        if (CollectionUtil.isNotEmpty(addList)) {
            // 先删除之前的数据
            remove(null);

            saveBatch(addList);
        }
    }

    @Override
    public List<MtCategoryVo> getMtCategoryList() {
        List<MtCategoryVo> categoryVos = Lists.newArrayList();

        MtCategoryVo mtCategoryVo1 = new MtCategoryVo();
        mtCategoryVo1.setValue(0);
        mtCategoryVo1.setName("推荐");
        categoryVos.add(mtCategoryVo1);

        MtCategoryVo mtCategoryVo2 = new MtCategoryVo();
        mtCategoryVo2.setValue(1);
        mtCategoryVo2.setName("东方美学");
        categoryVos.add(mtCategoryVo2);

        MtCategoryVo mtCategoryVo3 = new MtCategoryVo();
        mtCategoryVo3.setValue(2);
        mtCategoryVo3.setName("影像艺术");
        categoryVos.add(mtCategoryVo3);

        MtCategoryVo mtCategoryVo4 = new MtCategoryVo();
        mtCategoryVo4.setValue(3);
        mtCategoryVo4.setName("设计概念");
        categoryVos.add(mtCategoryVo4);

        MtCategoryVo mtCategoryVo5 = new MtCategoryVo();
        mtCategoryVo5.setValue(5);
        mtCategoryVo5.setName("三维空间");
        categoryVos.add(mtCategoryVo5);

//        MtCategoryVo mtCategoryVo6 = new MtCategoryVo();
//        mtCategoryVo6.setValue(4);
//        mtCategoryVo6.setName("模型广场");
//        categoryVos.add(mtCategoryVo6);

        return categoryVos;
    }
}