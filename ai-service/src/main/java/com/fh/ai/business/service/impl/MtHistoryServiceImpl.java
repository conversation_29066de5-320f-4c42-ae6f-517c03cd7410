package com.fh.ai.business.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.liblib.LiblibUtil;
import com.fh.ai.common.liblib.bo.LiblibBo;
import com.fh.ai.common.liblib.dto.*;
import com.fh.ai.common.meitu.bo.MeituBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryBo;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryConditionBo;
import com.fh.ai.business.entity.dto.mtHistory.MtHistoryDto;
import com.fh.ai.business.entity.dto.mtUser.MtUserDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.entity.vo.mtHistory.MtFileVo;
import com.fh.ai.business.entity.vo.mtHistory.MtHistoryVo;
import com.fh.ai.business.entity.vo.mtUser.MtUserVo;
import com.fh.ai.business.mapper.MtHistoryMapper;
import com.fh.ai.business.mapper.MtUserMapper;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IMtHistoryService;
import com.fh.ai.common.meitu.MeituUtil;
import com.fh.ai.common.meitu.model.MtlabParamsDTO;
import com.fh.ai.common.meitu.vo.MtDataResult;
import com.fh.ai.common.meitu.vo.MtResult;
import com.fh.ai.common.utils.ListKit;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 美图生成记录表接口实现类
 *
 * <AUTHOR>
 * @date 2024-08-16 09:35:50
 */
@Service
public class MtHistoryServiceImpl extends ServiceImpl<MtHistoryMapper, MtHistoryDto> implements IMtHistoryService {

    @Resource
    private MtUserMapper mtUserMapper;

    @Resource
    private MtHistoryMapper mtHistoryMapper;

    @Autowired
    private MeituUtil meituUtil;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private LiblibUtil liblibUtil;

    @Override
    public Map<String, Object> getMtHistoryListByCondition(MtHistoryConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MtHistoryVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = mtHistoryMapper.getMtHistoryListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MtHistoryVo> mtHistoryVos = mtHistoryMapper.getMtHistoryListByCondition(conditionBo);
            PageInfo<MtHistoryVo> pageInfo = new PageInfo<>(mtHistoryVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MtUserVo saveMtUseInfo(MtHistoryBo mtHistoryBo) {
        Date now = new Date();
        MtHistoryDto mtHistory = new MtHistoryDto();
        BeanUtils.copyProperties(mtHistoryBo, mtHistory);

        mtHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtHistory.setCreateTime(now);
        if (StringUtils.isNotEmpty(mtHistory.getResult())) {
            mtHistory.setUpdateTime(now);
        }
        save(mtHistory);

        MtUserDto mtUser = new MtUserDto();
        mtUser.setUserOid(mtHistory.getUserOid());
        mtUser.setOrganizationId(mtHistory.getOrganizationId());
        mtUser.setType(mtHistory.getType());
        mtUser.setTaskId(mtHistory.getTaskId());
        mtUser.setState(mtHistory.getState());
        mtUser.setResult(mtHistory.getResult());
        mtUser.setIsFavorite(IsFavorite.NOT_FAVORITE.getCode());
        mtUser.setChannel(mtHistory.getChannel());
        mtUser.setHistoryId(mtHistory.getId());
        mtUser.setAppType(mtHistory.getAppType());
        mtUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtUser.setCreateBy(mtHistory.getCreateBy());
        mtUser.setCreateTime(mtHistory.getCreateTime());
        mtUser.setUpdateBy(mtHistory.getUpdateBy());
        mtUser.setUpdateTime(mtHistory.getUpdateTime());
        mtUser.setParams(mtHistoryBo.getParams());
        mtUser.setSourceType(mtHistory.getSourceType());

        mtUserMapper.insert(mtUser);

        MtUserVo mtUserVo = new MtUserVo();
        BeanUtils.copyProperties(mtUser, mtUserVo);

        return mtUserVo;
    }

    @Override
    public void updateInfo(Long id, MtHistoryBo mtHistoryBo) {
        MtUserDto mtUserDto = mtUserMapper.selectById(id);
        mtUserDto.setTaskId(mtHistoryBo.getTaskId());
        mtUserDto.setResult(mtHistoryBo.getResult());
        mtUserDto.setState(mtHistoryBo.getState());
        mtUserDto.setResponseData(mtHistoryBo.getResponseData());
        if (StringUtils.isNotBlank(mtHistoryBo.getParams())) {
            mtUserDto.setParams(mtHistoryBo.getParams());
        }
        mtUserMapper.updateById(mtUserDto);
        MtHistoryDto mtHistory = new MtHistoryDto();
        BeanUtils.copyProperties(mtHistoryBo, mtHistory);
        mtHistory.setId(mtUserDto.getHistoryId());
        updateById(mtHistory);
    }

    @Override
    public AjaxResult addMtHistory(MtHistoryBo mtHistoryBo) {
        MtHistoryDto mtHistory = new MtHistoryDto();
        BeanUtils.copyProperties(mtHistoryBo, mtHistory);

        mtHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtHistory.setCreateTime(new Date());
        save(mtHistory);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateMtHistory(MtHistoryBo mtHistoryBo) {
        MtHistoryDto mtHistory = new MtHistoryDto();
        BeanUtils.copyProperties(mtHistoryBo, mtHistory);

        mtHistory.setUpdateTime(new Date());
        updateById(mtHistory);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<MtHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtHistoryDto::getId, id);

        MtHistoryDto mtHistory = getOne(lqw);
        if (null == mtHistory) {
            return AjaxResult.fail("美图生成记录表数据不存在");
        }

        MtHistoryVo mtHistoryVo = new MtHistoryVo();
        BeanUtils.copyProperties(mtHistory, mtHistoryVo);

        return AjaxResult.success(mtHistoryVo);
    }

    @Override
    public AjaxResult getByTaskId(String taskId) {
        LambdaQueryWrapper<MtHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtHistoryDto::getTaskId, taskId);

        MtHistoryDto mtHistory = getOne(lqw);
        if (null == mtHistory) {
            return AjaxResult.fail("美图生成记录表数据不存在");
        }

        MtHistoryVo mtHistoryVo = new MtHistoryVo();
        BeanUtils.copyProperties(mtHistory, mtHistoryVo);

        return AjaxResult.success(mtHistoryVo);
    }

    @Override
    public AjaxResult updateState(MtHistoryBo mtHistoryBo) {
        // 更新状态
        LambdaQueryWrapper<MtHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtHistoryDto::getId, mtHistoryBo.getId());

        MtHistoryDto mtHistory = getOne(lqw);
        if (null == mtHistory) {
            return AjaxResult.fail("美图生成记录表数据不存在");
        }

        MtHistoryDto dto = new MtHistoryDto();
        dto.setId(mtHistory.getId());
        dto.setState(mtHistoryBo.getState());
        dto.setUpdateBy(mtHistoryBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteMtHistory(MtHistoryBo mtHistoryBo) {
        // 删除信息
        LambdaQueryWrapper<MtHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtHistoryDto::getId, mtHistoryBo.getId());

        MtHistoryDto mtHistory = getOne(lqw);
        if (null == mtHistory) {
            return AjaxResult.fail("美图生成记录表数据不存在");
        }

        MtHistoryDto dto = new MtHistoryDto();
        dto.setId(mtHistory.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(mtHistoryBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public void getMtCreateResult() {
        LambdaQueryWrapper<MtHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtHistoryDto::getState, MtStateEnum.DEALING.getCode());
        lqw.isNotNull(MtHistoryDto::getTaskId);

        List<MtHistoryDto> mtHistoryDtos = mtHistoryMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(mtHistoryDtos)) {
            return;
        }

        Date now = new Date();
        for (MtHistoryDto mtHistoryDto : mtHistoryDtos) {
            ThreadUtil.taskXfExecute(new Runnable() {
                @Override
                public void run() {
                    if (ImageSourceType.MEITU.getValue().equals(mtHistoryDto.getSourceType())) {
                        createMeituResult(mtHistoryDto);
                    } else if (ImageSourceType.LIBLIB.getValue().equals(mtHistoryDto.getSourceType())) {
                        createLiblibResult(mtHistoryDto);
                    }
                }
            });
        }
    }

    /**
     * 获取美图生图结果
     *
     * @param mtHistoryDto
     * @return void
     * <AUTHOR>
     * @date 2025/1/20 9:20
     **/
    private void createMeituResult(MtHistoryDto mtHistoryDto) {
        String parameterJson = mtHistoryDto.getParameterJson();
        MtResult resultStatus = meituUtil.getResultStatus(mtHistoryDto.getTaskId(), mtHistoryDto.getType());
        // 状态码，-1没有找到任务，0任务创建成功，1 任务执行中，2任务失败，10任务成功。
        if (resultStatus.getCode() == 0) {

            int status = resultStatus.getData().getStatus();
            MtDataResult result = resultStatus.getData().getResult();
            if (status == 10) {
                // 更新结果
                MtHistoryDto updateDto = new MtHistoryDto();
                updateDto.setId(mtHistoryDto.getId());
                updateDto.setState(MtStateEnum.FINISH.getCode());

                List<MtFileVo> mtFileVos =
                        dealPic(parameterJson, result.getUrls(), mtHistoryDto.getCreateBy());
                updateDto.setResult(JSONObject.toJSONString(mtFileVos));
                updateDto.setUpdateTime(new Date());

                updateById(updateDto);
                // 更新使用结果
                LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
                lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

                MtUserDto mtUser = mtUserMapper.selectOne(lqw);
                MtUserDto updateMtUserDto = new MtUserDto();
                updateMtUserDto.setId(mtUser.getId());
                updateMtUserDto.setResult(updateDto.getResult());
                updateMtUserDto.setState(updateDto.getState());
                updateMtUserDto.setResponseData(updateDto.getResponseData());
                updateMtUserDto.setUpdateTime(new Date());

                // 随机种子放入params
                try {
                    MeituBo meituBo = JSON.parseObject(mtUser.getParams(), MeituBo.class);
                    Map parameters = JSON.parseObject(JSON.toJSONString(resultStatus.getData().getResult().getParameters()), Map.class);
                    if (parameters != null && parameters.containsKey("seed")) {
                        List<String> randomSeed = Lists.newArrayList();
                        if (parameters.get("seed").toString().startsWith("[")) {
                            JSONArray jsonArray = (JSONArray) parameters.get("seed");
                            randomSeed = JSONArray.parseArray(jsonArray.toJSONString(), String.class);
                        } else {
                            randomSeed.add(parameters.get("seed").toString());
                        }
                        meituBo.setRandomSeed(randomSeed);
                        Map map = JSON.parseObject(meituBo.getParams(), Map.class);
                        map.put("randomSeed", JSON.toJSONString(randomSeed));
                        meituBo.setParams(JSON.toJSONString(map));
                    }
                    updateMtUserDto.setParams(JSON.toJSONString(meituBo));
                } catch (Exception e) {
                    log.error("随机种子回显设置失败,e=" + e);
                }

                mtUserMapper.updateById(updateMtUserDto);

            } else if (status == -1 || status == 2) {
                log.error("美图生成失败：taskId：" + mtHistoryDto.getTaskId() + ";返回值："
                        + JSON.toJSONString(resultStatus));
                MtHistoryDto updateDto = new MtHistoryDto();
                updateDto.setId(mtHistoryDto.getId());
                updateDto.setState(MtStateEnum.FAIL.getCode());
                updateDto.setUpdateTime(new Date());
                updateDto.setResult(JSONObject.toJSONString(resultStatus));

                updateById(updateDto);
                // 更新使用结果
                LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
                lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

                MtUserDto mtUser = mtUserMapper.selectOne(lqw);
                MtUserDto updateMtUserDto = new MtUserDto();
                updateMtUserDto.setId(mtUser.getId());
                updateMtUserDto.setResult(updateDto.getResult());
                updateMtUserDto.setState(updateDto.getState());
                updateMtUserDto.setResponseData(updateDto.getResponseData());
                updateMtUserDto.setUpdateTime(new Date());

                mtUserMapper.updateById(updateMtUserDto);
            }
        } else {
            log.error(
                    "美图生成失败：taskId：" + mtHistoryDto.getTaskId() + ";返回值：" + JSON.toJSONString(resultStatus));
            MtHistoryDto updateDto = new MtHistoryDto();
            updateDto.setId(mtHistoryDto.getId());
            updateDto.setState(MtStateEnum.FAIL.getCode());
            updateDto.setResult(JSONObject.toJSONString(resultStatus));
            updateDto.setUpdateTime(new Date());

            updateById(updateDto);

            // 更新使用结果
            LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

            MtUserDto mtUser = mtUserMapper.selectOne(lqw);
            MtUserDto updateMtUserDto = new MtUserDto();
            updateMtUserDto.setId(mtUser.getId());
            updateMtUserDto.setResult(updateDto.getResult());
            updateMtUserDto.setState(updateDto.getState());
            updateMtUserDto.setResponseData(updateDto.getResponseData());
            updateMtUserDto.setUpdateTime(new Date());

            mtUserMapper.updateById(updateMtUserDto);
        }
    }

    /**
     * 获取liblib生图结果
     *
     * @param mtHistoryDto
     * @return void
     * <AUTHOR>
     * @date 2025/1/20 9:20
     **/
    private void createLiblibResult(MtHistoryDto mtHistoryDto) {
        String parameterJson = mtHistoryDto.getParameterJson();
        GenerateTaskDto taskDto = new GenerateTaskDto();
        taskDto.setGenerateUuid(mtHistoryDto.getTaskId());
        CommonResultDto<GenerateDataDto> result = liblibUtil.getGenerateImgResult(taskDto);
        if (result.getCode() == ConstantsInteger.NUM_0) {
            GenerateDataDto generateDataDto = result.getData();
            // 成功
            if (LiblibGenerateStatus.SUCCESS.getValue().equals(generateDataDto.getGenerateStatus())) {
                // 更新结果
                MtHistoryDto updateDto = new MtHistoryDto();
                updateDto.setId(mtHistoryDto.getId());
                updateDto.setState(MtStateEnum.FINISH.getCode());

                List<String> urls = result.getData().getImages().stream().map(ImageDto::getImageUrl).collect(Collectors.toList());
                List<MtFileVo> mtFileVos =
                        dealLiblibPic(parameterJson, urls, mtHistoryDto.getCreateBy());
                updateDto.setResult(JSONObject.toJSONString(mtFileVos));
                updateDto.setUpdateTime(new Date());

                updateById(updateDto);
                // 更新使用结果
                LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
                lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

                MtUserDto mtUser = mtUserMapper.selectOne(lqw);
                MtUserDto updateMtUserDto = new MtUserDto();
                updateMtUserDto.setId(mtUser.getId());
                updateMtUserDto.setResult(updateDto.getResult());
                updateMtUserDto.setState(updateDto.getState());
                updateMtUserDto.setResponseData(updateDto.getResponseData());
                updateMtUserDto.setUpdateTime(new Date());

                // 随机种子放入params
                try {
                    LiblibBo liblibBo = JSON.parseObject(mtUser.getParams(), LiblibBo.class);
                    List<String> randomSeed = Lists.newArrayList();
                    for (ImageDto image : generateDataDto.getImages()) {
                        if (image.getSeed() != null) {
                            randomSeed.add(image.getSeed().toString());
                        }
                    }
                    liblibBo.setRandomSeed(randomSeed);
                    Map map = JSON.parseObject(liblibBo.getParams(), Map.class);
                    map.put("randomSeed", JSON.toJSONString(randomSeed));
                    liblibBo.setParams(JSON.toJSONString(map));
                    updateMtUserDto.setParams(JSON.toJSONString(liblibBo));
                } catch (Exception e) {
                    log.error("随机种子回显设置失败,e=" + e);
                }

                mtUserMapper.updateById(updateMtUserDto);
            } else if (LiblibGenerateStatus.FAIL.getValue().equals(generateDataDto.getGenerateStatus())) {
                log.error("liblib生成失败：taskId：" + mtHistoryDto.getTaskId() + ";返回值："
                        + JSON.toJSONString(result));
                MtHistoryDto updateDto = new MtHistoryDto();
                updateDto.setId(mtHistoryDto.getId());
                updateDto.setState(MtStateEnum.FAIL.getCode());
                updateDto.setUpdateTime(new Date());
                updateDto.setResult(JSONObject.toJSONString(result));

                updateById(updateDto);
                // 更新使用结果
                LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
                lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

                MtUserDto mtUser = mtUserMapper.selectOne(lqw);
                MtUserDto updateMtUserDto = new MtUserDto();
                updateMtUserDto.setId(mtUser.getId());
                updateMtUserDto.setResult(updateDto.getResult());
                updateMtUserDto.setState(updateDto.getState());
                updateMtUserDto.setResponseData(updateDto.getResponseData());
                updateMtUserDto.setUpdateTime(new Date());

                mtUserMapper.updateById(updateMtUserDto);
            }
        } else {
            log.error(
                    "liblib生成失败：taskId：" + mtHistoryDto.getTaskId() + ";返回值：" + JSON.toJSONString(result));
            MtHistoryDto updateDto = new MtHistoryDto();
            updateDto.setId(mtHistoryDto.getId());
            updateDto.setState(MtStateEnum.FAIL.getCode());
            updateDto.setResult(JSONObject.toJSONString(result));
            updateDto.setUpdateTime(new Date());

            updateById(updateDto);

            // 更新使用结果
            LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(MtUserDto::getHistoryId, mtHistoryDto.getId());

            MtUserDto mtUser = mtUserMapper.selectOne(lqw);
            MtUserDto updateMtUserDto = new MtUserDto();
            updateMtUserDto.setId(mtUser.getId());
            updateMtUserDto.setResult(updateDto.getResult());
            updateMtUserDto.setState(updateDto.getState());
            updateMtUserDto.setResponseData(updateDto.getResponseData());
            updateMtUserDto.setUpdateTime(new Date());

            mtUserMapper.updateById(updateMtUserDto);
        }
    }

    @Override
    public void performTaskError() {
        Date date = new Date();
        Date tenDate = DateUtils.addMinutes(date, -10);
        LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserDto::getState, MtStateEnum.DEALING.getCode());
        lqw.lt(MtUserDto::getCreateTime, tenDate);
        lqw.isNull(MtUserDto::getTaskId);

        List<MtUserDto> userMTList = mtUserMapper.selectList(lqw);

        if (CollectionUtil.isNotEmpty(userMTList)) {
            for (MtUserDto mtu : userMTList) {
                mtu.setUpdateTime(new Date());
                mtu.setState(MtStateEnum.FAIL.getCode());
                mtUserMapper.updateById(mtu);
                MtHistoryDto mtHistory = new MtHistoryDto();
                mtHistory.setState(MtStateEnum.FAIL.getCode());
                mtHistory.setId(mtu.getHistoryId());
                updateById(mtHistory);
            }
        }

    }

    @Override
    public List<MtFileVo> dealPic(String parameterJson, List<String> urls, String userOid) {
        // 原图地址
        List<String> originalUrls = Lists.newArrayList();
        if (StringUtils.isNotBlank(parameterJson)) {
            MtlabParamsDTO sdkRequestDTO = JSONObject.parseObject(parameterJson, MtlabParamsDTO.class);
            if (sdkRequestDTO != null) {
                originalUrls = ListKit.array2list(sdkRequestDTO.getInitImages());
            }
        }

        // 下载图片
        List<MtFileVo> mtFileVos = Lists.newArrayList();
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(userOid);
        int i = 0;
        for (String url : urls) {

            AttachmentVo attachmentVo = attachmentService.uploadImage(url, attachmentBo);
            MtFileVo mtFileVo = new MtFileVo();
            mtFileVo.setMtUrl(url);
            mtFileVo.setDownloadUrl(attachmentVo.getDownloadUrl());
            mtFileVo.setFileOid(attachmentVo.getOid());
            mtFileVo.setSmallUrl(attachmentVo.getScalePath());
            mtFileVo.setViewPath(attachmentVo.getViewPath());
            if(CollectionUtils.isNotEmpty(originalUrls)){
                if(originalUrls.size()>i){
                    mtFileVo.setOriginalUrl(originalUrls.get(i));
                }else {
                    mtFileVo.setOriginalUrl(originalUrls.get(0));
                }
            }
            mtFileVos.add(mtFileVo);
            i++;
        }

        return mtFileVos;
    }

    @Override
    public List<MtFileVo> dealLiblibPic(String parameterJson, List<String> urls, String userOid) {
        // 原图地址
        String originalUrl = null;
        if (StringUtils.isNotBlank(parameterJson)) {
            LiblibRequestDto liblibRequestDto = JSONObject.parseObject(parameterJson, LiblibRequestDto.class);
            if (liblibRequestDto != null) {
                originalUrl = liblibRequestDto.getGenerateParams().getSourceImage();
            }
        }

        // 下载图片
        List<MtFileVo> mtFileVos = Lists.newArrayList();
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(userOid);
        int i = 0;
        for (String url : urls) {

            AttachmentVo attachmentVo = attachmentService.uploadImage(url, attachmentBo);
            MtFileVo mtFileVo = new MtFileVo();
            mtFileVo.setMtUrl(url);
            mtFileVo.setDownloadUrl(attachmentVo.getDownloadUrl());
            mtFileVo.setFileOid(attachmentVo.getOid());
            mtFileVo.setSmallUrl(attachmentVo.getScalePath());
            mtFileVo.setViewPath(attachmentVo.getViewPath());
            mtFileVo.setOriginalUrl(originalUrl);
            mtFileVos.add(mtFileVo);
            i++;
        }

        return mtFileVos;
    }

}