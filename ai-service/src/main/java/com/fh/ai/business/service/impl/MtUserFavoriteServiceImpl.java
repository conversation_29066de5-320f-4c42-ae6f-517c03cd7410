package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.mtUserFavorite.MtUserFavoriteBo;
import com.fh.ai.business.entity.bo.mtUserFavorite.MtUserFavoriteConditionBo;
import com.fh.ai.business.entity.dto.mtUserFavorite.MtUserFavoriteDto;
import com.fh.ai.business.entity.vo.mtUserFavorite.MtUserFavoriteVo;
import com.fh.ai.business.mapper.MtUserFavoriteMapper;
import com.fh.ai.business.service.IMtUserFavoriteService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 美图风格用户收藏表接口实现类
 *
 * <AUTHOR>
 * @date 2024-08-16 09:50:56
 */
@Service
public class MtUserFavoriteServiceImpl extends ServiceImpl<MtUserFavoriteMapper, MtUserFavoriteDto> implements IMtUserFavoriteService {

    @Resource
    private MtUserFavoriteMapper mtUserFavoriteMapper;

    @Override
    public Map<String, Object> getMtUserFavoriteListByCondition(MtUserFavoriteConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MtUserFavoriteVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = mtUserFavoriteMapper.getMtUserFavoriteListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MtUserFavoriteVo> mtUserFavoriteVos = mtUserFavoriteMapper.getMtUserFavoriteListByCondition(conditionBo);
            PageInfo<MtUserFavoriteVo> pageInfo = new PageInfo<>(mtUserFavoriteVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo) {
        MtUserFavoriteDto mtUserFavorite = new MtUserFavoriteDto();
        BeanUtils.copyProperties(mtUserFavoriteBo, mtUserFavorite);

        mtUserFavorite.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtUserFavorite.setCreateTime(new Date());
        save(mtUserFavorite);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo) {
        MtUserFavoriteDto mtUserFavorite = new MtUserFavoriteDto();
        BeanUtils.copyProperties(mtUserFavoriteBo, mtUserFavorite);

        mtUserFavorite.setUpdateTime(new Date());
        updateById(mtUserFavorite);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<MtUserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserFavoriteDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserFavoriteDto::getId, id);

        MtUserFavoriteDto mtUserFavorite = getOne(lqw);
        if (null == mtUserFavorite) {
            return AjaxResult.fail("美图风格用户收藏表数据不存在");
        }

        MtUserFavoriteVo mtUserFavoriteVo = new MtUserFavoriteVo();
        BeanUtils.copyProperties(mtUserFavorite, mtUserFavoriteVo);

        return AjaxResult.success(mtUserFavoriteVo);
    }

    @Override
    public AjaxResult deleteMtUserFavorite(MtUserFavoriteBo mtUserFavoriteBo) {
        // 删除信息
        LambdaQueryWrapper<MtUserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserFavoriteDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserFavoriteDto::getId, mtUserFavoriteBo.getId());

        MtUserFavoriteDto mtUserFavorite = getOne(lqw);
        if (null == mtUserFavorite) {
            return AjaxResult.fail("美图风格用户收藏表数据不存在");
        }

        MtUserFavoriteDto dto = new MtUserFavoriteDto();
        dto.setId(mtUserFavorite.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(mtUserFavoriteBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}