package com.fh.ai.business.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.mtUser.MtUserBo;
import com.fh.ai.business.entity.bo.mtUser.MtUserConditionBo;
import com.fh.ai.business.entity.dto.mtHistory.MtHistoryDto;
import com.fh.ai.business.entity.dto.mtUser.MtUserDto;
import com.fh.ai.business.entity.vo.mtHistory.MtFileVo;
import com.fh.ai.business.entity.vo.mtHistory.MtHistoryVo;
import com.fh.ai.business.entity.vo.mtUser.MtUserVo;
import com.fh.ai.business.mapper.MtUserMapper;
import com.fh.ai.business.service.IMtUserService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.meitu.vo.MtResult;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.jam.JSourcePosition;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 美图用户使用表接口实现类
 *
 * <AUTHOR>
 * @date 2024-08-16 09:45:28
 */
@Service
public class MtUserServiceImpl extends ServiceImpl<MtUserMapper, MtUserDto> implements IMtUserService {

    @Resource
    private MtUserMapper mtUserMapper;

    @Override
    public Map<String, Object> getMtUserListByCondition(MtUserConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MtUserVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = mtUserMapper.getMtUserListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MtUserVo> mtUserVos = mtUserMapper.getMtUserListByCondition(conditionBo);
            PageInfo<MtUserVo> pageInfo = new PageInfo<>(mtUserVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addMtUser(MtUserBo mtUserBo) {
        MtUserDto mtUser = new MtUserDto();
        BeanUtils.copyProperties(mtUserBo, mtUser);

        mtUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        mtUser.setCreateTime(new Date());
        save(mtUser);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateMtUser(MtUserBo mtUserBo) {
        MtUserDto mtUser = new MtUserDto();
        BeanUtils.copyProperties(mtUserBo, mtUser);

        mtUser.setUpdateTime(new Date());
        updateById(mtUser);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserDto::getId, id);

        MtUserDto mtUser = getOne(lqw);
        if (null == mtUser) {
            return AjaxResult.fail("美图用户使用表数据不存在");
        }

        MtUserVo mtUserVo = new MtUserVo();
        BeanUtils.copyProperties(mtUser, mtUserVo);

        return AjaxResult.success(mtUserVo);
    }

    @Override
    public AjaxResult deleteMtUser(MtUserBo mtUserBo) {
        // 删除信息
        LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserDto::getId, mtUserBo.getId());
        lqw.eq(MtUserDto::getUserOid, mtUserBo.getUserOid());

        MtUserDto mtUser = getOne(lqw);
        if (null == mtUser) {
            return AjaxResult.fail("美图用户使用表数据不存在");
        }

        MtUserDto dto = new MtUserDto();
        dto.setId(mtUser.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(mtUserBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteOne(MtUserBo mtUserBo) {
        // 删除信息
        LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserDto::getId, mtUserBo.getId());
        lqw.eq(MtUserDto::getUserOid, mtUserBo.getUserOid());

        MtUserDto mtUser = getOne(lqw);
        if (null == mtUser) {
            return AjaxResult.fail("美图用户使用表数据不存在");
        }

        String result = mtUser.getResult();
        if (StringUtils.isEmpty(result)) {
            return AjaxResult.fail("美图用户使用表数据不存在");
        }
        List<MtFileVo> mtFileVos = JSONUtil.toList(result, MtFileVo.class);
        List<MtFileVo> arr = new ArrayList<>();

        for (MtFileVo vo :mtFileVos){
            if(vo.getFileOid().equals(mtUserBo.getFileOid())){
                continue;
            }
            arr.add(vo);
        }
        mtUser.setResult(JSONObject.toJSONString(arr));
        mtUser.setUpdateTime(new Date());
        updateById(mtUser);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getByTaskId(String taskId) {
        LambdaQueryWrapper<MtUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtUserDto::getTaskId, taskId);

        MtUserDto one = getOne(lqw);
        if (null == one) {
            return AjaxResult.fail("美图生成记录表数据不存在");
        }

        MtUserVo mtUserVo = new MtUserVo();
        BeanUtils.copyProperties(one, mtUserVo);

        return AjaxResult.success(mtUserVo);
    }

}