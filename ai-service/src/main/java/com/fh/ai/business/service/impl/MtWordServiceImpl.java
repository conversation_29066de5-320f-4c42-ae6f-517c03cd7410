package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.mtWord.MtWordBo;
import com.fh.ai.business.entity.bo.mtWord.MtWordConditionBo;
import com.fh.ai.business.entity.dto.mtWord.MtWordDto;
import com.fh.ai.business.entity.vo.mtWord.MtWordVo;
import com.fh.ai.business.mapper.MtWordMapper;
import com.fh.ai.business.service.IMtWordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 美图词库表接口实现类
 *
 * <AUTHOR>
 * @date 2024-08-19 14:52:50
 */
@Service
public class MtWordServiceImpl extends ServiceImpl<MtWordMapper, MtWordDto> implements IMtWordService {

	@Resource
	private MtWordMapper mtWordMapper;
	
    @Override
	public Map<String, Object> getMtWordListByCondition(MtWordConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<MtWordVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = mtWordMapper.getMtWordListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<MtWordVo> mtWordVos = mtWordMapper.getMtWordListByCondition(conditionBo);
            PageInfo<MtWordVo> pageInfo = new PageInfo<>(mtWordVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
	}

	@Override
	public AjaxResult addMtWord(MtWordBo mtWordBo) {
		MtWordDto mtWord = new MtWordDto();
		BeanUtils.copyProperties(mtWordBo, mtWord);

		mtWord.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		mtWord.setCreateTime(new Date());
        save(mtWord);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult updateMtWord(MtWordBo mtWordBo) {
		MtWordDto mtWord = new MtWordDto();
		BeanUtils.copyProperties(mtWordBo, mtWord);

		mtWord.setUpdateTime(new Date());
        updateById(mtWord);

        return AjaxResult.success();
	}

	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<MtWordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(MtWordDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(MtWordDto::getId, id);

        MtWordDto mtWord = getOne(lqw);
        if(null == mtWord) {
            return AjaxResult.fail("美图词库表数据不存在");
        }

		MtWordVo mtWordVo = new MtWordVo();
		BeanUtils.copyProperties(mtWord, mtWordVo);

		return AjaxResult.success(mtWordVo);
	}

    @Override
    public AjaxResult deleteMtWord(MtWordBo mtWordBo) {
        // 删除信息
        LambdaQueryWrapper<MtWordDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MtWordDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(MtWordDto::getId, mtWordBo.getId());

        MtWordDto mtWord = getOne(lqw);
        if(null == mtWord) {
            return AjaxResult.fail("美图词库表数据不存在");
        }

        MtWordDto dto = new MtWordDto();
        dto.setId(mtWord.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(mtWordBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}