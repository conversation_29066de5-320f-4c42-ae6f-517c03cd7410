package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeConditionBo;
import com.fh.ai.business.entity.bo.myWorks.MyWorksBo;
import com.fh.ai.business.entity.bo.myWorks.MyWorksConditionBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.mtUser.MtUserBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.dto.mtUser.MtUserDto;
import com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo;
import com.fh.ai.business.entity.vo.myWorks.MyWorksVo;
import com.fh.ai.business.mapper.MyWorksMapper;
import com.fh.ai.business.service.*;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.MyWorksType;
import com.fh.ai.common.enums.TypeEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-26  14:05
 */
@Service
public class MyWorksServiceImpl implements IMyWorksService {
    @Resource
    private MyWorksMapper myWorksMapper;
    @Lazy
    @Resource
    private IMtUserService mtUserService;

    @Lazy
    @Resource
    private IHistoryAppService historyAppService;


    @Override
    public Map<String, Object> getMyWorksListByCondition(MyWorksConditionBo conditionBo) {
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = new HashMap<>(4);
        List<MyWorksVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            if (conditionBo.getMyWorksType() == null || conditionBo.getMyWorksType() == 1) {
                list = myWorksMapper.getMyImageWorks(conditionBo);
            } else {
                list = myWorksMapper.getMyPPTWorks(conditionBo);
            }
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit());
            List<MyWorksVo> myImageWorks = Lists.newArrayList();
            if (conditionBo.getMyWorksType() == null || conditionBo.getMyWorksType() == 1) {
                myImageWorks = myWorksMapper.getMyImageWorks(conditionBo);
            } else {
                myImageWorks = myWorksMapper.getMyPPTWorks(conditionBo);
            }
            PageInfo<MyWorksVo> pageInfo = new PageInfo<>(myImageWorks);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }


    @Override
    public AjaxResult removeMyWorks(MyWorksBo myWorksBo) {
        // 图片
        if (myWorksBo.getMyWorksType() == null || myWorksBo.getMyWorksType() == MyWorksType.IMAGE.getValue()) {
            // 1.美图
            List<Integer> mtTypes = Lists.newArrayList(TypeEnum.IMAGE2IMAGE.getCode(), TypeEnum.TEXT2IMAGE.getCode(),
                    TypeEnum.PARTIAL_REDRAW.getCode(), TypeEnum.LOSSLESS_ZOOM.getCode(),
                    TypeEnum.AI_TRACELESS_ELIMINATION.getCode(), TypeEnum.INTELLIGENT_CUTTING.getCode());
            if (mtTypes.contains(myWorksBo.getType())) {
                MtUserBo mtUserBo = new MtUserBo();
                mtUserBo.setId(myWorksBo.getId());
                mtUserBo.setUpdateBy(myWorksBo.getUserOid());
                mtUserBo.setUserOid(myWorksBo.getUserOid());
                // 删除单个图片
                if (StringUtils.isNotBlank(myWorksBo.getFileOid())) {
                    mtUserBo.setFileOid(myWorksBo.getFileOid());
                    return mtUserService.deleteOne(mtUserBo);
                }
                return mtUserService.deleteMtUser(mtUserBo);
            }
            // 2.图书商品图
            if (TypeEnum.GOODS_IMAGE.getCode().equals(myWorksBo.getType())) {
                HistoryAppBo historyAppBo = new HistoryAppBo();
                historyAppBo.setId(myWorksBo.getId());
                historyAppBo.setUpdateBy(myWorksBo.getUserOid());
                historyAppBo.setUserOid(myWorksBo.getUserOid());
                historyAppBo.setResult(JSON.toJSONString(new ArrayList<>()));
                return historyAppService.updateHistoryApp(historyAppBo);
            }
        }
        // ppt
        if (myWorksBo.getMyWorksType() == MyWorksType.PPT.getValue()) {
            HistoryAppBo historyAppBo = new HistoryAppBo();
            historyAppBo.setId(myWorksBo.getId());
            historyAppBo.setUpdateBy(myWorksBo.getUserOid());
            historyAppBo.setFileOid(myWorksBo.getFileOid());
            historyAppBo.setUserOid(myWorksBo.getUserOid());
            return historyAppService.removeBusinessJson(historyAppBo);
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateMyWorks(MyWorksBo myWorksBo) {
        // 图片
        if (myWorksBo.getMyWorksType() == null || myWorksBo.getMyWorksType() == MyWorksType.IMAGE.getValue()) {
            // 1.美图
            List<Integer> mtTypes = Lists.newArrayList(TypeEnum.IMAGE2IMAGE.getCode(), TypeEnum.TEXT2IMAGE.getCode(),
                    TypeEnum.PARTIAL_REDRAW.getCode(), TypeEnum.LOSSLESS_ZOOM.getCode(),
                    TypeEnum.AI_TRACELESS_ELIMINATION.getCode(), TypeEnum.INTELLIGENT_CUTTING.getCode());
            if (mtTypes.contains(myWorksBo.getType())) {
                MtUserDto mtUserDto = mtUserService.getById(myWorksBo.getId());
                if (!myWorksBo.getUserOid().equals(mtUserDto.getUserOid())) {
                    return AjaxResult.fail("未找到数据");
                }
                MtUserBo mtUserBo = new MtUserBo();
                mtUserBo.setId(myWorksBo.getId());
                mtUserBo.setResult(myWorksBo.getResult());
                mtUserBo.setUpdateBy(myWorksBo.getUserOid());
                mtUserBo.setUserOid(myWorksBo.getUserOid());
                return mtUserService.updateMtUser(mtUserBo);
            }
            // 2.图书商品图
            if (TypeEnum.GOODS_IMAGE.getCode().equals(myWorksBo.getType())) {
                HistoryAppBo historyAppBo = new HistoryAppBo();
                historyAppBo.setId(myWorksBo.getId());
                historyAppBo.setUpdateBy(myWorksBo.getUserOid());
                historyAppBo.setUserOid(myWorksBo.getUserOid());
                historyAppBo.setResult(myWorksBo.getResult());
                return historyAppService.updateHistoryApp(historyAppBo);
            }
        }
        return AjaxResult.fail();
    }

}
