package com.fh.ai.business.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.publishNews.NewsHashBo;
import com.fh.ai.business.entity.bo.publishNews.NewsHashConditionBo;
import com.fh.ai.business.entity.dto.publishNews.NewsHashDto;
import com.fh.ai.business.entity.vo.publishNews.NewsHashVo;
import com.fh.ai.business.mapper.NewsHashMapper;
import com.fh.ai.business.service.INewsHashService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * 热点新闻接口调用地址表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-23 15:10:51
 */
@Service
public class NewsHashServiceImpl extends ServiceImpl<NewsHashMapper, NewsHashDto> implements INewsHashService {

	@Resource
	private NewsHashMapper newsHashMapper;
	
    @Override
	public List<NewsHashVo> getNewsHashListByCondition(NewsHashConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return newsHashMapper.getNewsHashListByCondition(condition);
	}

	@Override
	public AjaxResult addNewsHash(NewsHashBo newsHashBo) {
		NewsHashDto newsHash = new NewsHashDto();
		BeanUtils.copyProperties(newsHashBo, newsHash);
		newsHash.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(newsHash)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateNewsHash(NewsHashBo newsHashBo) {
		NewsHashDto newsHash = new NewsHashDto();
		BeanUtils.copyProperties(newsHashBo, newsHash);
		if(updateById(newsHash)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public NewsHashVo getNewsHashByCondition(NewsHashConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		NewsHashVo vo = newsHashMapper.getNewsHashByCondition(condition);
		return vo;
	}

}