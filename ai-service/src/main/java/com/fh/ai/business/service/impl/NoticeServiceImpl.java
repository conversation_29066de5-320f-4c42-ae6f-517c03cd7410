package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.ai.business.entity.bo.notice.NoticeBo;
import com.fh.ai.business.entity.bo.notice.NoticeConditionBo;
import com.fh.ai.business.entity.dto.notice.NoticeDto;
import com.fh.ai.business.entity.vo.examPaper.ExamPaperVo;
import com.fh.ai.business.entity.vo.notice.NoticeVo;
import com.fh.ai.business.mapper.NoticeMapper;
import com.fh.ai.business.service.INoticeService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 消息通知接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Service
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, NoticeDto> implements INoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    @Override
    public Map<String, Object> getNoticeListByCondition(NoticeConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<NoticeVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = noticeMapper.getNoticeListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<NoticeVo> prizeVos = noticeMapper.getNoticeListByCondition(conditionBo);
            PageInfo<NoticeVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addNotice(NoticeBo noticeBo) {
        NoticeDto notice = new NoticeDto();
        BeanUtils.copyProperties(noticeBo, notice);
        notice.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(notice)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateNotice(NoticeBo noticeBo) {
        NoticeDto notice = new NoticeDto();
        BeanUtils.copyProperties(noticeBo, notice);
        if (updateById(notice)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult readNotice(NoticeBo noticeBo) {
        LambdaQueryWrapper<NoticeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NoticeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(NoticeDto::getId, noticeBo.getId());
        NoticeDto notice = getOne(lqw);
        if (notice.getUserOid().equals(noticeBo.getUserOid())) {
            notice.setReadState(2);
            if (updateById(notice)) {
                return AjaxResult.success("保存成功");
            }
        }else{
			return AjaxResult.success("修改用户不正确");
		}
        return AjaxResult.fail("保存失败");
    }

    @Override
    public AjaxResult readAllNotice(NoticeBo noticeBo) {
        LambdaUpdateWrapper<NoticeDto> luw = new LambdaUpdateWrapper();
        luw.eq(NoticeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        luw.eq(NoticeDto::getUserOid, noticeBo.getUserOid());
        luw.set(NoticeDto::getReadState, 2);
        update(luw);
        return AjaxResult.success("保存成功");

    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<NoticeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(NoticeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(NoticeDto::getId, id);
        NoticeDto notice = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("noticeVo", notice == null ? new NoticeVo() : notice);
        return reuslt;
    }

}