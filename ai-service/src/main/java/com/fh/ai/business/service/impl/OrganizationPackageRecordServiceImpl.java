package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.organizationPackageRecord.OrganizationPackageRecordBo;
import com.fh.ai.business.entity.bo.organizationPackageRecord.OrganizationPackageRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationPackageRecord.OrganizationPackageRecordDto;
import com.fh.ai.business.entity.vo.organizationPackageRecord.OrganizationPackageRecordVo;
import com.fh.ai.business.mapper.OrganizationPackageRecordMapper;
import com.fh.ai.business.service.IOrganizationPackageRecordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
/**
 * 企业套餐开通记录接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:38
 */
@Service
public class OrganizationPackageRecordServiceImpl extends ServiceImpl<OrganizationPackageRecordMapper, OrganizationPackageRecordDto> implements IOrganizationPackageRecordService {

	@Resource
	private OrganizationPackageRecordMapper pOrganizationPackageRecordMapper;
	
    @Override
	public List<OrganizationPackageRecordVo> getPOrganizationPackageRecordListByCondition(OrganizationPackageRecordConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return pOrganizationPackageRecordMapper.getPOrganizationPackageRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addPOrganizationPackageRecord(OrganizationPackageRecordBo organizationPackageRecordBo) {
		OrganizationPackageRecordDto organizationPackageRecord = new OrganizationPackageRecordDto();
		BeanUtils.copyProperties(organizationPackageRecordBo, organizationPackageRecord);
		organizationPackageRecord.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(organizationPackageRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePOrganizationPackageRecord(OrganizationPackageRecordBo organizationPackageRecordBo) {
		OrganizationPackageRecordDto organizationPackageRecord = new OrganizationPackageRecordDto();
		BeanUtils.copyProperties(organizationPackageRecordBo, organizationPackageRecord);
		if(updateById(organizationPackageRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public OrganizationPackageRecordVo getPOrganizationPackageRecordByCondition(OrganizationPackageRecordConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return pOrganizationPackageRecordMapper.getPOrganizationPackageRecordByCondition(condition);
	}

}