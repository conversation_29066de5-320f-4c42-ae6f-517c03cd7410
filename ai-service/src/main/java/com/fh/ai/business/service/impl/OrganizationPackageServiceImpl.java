package com.fh.ai.business.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageBo;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageConditionBo;
import com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo;
import com.fh.ai.business.mapper.OrganizationPackageMapper;
import com.fh.ai.business.service.IOrganizationPackageService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 企业套餐接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:58
 */
@Service
public class OrganizationPackageServiceImpl extends ServiceImpl<OrganizationPackageMapper, OrganizationPackageDto> implements IOrganizationPackageService {

	@Resource
	private OrganizationPackageMapper pOrganizationPackageMapper;
	@Resource
	private IOrganizationService organizationService;
	
    @Override
	public List<OrganizationPackageVo> getPOrganizationPackageListByCondition(OrganizationPackageConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return pOrganizationPackageMapper.getOrganizationPackageListByCondition(condition);
	}

	@Override
	public AjaxResult addPOrganizationPackage(OrganizationPackageBo organizationPackageBo) {
		OrganizationPackageDto organizationPackage = new OrganizationPackageDto();
		BeanUtils.copyProperties(organizationPackageBo, organizationPackage);
		organizationPackage.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(organizationPackage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePOrganizationPackage(OrganizationPackageBo organizationPackageBo) {
		OrganizationPackageDto organizationPackage = new OrganizationPackageDto();
		BeanUtils.copyProperties(organizationPackageBo, organizationPackage);
		if(updateById(organizationPackage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public OrganizationPackageVo getPOrganizationPackageByCondition(OrganizationPackageConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return pOrganizationPackageMapper.getOrganizationPackageByCondition(condition);
	}

	@Override
	public Long getOrganizationQuotaByCondition(Long organizationId) {
		if(organizationId == null){
			return null;
		}
		OrganizationConditionBo organizationConditionBo = new OrganizationConditionBo();
		organizationConditionBo.setId(organizationId);
		organizationConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		OrganizationVo organizationVo = organizationService.getOrganizationByCondition(organizationConditionBo);
		if(organizationVo == null){
			return null;
		}
		Long rootOrganizationId = StringKit.getRootOrganizationId(organizationId, organizationVo.getSuperiorIds());
		// 查询当前组织的套餐有没有
		OrganizationPackageConditionBo organizationPackageConditionBo = new OrganizationPackageConditionBo();
		organizationPackageConditionBo.setOrganizationId(organizationId);
		organizationPackageConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		OrganizationPackageVo organizationPackageVo = getPOrganizationPackageByCondition(organizationPackageConditionBo);
		// 查询根的组织的套餐有没有
		if(organizationPackageVo == null){
			organizationPackageConditionBo.setOrganizationId(rootOrganizationId);
			organizationPackageVo = getPOrganizationPackageByCondition(organizationPackageConditionBo);
		}
		if(organizationPackageVo == null){
			return null;
		}
		return organizationPackageVo.getQuota();
	}
}