package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordBo;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordConditionBo;
import com.fh.ai.business.entity.dto.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordDto;
import com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo;
import com.fh.ai.business.mapper.OrganizationReduceQuotaRecordMapper;
import com.fh.ai.business.service.IOrganizationReduceQuotaRecordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  13:56
 */
@Service
public class OrganizationReduceQuotaRecordServiceImpl extends ServiceImpl<OrganizationReduceQuotaRecordMapper, OrganizationReduceQuotaRecordDto> implements IOrganizationReduceQuotaRecordService {
    @Resource
    private OrganizationReduceQuotaRecordMapper organizationReduceQuotaRecordMapper;

    @Override
    public AjaxResult addOrganizationReduceQuotaRecord(OrganizationReduceQuotaRecordBo organizationReduceQuotaRecordBo) {
        OrganizationReduceQuotaRecordDto entity = new OrganizationReduceQuotaRecordDto();
        BeanUtils.copyProperties(organizationReduceQuotaRecordBo, entity);
        entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        entity.setCreateTime(new Date());
        if (save(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public List<OrganizationReduceQuotaRecordVo> getOrganizationReduceQuotaRecordListByCondition(OrganizationReduceQuotaRecordConditionBo conditionBo) {
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return organizationReduceQuotaRecordMapper.getOrganizationReduceQuotaRecordListByCondition(conditionBo);
    }
}
