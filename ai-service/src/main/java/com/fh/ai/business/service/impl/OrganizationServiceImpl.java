package com.fh.ai.business.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordBo;
import com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationAccumulateUsageStatisticVo;
import com.fh.ai.business.entity.vo.user.UserAccountCountVo;
import com.fh.ai.business.service.IOrganizationPackageService;
import com.fh.ai.business.service.IOrganizationReduceQuotaRecordService;
import com.fh.ai.common.constants.ConstantsUnitPrice;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.sms.douyin.DouyinSmsService;
import com.fh.ai.common.sms.enums.SmsSendTypeEnum;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.admin.AdminConditionBo;
import com.fh.ai.business.entity.bo.organization.OrganizationBo;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.bo.organizationPackage.OrganizationPackageConditionBo;
import com.fh.ai.business.entity.bo.user.UserConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.mapper.AdminMapper;
import com.fh.ai.business.mapper.OrganizationMapper;
import com.fh.ai.business.mapper.OrganizationPackageMapper;
import com.fh.ai.business.mapper.UserMapper;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

/**
 * 组织机构表接口实现类
 *
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
@Service
@Slf4j
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDto> implements IOrganizationService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private OrganizationPackageMapper organizationPackageMapper;

    @Resource
    private IOrganizationPackageService organizationPackageService;

    /**
     * 企业管理员角色id
     */
    private static final Long ORG_ADMIN_ROLE_ID = 10L;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DouyinSmsService douyinSmsService;

    @Resource
    private IOrganizationReduceQuotaRecordService organizationReduceQuotaRecordService;

    @Override
    public Map<String, Object> getOrganizationListByCondition(OrganizationConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<OrganizationVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = organizationMapper.getOrganizationListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<OrganizationVo> organizationVos = organizationMapper.getOrganizationListByCondition(conditionBo);
            PageInfo<OrganizationVo> pageInfo = new PageInfo<>(organizationVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public List<OrganizationVo> getStatisticsOrganizationList() {
        List<OrganizationVo> statisticsOrganizationList = organizationMapper.getStatisticsOrganizationList();
        if (CollectionUtil.isNotEmpty(statisticsOrganizationList)) {
            OrganizationVo organizationVo = new OrganizationVo();
            organizationVo.setId(Constants.OTHER_ORG_ID);
            organizationVo.setName(Constants.OTHER_ORG_NAME);
            statisticsOrganizationList.add(organizationVo);
        }

        return statisticsOrganizationList;
    }

    @Override
    public OrganizationDto analysisDepOrganization(String depPath, String createOid) {
        if (StringUtils.isBlank(depPath)) {
            return null;
        }

        OrganizationDto firstOrg = null;
        String[] depNames = depPath.split("/");
        // 初始父节点的id为0，表示根节点
        Long parentId = 0L;
        Date now = new Date();

        String superiorIds = ",0,";
        // 从后往前遍历
        for (int i = depNames.length - 1; i >= 0; i--) {
            String orgName = depNames[i];

            if (StringUtils.isBlank(orgName)) {
                continue;
            }

            OrganizationDto org = baseMapper.selectOne(new LambdaQueryWrapper<OrganizationDto>().eq(OrganizationDto::getName, orgName)
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()).eq(OrganizationDto::getParentId, parentId));
            if (null != org) {
                parentId = org.getId();
                superiorIds = superiorIds + parentId + ",";
            } else {
                org = new OrganizationDto();
                org.setName(orgName);
                org.setParentId(parentId);
                org.setSuperiorIds(superiorIds);
                org.setCreateTime(now);
                org.setCreateBy(createOid);

                baseMapper.insert(org);

                parentId = org.getId();
                superiorIds = superiorIds + parentId + ",";
            }

            if (i == 0) {
                firstOrg = org;
            }
        }

        return firstOrg;
    }

    /**
     * 根据部门名称解析组织机构
     *
     * @param depPath   部门名称，例：软件开发部/江苏凤凰信息科技有限公司
     * @param createOid 创建人oid
     * @return 返回解析的所有组织map
     */
    @Override
    public Map<String, OrganizationDto> analysisAllOrganization(String depPath, String createOid) {
        if (StringUtils.isBlank(depPath)) {
            return null;
        }

        Map<String, OrganizationDto> orgMap = new HashMap<>();
        String[] depNames = depPath.split("/");
        // 初始父节点的id为0，表示根节点
        Long parentId = 0L;
        Date now = new Date();

        // 从后往前遍历
        for (int i = depNames.length - 1; i >= 0; i--) {
            String orgName = depNames[i];

            if (StringUtils.isBlank(orgName)) {
                continue;
            }

            OrganizationDto orgDto = baseMapper.selectOne(new LambdaQueryWrapper<OrganizationDto>().eq(OrganizationDto::getName, orgName)
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            if (null != orgDto) {
                parentId = orgDto.getId();
                orgMap.put(orgName, orgDto);
                continue;
            }

            OrganizationDto group = new OrganizationDto();
            group.setName(orgName);
            group.setParentId(parentId);
            group.setCreateTime(now);
            group.setCreateBy(createOid);

            baseMapper.insert(group);

            // 将当前id设置为下一个的父id
            parentId = group.getId();
            orgMap.put(orgName, group);
        }

        return orgMap;
    }

    @Override
    public List<OrgTreeNodeVo> getFamilyOrganizationTree(Long bottomOrgId, Long topOrgId) {
        // 返回树
        List<OrgTreeNodeVo> familyOrgTreeVos = Lists.newArrayList();
        List<OrganizationDto> orgDtos = baseMapper.selectList(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(orgDtos)) {
            return familyOrgTreeVos;
        }

        List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);

        Map<Long, OrgTreeNodeVo> orgTreeNodeVoMap = orgVos.stream().collect(Collectors.toMap(t -> t.getId(), r -> r));
        if (null == orgTreeNodeVoMap.get(bottomOrgId) || null == orgTreeNodeVoMap.get(topOrgId)) {
            return familyOrgTreeVos;
        }

        List<OrgTreeNodeVo> allFamilyNodes = findFamilyNodes(orgVos, bottomOrgId, topOrgId);
        Map<Long, OrgTreeNodeVo> familyOrgTreeNodeVoMap = allFamilyNodes.stream().collect(Collectors.toMap(t -> t.getId(), r -> r));
        if (null == familyOrgTreeNodeVoMap.get(topOrgId)) {
            // 没有找到祖先
            return familyOrgTreeVos;
        }

        OrgTreeNodeVo topNodeVo = familyOrgTreeNodeVoMap.get(topOrgId);

        familyOrgTreeVos = buildOrgTree(allFamilyNodes, topNodeVo.getParentId());
        return familyOrgTreeVos;
    }

    @Override
    public List<OrgTreeNodeVo> getOrganizationTree(List<Long> orgIds) {
        // 返回树
        List<OrgTreeNodeVo> orgTreeNodeVos = Lists.newArrayList();
        List<OrganizationDto> orgDtos = baseMapper.selectList(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(orgDtos)) {
            return orgTreeNodeVos;
        }

        List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);

        if (CollectionUtil.isEmpty(orgIds)) {
            // 不传，返回所有一级组织树
            return buildOrgTree(orgVos, Constants.ORG_ONE_LEVEL_PID);
        }


        HashSet<Long> orgIdSet = new HashSet<>();
        for (Long orgId : orgIds) {
            // 寻找orgId所属一级公司
            OrgTreeNodeVo rootNode = findRootNode(orgVos, orgId, Constants.ORG_ONE_LEVEL_PID);
            if (null != rootNode) {
                orgIdSet.add(rootNode.getId());
            }
        }

        // 没有找到
        if (CollectionUtil.isEmpty(orgIdSet)) {
            return orgTreeNodeVos;
        }

        // 一级公司树（集团的id为1）
        List<OrgTreeNodeVo> orgTreeNodeVoList = buildOrgTree(orgVos, Constants.ORG_ONE_LEVEL_PID);
        orgTreeNodeVos = orgTreeNodeVoList.stream().filter(v -> orgIdSet.contains(v.getId())).collect(Collectors.toList());
        return orgTreeNodeVos;
    }

    @Override
    public OrgTreeNodeVo getSubOrganizationTree(Long orgId) {
        if (null == orgId) {
            return null;
        }

        List<OrganizationDto> orgDtos = baseMapper.selectList(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(orgDtos)) {
            return null;
        }

        List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
        return getSubOrgTree(orgVos, orgId);
    }

    @Override
    public OrgTreeNodeVo getSubOrganizationTreeWithAdmin(Long orgId) {
        if (null == orgId) {
            return null;
        }

        List<OrganizationDto> orgDtos = baseMapper.selectList(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(orgDtos)) {
            return null;
        }

        List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);

        List<UserDto> userDtos = userMapper.selectList(new LambdaQueryWrapper<UserDto>()
                .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                .eq(UserDto::getIsLocked, IsLocked.NO.getCode()));
        if (CollectionUtil.isNotEmpty(userDtos)) {
            List<UserVo> userVos = BeanUtil.copyToList(userDtos, UserVo.class);
            for (UserVo vo : userVos) {
                String orgNamePath = findOrgNamePath(orgVos, vo.getOrganizationId());
                vo.setOrgPath(orgNamePath);
            }

            Map<Long, List<UserVo>> userOrgMap = userVos.stream().collect(Collectors.groupingBy(UserVo::getOrganizationId));

            for (OrgTreeNodeVo nodeVo : orgVos) {
                List<UserVo> subUserVos = userOrgMap.get(nodeVo.getId());
                if (CollectionUtil.isEmpty(subUserVos)) {
                    continue;
                }

                List<OrgTreeNodeVo> subNodeVos = Lists.newArrayList();
                for (UserVo userVo : subUserVos) {
                    OrgTreeNodeVo userNodeVo = new OrgTreeNodeVo();
                    userNodeVo.setParentId(nodeVo.getId());
                    userNodeVo.setId(userVo.getId());
                    userNodeVo.setName(userVo.getRealName());
                    userNodeVo.setUserVo(userVo);

                    subNodeVos.add(userNodeVo);
                }

                List<OrgTreeNodeVo> children = nodeVo.getChildren();
                if (CollectionUtil.isEmpty(children)) {
                    children = Lists.newArrayList();
                }
                children.addAll(subNodeVos);

                nodeVo.setChildren(children);
            }
        }

        return getSubOrgTree(orgVos, orgId);
    }

    public List<OrgTreeNodeVo> findFamilyNodes(List<OrgTreeNodeVo> orgVos, Long bottomOrgId, Long topOrgId) {
        List<OrgTreeNodeVo> allFamilyNodes = Lists.newArrayList();

        for (OrgTreeNodeVo nodeVo : orgVos) {
            // 寻找节点
            if (bottomOrgId.equals(nodeVo.getId())) {
                allFamilyNodes.add(nodeVo);

                if (bottomOrgId.equals(topOrgId)) {
                    // 截止
                    break;
                }

                List<OrgTreeNodeVo> upParentNodes = findFamilyNodes(orgVos, nodeVo.getParentId(), topOrgId);
                allFamilyNodes.addAll(upParentNodes);
                break;
            }
        }

        return allFamilyNodes;
    }

    public OrgTreeNodeVo findRootNode(List<OrgTreeNodeVo> orgVos, Long orgId, Long findLevelPid) {
        OrgTreeNodeVo findNode = null;

        for (OrgTreeNodeVo nodeVo : orgVos) {
            if (orgId.equals(nodeVo.getId())) {
                // 寻找父节点
                if (findLevelPid.equals(nodeVo.getParentId())) {
                    // 找到
                    findNode = nodeVo;
                    break;
                }

                findNode = findRootNode(orgVos, nodeVo.getParentId(), findLevelPid);
                break;
            }
        }

        return findNode;
    }

    public String findOrgNamePath(List<OrgTreeNodeVo> orgVos, Long orgId) {
        if (CollectionUtil.isEmpty(orgVos) || null == orgId) {
            return null;
        }

        StringBuilder namePath = new StringBuilder();
        for (OrgTreeNodeVo nodeVo : orgVos) {
            if (orgId.equals(nodeVo.getId())) {
                // 寻找父节点
                if (Constants.ORG_TOP_LEVEL_PID.equals(nodeVo.getParentId())) {
                    // 找到
                    namePath.append(nodeVo.getName());
                    break;
                }

                String orgNamePath = findOrgNamePath(orgVos, nodeVo.getParentId());
                if (StringUtils.isNotBlank(orgNamePath)) {
                    namePath.append(nodeVo.getName()).append("/").append(orgNamePath);
                }
                break;
            }
        }

        return namePath.toString();
    }

    public List<OrgTreeNodeVo> buildOrgTree(List<OrgTreeNodeVo> orgVos, Long pid) {
        Map<Long, List<OrgTreeNodeVo>> parentMap = orgVos.stream().collect(Collectors.groupingBy(OrgTreeNodeVo::getParentId));
//        orgVos.forEach(t -> t.setChildren(parentMap.get(t.getId())));
        for (OrgTreeNodeVo nodeVo : orgVos) {
            List<OrgTreeNodeVo> orgTreeNodeVos = parentMap.get(nodeVo.getId());
            if (CollectionUtil.isNotEmpty(orgTreeNodeVos)) {
                List<OrgTreeNodeVo> children = nodeVo.getChildren();
                if (CollectionUtil.isEmpty(children)) {
                    children = Lists.newArrayList();
                }
                children.addAll(orgTreeNodeVos);
                nodeVo.setChildren(children);
            }
        }

        return orgVos.stream().filter(v -> v.getParentId().equals(pid)).collect(Collectors.toList());
    }

    public OrgTreeNodeVo getSubOrgTree(List<OrgTreeNodeVo> orgVos, Long id) {
        Map<Long, List<OrgTreeNodeVo>> parentMap = orgVos.stream().collect(Collectors.groupingBy(OrgTreeNodeVo::getParentId));
//        orgVos.forEach(t -> t.setChildren(parentMap.get(t.getId())));

        OrgTreeNodeVo treeNodeVo = null;
        for (OrgTreeNodeVo nodeVo : orgVos) {
            List<OrgTreeNodeVo> orgTreeNodeVos = parentMap.get(nodeVo.getId());
            if (CollectionUtil.isNotEmpty(orgTreeNodeVos)) {
                List<OrgTreeNodeVo> children = nodeVo.getChildren();
                if (CollectionUtil.isEmpty(children)) {
                    children = Lists.newArrayList();
                }
                children.addAll(orgTreeNodeVos);
                nodeVo.setChildren(children);
            }

            if (nodeVo.getId().equals(id)) {
                treeNodeVo = nodeVo;
            }
        }

        return treeNodeVo;
    }

    public List<OrgTreeNodeVo> getSubOrgNodes(List<OrgTreeNodeVo> orgVos, Long id) {
        List<OrgTreeNodeVo> allSubNodes = Lists.newArrayList();
        for (OrgTreeNodeVo nodeVo : orgVos) {
            if (nodeVo.getId().equals(id)) {
                // 本身
                allSubNodes.add(nodeVo);
            }

            if (nodeVo.getParentId().equals(id)) {
                // 找到子节点
                List<OrgTreeNodeVo> subOrgNodes = getSubOrgNodes(orgVos, nodeVo.getId());
                allSubNodes.addAll(subOrgNodes);
            }
        }

        return allSubNodes;
    }

    @Override
    public AjaxResult addOrganization(OrganizationBo organizationBo) {
        OrganizationDto organization = new OrganizationDto();
        BeanUtils.copyProperties(organizationBo, organization);

        organization.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        organization.setCreateTime(new Date());
        save(organization);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateOrganization(OrganizationBo organizationBo) {
        OrganizationDto organization = new OrganizationDto();
        BeanUtils.copyProperties(organizationBo, organization);

        organization.setUpdateTime(new Date());
        updateById(organization);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<OrganizationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(OrganizationDto::getId, id);

        OrganizationDto organization = getOne(lqw);
        if (null == organization) {
            return AjaxResult.fail("组织机构表数据不存在");
        }

        OrganizationVo organizationVo = new OrganizationVo();
        BeanUtils.copyProperties(organization, organizationVo);

        List<OrganizationDto> orgDtos = list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            String orgNamePath = findOrgNamePath(orgVos, organizationVo.getId());
            organizationVo.setOrgPath(orgNamePath);
        }

        return AjaxResult.success(organizationVo);
    }

    @Override
    public OrganizationVo getOrganizationByCondition(OrganizationConditionBo condition) {
        List<OrganizationVo> organizationVos = organizationMapper.getOrganizationListByCondition(condition);
        if (CollectionUtils.isNotEmpty(organizationVos)) {
            return organizationVos.get(0);
        }
        return null;
    }

    @Override
    public AjaxResult updateState(OrganizationBo organizationBo) {
        // 更新状态
        LambdaQueryWrapper<OrganizationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(OrganizationDto::getId, organizationBo.getId());

        OrganizationDto organization = getOne(lqw);
        if (null == organization) {
            return AjaxResult.fail("组织机构表数据不存在");
        }

        OrganizationDto dto = new OrganizationDto();
        dto.setId(organization.getId());
        dto.setState(organizationBo.getState());
        dto.setUpdateBy(organizationBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteOrganization(OrganizationBo organizationBo) {
        // 删除信息
        LambdaQueryWrapper<OrganizationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(OrganizationDto::getId, organizationBo.getId());

        OrganizationDto organization = getOne(lqw);
        if (null == organization) {
            return AjaxResult.fail("组织机构表数据不存在");
        }

        OrganizationDto dto = new OrganizationDto();
        dto.setId(organization.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(organizationBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult organizationDetailWithPackage(Long id) {
        LambdaQueryWrapper<OrganizationDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(OrganizationDto::getId, id);

        OrganizationDto organization = getOne(lqw);
        if (null == organization) {
            return AjaxResult.fail("组织机构表数据不存在");
        }

        OrganizationVo organizationVo = new OrganizationVo();
        BeanUtils.copyProperties(organization, organizationVo);

        List<OrganizationDto> orgDtos = list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            String orgNamePath = findOrgNamePath(orgVos, organizationVo.getId());
            organizationVo.setOrgPath(orgNamePath);
        }

        // 已生成账号数
        UserConditionBo userConditionBo = new UserConditionBo();
        userConditionBo.setOrganizationId(id);
        userConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<UserVo> userVos = userMapper.getUserListByCondition(userConditionBo);
        if (CollectionUtil.isEmpty(userVos)) {
            organizationVo.setUserCount(0);
        } else {
            organizationVo.setUserCount(userVos.size());
        }

        // 查询企业管理员
        AdminConditionBo adminConditionBo = new AdminConditionBo();
        adminConditionBo.setOrganizationId(id);
        adminConditionBo.setRoleId(ORG_ADMIN_ROLE_ID);
        adminConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<AdminVo> adminVos = adminMapper.getAdminListByCondition(adminConditionBo);
        if (CollectionUtil.isNotEmpty(adminVos)) {
            organizationVo.setOrganizationAdmin(adminVos.get(0));
        }

        // 查询企业套餐
        OrganizationPackageConditionBo organizationPackageConditionBo = new OrganizationPackageConditionBo();
        organizationPackageConditionBo.setOrganizationId(id);
        List<OrganizationPackageVo> organizationPackageVos
                = organizationPackageService.getPOrganizationPackageListByCondition(organizationPackageConditionBo);
        if (CollectionUtil.isNotEmpty(organizationPackageVos)) {
            OrganizationPackageVo organizationPackageVo = organizationPackageVos.get(0);
            organizationPackageVo.setAuthTimeStart(organizationVo.getAuthStartTime());
            organizationPackageVo.setAuthTimeEnd(organizationVo.getAuthEndTime());
            organizationVo.setOrganizationPackage(organizationPackageVo);
        }

        return AjaxResult.success(organizationVo);
    }

    @Override
    public Map<String, Object> organizationListWithPackage(OrganizationConditionBo conditionBo) {
        conditionBo.setOrderBy("create_time desc");
        Map<String, Object> map = new HashMap<>(4);
        List<OrganizationVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = organizationMapper.getOrganizationListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<OrganizationVo> organizationVos = organizationMapper.getOrganizationListByCondition(conditionBo);
            PageInfo<OrganizationVo> pageInfo = new PageInfo<>(organizationVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            List<Long> organizationIds = list.stream().map(OrganizationVo::getId).collect(Collectors.toList());

            // 开通账号数
            Map<Long, Integer> userAccountCountMap = Maps.newHashMap();
            List<UserAccountCountVo> userAccountCountVos = userMapper.getUserAccountCountByOrganizationIds(organizationIds);
            if (CollectionUtil.isNotEmpty(userAccountCountVos)) {
                userAccountCountMap = userAccountCountVos.stream()
                        .collect(Collectors.toMap(UserAccountCountVo::getOrganizationId, UserAccountCountVo::getAccountNum, (v1, v2) -> v1));
            }

            // 查询企业套餐
            Map<Long, OrganizationPackageVo> organizationPackageVoMap = Maps.newHashMap();
            OrganizationPackageConditionBo organizationPackageConditionBo = new OrganizationPackageConditionBo();
            organizationPackageConditionBo.setOrganizationIds(organizationIds);
            List<OrganizationPackageVo> organizationPackageVos
                    = organizationPackageService.getPOrganizationPackageListByCondition(organizationPackageConditionBo);
            if (CollectionUtil.isNotEmpty(organizationPackageVos)) {
                organizationPackageVoMap = organizationPackageVos.stream()
                        .collect(Collectors.toMap(OrganizationPackageVo::getOrganizationId, op -> op, (v1, v2) -> v1));
            }

            for (OrganizationVo organizationVo : list) {
                organizationVo.setUserCount(0);
                if (userAccountCountMap.containsKey(organizationVo.getId())) {
                    organizationVo.setUserCount(userAccountCountMap.get(organizationVo.getId()));
                }
                if (organizationPackageVoMap.containsKey(organizationVo.getId())) {
                    OrganizationPackageVo organizationPackageVo = organizationPackageVoMap.get(organizationVo.getId());
                    organizationPackageVo.setAuthTimeStart(organizationVo.getAuthStartTime());
                    organizationPackageVo.setAuthTimeEnd(organizationVo.getAuthEndTime());
                    organizationVo.setOrganizationPackage(organizationPackageVo);
                }
            }
        }


        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public OrganizationAccumulateUsageStatisticVo getOrganizationAccumulateUsageStatistic() {
        return organizationPackageMapper.getOrganizationAccumulateUsageStatistic();
    }

    @Override
    public boolean reduceQuota(OrganizationReduceQuotaBo organizationReduceQuotaBo) {
        log.info("扣减企业库存开始......");
        RLock lock = redissonClient.getLock("lock:organization:" + organizationReduceQuotaBo.getOrganizationId());
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");

            /**
             * 没有使用配额，结束扣款流程
             */
            if (organizationReduceQuotaBo.getUsageQuota() <= 0L) {
                log.info("本次无使用配额，参数：" + JSON.toJSONString(organizationReduceQuotaBo));
                return true;
            }

            // 扣款记录bo
            OrganizationReduceQuotaRecordBo reduceQuotaRecordBo = new OrganizationReduceQuotaRecordBo();
            reduceQuotaRecordBo.setOrganizationId(organizationReduceQuotaBo.getOrganizationId());
            reduceQuotaRecordBo.setUserOid(organizationReduceQuotaBo.getUserOid());

            // 文本推理type
//            List<Integer> inferenceTypes = Lists.newArrayList(TypeEnum.DEALING.getCode(), TypeEnum.ASK.getCode(),
//                    TypeEnum.NEW.getCode(), TypeEnum.DAY.getCode(), TypeEnum.PPT.getCode(), TypeEnum.SWOT.getCode(),
//                    TypeEnum.CHOOSE.getCode(), TypeEnum.BOOLEVAL.getCode(), TypeEnum.COURSE.getCode(), TypeEnum.ANCIENT.getCode(),
//                    TypeEnum.AI_CHECK.getCode(), TypeEnum.OLD_EDITOR.getCode(), TypeEnum.CHECK.getCode(), TypeEnum.FIRST.getCode(),
//                    TypeEnum.TITLE.getCode(), TypeEnum.OPERATION_COPY.getCode(), TypeEnum.ACTIVITY.getCode(), TypeEnum.SHORT_VIDEO.getCode(),
//                    TypeEnum.BROADCAST.getCode(), TypeEnum.MASTER_BOOK.getCode(), TypeEnum.MARKET_PLAN.getCode(),
//                    TypeEnum.PROMOTION.getCode(), TypeEnum.DIRECT.getCode(), TypeEnum.MARKETING_APP.getCode(), TypeEnum.MEETING.getCode());
            // 音频文件转写type
            List<Integer> transliterateTypes = Lists.newArrayList();
            transliterateTypes.add(TypeEnum.MEETING.getCode());
            // 图片生成type
            List<Integer> mtTypes = Lists.newArrayList(TypeEnum.MARKET.getCode(), TypeEnum.TEXT2IMAGE.getCode(),
                    TypeEnum.IMAGE2IMAGE.getCode(), TypeEnum.PARTIAL_REDRAW.getCode(), TypeEnum.LOSSLESS_ZOOM.getCode(),
                    TypeEnum.AI_TRACELESS_ELIMINATION.getCode(), TypeEnum.INTELLIGENT_CUTTING.getCode(),
                    TypeEnum.GOODS_IMAGE.getCode());
            // ppt
            List<Integer> pptTypes = Lists.newArrayList();
            pptTypes.add(TypeEnum.PPT.getCode());
            // deepseek
            List<Integer> deepseekTypes = Lists.newArrayList();
            deepseekTypes.add(TypeEnum.DEEP_SEEK.getCode());

            // 查询企业套餐
            OrganizationPackageConditionBo organizationPackageConditionBo = new OrganizationPackageConditionBo();
            organizationPackageConditionBo.setOrganizationId(organizationReduceQuotaBo.getOrganizationId());
            List<OrganizationPackageVo> organizationPackageVos
                    = organizationPackageService.getPOrganizationPackageListByCondition(organizationPackageConditionBo);
            OrganizationPackageVo organizationPackageVo = organizationPackageVos.get(0);

            OrganizationPackageDto entity = new OrganizationPackageDto();
            entity.setId(organizationPackageVo.getId());
            // 本次消费金额
            BigDecimal consumeAmount = new BigDecimal(0);
            // 文本推理（非图片生成类型） 智能会议根据参数quotaType类型判断 ppt根据参数quotaType类型判断
            if (!mtTypes.contains(organizationReduceQuotaBo.getType())
                    && !OrganizationQuotaType.TRANSLITERATE.getValue().equals(organizationReduceQuotaBo.getQuotaType())
                    && !OrganizationQuotaType.PPT.getValue().equals(organizationReduceQuotaBo.getQuotaType())) {
                consumeAmount = UnitPriceEnum.INFERENCE.getPrice()
                        .multiply(new BigDecimal(organizationReduceQuotaBo.getUsageQuota()))
                        .divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP)
                        .multiply(UnitPriceEnum.INFERENCE.getCoefficient())
                        .setScale(2, RoundingMode.HALF_UP);
                // 文本推理使用量增加
                entity.setInferenceUsageNum(organizationPackageVo.getInferenceUsageNum() + organizationReduceQuotaBo.getUsageQuota());
                // 文本推理消费金额增加
                entity.setInferenceUsageAmount(organizationPackageVo.getInferenceUsageAmount().add(consumeAmount));
                // 扣款记录
                reduceQuotaRecordBo.setInferenceUsageNum(organizationReduceQuotaBo.getUsageQuota());
                reduceQuotaRecordBo.setInferenceUsageAmount(consumeAmount);
            }
            // 录音文件转写
            if (transliterateTypes.contains(organizationReduceQuotaBo.getType())
                    && OrganizationQuotaType.TRANSLITERATE.getValue().equals(organizationReduceQuotaBo.getQuotaType())) {
                consumeAmount = new BigDecimal(organizationReduceQuotaBo.getUsageQuota())
                        .multiply(UnitPriceEnum.TRANSLITERATE.getPrice())
                        .divide(new BigDecimal(60), 5, RoundingMode.HALF_UP)
                        .multiply(UnitPriceEnum.TRANSLITERATE.getCoefficient())
                        .setScale(2, RoundingMode.HALF_UP);
                // 录音文件转写使用量增加
                entity.setTransliterateUsageNum(organizationPackageVo.getTransliterateUsageNum() + organizationReduceQuotaBo.getUsageQuota());
                // 录音文件传写消费金额增加
                entity.setTransliterateUsageAmount(organizationPackageVo.getTransliterateUsageAmount().add(consumeAmount));
                // 扣款记录
                reduceQuotaRecordBo.setTransliterateUsageNum(organizationReduceQuotaBo.getUsageQuota());
                reduceQuotaRecordBo.setTransliterateUsageAmount(consumeAmount);
            }
            // 图片生成
            if (mtTypes.contains(organizationReduceQuotaBo.getType())) {
                consumeAmount = UnitPriceEnum.MT.getPrice()
                        .multiply(new BigDecimal(organizationReduceQuotaBo.getUsageQuota()))
                        .multiply(UnitPriceEnum.MT.getCoefficient())
                        .setScale(2, RoundingMode.HALF_UP);
                // 图片生成使用量增加
                entity.setMtUsageNum(organizationPackageVo.getMtUsageNum() + organizationReduceQuotaBo.getUsageQuota());
                // 图片生成消费金额增加
                entity.setMtUsageAmount(organizationPackageVo.getMtUsageAmount().add(consumeAmount));
                // 扣款记录
                reduceQuotaRecordBo.setMtUsageNum(organizationReduceQuotaBo.getUsageQuota());
                reduceQuotaRecordBo.setMtUsageAmount(consumeAmount);
            }
            // ppt
            if (OrganizationQuotaType.PPT.getValue().equals(organizationReduceQuotaBo.getQuotaType())) {
                consumeAmount = UnitPriceEnum.PPT.getPrice()
                        .multiply(new BigDecimal(organizationReduceQuotaBo.getUsageQuota()))
                        .multiply(UnitPriceEnum.PPT.getCoefficient())
                        .setScale(2, RoundingMode.HALF_UP);
                // ppt生成使用量增加
                entity.setPptUsageNum(organizationPackageVo.getPptUsageNum() + organizationReduceQuotaBo.getUsageQuota());
                // ppt生成消费金额增加
                entity.setPptUsageAmount(organizationPackageVo.getPptUsageAmount().add(consumeAmount));
                // 扣款记录
                reduceQuotaRecordBo.setPptUsageNum(organizationReduceQuotaBo.getUsageQuota());
                reduceQuotaRecordBo.setPptUsageAmount(consumeAmount);
            }
            // deepseek，算到文本推理中，价格不同
            if (deepseekTypes.contains(organizationReduceQuotaBo.getType())) {
                consumeAmount = UnitPriceEnum.DEEPSEEK.getPrice()
                        .multiply(new BigDecimal(organizationReduceQuotaBo.getUsageQuota()))
                        .divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP)
                        .multiply(UnitPriceEnum.DEEPSEEK.getCoefficient())
                        .setScale(2, RoundingMode.HALF_UP);
                // 文本推理使用量增加
                entity.setInferenceUsageNum(organizationPackageVo.getInferenceUsageNum() + organizationReduceQuotaBo.getUsageQuota());
                // 文本推理消费金额增加
                entity.setInferenceUsageAmount(organizationPackageVo.getInferenceUsageAmount().add(consumeAmount));
                // 扣款记录
                reduceQuotaRecordBo.setInferenceUsageNum(organizationReduceQuotaBo.getUsageQuota());
                reduceQuotaRecordBo.setInferenceUsageAmount(consumeAmount);
            }

            // 余额扣减
            BigDecimal balance = organizationPackageVo.getBalance();
            balance = balance.subtract(consumeAmount);
            entity.setBalance(balance);
            // 消费金额增加
            entity.setConsumeAmount(organizationPackageVo.getConsumeAmount().add(consumeAmount));

            // 更新记录
            organizationPackageService.updateById(entity);

            // 欠费提醒
            if (balance.compareTo(new BigDecimal(0)) <= 0
                    && SmsRemindType.NOT_REMIND.getValue() == organizationPackageVo.getSmsArrearsRemind()) {
                smsArrearsRemind(organizationReduceQuotaBo.getOrganizationId(), organizationPackageVo.getId());
            }

            // 保存扣费记录
            reduceQuotaRecordBo.setType(organizationReduceQuotaBo.getType());
            organizationReduceQuotaRecordService.addOrganizationReduceQuotaRecord(reduceQuotaRecordBo);

            return true;
        } catch (Exception e) {
            log.error("企业扣减费用异常，e：" + e);
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }
    }

    @Override
    public boolean checkOrganizationQuota(Long organizationId) {
        // 查询企业套餐
        OrganizationPackageConditionBo organizationPackageConditionBo = new OrganizationPackageConditionBo();
        organizationPackageConditionBo.setOrganizationId(organizationId);
        List<OrganizationPackageVo> organizationPackageVos
                = organizationPackageService.getPOrganizationPackageListByCondition(organizationPackageConditionBo);
        // 没有套餐，返回false
        if (CollectionUtil.isEmpty(organizationPackageVos)) {
            return false;
        }
        OrganizationPackageVo organizationPackageVo = organizationPackageVos.get(0);
        boolean check = organizationPackageVo.getBalance().compareTo(new BigDecimal(0)) > 0;
        if (!check && SmsRemindType.NOT_REMIND.getValue() == organizationPackageVo.getSmsArrearsRemind()) {
            // 欠费提醒
            smsArrearsRemind(organizationId, organizationPackageVo.getId());
        }
        return check;
    }

    @Override
    public OrgTreeNodeVo findStatisticOrg(List<OrgTreeNodeVo> orgVos, List<Long> statisticOrgIds, Long orgId) {
        if (CollectionUtil.isEmpty(orgVos) || null == orgId || CollectionUtil.isEmpty(statisticOrgIds)) {
            return null;
        }

        for (OrgTreeNodeVo nodeVo : orgVos) {
            if (orgId.equals(nodeVo.getId())) {
                // 寻找父节点
                if (Constants.ORG_TOP_LEVEL_PID.equals(nodeVo.getParentId())) {
                    // 找到
                    return nodeVo;
                }

                // 找到统计组织，返回
                if (statisticOrgIds.contains(nodeVo.getId())) {
                    return nodeVo;
                }
                return findStatisticOrg(orgVos, statisticOrgIds, nodeVo.getParentId());
            }
        }

        return null;
    }

    /**
     * 欠费短信提醒
     *
     * @param organizationId
     * @param organizationPackageId
     * @return void
     * <AUTHOR>
     * @date 2024/10/31 11:35
     **/
    private void smsArrearsRemind(Long organizationId, Long organizationPackageId) {
        String phone = "";
        // 查询企业管理员
        AdminConditionBo adminConditionBo = new AdminConditionBo();
        adminConditionBo.setOrganizationId(organizationId);
        adminConditionBo.setRoleId(ORG_ADMIN_ROLE_ID);
        adminConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        List<AdminVo> adminVos = adminMapper.getAdminListByCondition(adminConditionBo);
        if (CollectionUtil.isNotEmpty(adminVos)) {
            phone = adminVos.get(0).getPhone();
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(phone)) {
            log.error("企业：{} 未找到管理员手机号", organizationId);
            return;
        }

        // 发送短信
        final boolean sendSuccess = this.douyinSmsService.sendNoticeMessage(phone, SmsSendTypeEnum.ORG_ARREARS_REMIND, null);
        if (sendSuccess) {
            // 设置已提醒
            OrganizationPackageDto organizationPackageDto = new OrganizationPackageDto();
            organizationPackageDto.setId(organizationPackageId);
            organizationPackageDto.setSmsArrearsRemind(SmsRemindType.IS_REMIND.getValue());
            organizationPackageService.updateById(organizationPackageDto);
        }
    }
}