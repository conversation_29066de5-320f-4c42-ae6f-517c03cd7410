package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.business.entity.bo.conversationFile.ConversationFileConditionBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.bo.mtHistory.MtHistoryConditionBo;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.bo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordConditionBo;
import com.fh.ai.business.entity.bo.organizationUsageStatistic.OrganizationUsageStatisticConditionBo;
import com.fh.ai.business.entity.bo.pictureDownloadHistory.PictureDownloadHistoryConditionBo;
import com.fh.ai.business.entity.dto.organizationUsageStatistic.OrganizationUsageStatisticDto;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.business.entity.vo.mtHistory.MtHistoryVo;
import com.fh.ai.business.entity.vo.mtUser.MtUserVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo;
import com.fh.ai.business.entity.vo.organizationUsageStatistic.OrganizationUsageStatisticVo;
import com.fh.ai.business.entity.vo.pictureDownloadHistory.PictureDownloadHistoryVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.IOrganizationUsageStatisticService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.TypeEnum;
import com.fh.ai.common.meitu.bo.MeituBo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-10-30  16:40
 */
@Service
@Slf4j
public class OrganizationUsageStatisticServiceImpl extends ServiceImpl<OrganizationUsageStatisticMapper, OrganizationUsageStatisticDto> implements IOrganizationUsageStatisticService {
    @Resource
    OrganizationUsageStatisticMapper organizationUsageStatisticMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private MtHistoryMapper mtHistoryMapper;
    @Resource
    private MtUserMapper mtUserMapper;
    @Resource
    private HistoryAppMapper historyAppMapper;
    @Resource
    private ConversationFileMapper conversationFileMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private PictureDownloadHistoryMapper pictureDownloadHistoryMapper;
    @Resource
    private OrganizationReduceQuotaRecordMapper organizationReduceQuotaRecordMapper;

    @Override
    public Map<String, Object> getOrganizationUsageStatisticListByCondition(OrganizationUsageStatisticConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        List<OrganizationUsageStatisticVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = organizationUsageStatisticMapper.getOrganizationUsageStatisticListByCondition(condition);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<OrganizationUsageStatisticVo> organizationUsageStatisticVos
                    = organizationUsageStatisticMapper.getOrganizationUsageStatisticListByCondition(condition);
            PageInfo<OrganizationUsageStatisticVo> pageInfo = new PageInfo<>(organizationUsageStatisticVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public void organizationUsageStatisticTask(Date statisticDay, List<Long> organizationIds) {
        log.info("企业每天用量统计开始......");
        RLock lock = redissonClient.getLock("lock:organization:usageStatistic");
        try {
            lock.lock(1, TimeUnit.HOURS);
            log.info("get lock");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            statisticDay = sdf.parse(sdf.format(statisticDay));
            log.info("企业用量统计，statisticDay=" + statisticDay);
            // 获取所有企业
            OrganizationConditionBo organizationConditionBo = new OrganizationConditionBo();
            organizationConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            organizationConditionBo.setIds(organizationIds);
            List<OrganizationVo> organizationVos = organizationMapper.getOrganizationListByCondition(organizationConditionBo);
            if (CollectionUtil.isEmpty(organizationVos)) {
                return;
            }

            // 获取企业用量
            OrganizationReduceQuotaRecordConditionBo reduceQuotaRecordConditionBo = new OrganizationReduceQuotaRecordConditionBo();
            reduceQuotaRecordConditionBo.setOrganizationIds(organizationIds);
            reduceQuotaRecordConditionBo.setStatisticDay(statisticDay);
            reduceQuotaRecordConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            List<OrganizationReduceQuotaRecordVo> organizationReduceQuotaRecordVos
                    = organizationReduceQuotaRecordMapper.getOrganizationReduceQuotaRecordListByDay(reduceQuotaRecordConditionBo);
            Map<Long, OrganizationReduceQuotaRecordVo> recordVoMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(organizationReduceQuotaRecordVos)) {
                recordVoMap = organizationReduceQuotaRecordVos.stream()
                        .collect(Collectors.toMap(OrganizationReduceQuotaRecordVo::getOrganizationId, recordVo -> recordVo, (v1, v2) -> v1));
            }

            List<OrganizationUsageStatisticDto> organizationUsageStatisticDtos = Lists.newArrayList();
            Date nowTime = new Date();
            for (OrganizationVo organizationVo : organizationVos) {
                OrganizationUsageStatisticDto organizationUsageStatisticDto = new OrganizationUsageStatisticDto();
                organizationUsageStatisticDto.setOrganizationId(organizationVo.getId());
                organizationUsageStatisticDto.setStatisticDay(statisticDay);
                organizationUsageStatisticDto.setCreateTime(nowTime);
                organizationUsageStatisticDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                if (recordVoMap.containsKey(organizationVo.getId())) {
                    OrganizationReduceQuotaRecordVo recordVo = recordVoMap.get(organizationVo.getId());
                    organizationUsageStatisticDto.setInferenceUsageNum(recordVo.getInferenceUsageNum());
                    organizationUsageStatisticDto.setTransliterateUsageNum(recordVo.getTransliterateUsageNum());
                    organizationUsageStatisticDto.setMtUsageNum(recordVo.getMtUsageNum());
                    organizationUsageStatisticDto.setPptUsageNum(recordVo.getPptUsageNum());
                } else {
                    organizationUsageStatisticDto.setInferenceUsageNum(0L);
                    organizationUsageStatisticDto.setTransliterateUsageNum(0L);
                    organizationUsageStatisticDto.setMtUsageNum(0L);
                    organizationUsageStatisticDto.setPptUsageNum(0L);
                }
                organizationUsageStatisticDtos.add(organizationUsageStatisticDto);
            }
            // 保存统计数据
            if (CollectionUtil.isNotEmpty(organizationUsageStatisticDtos)) {
                saveBatch(organizationUsageStatisticDtos);
            }
        } catch (Exception e) {
            log.error("企业每天用量统计异常，e:" + e);
        } finally {
            lock.unlock();
            log.info("release lock");
        }
    }
}
