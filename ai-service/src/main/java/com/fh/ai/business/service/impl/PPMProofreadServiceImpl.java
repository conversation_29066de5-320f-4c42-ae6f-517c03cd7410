package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingRecordDto;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import com.fh.ai.business.mapper.ProofreadingTaskMapper;
import com.fh.ai.business.service.IPPMProofreadService;
import com.fh.ai.business.service.IProofreadingRecordService;
import com.fh.ai.business.service.IProofreadingTaskService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.proofreading.ProofreadConstants;
import com.fh.ai.common.proofreading.fh.PPMProofreadingUtil;
import com.fh.ai.common.proofreading.fh.req.PPMSetting;
import com.fh.ai.common.proofreading.fh.req.PPMSettingOption;
import com.fh.ai.common.proofreading.fh.vo.*;
import com.fh.ai.common.proofreading.fz.vo.FileResource;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Classname IPPMProofreadServiceImpl
 * @Description 凤凰报刊审校业务处理
 * @Date 2025/1/17 15:43
 * @Created by admin
 */
@Service
@Slf4j
public class PPMProofreadServiceImpl implements IPPMProofreadService {
    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    /**
     * 查询异步任务状态最大重试次数
     */
    private static final Integer MAX_ATTEMPTS = 90;
    /**
     * 查询异步任务状态线程休眠时间
     */
    private static final Integer SLEEP_INTERVAL_MS = 10000;

    /**
     * 凤凰报刊审校工具类
     */
    @Resource
    PPMProofreadingUtil ppmProofreadingUtil;
    /**
     * 审校任务业务类
     */
    @Resource
    IProofreadingTaskService proofreadingTaskService;

    /**
     * 审校记录业务类
     */
    @Resource
    IProofreadingRecordService proofreadingRecordService;
    /**
     * 审校任务mapper
     */
    @Resource
    ProofreadingTaskMapper proofreadingTaskMapper;

    @Resource
    RedisComponent redisComponent;


    /***
     * 凤凰在线同步审校
     * @param ppmSetting 选项
     * @return ProofreadingTaskDto的主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createPPMOnlineSyncTask(PPMSetting ppmSetting) {
        // 根据审校记录id找到审校记录
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setId(ppmSetting.getRecordId());
        ProofreadingRecordVo record =
                proofreadingRecordService.getProofreadingRecordByCondition(conditionBo);
        // 校验审校记录
        AjaxResult checkRecordLegal = proofreadingRecordService.checkRecordLegal(record, ppmSetting.getType());
        if(!checkRecordLegal.getSuccess()){
            return checkRecordLegal;
        }
        // 校验审校记录的待执行列表
        List<Integer> excutedList = JSON.parseArray(record.getExecutedTaskInfo(), Integer.class);
        if (!excutedList.contains(ProofreadingExcutedEnum.OLINE_PPM_WORD.getCode())){
            return AjaxResult.fail("审校记录执行列表未找到凤凰文件审校任务");
        }


        Map<Integer, ProofreadingTaskVo> ppmRecordTaskMap = getPPMRecordTaskMap(record.getId());
        if (ppmRecordTaskMap.containsKey(ProofreadingTaskType.WORDS_CHECK.getCode())){
            return AjaxResult.fail("当前审校记录已经存在处理中或处理成功的在线文本审校任务，请重新发起审校记录");
        }
        // 从缓存获取token
        String token = ppmProofreadingUtil.getPPMTokenFromCache();
        // 路径参数
        Map<String,Object> pathParam = new HashMap<>();
        pathParam.put(ProofreadConstants.TOKEN,token);
        // 表单参数
        Map<String,String> formData = new HashMap<>();
        // 审校文本内容
        String content = ppmSetting.getContent();
        // 审校设置项
        String functions = buildPPMFunctions(ppmSetting.getOption());
        // 功能点
        formData.put(ProofreadConstants.PPM_FUNCTIONS,functions);
        // 文本内容
        formData.put(ProofreadConstants.PPM_CONTENT,content);

        // 创建并保存审校任务
        ProofreadingTaskBo proofreadingTaskBo = buildProofreadingTask(ppmSetting, formData);
        AjaxResult proofreadingTaskResult = proofreadingTaskService.createProofreadingTask(proofreadingTaskBo);
        ProofreadingTaskDto proofreadingTask = (ProofreadingTaskDto)proofreadingTaskResult.getData();

        // rpc凤凰接口
        AjaxResult onlineSyncTask = ppmProofreadingUtil.createOnlineSyncTask(pathParam, formData);
        // 审校失败
        if (!onlineSyncTask.getSuccess()){
            proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(),JSON.toJSONString(onlineSyncTask),onlineSyncTask.getMsg());
            return onlineSyncTask;
        }
        // 审校成功
        PPMProofreadResultTaskVo ppmProofreadResultTaskVo = (PPMProofreadResultTaskVo)onlineSyncTask.getData();
        ProofreadingTaskDto updateTask = new ProofreadingTaskDto();
        updateTask.setId(proofreadingTask.getId());
        // 状态成功
        updateTask.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        // 接口返回体
        updateTask.setResponseInfo(JSON.toJSONString(ppmProofreadResultTaskVo));
        updateTask.setThirdTaskId(ppmProofreadResultTaskVo.getTaskid());
        updateTask.setFinishTime(new Date());
        updateTask.setUpdateTime(new Date());
        proofreadingTaskService.updateById(updateTask);
        // 审校结果
        PPMResultVo ppmResultVo = new PPMResultVo();
        ppmResultVo.setTaskid(ppmProofreadResultTaskVo.getTaskid());
        List<PPMRpcResultVo> ppmRpcResultVos = PPMProofreadingUtil.handleResult(ppmProofreadResultTaskVo.getResult());
        ppmResultVo.setResultVos(ppmRpcResultVos);
        return AjaxResult.success(ppmResultVo,"在线审校成功");
    }

    /***
     * 凤凰文档审校
     * @param ppmSetting 选项
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createPPMFileTask(PPMSetting ppmSetting) {
        Long recordId = ppmSetting.getRecordId();
        Integer fileType = FileTypeEnum.getValue(ppmSetting.getFileType());
        // 根据审校记录id找到审校记录
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setId(recordId);
        ProofreadingRecordVo record =
                proofreadingRecordService.getProofreadingRecordByCondition(conditionBo);
        // 校验审校记录
        AjaxResult checkRecordLegal = proofreadingRecordService.checkRecordLegal(record, ppmSetting.getType());
        if(!checkRecordLegal.getSuccess()){
            return checkRecordLegal;
        }
        // 校验审校记录的待执行列表
        List<Integer> excutedList = JSON.parseArray(record.getExecutedTaskInfo(), Integer.class);
        if (!excutedList.contains(ProofreadingExcutedEnum.FILE_PPM_WORD.getCode())){
            return AjaxResult.fail("审校记录执行列表未找到凤凰文件审校任务");
        }

        Map<Integer, ProofreadingTaskVo> ppmRecordTaskMap = getPPMRecordTaskMap(record.getId());
        if (ppmRecordTaskMap.containsKey(ProofreadingTaskType.WORDS_CHECK.getCode())){
            return AjaxResult.fail("当前审校记录已经存在处理中或处理成功的文件审校任务，请重新发起审校记录");
        }
        // 从缓存获取token
        String token = ppmProofreadingUtil.getPPMTokenFromCache();
        String originalFileUrl = record.getOriginalFileUrl();
        String filePath = SystemUtil.isWindows() ? windowsPath + originalFileUrl : linuxPath + originalFileUrl;
        File file = new File(filePath);
        Map<String,File> fileMap = new HashMap<>();
        fileMap.put("file",file);
        // 路径参数
        Map<String,Object> pathParam = new HashMap<>();
        pathParam.put(ProofreadConstants.TOKEN,token);
        // 表单参数
        Map<String,String> formData = new HashMap<>();
        // 审校设置项
        String functions = buildPPMFunctions(ppmSetting.getOption());
        // 功能点
        formData.put(ProofreadConstants.PPM_FUNCTIONS,functions);
        // 创建并保存审校任务
        ProofreadingTaskBo proofreadingTaskBo = buildProofreadingTask(ppmSetting, formData);
        AjaxResult proofreadingTaskResult = proofreadingTaskService.createProofreadingTask(proofreadingTaskBo);
        ProofreadingTaskDto proofreadingTask = (ProofreadingTaskDto)proofreadingTaskResult.getData();
        ProofreadingTaskDto updateTask = new ProofreadingTaskDto();
        updateTask.setId(proofreadingTask.getId());
        // 执行文档审校接口
        CompletableFuture
                // 创建凤凰文档审校任务，拿到审校任务id
                .supplyAsync(()->ppmProofreadingUtil.createFileTask(pathParam,formData,fileMap,fileType),ThreadUtil.ppmFilePool)
                // 根据审校任务id，获取审校任务结果。若任务未完成，需自旋等待。
                .thenApply((fileTaskResult)->{
                    // 失败，直接返回失败结果
                    if (!fileTaskResult.getSuccess()){
                        return fileTaskResult;
                    }
                    PPMCreateAsyncTaskVo ppmCreateAsyncTaskVo = (PPMCreateAsyncTaskVo)fileTaskResult.getData();
                    // 从结果中获取任务id
                    String taskid = ppmCreateAsyncTaskVo.getTaskid();
                    updateTask.setThirdTaskId(taskid);
                    // 根据任务id获取任务结果
                    return checkJobStateAndSpin(taskid, token);
                })
                // 处理结果
                .handle(((result, e) -> {
                    // 发生异常
                    if (e != null) {
                        handleException(e,proofreadingTask);
                    }
                    // 执行失败
                    if (!result.getSuccess()) {
                        proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), JSON.toJSONString(result),
                                result.getMsg());
                    // 执行成功
                    }else {
                        PPMProofreadResultTaskVo ppmProofreadResultTaskVo = (PPMProofreadResultTaskVo)result.getData();
                        updateTask.setResponseInfo(JSON.toJSONString(ppmProofreadResultTaskVo));
                        List<FileResource> fileResources = new ArrayList<>();
                        if (StringUtils.isNotBlank(ppmProofreadResultTaskVo.getFileUrl())){
                            FileResource fileResource = new FileResource();
                            fileResource.setUrl(ppmProofreadResultTaskVo.getFileUrl());
                            fileResource.setFileName(record.getOriginalFileName());
                            fileResource.setFileType(FileResourceFileTypeEnum.MARK_FILE.getCode());
                            fileResources.add(fileResource);
                        }
                        proofreadingTaskService.uploadFileProofreadingTask(fileResources,updateTask,record.getOriginalFileName(),"方案一");
                    }
                    return result;
                }));
        return AjaxResult.success("发起凤凰文件审校成功，请等待处理结果....");
    }


    @Override
    public AjaxResult flashToken() {
        AjaxResult ppmTokenResult = ppmProofreadingUtil.getPPMToken();
        if (!ppmTokenResult.getSuccess()){
            log.error("凤凰审校调用接口获取token失败，原因：{}",ppmTokenResult.getMsg());
            return AjaxResult.fail("凤凰审校调用接口获取token失败，原因："+ ppmTokenResult.getMsg());
        }
        CreateTokenVo data = (CreateTokenVo)ppmTokenResult.getData();
        if (Objects.isNull(data) || StringUtils.isBlank(data.getToken())){
            log.error("凤凰审校获取token接口返回成功，但返回数据为空，返回体：{}",JSON.toJSONString(ppmTokenResult));
            return AjaxResult.fail("凤凰审校获取token接口返回成功，但返回数据为空，返回体：{}"+JSON.toJSONString(ppmTokenResult));
        }
        redisComponent.set(ProofreadConstants.PPM_REDIS_TOKEN_KEY,data.getToken(),ProofreadConstants.PPM_REDIS_TOKEN_KEY_EXPIRE_TIME);

        return AjaxResult.success(data.getToken());
    }

    /**
     * 构建审校任务
     * @param ppmSetting
     * @param formData
     * @return
     */
    private ProofreadingTaskBo buildProofreadingTask(PPMSetting ppmSetting, Map<String,String> formData){
        ProofreadingTaskBo proofreadingTaskBo = new ProofreadingTaskBo();
        proofreadingTaskBo.setProofreadingRecordId(ppmSetting.getRecordId());
        proofreadingTaskBo.setSourceType(ProofreadingTaskSourceType.PPM.getCode());
        proofreadingTaskBo.setTaskType(ProofreadingTaskType.WORDS_CHECK.getCode());
        proofreadingTaskBo.setTaskState(ProofreadingTaskState.HANDLER_ING.getCode());
        proofreadingTaskBo.setSettingOptionInfo(JSON.toJSONString(ppmSetting.getOption()));
        proofreadingTaskBo.setRequestInfo(JSON.toJSONString(formData));
        proofreadingTaskBo.setSubmitTime(new Date());
        return proofreadingTaskBo;
    }

    /**
     * 根据设置对象获取功能选项
     * @param option
     * @return
     */
    private String buildPPMFunctions(PPMSettingOption option){
        List<Integer> functionList = new ArrayList<>();
        // 全面模式，如果是精准模式传null。
        if (StringUtils.isNotBlank(option.getMode())&& ProofreadModeSettingEnum.ALL_MODE.getCode().equals(option.getMode())){
            functionList.add(PPMProofreadSettingEnum.COMPREHENSIVE_MODE.getCode());
        }
        // 可选项
        Map<String, Integer> selectOptionMap = option.getSelectOptionMap();
        List<String> chooseList = new ArrayList<>();
        // 筛选出已选项
        for (Map.Entry<String, Integer> entry : selectOptionMap.entrySet()){
            if (entry.getValue().equals(ProofreadingOptionType.CHOOSE.getCode())){
                chooseList.add(entry.getKey());
            }
        }
        // 通过key找到对应值
        for (String key : chooseList){
            Integer code = PPMProofreadSettingEnum.getCode(key);
            if (Objects.nonNull(code)){
                functionList.add(code);
            }
        }

        return JSON.toJSONString(functionList);

    }

    /**
     * 根据任务id获取任务结果，循环调用，最大调用次数20，每次间隔10秒。
     * @param jobId
     * @param token
     * @return
     */
    private AjaxResult checkJobStateAndSpin(String jobId,String token) {
        int attemptCount = 0;
        boolean isCompleted = Boolean.FALSE;
        AjaxResult pPmProofreadResult = null;
        while (!isCompleted && attemptCount < MAX_ATTEMPTS) {
            pPmProofreadResult = ppmProofreadingUtil.getPPmProofreadResult(jobId, token);
            // 失败
            if(!pPmProofreadResult.getSuccess()){
                // -106代表审校任务正在处理中
                if (pPmProofreadResult.getCode().equals(ProofreadConstants.PPM_TASK_HANDLING_CODE)){
                    try {
                        Thread.sleep(SLEEP_INTERVAL_MS); // 自旋等待一段时间
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    attemptCount++;
                }else {
                    return pPmProofreadResult;
                }
            // 执行成功，说明审校任务已经完成，退出循环。
            }else {
                isCompleted = Boolean.TRUE;
            }
        }
        if (!isCompleted){
            return AjaxResult.fail("查询凤凰异步审校任务状态失败，已超过最大重试次数，任务id: "+jobId);
        }
        return pPmProofreadResult;
    }

    /**
     * 处理异常
     * @param e
     * @param proofreadingTask
     * @return
     */
    private String handleException(Throwable e,ProofreadingTaskDto proofreadingTask){
        String msg = null;
        // 发生异常
        Throwable cause = e.getCause();
        if (Objects.nonNull(cause)) {
            msg = cause.getMessage();
        }else {
            msg = e.getMessage();
        }
        log.error("执行{}发生异常，异常信息：{}，异常堆栈",ProofreadingTaskType.getValue(proofreadingTask.getTaskType()),msg,e);
        // 将任务置为失败
        proofreadingTaskService.failProofreadingTask(proofreadingTask.getId(), null,
                msg);

        return msg;
    }


    /**
     * 查询当前审校记录下，正在处理或者已经处理完成的审校任务。
     * @param recordId
     * @return
     */
    private Map<Integer, ProofreadingTaskVo> getPPMRecordTaskMap(Long recordId){
        ProofreadingTaskConditionBo taskConditionBo = new ProofreadingTaskConditionBo();
        taskConditionBo.setProofreadingRecordId(recordId);
        taskConditionBo.setSourceType(ProofreadingTaskSourceType.PPM.getCode());
        List<Integer> list = Arrays.asList(ProofreadingTaskState.HANDLER_ING.getCode(), ProofreadingTaskState.SUCCESS.getCode());
        taskConditionBo.setTaskStates(list);
        List<ProofreadingTaskVo> oldTasks = proofreadingTaskMapper.getTaskByRecordIdExcludeLongColumn(taskConditionBo);
        return oldTasks.stream().collect(Collectors.toMap(ProofreadingTaskVo::getTaskType, x -> x, (x, y) -> x));
    }

}
