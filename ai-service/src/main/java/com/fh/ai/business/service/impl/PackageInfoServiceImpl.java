package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.packageInfo.PackageInfoBo;
import com.fh.ai.business.entity.bo.packageInfo.PackageInfoConditionBo;
import com.fh.ai.business.entity.dto.packageInfo.PackageInfoDto;
import com.fh.ai.business.entity.vo.packageInfo.PackageInfoVo;
import com.fh.ai.business.mapper.PackageInfoMapper;
import com.fh.ai.business.service.IPackageInfoService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

/**
 * 套餐表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-23 09:28:13
 */
@Service
public class PackageInfoServiceImpl extends ServiceImpl<PackageInfoMapper, PackageInfoDto> implements IPackageInfoService {

	@Resource
	private PackageInfoMapper packageInfoMapper;
	
    @Override
	public List<PackageInfoVo> getPPackageInfoListByCondition(PackageInfoConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return packageInfoMapper.getPackageInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addPPackageInfo(PackageInfoBo packageInfoBo) {
		PackageInfoDto packageInfo = new PackageInfoDto();
		BeanUtils.copyProperties(packageInfoBo, packageInfo);
		packageInfo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(packageInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePPackageInfo(PackageInfoBo packageInfoBo) {
		PackageInfoDto packageInfo = new PackageInfoDto();
		BeanUtils.copyProperties(packageInfoBo, packageInfo);
		if(updateById(packageInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PackageInfoVo getPPackageInfoByCondition(PackageInfoConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return packageInfoMapper.getPackageInfoByCondition(condition);
	}

}