package com.fh.ai.business.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.personalFile.PersonalFileBo;
import com.fh.ai.business.entity.bo.personalFile.PersonalFileConditionBo;
import com.fh.ai.business.entity.dto.personalFile.PersonalFileDto;
import com.fh.ai.business.entity.vo.personalFile.PersonalFileVo;
import com.fh.ai.business.mapper.PersonalFileMapper;
import com.fh.ai.business.service.IPersonalFileService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;

/**
 * 用户上传的个人文件表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14 10:00:00
 */
@Service
public class PersonalFileServiceImpl extends ServiceImpl<PersonalFileMapper, PersonalFileDto>
    implements IPersonalFileService {

    @Resource
    private PersonalFileMapper personalFileMapper;

    @Override
    public Map<String, Object> getPersonalFileListByConditionAndPage(PersonalFileConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        List<PersonalFileVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = personalFileMapper.getPersonalFileListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit());
            List<PersonalFileVo> personalFileVos = personalFileMapper.getPersonalFileListByCondition(condition);
            PageInfo<PersonalFileVo> pageInfo = new PageInfo<>(personalFileVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public List<PersonalFileVo> getPersonalFileListByCondition(PersonalFileConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return personalFileMapper.getPersonalFileListByCondition(condition);
    }

    @Override
    public PersonalFileVo getPersonalFileByCondition(PersonalFileConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return personalFileMapper.getPersonalFileByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult addPersonalFile(PersonalFileBo personalFileBo) {
        PersonalFileDto personalFile = new PersonalFileDto();
        BeanUtils.copyProperties(personalFileBo, personalFile);
        personalFile.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(personalFile)) {
            return AjaxResult.success("新增成功");
        } else {
            return AjaxResult.fail("新增失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AjaxResult updatePersonalFile(PersonalFileBo personalFileBo) {
        PersonalFileDto personalFile = new PersonalFileDto();
        BeanUtils.copyProperties(personalFileBo, personalFile);
        if (updateById(personalFile)) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.fail("修改失败");
        }
    }

    @Override
    public AjaxResult deletePersonalFile(Long id, String userOid) {
        // 逻辑删除
        boolean result = lambdaUpdate()
            .eq(PersonalFileDto::getId, id)
            .eq(PersonalFileDto::getUserOid, userOid)
            .set(PersonalFileDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
            .set(PersonalFileDto::getUpdateBy, userOid)
            .set(PersonalFileDto::getUpdateTime, new Date())
            .update();
        if (result) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.fail("删除失败");
        }
    }

    @Override
    public AjaxResult batchDeletePersonalFile(List<Long> ids, String userOid) {
        if (CollectionUtils.isEmpty(ids)) {
            return AjaxResult.fail("ID列表不能为空");
        }
        // 批量逻辑删除
        boolean result = lambdaUpdate()
            .in(PersonalFileDto::getId, ids)
            .eq(PersonalFileDto::getUserOid, userOid)
            .set(PersonalFileDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
            .set(PersonalFileDto::getUpdateBy, userOid)
            .set(PersonalFileDto::getUpdateTime, new Date())
            .update();
        if (result) {
            return AjaxResult.success("批量删除成功");
        } else {
            return AjaxResult.fail("批量删除失败");
        }
    }

}
