package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.pictureDownloadHistory.PictureDownloadHistoryBo;
import com.fh.ai.business.entity.dto.pictureDownloadHistory.PictureDownloadHistoryDto;
import com.fh.ai.business.mapper.PictureDownloadHistoryMapper;
import com.fh.ai.business.service.IPictureDownloadHistoryService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  09:53
 */
@Service
public class PictureDownloadHistoryServiceImpl extends ServiceImpl<PictureDownloadHistoryMapper, PictureDownloadHistoryDto> implements IPictureDownloadHistoryService {

    @Override
    public AjaxResult addPictureDownloadHistory(PictureDownloadHistoryBo pictureDownloadHistoryBo) {
        PictureDownloadHistoryDto entity = new PictureDownloadHistoryDto();
        BeanUtils.copyProperties(pictureDownloadHistoryBo, entity);
        entity.setCreateTime(new Date());
        entity.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }
}
