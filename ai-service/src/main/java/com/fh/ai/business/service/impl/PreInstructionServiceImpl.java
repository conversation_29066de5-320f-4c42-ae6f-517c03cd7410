package com.fh.ai.business.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.preInstruction.PreInstructionBo;
import com.fh.ai.business.entity.bo.preInstruction.PreInstructionConditionBo;
import com.fh.ai.business.entity.dto.preInstruction.PreInstructionDto;
import com.fh.ai.business.entity.vo.preInstruction.PreInstructionVo;
import com.fh.ai.business.mapper.PreInstructionMapper;
import com.fh.ai.business.service.IPreInstructionService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

/**
 * 预置指令接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-07 16:45:32
 */
@Service
public class PreInstructionServiceImpl extends ServiceImpl<PreInstructionMapper, PreInstructionDto> implements IPreInstructionService {

	@Resource
	private PreInstructionMapper preInstructionMapper;
	
    @Override
	public List<PreInstructionVo> getPreInstructionListByCondition(PreInstructionConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return preInstructionMapper.getPreInstructionListByCondition(condition);
	}

	@Override
	public AjaxResult addPreInstruction(PreInstructionBo preInstructionBo) {
		PreInstructionDto preInstruction = new PreInstructionDto();
		BeanUtils.copyProperties(preInstructionBo, preInstruction);
		preInstruction.setCreateTime(new Date());
		preInstruction.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(preInstruction)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePreInstruction(PreInstructionBo preInstructionBo) {
		PreInstructionConditionBo conditionBo = new PreInstructionConditionBo();
		conditionBo.setUuid(preInstructionBo.getUuid());
		conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		PreInstructionVo preInstructionVo = preInstructionMapper.getPreInstructionByCondition(conditionBo);
		if (preInstructionVo == null) {
			return AjaxResult.fail("未找到相应数据");
		}
		PreInstructionDto entity = new PreInstructionDto();
		BeanUtils.copyProperties(preInstructionBo, entity);
		entity.setId(preInstructionVo.getId());
		entity.setUpdateTime(new Date());
		if (updateById(entity)) {
			return AjaxResult.success();
		}
		return AjaxResult.fail();
	}

	@Override
	public PreInstructionVo getPreInstructionByCondition(PreInstructionConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		PreInstructionVo vo = preInstructionMapper.getPreInstructionByCondition(condition);
		return vo;
	}

	@Override
	public Map<String, Object> getPreInstructListByConditionPage(PreInstructionConditionBo conditionBo) {
		conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		Map<String, Object> map = new HashMap<>(4);
		List<PreInstructionVo> list = Lists.newArrayList();
		long count = 0L;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			list = preInstructionMapper.getPreInstructionListByCondition(conditionBo);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<PreInstructionVo> preInstructionVos = preInstructionMapper.getPreInstructionListByCondition(conditionBo);
			PageInfo<PreInstructionVo> pageInfo = new PageInfo<>(preInstructionVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

	@Override
	public AjaxResult deletePreInstruct(PreInstructionBo preInstructionBo) {
		PreInstructionConditionBo conditionBo = new PreInstructionConditionBo();
		conditionBo.setUuid(preInstructionBo.getUuid());
		conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		PreInstructionVo preInstructionVo = preInstructionMapper.getPreInstructionByCondition(conditionBo);
		if (preInstructionVo == null) {
			return AjaxResult.fail("未找到相应数据");
		}
		PreInstructionDto entity = new PreInstructionDto();
		entity.setId(preInstructionVo.getId());
		entity.setUpdateBy(preInstructionBo.getUpdateBy());
		entity.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
		entity.setUpdateTime(new Date());
		if (updateById(entity)) {
			return AjaxResult.success();
		}
		return AjaxResult.fail();
	}
}