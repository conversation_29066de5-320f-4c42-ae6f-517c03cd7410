package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.prizeRedeemcode.PrizeRedeemcodeBo;
import com.fh.ai.business.entity.bo.prizeRedeemcode.PrizeRedeemcodeConditionBo;
import com.fh.ai.business.entity.dto.prizeRedeemcode.PrizeRedeemcodeDto;
import com.fh.ai.business.entity.vo.prizeRedeemcode.PrizeRedeemcodeVo;
import com.fh.ai.business.mapper.PrizeRedeemcodeMapper;
import com.fh.ai.business.service.IPrizeRedeemcodeService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 奖品兑换码表接口实现类
 *
 * <AUTHOR>
 * @date 2024-05-13 15:04:13
 */
@Service
public class PrizeRedeemcodeServiceImpl extends ServiceImpl<PrizeRedeemcodeMapper, PrizeRedeemcodeDto> implements IPrizeRedeemcodeService {

    @Resource
    private PrizeRedeemcodeMapper prizeRedeemcodeMapper;

    @Override
    public Map<String, Object> getPrizeRedeemcodeListByCondition(PrizeRedeemcodeConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<PrizeRedeemcodeVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = prizeRedeemcodeMapper.getPrizeRedeemcodeListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<PrizeRedeemcodeVo> prizeRedeemcodeVos = prizeRedeemcodeMapper.getPrizeRedeemcodeListByCondition(conditionBo);
            PageInfo<PrizeRedeemcodeVo> pageInfo = new PageInfo<>(prizeRedeemcodeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addPrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo) {
        PrizeRedeemcodeDto prizeRedeemcode = new PrizeRedeemcodeDto();
        BeanUtils.copyProperties(prizeRedeemcodeBo, prizeRedeemcode);

        prizeRedeemcode.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        prizeRedeemcode.setCreateTime(new Date());
        save(prizeRedeemcode);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updatePrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo) {
        PrizeRedeemcodeDto prizeRedeemcode = new PrizeRedeemcodeDto();
        BeanUtils.copyProperties(prizeRedeemcodeBo, prizeRedeemcode);

        prizeRedeemcode.setUpdateTime(new Date());
        updateById(prizeRedeemcode);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<PrizeRedeemcodeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeRedeemcodeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeRedeemcodeDto::getId, id);

        PrizeRedeemcodeDto prizeRedeemcode = getOne(lqw);
        if (null == prizeRedeemcode) {
            return AjaxResult.fail("奖品兑换码表数据不存在");
        }

        PrizeRedeemcodeVo prizeRedeemcodeVo = new PrizeRedeemcodeVo();
        BeanUtils.copyProperties(prizeRedeemcode, prizeRedeemcodeVo);

        return AjaxResult.success(prizeRedeemcodeVo);
    }

    @Override
    public AjaxResult updateState(PrizeRedeemcodeBo prizeRedeemcodeBo) {
        // 更新状态
        LambdaQueryWrapper<PrizeRedeemcodeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeRedeemcodeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeRedeemcodeDto::getId, prizeRedeemcodeBo.getId());

        PrizeRedeemcodeDto prizeRedeemcode = getOne(lqw);
        if (null == prizeRedeemcode) {
            return AjaxResult.fail("奖品兑换码表数据不存在");
        }

        PrizeRedeemcodeDto dto = new PrizeRedeemcodeDto();
        dto.setId(prizeRedeemcode.getId());
        dto.setState(prizeRedeemcodeBo.getState());
        dto.setUpdateBy(prizeRedeemcodeBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deletePrizeRedeemcode(PrizeRedeemcodeBo prizeRedeemcodeBo) {
        // 删除信息
        LambdaQueryWrapper<PrizeRedeemcodeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeRedeemcodeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeRedeemcodeDto::getId, prizeRedeemcodeBo.getId());

        PrizeRedeemcodeDto prizeRedeemcode = getOne(lqw);
        if (null == prizeRedeemcode) {
            return AjaxResult.fail("奖品兑换码表数据不存在");
        }

        PrizeRedeemcodeDto dto = new PrizeRedeemcodeDto();
        dto.setId(prizeRedeemcode.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(prizeRedeemcodeBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}