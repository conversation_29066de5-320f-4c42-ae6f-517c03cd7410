package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.prize.PrizeBo;
import com.fh.ai.business.entity.bo.prize.PrizeConditionBo;
import com.fh.ai.business.entity.dto.prize.PrizeDto;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.vo.prize.PrizeVo;
import com.fh.ai.business.mapper.PrizeMapper;
import com.fh.ai.business.mapper.UserPrizeMapper;
import com.fh.ai.business.service.IPrizeService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.dingding.DingDingUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.PrizeStateEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 奖品表接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-20 17:21:24
 */
@Slf4j
@Service
public class PrizeServiceImpl extends ServiceImpl<PrizeMapper, PrizeDto> implements IPrizeService {

    @Resource
    private PrizeMapper prizeMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private DingDingUtil dingDingUtil;

    @Resource
    private UserPrizeMapper userPrizeMapper;

    @Override
    public Map<String, Object> getPrizeListByCondition(PrizeConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<PrizeVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = prizeMapper.getPrizeListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<PrizeVo> prizeVos = prizeMapper.getPrizeListByCondition(conditionBo);
            PageInfo<PrizeVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addPrize(PrizeBo prizeBo) {
        if(prizeBo.getRedeemcodeType() != null && prizeBo.getRedeemcodeType().equals(2)){
            if(StringUtils.isEmpty(prizeBo.getCodeName())){
                return AjaxResult.fail("兑换码名称参数错误");
            }
            List<Map> code = prizeMapper.code();
            if(CollUtil.isEmpty(code)){
                return AjaxResult.fail("兑换码不足");
            }
            Map codeMap = code.stream().filter(o -> o.get("codeName").toString().equals(prizeBo.getCodeName())).findFirst().get();

            if(codeMap == null){
                return AjaxResult.fail("兑换码不足");
            }

            Long canExchange = new Long(codeMap.get("count").toString()) - new Long(codeMap.get("exchanged").toString());
            if(prizeBo.getSupply().compareTo(canExchange)>0){
                return AjaxResult.fail("兑换码不足");
            }
        }

        PrizeDto prize = new PrizeDto();
        BeanUtils.copyProperties(prizeBo, prize);

        prize.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        prize.setCreateTime(new Date());
        save(prize);
        if(StringUtils.isNotEmpty(prize.getCodeName())){
            prizeMapper.updateCode(prize.getId(),prize.getCodeName(),prize.getSupply());
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult updatePrize(PrizeBo prizeBo) {
        if(prizeBo.getRedeemcodeType() != null && prizeBo.getRedeemcodeType().equals(2)){
            if(StringUtils.isEmpty(prizeBo.getCodeName())){
                return AjaxResult.fail("兑换码名称参数错误");
            }
            List<Map> code = prizeMapper.code();
            if(CollUtil.isEmpty(code)){
                return AjaxResult.fail("兑换码不足");
            }
            Map codeMap = code.stream().filter(o -> o.get("codeName").toString().equals(prizeBo.getCodeName())).findFirst().get();

            if(codeMap == null){
                return AjaxResult.fail("兑换码不足");
            }

            Long canExchange = new Long(codeMap.get("count").toString()) - new Long(codeMap.get("exchanged").toString());
            if(prizeBo.getSupply().compareTo(canExchange)>0){
                return AjaxResult.fail("兑换码不足");
            }
            PrizeDto old = getById(prizeBo.getId());
            if(old.getRedeemcodeType().equals(99) || !old.getSupply().equals(prizeBo.getSupply())){
                prizeMapper.updateCode(prizeBo.getId(),prizeBo.getCodeName(),prizeBo.getSupply());
            }
        }

        if(prizeBo.getSort() != null){
            if(prizeBo.getSort() > 0){
                update(null,new LambdaUpdateWrapper<PrizeDto>()
                        .eq(PrizeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(PrizeDto::getSort,prizeBo.getSort())
                        .set(PrizeDto::getSort,null)
                );
            }else{
                update(null,new LambdaUpdateWrapper<PrizeDto>()
                        .eq(PrizeDto::getId,prizeBo.getId())
                        .set(PrizeDto::getSort,null)
                );
                return AjaxResult.success();
            }
        }
        PrizeDto prize = new PrizeDto();
        BeanUtils.copyProperties(prizeBo, prize);

        prize.setUpdateTime(new Date());
        updateById(prize);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<PrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeDto::getId, id);

        PrizeDto prize = getOne(lqw);
        if (null == prize) {
            return AjaxResult.fail("奖品表数据不存在");
        }

        PrizeVo prizeVo = new PrizeVo();
        BeanUtils.copyProperties(prize, prizeVo);

        return AjaxResult.success(prizeVo);
    }

    @Override
    public AjaxResult updateState(PrizeBo prizeBo) {
         // 更新状态
        LambdaQueryWrapper<PrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeDto::getId, prizeBo.getId());

        PrizeDto prize = getOne(lqw);
        if (null == prize) {
            return AjaxResult.fail("奖品表数据不存在");
        }

        PrizeDto dto = new PrizeDto();
        dto.setId(prize.getId());
        dto.setState(prizeBo.getState());
        dto.setUpdateBy(prizeBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deletePrize(PrizeBo prizeBo) {
        List<UserPrizeDto> userPrizeDtos = userPrizeMapper.selectList(new LambdaQueryWrapper<UserPrizeDto>()
                .eq(UserPrizeDto::getPrizeId, prizeBo.getId())
        );
        if (CollUtil.isNotEmpty(userPrizeDtos)) {
            return AjaxResult.fail("存在用户已经兑换，无法删除");
        }
        // 删除信息
        LambdaQueryWrapper<PrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeDto::getId, prizeBo.getId());

        PrizeDto prize = getOne(lqw);
        if (null == prize) {
            return AjaxResult.fail("奖品表数据不存在");
        }

        PrizeDto dto = new PrizeDto();
        dto.setId(prize.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(prizeBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    /**
     * 扣减库存
     *
     * @param id
     * @return
     */
    @Override
    public boolean reduceStock(Long id) {
        log.info("扣减库存开始......");
        RLock lock = redissonClient.getLock("lock:prize:" + id);
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");
            //接口调用的幂等性：无论接口被调用多少次，以下业务执行一次
            //获取库存数量
            PrizeDto prizeDto = prizeMapper.selectById(id);
            log.info("当前库存量:" + prizeDto.getSupply());
            //扣减库存
            if (null == prizeDto.getSupply() || prizeDto.getSupply().compareTo(Constants.ZERO) <= 0) {
                log.info("扣减库存失败,无库存可用......");
                return false;
            }

            //默认扣减1
            prizeDto.setSupply(prizeDto.getSupply() - 1);
            prizeMapper.updateById(prizeDto);
        } catch (Exception e) {
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }

        return true;
    }

    @Override
    public void prizeSupplyRemind() {
        // 更新状态
        LambdaQueryWrapper<PrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PrizeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(PrizeDto::getState, PrizeStateEnum.ON_SHELF.getCode());

        List<PrizeDto> prizeDtos = prizeMapper.selectList(lqw);
        if (CollectionUtil.isNotEmpty(prizeDtos)) {
            for (PrizeDto prizeDto : prizeDtos) {
                if (null == prizeDto.getRemindValue() || null == prizeDto.getSupply()) {
                    continue;
                }

                if (prizeDto.getSupply().compareTo(prizeDto.getRemindValue()) < 0) {
                    dingDingUtil.sendRobotMsg(prizeDto.getName() + "库存不足");
                }
            }
        }

    }

    @Override
    public AjaxResult code() {
        List<Map> code = prizeMapper.code();
        List<Map> res = code.stream().filter(o -> !o.get("count").toString().equals("0")).collect(Collectors.toList());
        return AjaxResult.success(res);
    }
}