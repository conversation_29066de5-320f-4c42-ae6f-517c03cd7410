package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.fh.ai.business.entity.bo.proofreading.ProofreadSettingBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadSettingConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadSettingDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo;
import com.fh.ai.business.mapper.ProofreadSettingMapper;
import com.fh.ai.business.service.IProofreadSettingService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ProofreadSettingEnum;
import com.fh.ai.common.proofreading.fh.req.PPMSettingOption;
import com.fh.ai.common.proofreading.fz.req.FZSettingOption;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

/**
 * 用户审校设置表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-20 14:39:57
 */
@Service
public class ProofreadSettingServiceImpl extends ServiceImpl<ProofreadSettingMapper, ProofreadSettingDto>
    implements IProofreadSettingService {

    @Resource
    private ProofreadSettingMapper proofreadSettingMapper;

    @Override
    public List<ProofreadSettingVo> getProofreadSettingListByCondition(ProofreadSettingConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return proofreadSettingMapper.getProofreadSettingListByCondition(condition);
    }

    /**
     * 添加审校设置
     * 
     * @param proofreadSettingBo
     * @return
     */
    @Override
    public AjaxResult addProofreadSetting(ProofreadSettingBo proofreadSettingBo) {
        ProofreadSettingConditionBo condition = new ProofreadSettingConditionBo();
        condition.setSettingType(proofreadSettingBo.getSettingType());
        condition.setUserOid(proofreadSettingBo.getUserOid());
        condition.setOrganizationId(proofreadSettingBo.getOrganizationId());
        ProofreadSettingVo userProofreadSetting = proofreadSettingMapper.getUserProofreadSetting(condition);
        // 说明该用户的某类型配置已经存在，做更新操作
        if (Objects.nonNull(userProofreadSetting)){
            proofreadSettingBo.setId(userProofreadSetting.getId());
            return updateProofreadSetting(proofreadSettingBo);
        }
        // 做添加操作
        ProofreadSettingDto proofreadSetting = new ProofreadSettingDto();
        BeanUtils.copyProperties(proofreadSettingBo, proofreadSetting);
        proofreadSetting.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        proofreadSetting.setCreateTime(new Date());
        proofreadSetting.setUpdateTime(new Date());
        String params = Objects.isNull(proofreadSettingBo.getPpmSettingOption())
            ? JSON.toJSONString(proofreadSettingBo.getFzSettingOption())
            : JSON.toJSONString(proofreadSettingBo.getPpmSettingOption());
        proofreadSetting.setSettingParams(params);
        if (save(proofreadSetting)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 更新审校设置
     * 
     * @param proofreadSettingBo
     * @return
     */
    @Override
    public AjaxResult updateProofreadSetting(ProofreadSettingBo proofreadSettingBo) {
        ProofreadSettingDto proofreadSettingDto = proofreadSettingMapper.selectById(proofreadSettingBo.getId());
        if (Objects.isNull(proofreadSettingDto)){
            return AjaxResult.fail("未找到该设置");
        }
        ProofreadSettingDto proofreadSetting = new ProofreadSettingDto();
        BeanUtils.copyProperties(proofreadSettingBo, proofreadSetting);
        String params = Objects.isNull(proofreadSettingBo.getPpmSettingOption())
                ? JSON.toJSONString(proofreadSettingBo.getFzSettingOption())
                : JSON.toJSONString(proofreadSettingBo.getPpmSettingOption());
        proofreadSetting.setSettingParams(params);
        proofreadSetting.setUpdateTime(new Date());
        if (updateById(proofreadSetting)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 查询审校设置
     * 
     * @param condition
     * @return
     */
    @Override
    public ProofreadSettingVo getProofreadSettingByCondition(ProofreadSettingConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        ProofreadSettingVo vo = proofreadSettingMapper.getUserProofreadSetting(condition);
        if (Objects.isNull(vo)) {
            return null;
        }
        String settingParams = vo.getSettingParams();
        Integer settingType = vo.getSettingType();
        if (StringUtils.isNotBlank(settingParams)) {
            if (ProofreadSettingEnum.PPM_TXT_SETTING.getCode().equals(settingType)
                || ProofreadSettingEnum.PPM_FILE_SETTING.getCode().equals(settingType)) {
                vo.setPpmSettingOption(JSON.parseObject(settingParams, PPMSettingOption.class));
            } else if (ProofreadSettingEnum.FZ_TXT_SETTING.getCode().equals(settingType)
                || ProofreadSettingEnum.FZ_FILE_SETTING_ACCURATE.getCode().equals(settingType)
                    ||ProofreadSettingEnum.FZ_FILE_SETTING_BALANCE.getCode().equals(settingType)
                    ||ProofreadSettingEnum.FZ_FILE_SETTING_ALL.getCode().equals(settingType)) {
                vo.setFzSettingOption(JSON.parseObject(settingParams, FZSettingOption.class));
            }
        }
        return vo;
    }

}