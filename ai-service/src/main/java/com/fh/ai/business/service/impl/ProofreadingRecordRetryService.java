package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fh.ai.business.entity.bo.proofreading.ProofreadRetryBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingRecordDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import com.fh.ai.business.mapper.ProofreadingRecordMapper;
import com.fh.ai.business.mapper.ProofreadingTaskMapper;
import com.fh.ai.business.service.IFZProofreadService;
import com.fh.ai.business.service.IPPMProofreadService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.proofreading.ProofreadConstants;
import com.fh.ai.common.proofreading.fh.req.PPMSetting;
import com.fh.ai.common.proofreading.fh.req.PPMSettingOption;
import com.fh.ai.common.proofreading.fz.req.FZReviewContentReq;
import com.fh.ai.common.proofreading.fz.req.FZReviewTxtReq;
import com.fh.ai.common.proofreading.fz.req.FZSetting;
import com.fh.ai.common.proofreading.fz.req.FZSettingOption;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname ProofreadingRecordRetryService
 * @Description 审校记录重试服务
 * @Date 2025/2/13 9:02
 * @Created by admin
 */
@Service
public class ProofreadingRecordRetryService {

    /**
     * 审校记录mapper
     */
    @Resource
    private ProofreadingRecordMapper proofreadingRecordMapper;

    /**
     * 审校任务mapper
     */
    @Resource
    private ProofreadingTaskMapper proofreadingTaskMapper;

    /**
     * 凤凰审校相关接口
     */
    @Resource
    private IPPMProofreadService ppmProofreadService;

    /**
     * 方正审校相关接口
     */
    @Resource
    private IFZProofreadService fzProofreadService;

    /**
     * redis
     */
    @Resource
    private RedisComponent redisComponent;

    /**
     * 重新审校
     * @param retryBo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult retryProofread(ProofreadRetryBo retryBo) {
        Long recordId = retryBo.getRecordId();
        String key = ProofreadConstants.RETRY_PROOFREAD+recordId;
        // 分布式锁，key为审校记录主键，过期时间60秒。
        boolean locked = redisComponent.lock(key, 60, 3, 1000);
        if (locked){
            try {

                ProofreadingRecordVo recordVo = proofreadingRecordMapper.selectByRecordId(recordId);
                if (Objects.isNull(recordVo)) {
                    return AjaxResult.fail("重新审校时未找到该审校记录");
                }
                if (!ProofreadingRecordStateEnum.FAILED.getCode().equals(recordVo.getRecordState())) {
                    return AjaxResult.fail(
                            "该审校记录状态为：" + ProofreadingRecordStateEnum.getValue(recordVo.getRecordState()) + "，只有失败的审校记录才能重新审校");
                }
                ProofreadingTaskConditionBo conditionBo = new ProofreadingTaskConditionBo();
                conditionBo.setProofreadingRecordId(recordId);
                conditionBo.setTaskStates(Collections.singletonList(ProofreadingTaskState.FAIL.getCode()));
                // 获取该审校记录下所有失败的审校任务
                List<ProofreadingTaskVo> failedTasks = proofreadingTaskMapper.getTaskByRecordId(conditionBo);
                if (CollectionUtils.isEmpty(failedTasks)) {
                    return AjaxResult.fail("该审校记录下无失败的审校任务，无需重新审校");
                }
                // 先将审校记录更新为审校中，重新设置提交时间
                ProofreadingRecordDto updateRecord = new ProofreadingRecordDto();
                updateRecord.setId(recordId);
                updateRecord.setRecordState(ProofreadingRecordStateEnum.HANDLING.getCode());
                updateRecord.setSubmitTime(new Date());
                updateRecord.setUpdateTime(new Date());
                proofreadingRecordMapper.updateById(updateRecord);
                // 软删除原来的失败审校记录
                List<Long> failedTaskIdList = failedTasks.stream().map(ProofreadingTaskVo::getId).collect(Collectors.toList());
                proofreadingTaskMapper.updateTaskIsDeleteBatch(failedTaskIdList);

                // 将失败的审校任务按照审校源进行分组：审校源：凤凰、方正
                Map<Integer, List<ProofreadingTaskVo>> failedTaskMap =
                        failedTasks.stream().collect(Collectors.groupingBy(ProofreadingTaskVo::getSourceType));
                List<ProofreadingTaskVo> ppmFailedTasks = failedTaskMap.get(ProofreadingTaskSourceType.PPM.getCode());
                List<ProofreadingTaskVo> fzFailedTasks = failedTaskMap.get(ProofreadingTaskSourceType.FZ.getCode());

                // 在线审校重试
                if (ProofreadingRecordType.ONLINE_SYNC.getCode().equals(recordVo.getRecordType())) {
                    // 重试在线凤凰审校
                    if (CollectionUtils.isNotEmpty(ppmFailedTasks)) {
                        for (ProofreadingTaskVo ppmOnlineTask : ppmFailedTasks) {
                            String requestInfo = ppmOnlineTask.getRequestInfo();
                            String settingOptionInfo = ppmOnlineTask.getSettingOptionInfo();
                            if (StringUtils.isNotBlank(requestInfo) && StringUtils.isNotBlank(settingOptionInfo)) {
                                Map<String, String> requestMap =
                                        JSON.parseObject(requestInfo, new TypeReference<Map<String, String>>() {});
                                String content = requestMap.get(ProofreadConstants.PPM_CONTENT);
                                PPMSettingOption ppmSettingOption = JSON.parseObject(settingOptionInfo, PPMSettingOption.class);
                                PPMSetting ppmSetting = new PPMSetting();
                                ppmSetting.setType(ProofreadEnum.PPM_TXT_PROOFREAD.getCode());
                                ppmSetting.setRecordId(recordId);
                                ppmSetting.setContent(content);
                                ppmSetting.setOption(ppmSettingOption);
                                // 重试凤凰在线文本审校。
                                ppmProofreadService.createPPMOnlineSyncTask(ppmSetting);
                            } else {
                                // 抛异常，确保事务回滚
                                throw new BusinessException("重试凤凰在线审校，未找到请求入参或者审校参数项，审校任务id：" + ppmOnlineTask.getId());
                            }
                        }
                    }
                    // 重试 方正在线审校、方正在线重要讲话审校
                    if (CollectionUtils.isNotEmpty(fzFailedTasks)) {
                        for (ProofreadingTaskVo fzOlineTask : fzFailedTasks) {
                            FZSetting fzSetting = new FZSetting();
                            fzSetting.setType(ProofreadEnum.FZ_TXT_PROOFREAD.getCode());
                            fzSetting.setRecordId(recordId);
                            String requestInfo = fzOlineTask.getRequestInfo();
                            if (StringUtils.isNotBlank(requestInfo)) {
                                // 在线文本审校
                                if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(fzOlineTask.getTaskType())) {
                                    String settingOptionInfo = fzOlineTask.getSettingOptionInfo();
                                    fzSetting.setOption(JSON.parseObject(settingOptionInfo, FZSettingOption.class));
                                    FZReviewTxtReq fzReviewTxtReq = JSON.parseObject(requestInfo, FZReviewTxtReq.class);
                                    String content = fzReviewTxtReq.getText();
                                    fzSetting.setContent(content);
                                    // 重试方正在线文本审校
                                    fzProofreadService.createFZOnlineSyncTask(fzSetting);
                                } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(fzOlineTask.getTaskType())) {
                                    FZReviewContentReq fzReviewContentReq =
                                            JSON.parseObject(requestInfo, FZReviewContentReq.class);
                                    String content = fzReviewContentReq.getText();
                                    fzSetting.setContent(content);
                                    // 重试方正在线内容审校
                                    fzProofreadService.createFZContentSyncTask(fzSetting);
                                }
                            } else {
                                // 抛异常，确保事务回滚
                                throw new BusinessException("重试方正在线审校，未找到请求入参，审校任务id：" + fzOlineTask.getId());
                            }
                        }
                    }
                    // 文档审校重试
                } else if (ProofreadingRecordType.UPLOAD_FILE.getCode().equals(recordVo.getRecordType())) {
                    // 重试凤凰文档审校
                    if (CollectionUtils.isNotEmpty(ppmFailedTasks)) {
                        for (ProofreadingTaskVo ppmFileTask : ppmFailedTasks) {
                            String settingOptionInfo = ppmFileTask.getSettingOptionInfo();
                            if (StringUtils.isBlank(settingOptionInfo)) {
                                throw new BusinessException("重试凤凰文件审校，未找到审校设置项参数，审校任务id：" + ppmFileTask.getId());
                            }
                            PPMSettingOption ppmSettingOption = JSON.parseObject(settingOptionInfo, PPMSettingOption.class);
                            PPMSetting ppmSetting = new PPMSetting();
                            ppmSetting.setType(ProofreadEnum.PPM_FILE_PROOFREAD.getCode());
                            ppmSetting.setRecordId(recordId);
                            ppmSetting.setOption(ppmSettingOption);
                            ppmSetting.setFileType(getFileType(recordVo.getOriginalFileName()));
                            // 重试凤凰文件审校
                            ppmProofreadService.createPPMFileTask(ppmSetting);
                        }
                    }
                    // 重试方正文档审校，包括文件审校、内容审校、上下文审校、参考文献审校四种任务。
                    if (CollectionUtils.isNotEmpty(fzFailedTasks)) {
                        // 方正文档审校，默认所有需要执行的任务类型
                        List<Integer> fzFileTypes = new ArrayList<>(Arrays.asList(ProofreadingTaskType.WORDS_CHECK.getCode(),
                                ProofreadingTaskType.CONTENT_CHECK.getCode(), ProofreadingTaskType.DUPLICATE_CHECK.getCode(),
                                ProofreadingTaskType.REFERENCES.getCode()));
                        FZSetting fzSetting = new FZSetting();
                        fzSetting.setType(ProofreadEnum.FZ_FILE_PROOFREAD.getCode());
                        fzSetting.setRecordId(recordId);
                        ProofreadingTaskVo proofreadingTaskVo = fzFailedTasks.get(ConstantsInteger.NUM_0);
                        String settingOptionInfo = proofreadingTaskVo.getSettingOptionInfo();
                        if (StringUtils.isBlank(settingOptionInfo)) {
                            throw new BusinessException("重试方正文件审校，未找到审校设置项参数，审校任务id：" + proofreadingTaskVo.getId());
                        }
                        fzSetting.setOption(JSON.parseObject(settingOptionInfo, FZSettingOption.class));
                        // 根据失败的任务和所有任务，计算执行成功的任务
                        for (ProofreadingTaskVo fzOFileTask : fzFailedTasks) {
                            fzFileTypes.removeIf(x -> fzOFileTask.getTaskType().equals(x));
                        }
                        // 执行成功的任务，不需要重试。将对应任务标记设为false。
                        if (CollectionUtils.isNotEmpty(fzFileTypes)) {
                            for (Integer type : fzFileTypes) {
                                if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(type)) {
                                    fzSetting.setStartFileTaskFlag(Boolean.FALSE);
                                }
                                if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(type)) {
                                    fzSetting.setStartContentTaskFlag(Boolean.FALSE);
                                }
                                if (ProofreadingTaskType.DUPLICATE_CHECK.getCode().equals(type)) {
                                    fzSetting.setStartDocDupTaskFlag(Boolean.FALSE);
                                }
                                if (ProofreadingTaskType.REFERENCES.getCode().equals(type)) {
                                    fzSetting.setStartReferenceTask(Boolean.FALSE);
                                }
                            }
                        }
                        // 重试方正文件审校
                        fzProofreadService.createFZFileTask(fzSetting);
                    }
                }

            }catch (Exception e){
                throw e;
            } finally {
                redisComponent.releaseLock(key);
            }

        }else {
            return AjaxResult.fail("重试中，请勿频繁操作");
        }

        return AjaxResult.success("执行重试成功，请等待结果");
    }

    /**
     * 根据文件类型后缀返回文件类型枚举
     * 
     * @param name
     * @return
     */
    private String getFileType(String name) {
        if (name.endsWith("doc") || name.endsWith("docx")) {
            return FileTypeEnum.WORD.getName();
        } else if (name.endsWith("pdf")) {
            return FileTypeEnum.PDF.getName();
        }
        return FileTypeEnum.WORD.getName();
    }

}
