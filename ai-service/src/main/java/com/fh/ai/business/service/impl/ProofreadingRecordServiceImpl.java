package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingRecordDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.entity.vo.proofreading.*;
import com.fh.ai.business.mapper.ProofreadingRecordMapper;
import com.fh.ai.business.mapper.ProofreadingTaskMapper;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IProofreadingRecordService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.proofreading.excel.*;
import com.fh.ai.common.proofreading.fh.PPMProofreadingUtil;
import com.fh.ai.common.proofreading.fh.vo.PPMJiuCuoDetailVo;
import com.fh.ai.common.proofreading.fh.vo.PPMProofreadResultTaskVo;
import com.fh.ai.common.proofreading.fh.vo.PPMRpcResultVo;
import com.fh.ai.common.proofreading.fz.vo.*;
import com.fh.ai.common.utils.DateUtil;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.vo.SheetData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户审校记录接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:02:37
 */
@Service
@Slf4j
public class ProofreadingRecordServiceImpl extends ServiceImpl<ProofreadingRecordMapper, ProofreadingRecordDto>
    implements IProofreadingRecordService {
    /**
     * 替换审校结果返回的标签字符串
     */
    private static final String REPLACE_STR_REGX = "<(em|dm|sm|cm|orange)>(.*?)</\\1>";
    /**
     * 审校记录提交后超时时间，单位：分钟
     */
    private static final Long RECORD_TIME_OUT = 60L;

    /**
     * 审校记录mapper
     */
    @Resource
    private ProofreadingRecordMapper proofreadingRecordMapper;

    /**
     * 文件服务
     */
    @Resource
    private IAttachmentService attachmentService;
    /**
     * 审校任务mapper
     */
    @Resource
    private ProofreadingTaskMapper proofreadingTaskMapper;

    @Override
    public PageInfo<ProofreadingRecordVo> getProofreadingRecordListByCondition(ProofreadingRecordConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        // 分页查询
        PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
        List<ProofreadingRecordVo> listByCondition = proofreadingRecordMapper.getProofreadingRecordList(condition);
        PageInfo<ProofreadingRecordVo> pageInfo = new PageInfo<>(listByCondition);
        return pageInfo;
    }

    /**
     * 获取审校记录详情
     * @param recordId
     * @return
     */
    @Override
    public AjaxResult getRecordDetail(Long recordId){
        ProofreadingRecordVo recordVo = proofreadingRecordMapper.selectByRecordId(recordId);
        if (Objects.isNull(recordVo)){
            return AjaxResult.fail("审校记录不存在或已被删除");
        }
        return AjaxResult.success(recordVo);
    }

    /**
     * 创建审校记录
     * 
     * @param proofreadingRecordBo
     * @return
     */
    @Override
    public AjaxResult addProofreadingRecord(ProofreadingRecordBo proofreadingRecordBo) {
        ProofreadingRecordDto proofreadingRecord = new ProofreadingRecordDto();
        BeanUtils.copyProperties(proofreadingRecordBo, proofreadingRecord);
        // 文档审校需上传文件
        if (ProofreadingRecordType.UPLOAD_FILE.getCode().equals(proofreadingRecord.getRecordType())) {
            // 根据文件oid获取文件对象
            AttachmentVo attachmentVo = attachmentService.getDetail(proofreadingRecordBo.getFileOid());
            if (Objects.isNull(attachmentVo)) {
                return AjaxResult.fail("未找到文件" + proofreadingRecordBo.getFileOid() + "无法创建文件审校任务");
            }
            proofreadingRecord.setOriginalFileName(attachmentVo.getOriginalName());
            proofreadingRecord.setOriginalFileOid(attachmentVo.getOid());
            proofreadingRecord.setOriginalFileUrl(attachmentVo.getOriginPath());
            proofreadingRecord.setUploadState(attachmentVo.getUploadState());
        }
        // 保存执行的审校任务信息
        proofreadingRecord.setExecutedTaskInfo(JSON.toJSONString(proofreadingRecordBo.getExecutedTaskInfoList()));
        proofreadingRecord.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        proofreadingRecord.setRecordState(ProofreadingRecordStateEnum.HANDLING.getCode());
        proofreadingRecord.setSubmitTime(new Date());
        proofreadingRecord.setCreateTime(new Date());
        proofreadingRecord.setUpdateTime(new Date());
        if (save(proofreadingRecord)) {
            return AjaxResult.success(proofreadingRecord.getId(), "保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateProofreadingRecord(ProofreadingRecordBo proofreadingRecordBo) {
        ProofreadingRecordVo recordVo = proofreadingRecordMapper.selectByRecordId(proofreadingRecordBo.getId());
        if (Objects.isNull(recordVo)) {
            return AjaxResult.fail("审校记录不存在或已删除");
        }
        ProofreadingRecordDto proofreadingRecord = new ProofreadingRecordDto();
        BeanUtils.copyProperties(proofreadingRecordBo, proofreadingRecord);
        proofreadingRecord.setUpdateTime(new Date());
        if (updateById(proofreadingRecord)) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.fail("修改失败");
        }
    }

    @Override
    public ProofreadingRecordVo getProofreadingRecordByCondition(ProofreadingRecordConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        ProofreadingRecordVo vo = proofreadingRecordMapper.getProofreadingRecordByCondition(condition);
        return vo;
    }

    /**
     * 校验审校记录是否符合当前调用的接口。即：在线审校记录只能发起凤凰或者方正的在线审校接口。文档审校记录只能发起凤凰或者方正的文档审校接口。
     * 
     * @param record 审校记录
     * @param type 1 凤凰在线审校，3凤凰文档审校 ，2 方正在线审校，4方正文档审校
     * @return
     */
    @Override
    public AjaxResult checkRecordLegal(ProofreadingRecordVo record, Integer type) {
        AjaxResult result = AjaxResult.success();
        if (Objects.isNull(record)) {
            result = AjaxResult.fail("审校记录不存在");
        }
        // 已处理或者失败的审校记录无法发起审校任务
        if (ProofreadingRecordStateEnum.SUCCESS.getCode().equals(record.getRecordState())
            || ProofreadingRecordStateEnum.FAILED.getCode().equals(record.getRecordState())) {
            result = AjaxResult.fail(
                "审校记录状态为：" + ProofreadingRecordStateEnum.getValue(record.getRecordState()) + "，不能发起审校任务，请重新创建审校记录");
        }
        // 在线审校
        if (ProofreadEnum.PPM_TXT_PROOFREAD.getCode().equals(type)
            || ProofreadEnum.FZ_TXT_PROOFREAD.getCode().equals(type)) {
            if (!Objects.equals(record.getRecordType(), ProofreadingRecordType.ONLINE_SYNC.getCode())) {
                result = AjaxResult.fail("该审校记录是在线审校记录，只能发起在线审校的任务");
            }
        }
        // 文档审校
        if (ProofreadEnum.PPM_FILE_PROOFREAD.getCode().equals(type)
            || ProofreadEnum.FZ_FILE_PROOFREAD.getCode().equals(type)) {
            if (!Objects.equals(record.getRecordType(), ProofreadingRecordType.UPLOAD_FILE.getCode())) {
                result = AjaxResult.fail("该审校记录是文档审校记录，只能发起文档审校的任务");
            }
            if (StringUtils.isBlank(record.getOriginalFileOid()) || StringUtils.isBlank(record.getOriginalFileUrl())) {
                result = AjaxResult.fail("该文档审校记录，缺少审校源文件，请检查审校文件是否上传成功");
            }
        }

        return result;
    }

    /**
     * 修改审校记录状态
     */
    @Override
    public void changeRecordStateCronJob() {
        // 找出正在处理中的审校记录
        List<ProofreadingRecordVo> handingRecords = proofreadingRecordMapper.getHandingRecord();
        if (CollectionUtils.isEmpty(handingRecords)) {
            return;
        }
        ProofreadingTaskConditionBo condition = new ProofreadingTaskConditionBo();
        ProofreadingRecordDto updateDto = new ProofreadingRecordDto();
        updateDto.setUpdateTime(new Date());
        for (ProofreadingRecordVo handingRecord : handingRecords) {
            Long recordId = handingRecord.getId();
            updateDto.setId(recordId);
            condition.setProofreadingRecordId(recordId);
            // 找出当前审校记录下，已经存在的审校任务
            List<ProofreadingTaskVo> taskByRecordId =
                    proofreadingTaskMapper.getTaskByRecordIdExcludeLongColumn(condition);
            // 当前时间
            LocalDateTime now = LocalDateTime.now();
            // 审校记录提交时间
            Date submitTime = handingRecord.getSubmitTime();
            LocalDateTime submitLocalDateTime = DateUtil.dateToLocalDateTime(submitTime);
            // 若审校记录在规定时间内，状态一直未变，则直接视为失败。
            if (now.minusMinutes(RECORD_TIME_OUT).isAfter(submitLocalDateTime)){
                log.warn("审校记录：{}，提交后超{}分钟，状态未变更，置为失败", recordId,RECORD_TIME_OUT);
                updateDto.setRecordState(ProofreadingRecordStateEnum.FAILED.getCode());
                updateDto.setUpdateTime(new Date());
                updateById(updateDto);
                // 如果审校任务列表不为空，则将正在处理中的审校任务置为失败
                if (CollectionUtils.isNotEmpty(taskByRecordId)){
                    proofreadingTaskMapper.updateTaskFailByRecordId(recordId);
                }

                continue;
            }

            // 找出当前审校记录预期执行的审校任务列表
            List<Integer> executedTaskList =
                JSON.parseObject(handingRecord.getExecutedTaskInfo(), new TypeReference<List<Integer>>() {});

            // 如果已经存在的审校任务为空，或者数量不符合预期，说明该审校记录的任务并没有全部执行，退出循环，等待下一次检查。
            if (CollectionUtils.isEmpty(taskByRecordId) || taskByRecordId.size() != executedTaskList.size()) {
                continue;
            }
            // 判断审校记录下是否存在正在处理中的审校任务，必须等所有审校任务都完成了，才能决定审校记录的结果。
            boolean b = taskByRecordId.stream()
                .anyMatch(x -> ProofreadingTaskState.HANDLER_ING.getCode().equals(x.getTaskState()));
            if (b) {
                continue;
            }
            // 按照审校源：凤凰、方正分组
            Map<Integer, List<ProofreadingTaskVo>> sourceTypeTaskMap =
                taskByRecordId.stream().collect(Collectors.groupingBy(ProofreadingTaskVo::getSourceType));
            // 凤凰审校任务
            List<ProofreadingTaskVo> ppmTasks = sourceTypeTaskMap.get(ProofreadingTaskSourceType.PPM.getCode());
            // 方正审校任务
            List<ProofreadingTaskVo> fzTasks = sourceTypeTaskMap.get(ProofreadingTaskSourceType.FZ.getCode());
            // 首先校验凤凰审校任务是否成功，若凤凰任务失败了，审校记录直接视为失败。
            if (!checkPPMTaskIsSuccess(ppmTasks)) {
                updateDto.setRecordState(ProofreadingRecordStateEnum.FAILED.getCode());
                updateDto.setUpdateTime(new Date());
                updateById(updateDto);
                continue;
            }
            // 若凤凰审校任务成功了，再校验方正审校任务
            if (!checkFZTaskIsSuccess(fzTasks)) {
                // 失败
                updateDto.setRecordState(ProofreadingRecordStateEnum.FAILED.getCode());
            } else {
                // 成功
                updateDto.setRecordState(ProofreadingRecordStateEnum.SUCCESS.getCode());
            }
            updateDto.setUpdateTime(new Date());
            updateById(updateDto);
        }
    }

    /**
     * 获取我的审校记录，不查询长文本字段
     * 
     * @param condition
     * @return
     */
    @Override
    public AjaxResult getMyProofreadRecord(ProofreadingRecordConditionBo condition) {
        // 按条件获取当前用户下所有的审校记录。同时过滤调那些并未发起审校任务的审校记录
        PageInfo<ProofreadingRecordVo> pageInfo = getProofreadingRecordListByCondition(condition);
        List<ProofreadingRecordVo> myRecordList = pageInfo.getList();
        long count = 0;
        Map<String,Object> map = new HashMap<>();

        if (CollectionUtils.isNotEmpty(myRecordList)) {
            //获取所有审校记录id；
            List<Long> myrecordIdList =
                myRecordList.stream().map(ProofreadingRecordVo::getId).collect(Collectors.toList());
            // 批量查询这些审校记录关联的所有审校任务
            ProofreadingTaskConditionBo conditionBo = new ProofreadingTaskConditionBo();
            conditionBo.setProofreadingRecordIds(myrecordIdList);
            List<ProofreadingTaskVo> allMyTaskList =
                Optional.ofNullable(proofreadingTaskMapper.getTaskByRecordIdExcludeLongColumn(conditionBo)).orElse(new ArrayList<>());

            // 将审校任务按照审校记录主键分组
            Map<Long, List<ProofreadingTaskVo>> recordTaskMap =
                allMyTaskList.stream().collect(Collectors.groupingBy(ProofreadingTaskVo::getProofreadingRecordId));
            // 遍历每一个审校记录
            for (ProofreadingRecordVo myRecord : myRecordList) {
                // 只有审校记录状态为成功并且审校记录为文件审校才组装审校任务。
                if (ProofreadingRecordStateEnum.SUCCESS.getCode().equals(myRecord.getRecordState())) {
                    if(ProofreadingRecordType.UPLOAD_FILE.getCode().equals(myRecord.getRecordType())){
                        List<ProofreadingTaskVo> myTaskList = recordTaskMap.get(myRecord.getId());
                        myRecord.setTasks(myTaskList);
                    }
                }

                String originalFileName = myRecord.getOriginalFileName();
                // 截取不带后缀的文件名。
                if (StringUtils.isNotBlank(originalFileName)) {
                    myRecord.setOriginalFileName(StringKit.getFileName(originalFileName));
                }

            }

            // 总数
            count = pageInfo.getTotal();
        }
        map.put("list",myRecordList);
        map.put("count",count);

        return AjaxResult.success(map, "我的审校记录查询成功");
    }

    /**
     * 获取审校结果SheetData对象
     *
     * @param recordId
     * @return
     */
    @Override
    public AjaxResult getProofreadSheetData(Long recordId) {
        ProofreadingRecordVo recordVo = proofreadingRecordMapper.selectByRecordId(recordId);
        if (Objects.isNull(recordVo)) {
            return AjaxResult.fail("审校记录不存在或者已删除");
        }
        if (!ProofreadingRecordStateEnum.SUCCESS.getCode().equals(recordVo.getRecordState())) {
            return AjaxResult
                .fail("审校记录状态为：" + ProofreadingRecordStateEnum.getValue(recordVo.getRecordState()) + "，无法下载结果");
        }
        // 获取该审校记录下所有状态为成功的审校任务
        ProofreadingTaskConditionBo conditionBo = new ProofreadingTaskConditionBo();
        conditionBo.setProofreadingRecordId(recordId);
        conditionBo.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        List<ProofreadingTaskVo> allTasks = proofreadingTaskMapper.getTaskByRecordId(conditionBo);
        List<SheetData> sheetDataList = null;
        // 根据审校任务构造sheet对象
        if (CollectionUtils.isNotEmpty(allTasks)) {
            if (ProofreadingRecordType.ONLINE_SYNC.getCode().equals(recordVo.getRecordType())) {
                sheetDataList = getOnlineProofreadResult(allTasks);
            } else if (ProofreadingRecordType.UPLOAD_FILE.getCode().equals(recordVo.getRecordType())) {
                sheetDataList = getFileProofreadResult(allTasks);
            }
        } else {
            return AjaxResult.fail("该审校记录未找到成功的审校任务，无法下载结果");
        }

        return AjaxResult.success(sheetDataList);
    }


    /**
     * 检查是否需要下载excel
     * @param recordId
     * @return
     */
    @Override
    public AjaxResult checkIsNeedDownloadExcel(Long recordId) {
        ProofreadingRecordVo recordVo = proofreadingRecordMapper.selectByRecordId(recordId);
        if (Objects.isNull(recordVo)) {
            return AjaxResult.fail("审校记录不存在或者已删除");
        }

        if (!ProofreadingRecordStateEnum.SUCCESS.getCode().equals(recordVo.getRecordState())) {
            return AjaxResult
                    .success(Boolean.FALSE,"审校记录状态为：" + ProofreadingRecordStateEnum.getValue(recordVo.getRecordState()) + "，无法下载结果");
        }
        // 是否需要下载excel的标识
        boolean isNeedDownload = Boolean.FALSE;
        // 获取该审校记录下所有状态为成功的审校任务
        ProofreadingTaskConditionBo conditionBo = new ProofreadingTaskConditionBo();
        conditionBo.setProofreadingRecordId(recordId);
        conditionBo.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
        List<ProofreadingTaskVo> allTasks = proofreadingTaskMapper.getTaskByRecordId(conditionBo);
        if (CollectionUtils.isNotEmpty(allTasks)){
            // 在线审校
            if (ProofreadingRecordType.ONLINE_SYNC.getCode().equals(recordVo.getRecordType())) {
                // 遍历审校任务，只要有一个符合下载条件，直接返回。
                for (ProofreadingTaskVo onlineTask : allTasks){
                    // 判断返回信息是否为空，为空跳过，继续下一个判断。
                    String onlineTaskResponseInfo = onlineTask.getResponseInfo();
                    if (StringUtils.isBlank(onlineTaskResponseInfo)){
                        continue;
                    }
                    // 凤凰在线审校
                    if (ProofreadingTaskSourceType.PPM.getCode().equals(onlineTask.getSourceType())){
                        PPMProofreadResultTaskVo ppmProofreadResultTaskVo =
                                JSON.parseObject(onlineTaskResponseInfo, PPMProofreadResultTaskVo.class);
                        List<PPMRpcResultVo> ppmRpcResultVos = PPMProofreadingUtil.handleResult(ppmProofreadResultTaskVo.getResult());
                        // 只要存在不为空的纠错集，可视为存在错误结果，需要下载excel。
                        if (ppmRpcResultVos.stream().anyMatch(x->MapUtils.isNotEmpty(x.getJiucuoMap())||CollectionUtils.isNotEmpty(x.getJiucuoList()))){
                            isNeedDownload = Boolean.TRUE;
                            break;
                        }
                    // 方正在线审校
                    } else if (ProofreadingTaskSourceType.FZ.getCode().equals(onlineTask.getSourceType())) {
                        // 方正在线字词字符审校
                        if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(onlineTask.getTaskType())) {
                            TxtReviewSync txtReviewSync = JSON.parseObject(onlineTaskResponseInfo, TxtReviewSync.class);
                            List<WordCorrect> wordCorrects = txtReviewSync.getDetail();
                            // 字词错误集合不为空，则需要下载excel
                            if (CollectionUtils.isNotEmpty(wordCorrects)){
                                isNeedDownload = Boolean.TRUE;
                                break;
                            }
                        // 方正在线重要讲话审校
                        } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(onlineTask.getTaskType())) {
                            ContentverifyTaskOutPut contentverifyTaskOutPut =
                                    JSON.parseObject(onlineTaskResponseInfo, ContentverifyTaskOutPut.class);
                            List<Base> bases = contentverifyTaskOutPut.getDetail();
                            // 不为空，需要下载excel
                            if (CollectionUtils.isNotEmpty(bases)){
                                isNeedDownload = Boolean.TRUE;
                                break;
                            }
                        }
                    }

                }
            // 文件审校
            } else if (ProofreadingRecordType.UPLOAD_FILE.getCode().equals(recordVo.getRecordType())) {
                // 由于凤凰文件审校不需要下载excel，过滤出方正审校列表。
                List<ProofreadingTaskVo> fzFileTasks =
                        allTasks.stream().filter(x -> ProofreadingTaskSourceType.FZ.getCode().equals(x.getSourceType()))
                                .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(fzFileTasks)){
                    // 遍历，有一个审校任务满足下载条件，直接返回。
                    for (ProofreadingTaskVo fileTask : fzFileTasks){
                        // 判断返回信息是否为空，为空跳过，继续下一个判断。
                        String fileTaskResponseInfo = fileTask.getResponseInfo();
                        if (StringUtils.isBlank(fileTaskResponseInfo)){
                            continue;
                        }
                        // 方正文件字词字符审校
                        if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(fileTask.getTaskType())) {
                            TxtReviewASync txtReviewASync = JSON.parseObject(fileTaskResponseInfo, TxtReviewASync.class);
                            ReviewStatus<WordCorrect> wordCorrectReviewStatus = txtReviewASync.getWordcorrect();
                            if (Objects.nonNull(wordCorrectReviewStatus)) {
                                List<WordCorrect> wordCorrects = wordCorrectReviewStatus.getDetail();
                                if (CollectionUtils.isNotEmpty(wordCorrects)) {
                                    isNeedDownload = Boolean.TRUE;
                                    break;
                                }
                            }
                        // 方正内容审校/方正参考文献审校
                        } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(fileTask.getTaskType())
                                || ProofreadingTaskType.REFERENCES.getCode().equals(fileTask.getTaskType())) {
                            ContentverifyTaskOutPut contentverifyTaskOutPut =
                                    JSON.parseObject(fileTaskResponseInfo, ContentverifyTaskOutPut.class);
                            List<Base> bases = contentverifyTaskOutPut.getDetail();
                            if (CollectionUtils.isNotEmpty(bases)) {
                                isNeedDownload = Boolean.TRUE;
                                break;
                            }
                        // 方正上下文查重审校
                        } else if (ProofreadingTaskType.DUPLICATE_CHECK.getCode().equals(fileTask.getTaskType())) {

                            TxtReviewASync txtReviewASync = JSON.parseObject(fileTaskResponseInfo, TxtReviewASync.class);
                            ReviewStatus<Docsim> docsimReviewStatus = txtReviewASync.getDocsim();
                            if (Objects.nonNull(docsimReviewStatus)) {
                                List<Docsim> docsims = docsimReviewStatus.getDetail();
                                if (CollectionUtils.isNotEmpty(docsims)) {
                                    isNeedDownload = Boolean.TRUE;
                                    break;
                                }
                            }

                        }
                }

            }

            }

        }else {
            return AjaxResult.success(Boolean.FALSE,"该审校记录未找到成功的审校任务，无法下载结果");
        }

        if (!isNeedDownload){
            return AjaxResult.success(Boolean.FALSE,"审校无错误，无需下载");
        }

        return AjaxResult.success(Boolean.TRUE);
    }


    /**
     * 构造在线审校结果的excel下载对象
     * 
     * @param allOlineTasks
     * @return
     */
    public List<SheetData> getOnlineProofreadResult(List<ProofreadingTaskVo> allOlineTasks) {
        List<SheetData> sheetDataList = new ArrayList<>();
        for (ProofreadingTaskVo taskVo : allOlineTasks) {
            SheetData sheetData = new SheetData();
            String responseInfo = taskVo.getResponseInfo();
            if (ProofreadingTaskSourceType.PPM.getCode().equals(taskVo.getSourceType())) {
                sheetData.setSheetName("审校方案一");
                sheetData.setPojoClass(ProofreadWordExcelVo.class);
                sheetData.setSort(ConstantsInteger.NUM_1);
                sheetData.setNeedSpecialFormatFlag(Boolean.TRUE);
                PPMProofreadResultTaskVo ppmProofreadResultTaskVo =
                    JSON.parseObject(responseInfo, PPMProofreadResultTaskVo.class);
                List<PPMRpcResultVo> ppmRpcResultVos = PPMProofreadingUtil.handleResult(ppmProofreadResultTaskVo.getResult());
                // 筛选出有问题的句子
                List<PPMRpcResultVo> errorPPMRpcResultMapVos = new ArrayList<>();
                List<PPMRpcResultVo> errorPPMRpcResultListVos = new ArrayList<>();

                for (PPMRpcResultVo ppmRpcResultVo : ppmRpcResultVos){
                    if (MapUtils.isNotEmpty(ppmRpcResultVo.getJiucuoMap())){
                        errorPPMRpcResultMapVos.add(ppmRpcResultVo);
                    }else if(CollectionUtils.isNotEmpty(ppmRpcResultVo.getJiucuoList())){
                        errorPPMRpcResultListVos.add(ppmRpcResultVo);
                    }
                }
                int seq = 1;
                List<ProofreadWordExcelVo> ppmOnlineExcelVos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(errorPPMRpcResultMapVos)) {
                    for (PPMRpcResultVo errorPPMRpcResult : errorPPMRpcResultMapVos) {
                        Map<String, PPMJiuCuoDetailVo> jiucuo = errorPPMRpcResult.getJiucuoMap();
                        for (Map.Entry<String, PPMJiuCuoDetailVo> entry : jiucuo.entrySet()) {
                            PPMJiuCuoDetailVo ppmJiuCuoDetailVo = entry.getValue();
                            String category = ppmJiuCuoDetailVo.getCategory();
                            ProofreadWordExcelVo ppmOnlineExcelVo = new ProofreadWordExcelVo();
                            ppmOnlineExcelVo.setSeq(seq++);
                            ppmOnlineExcelVo.setErrorType(PPMCategoryTypeEnum.getValue(category));
                            ppmOnlineExcelVo.setSuspiciousContent(ppmJiuCuoDetailVo.getTi());
                            ppmOnlineExcelVo.setPointOut(ppmJiuCuoDetailVo.getTishi());
                            ppmOnlineExcelVo.setOriginalContent(errorPPMRpcResult.getYuanju());
                            // 获取错误结束位置
                            Integer h = ppmJiuCuoDetailVo.getH();
                            int ti = ppmJiuCuoDetailVo.getTi().length();
                            ppmOnlineExcelVo.setStartIndex(h-ti);
                            ppmOnlineExcelVo.setEndIndex(h);
                            ppmOnlineExcelVos.add(ppmOnlineExcelVo);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(errorPPMRpcResultListVos)){
                    for (PPMRpcResultVo ppmRpcResultVo : errorPPMRpcResultListVos){
                        List<PPMJiuCuoDetailVo> jiucuoList = ppmRpcResultVo.getJiucuoList();
                        for (PPMJiuCuoDetailVo ppmJiuCuoDetailVo : jiucuoList){
                            String category = ppmJiuCuoDetailVo.getCategory();
                            ProofreadWordExcelVo ppmOnlineExcelVo = new ProofreadWordExcelVo();
                            ppmOnlineExcelVo.setSeq(seq++);
                            ppmOnlineExcelVo.setErrorType(PPMCategoryTypeEnum.getValue(category));
                            ppmOnlineExcelVo.setSuspiciousContent(ppmJiuCuoDetailVo.getTi());
                            ppmOnlineExcelVo.setPointOut(ppmJiuCuoDetailVo.getTishi());
                            ppmOnlineExcelVo.setOriginalContent(ppmRpcResultVo.getYuanju());
                            // 获取错误结束位置
                            Integer h = ppmJiuCuoDetailVo.getH();
                            int ti = ppmJiuCuoDetailVo.getTi().length();
                            ppmOnlineExcelVo.setStartIndex(h-ti);
                            ppmOnlineExcelVo.setEndIndex(h);
                            ppmOnlineExcelVos.add(ppmOnlineExcelVo);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(ppmOnlineExcelVos)){
                    sheetData.setDataList(ppmOnlineExcelVos);
                    sheetDataList.add(sheetData);
                }
            } else if (ProofreadingTaskSourceType.FZ.getCode().equals(taskVo.getSourceType())) {
                if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(taskVo.getTaskType())) {
                    sheetData.setSheetName("审校方案二");
                    sheetData.setPojoClass(ProofreadWordExcelVo.class);
                    sheetData.setSort(ConstantsInteger.NUM_2);
                    sheetData.setNeedSpecialFormatFlag(Boolean.TRUE);
                    TxtReviewSync txtReviewSync = JSON.parseObject(responseInfo, TxtReviewSync.class);
                    List<WordCorrect> wordCorrects = txtReviewSync.getDetail();
                    if (CollectionUtils.isNotEmpty(wordCorrects)) {
                        List<ProofreadWordExcelVo> fzOnlineExcelVos = handleWordCorrect(wordCorrects);
                        sheetData.setDataList(fzOnlineExcelVos);
                        sheetDataList.add(sheetData);
                    }
                } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(taskVo.getTaskType())) {
                    sheetData.setSheetName("重要讲话");
                    sheetData.setPojoClass(ProofreadFZContentExcelVo.class);
                    sheetData.setSort(ConstantsInteger.NUM_3);
                    ContentverifyTaskOutPut contentverifyTaskOutPut =
                        JSON.parseObject(responseInfo, ContentverifyTaskOutPut.class);
                    List<Base> bases = contentverifyTaskOutPut.getDetail();
                    if (CollectionUtils.isNotEmpty(bases)) {
                        List<ProofreadFZContentExcelVo> contentExcelVos = handleBase(bases);
                        sheetData.setDataList(contentExcelVos);
                        sheetDataList.add(sheetData);
                    }
                }
            }
        }
        return sheetDataList;
    }

    /**
     * 构造文件审校结果的excel下载对象
     * 
     * @param allFileTasks
     * @return
     */
    public List<SheetData> getFileProofreadResult(List<ProofreadingTaskVo> allFileTasks) {
        // 过滤掉凤凰文件审校任务
        List<ProofreadingTaskVo> fzFileTasks =
            allFileTasks.stream().filter(x -> ProofreadingTaskSourceType.FZ.getCode().equals(x.getSourceType()))
                .collect(Collectors.toList());
        List<SheetData> sheetDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fzFileTasks)) {
            for (ProofreadingTaskVo fzFileTask : fzFileTasks) {
                SheetData sheetData = new SheetData();
                String responseInfo = fzFileTask.getResponseInfo();
                // 文件审校任务
                if (ProofreadingTaskType.WORDS_CHECK.getCode().equals(fzFileTask.getTaskType())) {
                    sheetData.setSheetName(ProofreadingTaskType.getValue(fzFileTask.getTaskType()));
                    sheetData.setPojoClass(ProofreadWordExcelVo.class);
                    sheetData.setSort(ConstantsInteger.NUM_1);
                    sheetData.setNeedSpecialFormatFlag(Boolean.TRUE);
                    TxtReviewASync txtReviewASync = JSON.parseObject(responseInfo, TxtReviewASync.class);
                    ReviewStatus<WordCorrect> wordCorrectReviewStatus = txtReviewASync.getWordcorrect();
                    if (Objects.nonNull(wordCorrectReviewStatus)) {
                        List<WordCorrect> wordCorrects = wordCorrectReviewStatus.getDetail();
                        if (CollectionUtils.isNotEmpty(wordCorrects)) {
                            List<ProofreadWordExcelVo> proofreadWordExcelVos = handleWordCorrect(wordCorrects);
                            sheetData.setDataList(proofreadWordExcelVos);
                            sheetDataList.add(sheetData);
                        }
                    }
                    // 内容审校/参考文献审校
                } else if (ProofreadingTaskType.CONTENT_CHECK.getCode().equals(fzFileTask.getTaskType())
                    || ProofreadingTaskType.REFERENCES.getCode().equals(fzFileTask.getTaskType())) {
                    sheetData.setSheetName(ProofreadingTaskType.getValue(fzFileTask.getTaskType()));
                    sheetData.setPojoClass(ProofreadFZContentExcelVo.class);
                    sheetData.setSort(ConstantsInteger.NUM_2);
                    ContentverifyTaskOutPut contentverifyTaskOutPut =
                        JSON.parseObject(responseInfo, ContentverifyTaskOutPut.class);
                    List<Base> bases = contentverifyTaskOutPut.getDetail();
                    if (CollectionUtils.isNotEmpty(bases)) {
                        List<ProofreadFZContentExcelVo> contentExcelVos = handleBase(bases);
                        sheetData.setDataList(contentExcelVos);
                        sheetDataList.add(sheetData);
                    }
                    // 上下文查重审校
                } else if (ProofreadingTaskType.DUPLICATE_CHECK.getCode().equals(fzFileTask.getTaskType())) {
                    sheetData.setSheetName(ProofreadingTaskType.getValue(fzFileTask.getTaskType()));
                    sheetData.setPojoClass(ProofreadDupExcelVo.class);
                    sheetData.setSort(ConstantsInteger.NUM_3);
                    TxtReviewASync txtReviewASync = JSON.parseObject(responseInfo, TxtReviewASync.class);
                    ReviewStatus<Docsim> docsimReviewStatus = txtReviewASync.getDocsim();
                    if (Objects.nonNull(docsimReviewStatus)) {
                        List<Docsim> docsims = docsimReviewStatus.getDetail();
                        if (CollectionUtils.isNotEmpty(docsims)) {
                            List<ProofreadDupExcelVo> proofreadDupExcelVos = handleDocsim(docsims);
                            sheetData.setDataList(proofreadDupExcelVos);
                            sheetDataList.add(sheetData);
                        }
                    }

                }

            }

        }
        return sheetDataList;
    }

    private List<ProofreadWordExcelVo> handleWordCorrect(List<WordCorrect> wordCorrects) {
        List<ProofreadWordExcelVo> fzOnlineExcelVos = new ArrayList<>();
        int seq = 1;
        for (WordCorrect wordCorrect : wordCorrects) {
            ProofreadWordExcelVo fzOnlineExcelVo = new ProofreadWordExcelVo();
            fzOnlineExcelVo.setSeq(seq++);
            fzOnlineExcelVo.setErrorType(wordCorrect.getInspectType());
            fzOnlineExcelVo.setSuspiciousContent(wordCorrect.getContent());
            fzOnlineExcelVo.setPointOut(wordCorrect.getLookup());

            String detail = wordCorrect.getDetail();
            Pattern pattern = Pattern.compile(REPLACE_STR_REGX);
            Matcher matcher = pattern.matcher(detail);
            String replaced = null;
            // 获取内容的开始和结束索引
            if(matcher.find()){
                String group = matcher.group(2);
                int start = matcher.start(0);
                fzOnlineExcelVo.setStartIndex(start);
                fzOnlineExcelVo.setEndIndex( start+group.length());
                replaced = detail.replaceAll(REPLACE_STR_REGX, group);
            }
            if(StringUtils.isNotBlank(replaced)){
                fzOnlineExcelVo.setOriginalContent(replaced);
            }else {
                fzOnlineExcelVo.setOriginalContent(detail);
            }
            fzOnlineExcelVos.add(fzOnlineExcelVo);
        }
        return fzOnlineExcelVos;
    }

    private List<ProofreadFZContentExcelVo> handleBase(List<Base> bases) {
        List<ProofreadFZContentExcelVo> contentExcelVos = new ArrayList<>();
        int seq = 1;
        for (Base base : bases) {
            ProofreadFZContentExcelVo proofreadFZContentExcelVo = new ProofreadFZContentExcelVo();
            proofreadFZContentExcelVo.setSeq(seq++);
            proofreadFZContentExcelVo.setReviewType(FZReviewTypeEnum.getValue(base.getReviewType()));
            proofreadFZContentExcelVo.setBaseContent(base.getBaseContent());
            List<ContentVerifyRef> refs = base.getRef();
            if (CollectionUtils.isNotEmpty(refs)) {
                List<ContentExcelVo> contentExcelVoList = new ArrayList<>();
                for (ContentVerifyRef ref : refs) {
                    ContentExcelVo contentExcelVo = new ContentExcelVo();
                    contentExcelVo.setRefContent(ref.getRefContent());
                    contentExcelVo.setSource(ref.getSource());
                    contentExcelVoList.add(contentExcelVo);
                }
                proofreadFZContentExcelVo.setContentExcelVos(contentExcelVoList);
            }
            contentExcelVos.add(proofreadFZContentExcelVo);
        }
        return contentExcelVos;
    }

    private List<ProofreadDupExcelVo> handleDocsim(List<Docsim> docsims) {
        List<ProofreadDupExcelVo> proofreadDupExcelVos = new ArrayList<>();
        // 将结果按照序号分组，序号相同为一组基准、疑似句
        Map<String, List<Docsim>> seqMap = docsims.stream().collect(Collectors.groupingBy(Docsim::getSeqNo));
        int seq = 1;
        for (Map.Entry<String, List<Docsim>> entry : seqMap.entrySet()) {
            ProofreadDupExcelVo proofreadDupExcelVo = new ProofreadDupExcelVo();
            proofreadDupExcelVo.setSeq(seq++);
            List<ChildDupExcelVo> childDupExcelVos = new ArrayList<>();
            List<Docsim> value = entry.getValue();
            for (Docsim child : value) {
                // 基准句
                if (child.getBaseSentence().equals(ConstantsInteger.NUM_1)) {
                    proofreadDupExcelVo.setContent(child.getContent());
                    // 疑似句
                } else {
                    ChildDupExcelVo childDupExcelVo = new ChildDupExcelVo();
                    childDupExcelVo.setContent(child.getContent());
                    childDupExcelVo.setPercDes(child.getPercDes());
                    childDupExcelVo.setRefPerc(child.getRefPerc());
                    childDupExcelVos.add(childDupExcelVo);
                }
            }
            proofreadDupExcelVo.setChildDupExcelVos(childDupExcelVos);
            proofreadDupExcelVos.add(proofreadDupExcelVo);
        }
        return proofreadDupExcelVos;
    }

    /**
     * 校验凤凰审校任务结果是否成功
     * 
     * @param ppmTasks
     * @return
     */
    private Boolean checkPPMTaskIsSuccess(List<ProofreadingTaskVo> ppmTasks) {
        Boolean isSuccess = Boolean.TRUE;
        // 如果不存在凤凰审校任务，说明没有发起凤凰任务，直接返回成功。
        if (CollectionUtils.isNotEmpty(ppmTasks)) {
            // 凤凰审校任务有且只有一个，直接get(0)
            ProofreadingTaskVo proofreadingTaskVo = ppmTasks.get(ConstantsInteger.NUM_0);
            if (ProofreadingTaskState.SUCCESS.getCode().equals(proofreadingTaskVo.getTaskState())) {
                isSuccess = Boolean.TRUE;
            } else if (ProofreadingTaskState.FAIL.getCode().equals(proofreadingTaskVo.getTaskState())) {
                isSuccess = Boolean.FALSE;
            }
        }
        return isSuccess;
    }

    /**
     * 校验方正审校任务。
     * 
     * @param fzTasks
     * @return
     */
    private Boolean checkFZTaskIsSuccess(List<ProofreadingTaskVo> fzTasks) {
        Boolean isSuccess = Boolean.TRUE;
        // 如果方正审校任务为空，说明没有发起方正审校，直接返回成功。
        if (CollectionUtils.isNotEmpty(fzTasks)) {
            // 如果存在任意一个失败的方正审校任务，则视为失败。否则成功
            if (fzTasks.stream().anyMatch(x -> ProofreadingTaskState.FAIL.getCode().equals(x.getTaskState()))) {
                isSuccess = Boolean.FALSE;
            } else {
                isSuccess = Boolean.TRUE;
            }
        }
        return isSuccess;
    }

}