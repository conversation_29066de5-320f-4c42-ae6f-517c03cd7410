package com.fh.ai.business.service.impl;


import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingTaskConditionBo;
import com.fh.ai.business.entity.dto.proofreading.ProofreadingTaskDto;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo;
import com.fh.ai.business.mapper.ProofreadingTaskMapper;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IProofreadingTaskService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.FileResourceFileTypeEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ProofreadingTaskState;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.proofreading.fz.vo.FileResource;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 审校任务表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-10 11:03:26
 */
@Service
public class ProofreadingTaskServiceImpl extends ServiceImpl<ProofreadingTaskMapper, ProofreadingTaskDto> implements IProofreadingTaskService {

	@Resource
	private ProofreadingTaskMapper proofreadingTaskMapper;


	@Resource
	IAttachmentService attachmentService;
	
    @Override
	public List<ProofreadingTaskVo> getProofreadingTaskListByCondition(ProofreadingTaskConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return proofreadingTaskMapper.getProofreadingTaskListByCondition(condition);
	}

	@Override
	public AjaxResult addProofreadingTask(ProofreadingTaskBo proofreadingTaskBo) {
		ProofreadingTaskDto proofreadingTask = new ProofreadingTaskDto();
		BeanUtils.copyProperties(proofreadingTaskBo, proofreadingTask);
		proofreadingTask.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(proofreadingTask)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateProofreadingTask(ProofreadingTaskBo proofreadingTaskBo) {
		ProofreadingTaskDto proofreadingTask = new ProofreadingTaskDto();
		BeanUtils.copyProperties(proofreadingTaskBo, proofreadingTask);
		proofreadingTask.setUpdateTime(new Date());
		if(updateById(proofreadingTask)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ProofreadingTaskVo getProofreadingTaskByCondition(ProofreadingTaskConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		ProofreadingTaskVo vo = proofreadingTaskMapper.getProofreadingTaskByCondition(condition);
		return vo;
	}

	@Override
	public AjaxResult createProofreadingTask(ProofreadingTaskBo proofreadingTaskBo) {
		ProofreadingTaskDto proofreadingTask = new ProofreadingTaskDto();
		BeanUtils.copyProperties(proofreadingTaskBo, proofreadingTask);
		proofreadingTask.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		proofreadingTask.setCreateTime(new Date());
		proofreadingTask.setUpdateTime(new Date());
		if (!save(proofreadingTask)){
			throw new BusinessException("保存审校任务失败");
		}
		return AjaxResult.success(proofreadingTask,"保存成功");
	}

	/**
	 * 将某个任务记为失败，并记录返回信息
	 *
	 * @param id 主键
	 * @param responseStr 接口返回
	 * @param remark 错误详细信息
	 * @return
	 */
	@Override
	public AjaxResult failProofreadingTask(Long id, String responseStr,String remark) {
		ProofreadingTaskDto proofreadingTaskDto = new ProofreadingTaskDto();
		proofreadingTaskDto.setId(id);
		proofreadingTaskDto.setResponseInfo(responseStr);
		proofreadingTaskDto.setTaskState(ProofreadingTaskState.FAIL.getCode());
		proofreadingTaskDto.setRemark(remark);
		proofreadingTaskDto.setFinishTime(new Date());
		proofreadingTaskDto.setUpdateTime(new Date());
		if (!updateById(proofreadingTaskDto)){
			return AjaxResult.fail();
		}
		return AjaxResult.success();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult uploadFileProofreadingTask(List<FileResource> fileResources, ProofreadingTaskDto updateTask,String originalName,String planName) {
		try {
			if (CollectionUtils.isNotEmpty(fileResources)){
				// 上传文件，并保存文件oid
				for (FileResource fileResource : fileResources){
					AttachmentBo attachmentBo = new AttachmentBo();
					String name = fileResource.getFileName();
					if (StringUtils.isBlank(name)){
						throw new BusinessException("文件名称不能为空");
					}
					// 截取文件后缀
					String fileExt = name.substring(name.lastIndexOf(".") + 1);
					attachmentBo.setSuffix(fileExt);

					// 上传文件到本地服务器
					AttachmentVo attachmentVo = attachmentService.uploadUrl(fileResource.getUrl(), attachmentBo);
					if (Objects.isNull(attachmentVo)){
						throw new BusinessException("上传审校结果文件至服务器失败");
					}
					// 记录上传后的本地文件信息
					if (fileResource.getFileType().equals(FileResourceFileTypeEnum.MARK_FILE.getCode())){
						updateTask.setResultFileName("【文档审校-"+planName+"-批注文件】"+ StringKit.getFileName(originalName)+"."+fileExt);
						updateTask.setResultFileOid(attachmentVo.getOid());
						updateTask.setResultFileUrl(attachmentVo.getOriginPath());
					}else if (fileResource.getFileType().equals(FileResourceFileTypeEnum.REPORT_FILE.getCode())){
						updateTask.setReportFileName("【文档审校-"+planName+"-审校报告】"+StringKit.getFileName(originalName)+".pdf");
						updateTask.setReportFileOid(attachmentVo.getOid());
						updateTask.setReportFileUrl(attachmentVo.getOriginPath());
					}
				}
			}else {
				updateTask.setRemark("文件审校成功，无错误，并未返回审校文档");
			}
			updateTask.setTaskState(ProofreadingTaskState.SUCCESS.getCode());
		}catch (Exception e){
			log.error("上传文件至服务器失败",e);
			updateTask.setTaskState(ProofreadingTaskState.FAIL.getCode());
			updateTask.setRemark("上传审校结果文件失败，异常信息：" + e.getMessage());
		}
		updateTask.setFinishTime(new Date());
		updateTask.setUpdateTime(new Date());
		updateById(updateTask);
		return AjaxResult.success();
	}

}