package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.BookInputType;
import com.fh.ai.common.enums.BookSourceType;
import com.fh.ai.common.enums.PublishNewsSourceType;
import com.fh.ai.common.tophub.*;
import com.fh.ai.business.entity.bo.publishNews.PublishNewsBo;
import com.fh.ai.business.entity.bo.publishNews.PublishNewsConditionBo;
import com.fh.ai.business.entity.bo.publishNews.QueryPublishListBo;
import com.fh.ai.business.entity.dto.publishNews.PublishNewsDto;
import com.fh.ai.business.entity.vo.publishNews.NewsHashVo;
import com.fh.ai.business.entity.vo.publishNews.PublishNewsVo;
import com.fh.ai.business.mapper.PublishNewsMapper;
import com.fh.ai.business.service.IPublishNewsService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.HttpUtil;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 出版资讯表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 17:23:42
 */
@Service
@Slf4j
public class PublishNewsServiceImpl extends ServiceImpl<PublishNewsMapper, PublishNewsDto>
    implements IPublishNewsService {

    @Resource
    private PublishNewsMapper publishNewsMapper;

    /**
     * 列表查询
     * 
     * @param condition
     * @return
     */
    @Override
    public List<PublishNewsVo> getPublishNewsListByCondition(PublishNewsConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return publishNewsMapper.getPublishNewsListByCondition(condition);
    }

    /**
     * 新增咨询
     * 
     * @param publishNewsBo
     * @return
     */
    @Override
    public AjaxResult addPublishNews(PublishNewsBo publishNewsBo) {
        PublishNewsDto publishNews = new PublishNewsDto();
        BeanUtils.copyProperties(publishNewsBo, publishNews);
        publishNews.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        publishNews.setCreateTime(new Date());
        if (save(publishNews)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 修改资讯
     * 
     * @param publishNewsBo
     * @return
     */
    @Override
    public AjaxResult updatePublishNews(PublishNewsBo publishNewsBo) {
        Long id = publishNewsMapper.selectIdById(publishNewsBo.getId());
        if (Objects.isNull(id)) {
            return AjaxResult.fail("未找到该资讯");
        }
        PublishNewsDto publishNews = new PublishNewsDto();
        BeanUtils.copyProperties(publishNewsBo, publishNews);
        publishNews.setUpdateTime(new Date());
        if (updateById(publishNews)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 资讯详情
     * 
     * @param id
     * @return
     */
    @Override
    public AjaxResult getPublishNewsByCondition(Long id) {
        PublishNewsVo vo = publishNewsMapper.getPublishNewsDetail(id);
        if (Objects.isNull(vo)) {
            return AjaxResult.fail("未找到该条资讯");
        }
        return AjaxResult.success(vo, "查询资讯详情成功");
    }

    /**
     * 资讯列表查询
     * 
     * @param requestBo
     * @return
     */
    @Override
    public Map<String, Object> queryNewsForList(QueryPublishListBo requestBo) {
        Map<String, Object> map = new HashMap<>();
        List<PublishNewsVo> list = null;
        long count = 0;
        // 列表
        if (null == requestBo.getPage() || null == requestBo.getLimit()) {
            list = publishNewsMapper.getPublishNewsList(requestBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(requestBo.getPage(), requestBo.getLimit(), requestBo.getOrderBy());
            list = publishNewsMapper.getPublishNewsList(requestBo);
            PageInfo<PublishNewsVo> pageInfo = new PageInfo<>(list);
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    /**
     * 远程获取并保存资讯信息，重试时使用该方法传入参数集合
     * 
     * @param paramMap key 参数名，value 参数体。
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult getAndSaveNewsRpc(Map<String, Object> paramMap) {
        NewsHashVo newsHashVo = (NewsHashVo)paramMap.get(Constants.NEWS_HASH_PARAM);
        GetHistoryParam getHistoryParam = null;
        if (Objects.nonNull(paramMap.get(Constants.NEWS_HISTORY_PARAM))) {
            getHistoryParam = (GetHistoryParam)paramMap.get(Constants.NEWS_HISTORY_PARAM);
        }
        String topHubDataUrl = (String)paramMap.get(Constants.NEWS_URL_PARAM);
        String token = (String)paramMap.get(Constants.NEWS_TOKEN_PARAM);
        return getAndSaveNewsRpc(newsHashVo, getHistoryParam, topHubDataUrl, token);
    }

    /**
     * 远程获取并保存资讯信息
     *
     * @param newsHashVo 本次查询的详细信息
     * @param historyParam 历史查询传参
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult getAndSaveNewsRpc(NewsHashVo newsHashVo, GetHistoryParam historyParam, String topHubDataUrl,
        String token) {
        AjaxResult ajaxResult = null;
        // 请求url
        String url = topHubDataUrl.concat(newsHashVo.getHashId());
        // 请求头
        Map<String, String> headers = new HashMap<>();
        headers.put(Constants.AUTHOR, token);
        // 查询历史榜单，拼接请求入参
        if (Objects.nonNull(historyParam)) {
            url = url.concat(historyParam.getHistoryStr());
            Map<String, Object> requestParamMap = new HashMap<>();
            requestParamMap.put(Constants.DATE_STR, historyParam.getDate());
            url = StringKit.urlConcat(url, requestParamMap);
            JSONObject historyJsonObject = HttpUtil.doGet(url, headers);
            if (Objects.nonNull(historyJsonObject)) {
                ajaxResult = handlerHistoryJsonResult(historyJsonObject, newsHashVo);
            } else {
                log.error("请求{}时，返回体是空", url);
                return AjaxResult.fail(Constants.RETRY_ERROR_CODE, "远程调用返回为空，请重试");
            }
            // 查询最新榜单数据
        } else {
            JSONObject latestJsonObject = HttpUtil.doGet(url, headers);
            if (Objects.nonNull(latestJsonObject)) {
                ajaxResult = handlerLatestJsonResult(latestJsonObject, newsHashVo);
            } else {
                log.error("请求{}时，返回体是空", url);
                return AjaxResult.fail(Constants.RETRY_ERROR_CODE, "远程调用返回为空，请重试");
            }
        }
        return ajaxResult;
    }

    /**
     * 处理历史资讯新闻返回体
     * 
     * @param historyJsonObject
     * @param newsHashVo
     * @return
     */
    private AjaxResult handlerHistoryJsonResult(JSONObject historyJsonObject, NewsHashVo newsHashVo) {
        RpcTopHubHistoryResult historyResult =
            JSON.parseObject(historyJsonObject.toJSONString(), RpcTopHubHistoryResult.class);
        if (historyResult.getError()) {
            return AjaxResult.fail("调用tophub接口失败，失败信息：" + historyResult.getMsg());
        }
        List<NewsInfo> historyDataS = historyResult.getData();
        if (CollectionUtils.isEmpty(historyDataS)) {
            return AjaxResult.fail("接口返回空");
        }
        List<PublishNewsVo> allNews = publishNewsMapper.selectAllNews();
        Map<String, PublishNewsVo> allNewsMap =
            allNews.stream().collect(Collectors.toMap(PublishNewsVo::getUrl, x -> x, (x, y) -> y));
        buildAndSaveNews(historyDataS, newsHashVo, allNewsMap);
        return AjaxResult.success(historyDataS.size(), "获取" + newsHashVo.getChannel() + "下的数据成功");
    }

    /**
     * 处理最新资讯新闻返回体
     * 
     * @param latestJsonObject
     * @param newsHashVo
     * @return
     */
    private AjaxResult handlerLatestJsonResult(JSONObject latestJsonObject, NewsHashVo newsHashVo) {
        RpcTopHubLatestResult rpcTopHubLatestResult =
            JSON.parseObject(latestJsonObject.toJSONString(), RpcTopHubLatestResult.class);
        if (rpcTopHubLatestResult.getError()) {
            return AjaxResult.fail("调用tophub接口失败，失败信息：" + rpcTopHubLatestResult.getMsg());
        }
        TopHubData data = rpcTopHubLatestResult.getData();
        if (Objects.nonNull(data) && CollectionUtils.isNotEmpty(data.getItems())) {
            List<PublishNewsVo> allNews = publishNewsMapper.selectAllNews();
            Map<String, PublishNewsVo> allNewsMap =
                allNews.stream().collect(Collectors.toMap(PublishNewsVo::getUrl, x -> x, (x, y) -> y));
            buildAndSaveNews(data.getItems(), newsHashVo, allNewsMap);
            return AjaxResult.success(data.getItems().size(), "获取" + newsHashVo.getChannel() + "下的数据成功");
        } else {
            return AjaxResult.fail("接口返回为空");
        }
    }

    /**
     * 处理构建资讯dto
     * 
     * @param newsInfos rpc 数据
     * @param newsHashVo 接口api信息
     * @param allNewsMap 已存在数据
     */
    public void buildAndSaveNews(List<NewsInfo> newsInfos, NewsHashVo newsHashVo,
        Map<String, PublishNewsVo> allNewsMap) {
        // 待插入列表
        List<PublishNewsDto> insertDTOs = new ArrayList<>();
        // 待更新列表
        List<PublishNewsDto> updateDTOs = new ArrayList<>();
        Date date = new Date();
        for (NewsInfo newsInfo : newsInfos) {
            if (allNewsMap.containsKey(newsInfo.getUrl())) {
                PublishNewsVo publishNewsVo = allNewsMap.get(newsInfo.getUrl());
                PublishNewsDto updateDTO = buildPublishNews(newsInfo, newsHashVo, publishNewsVo, date);
                updateDTOs.add(updateDTO);
            } else {
                PublishNewsDto insertDTO = buildPublishNews(newsInfo, newsHashVo, null, date);
                insertDTOs.add(insertDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertDTOs)) {
            this.saveBatch(insertDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateDTOs)) {
            this.updateBatchById(updateDTOs);
        }

    }

    /**
     * 通过topHub返回的数据构建新闻实体
     * 
     * @param newsInfo
     * @param newsHashVo
     * @param publishNewsVo
     * @param date
     * @return
     */
    private PublishNewsDto buildPublishNews(NewsInfo newsInfo, NewsHashVo newsHashVo, PublishNewsVo publishNewsVo,
        Date date) {
        PublishNewsDto publishNewsDto = new PublishNewsDto();
        publishNewsDto.setSourceType(PublishNewsSourceType.Top_Hub.getValue());
        publishNewsDto.setNewsType(newsHashVo.getNewsType());
        publishNewsDto.setNewsCategory(newsHashVo.getNewsCategory());
        publishNewsDto.setChannel(newsHashVo.getChannel());
        publishNewsDto.setInputYpe(BookInputType.CRAWLER.getValue());
        publishNewsDto.setTitle(newsInfo.getTitle());
        publishNewsDto.setContent(newsInfo.getDescription());
        publishNewsDto.setUrl(newsInfo.getUrl());
        Long time = newsInfo.getTime();
        publishNewsDto.setPublishTime(Objects.isNull(time) ? null : new Date(time * ConstantsInteger.NUM_1000));
        if (Objects.isNull(publishNewsVo)) {
            publishNewsDto.setCreateTime(date);
            publishNewsDto.setUpdateTime(date);
            publishNewsDto.setRefreshTime(date);
            publishNewsDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        } else {
            publishNewsDto.setUpdateTime(date);
            publishNewsDto.setRefreshTime(date);
            publishNewsDto.setId(publishNewsVo.getId());
        }
        return publishNewsDto;
    }

}