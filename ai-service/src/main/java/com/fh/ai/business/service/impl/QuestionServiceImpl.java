package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.fh.ai.business.entity.bo.question.QuestionBo;
import com.fh.ai.business.entity.bo.question.QuestionConditionBo;
import com.fh.ai.business.entity.dto.question.QuestionDto;
import com.fh.ai.business.entity.vo.notice.NoticeVo;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import com.fh.ai.business.mapper.QuestionMapper;
import com.fh.ai.business.service.IQuestionService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 题库表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, QuestionDto> implements IQuestionService {

	@Resource
	private QuestionMapper questionMapper;
	
    @Override
	public Map<String, Object> getQuestionListByCondition(QuestionConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<QuestionVo> list = null;
		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = questionMapper.getQuestionListByCondition(conditionBo);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<QuestionVo> prizeVos = questionMapper.getQuestionListByCondition(conditionBo);
			PageInfo<QuestionVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}

		if(CollUtil.isNotEmpty(list)){
			for (QuestionVo vo:list){
				vo.setTitle(stripHtmlTags(vo.getTitle()));
			}
		}
		map.put("list", list);
		map.put("count", count);

		return map;
	}

	public static String stripHtmlTags(String html) {
		return html.replaceAll("<[^>]*>", "");
	}

	@Override
	public AjaxResult addQuestion(QuestionBo questionBo) {
		QuestionDto question = new QuestionDto();
		BeanUtils.copyProperties(questionBo, question);
		question.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(question)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateQuestion(QuestionBo questionBo) {
		QuestionDto question = new QuestionDto();
		BeanUtils.copyProperties(questionBo, question);
		if (question.getState() != null) {
			question.setUpdateTime(null);
		}
		if(updateById(question)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<QuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(QuestionDto::getId, id);
		QuestionDto question = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		QuestionVo questionVo = new QuestionVo();
		if(question != null){
			BeanUtils.copyProperties(question, questionVo);
			questionVo.setUserAnswerCount(questionMapper.userAnswerCount(question.getId()));
		}
		reuslt.put("questionVo", questionVo);
		return reuslt;
	}

}