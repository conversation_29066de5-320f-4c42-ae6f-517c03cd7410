package com.fh.ai.business.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.bo.book.RankingWeixinConditionBo;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;
import com.fh.ai.business.entity.vo.book.RankingWeixinVo;
import com.fh.ai.business.util.BookCommonUtil;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingDangdangBo;
import com.fh.ai.business.entity.bo.book.RankingDangdangConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDangdangDto;
import com.fh.ai.business.entity.vo.book.RankingDangdangVo;
import com.fh.ai.business.mapper.RankingDangdangMapper;
import com.fh.ai.business.service.IRankingDangdangService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 当当榜单表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingDangdangServiceImpl extends ServiceImpl<RankingDangdangMapper, RankingDangdangDto>
    implements IRankingDangdangService {

    @Resource
    private RankingDangdangMapper rankingDangdangMapper;

    @Override
    public Map<String, Object> getRankingDangdangListByConditionAndPage(RankingDangdangConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RankingDangdangVo> list = new ArrayList<>();
        // 查询最新一次榜单的批次id，当不传查询时间时，默认查询数据库中最新的数据
        if (Objects.equals(Boolean.TRUE,conditionBo.getQueryLatest()) || StringUtils.isBlank(conditionBo.getSearchDate())) {
            RankingDangdangVo rankingDangdangVo = rankingDangdangMapper.getLatestRankingDangdangDateUuid(conditionBo);
            if (Objects.isNull(rankingDangdangVo)){
                map.put("list", list);
                map.put("count", ConstantsInteger.NUM_0);
                return map;
            }
            conditionBo.setUuid(rankingDangdangVo.getUuid());
            conditionBo.setSearchDate(DateUtil.formatDate(rankingDangdangVo.getCreateTime()));

        }
        // 当查当天
        if (StringUtils.isNotBlank(conditionBo.getSearchDate())){
            conditionBo.setSearchTimeBegin(conditionBo.getSearchDate() + " 00:00:00");
            conditionBo.setSearchTimeEnd(conditionBo.getSearchDate() + " 23:59:59");
        }
        // 获取前一天的数据
        Map<Long, RankingDangdangVo> yesterMap = getYesterdayRankingDangdang(conditionBo);

        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            List<RankingDangdangVo> byCondition = rankingDangdangMapper.getRankingDangdangListWithBookByCondition(conditionBo);
            // 计算排名
            list = BookCommonUtil.calculateRankChange(yesterMap, byCondition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RankingDangdangVo> prizeVos =
                rankingDangdangMapper.getRankingDangdangListWithBookByCondition(conditionBo);
            PageInfo<RankingDangdangVo> pageInfo = new PageInfo<>(prizeVos);
            // 计算排名
            list = BookCommonUtil.calculateRankChange(yesterMap, pageInfo.getList());
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    /**
     * 获取前一天的数据
     * @param conditionBo
     * @return
     */
    private Map<Long, RankingDangdangVo> getYesterdayRankingDangdang(RankingDangdangConditionBo conditionBo){
        // 如果没有传时间，直接返回空，不做处理。
        if (StringUtils.isBlank(conditionBo.getSearchDate())) return null;
        //构造条件查询前一天的数据
        RankingDangdangConditionBo yesterdayCondition = new RankingDangdangConditionBo();
        BeanUtils.copyProperties(conditionBo, yesterdayCondition);
        // 获取前一天
        String yesterday = DateUtil.formatDateDefault(LocalDate.parse(conditionBo.getSearchDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                .minusDays(Constants.NUM_ONE));
        yesterdayCondition.setSearchTimeBegin(yesterday + " 00:00:00");
        yesterdayCondition.setSearchTimeEnd(yesterday + " 23:59:59");
        yesterdayCondition.setUuid(null);
        List<RankingDangdangVo> yesterdayDataList = rankingDangdangMapper.getRankingDangdangListWithBookByCondition(yesterdayCondition);
        Map<Long, RankingDangdangVo> yesterMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(yesterdayDataList)) {
            yesterMap = yesterdayDataList.stream().collect(Collectors.toMap(RankingDangdangVo::getBookId, x -> x, (a, b) -> b));
        }
        return yesterMap;
    }


    @Override
    public List<RankingDangdangVo> getRankingDangdangListByCondition(RankingDangdangConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingDangdangMapper.getRankingDangdangListByCondition(condition);
    }

    @Override
    public AjaxResult addRankingDangdang(RankingDangdangBo rankingDangdangBo) {
        RankingDangdangDto rankingDangdang = new RankingDangdangDto();
        BeanUtils.copyProperties(rankingDangdangBo, rankingDangdang);
        rankingDangdang.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(rankingDangdang)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRankingDangdang(RankingDangdangBo rankingDangdangBo) {
        RankingDangdangDto rankingDangdang = new RankingDangdangDto();
        BeanUtils.copyProperties(rankingDangdangBo, rankingDangdang);
        if (updateById(rankingDangdang)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public RankingDangdangVo getRankingDangdangByCondition(RankingDangdangConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        RankingDangdangVo vo = rankingDangdangMapper.getRankingDangdangByCondition(condition);
        return vo;
    }

}