package com.fh.ai.business.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.ai.business.entity.bo.book.RankingWeixinConditionBo;
import com.fh.ai.business.entity.vo.book.RankingWeixinVo;
import com.fh.ai.business.util.BookCommonUtil;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingDoubanBo;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDoubanDto;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;
import com.fh.ai.business.mapper.RankingDoubanMapper;
import com.fh.ai.business.service.IRankingDoubanService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 豆瓣榜单表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingDoubanServiceImpl extends ServiceImpl<RankingDoubanMapper, RankingDoubanDto> implements IRankingDoubanService {

	@Resource
	private RankingDoubanMapper rankingDoubanMapper;

	@Override
	public Map<String, Object> getRankingDoubanListByConditionAndPage(RankingDoubanConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<RankingDoubanVo> list = new ArrayList<>();
		// 查询最新一次榜单的批次id，不传查询时间则查数据库中最新的数据
		if (Objects.equals(Boolean.TRUE,conditionBo.getQueryLatest()) || StringUtils.isBlank(conditionBo.getSearchDate())) {
			RankingDoubanVo rankingDoubanVo= rankingDoubanMapper.getLatestRankingDoubanDateUuid(conditionBo);
			if (Objects.isNull(rankingDoubanVo)){
				map.put("list", list);
				map.put("count", ConstantsInteger.NUM_0);
				return map;
			}
			conditionBo.setUuid(rankingDoubanVo.getUuid());
			conditionBo.setSearchDate(DateUtil.formatDate(rankingDoubanVo.getCreateTime()));
		}
		if (StringUtils.isNotBlank(conditionBo.getSearchDate())){
			conditionBo.setSearchTimeBegin(conditionBo.getSearchDate() + " 00:00:00");
			conditionBo.setSearchTimeEnd(conditionBo.getSearchDate() + " 23:59:59");
		}
		// 查询前一天的数据
		Map<Long, RankingDoubanVo> yesterdayRankingDouban = getYesterdayRankingDouban(conditionBo);
		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			List<RankingDoubanVo> byCondition = rankingDoubanMapper.getRankingDoubanListWithBookByCondition(conditionBo);
			// 计算排名变化
			list = BookCommonUtil.calculateRankChange(yesterdayRankingDouban,byCondition);
			count = list.size();
		} else {
			// 分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<RankingDoubanVo> prizeVos =
					rankingDoubanMapper.getRankingDoubanListWithBookByCondition(conditionBo);
			PageInfo<RankingDoubanVo> pageInfo = new PageInfo<>(prizeVos);
			// 计算排名变化
			list = BookCommonUtil.calculateRankChange(yesterdayRankingDouban,pageInfo.getList());
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

	/**
	 * 获取前一天的排名
	 * @param conditionBo
	 * @return
	 */
	private Map<Long,RankingDoubanVo> getYesterdayRankingDouban(RankingDoubanConditionBo conditionBo){
		// 不传查询时间，则不计算排名，直接返回null。
		if (StringUtils.isBlank(conditionBo.getSearchDate())) return null;
		//构造条件查询前一天的数据
		RankingDoubanConditionBo yesterdayCondition = new RankingDoubanConditionBo();
		BeanUtils.copyProperties(conditionBo, yesterdayCondition);
		String yesterday = DateUtil.formatDateDefault(LocalDate.parse(conditionBo.getSearchDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))
				.minusDays(Constants.NUM_ONE));
		yesterdayCondition.setSearchTimeBegin(yesterday + " 00:00:00");
		yesterdayCondition.setSearchTimeEnd(yesterday + " 23:59:59");
		yesterdayCondition.setUuid(null);
		List<RankingDoubanVo> yesterdayDataList = rankingDoubanMapper.getRankingDoubanListWithBookByCondition(yesterdayCondition);
		Map<Long, RankingDoubanVo> yesterMap = new HashMap<>(16);
		if (CollectionUtils.isNotEmpty(yesterdayDataList)) {
			yesterMap = yesterdayDataList.stream().collect(Collectors.toMap(RankingDoubanVo::getBookId, x -> x, (a, b) -> b));
		}

		return yesterMap;
	}
	
    @Override
	public List<RankingDoubanVo> getRankingDoubanListByCondition(RankingDoubanConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingDoubanMapper.getRankingDoubanListByCondition(condition);
	}

	@Override
	public AjaxResult addRankingDouban(RankingDoubanBo rankingDoubanBo) {
		RankingDoubanDto rankingDouban = new RankingDoubanDto();
		BeanUtils.copyProperties(rankingDoubanBo, rankingDouban);
		rankingDouban.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(rankingDouban)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateRankingDouban(RankingDoubanBo rankingDoubanBo) {
		RankingDoubanDto rankingDouban = new RankingDoubanDto();
		BeanUtils.copyProperties(rankingDoubanBo, rankingDouban);
		if(updateById(rankingDouban)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public RankingDoubanVo getRankingDoubanByCondition(RankingDoubanConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		RankingDoubanVo vo = rankingDoubanMapper.getRankingDoubanByCondition(condition);
		return vo;
	}

}