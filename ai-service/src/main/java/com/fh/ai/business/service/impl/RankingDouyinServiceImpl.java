package com.fh.ai.business.service.impl;

import java.util.*;

import javax.annotation.Resource;

import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingDouyinBo;
import com.fh.ai.business.entity.bo.book.RankingDouyinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingDouyinDto;
import com.fh.ai.business.entity.vo.book.RankingDouyinVo;
import com.fh.ai.business.mapper.RankingDouyinMapper;
import com.fh.ai.business.service.IRankingDouyinService;
import com.fh.ai.common.vo.AjaxResult;
/**
 * 抖音榜单表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
@Service
public class RankingDouyinServiceImpl extends ServiceImpl<RankingDouyinMapper, RankingDouyinDto> implements IRankingDouyinService {

	@Resource
	private RankingDouyinMapper rankingDouyinMapper;
	
    @Override
	public List<RankingDouyinVo> getRankingDouyinListByCondition(RankingDouyinConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingDouyinMapper.getRankingDouyinListByCondition(condition);
	}

	@Override
	public AjaxResult addRankingDouyin(RankingDouyinBo rankingDouyinBo) {
		RankingDouyinDto rankingDouyin = new RankingDouyinDto();
		BeanUtils.copyProperties(rankingDouyinBo, rankingDouyin);
		rankingDouyin.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(rankingDouyin)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateRankingDouyin(RankingDouyinBo rankingDouyinBo) {
		RankingDouyinDto rankingDouyin = new RankingDouyinDto();
		BeanUtils.copyProperties(rankingDouyinBo, rankingDouyin);
		if(updateById(rankingDouyin)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public RankingDouyinVo getRankingDouyinByCondition(RankingDouyinConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		RankingDouyinVo vo = rankingDouyinMapper.getRankingDouyinByCondition(condition);
		return vo;
	}

	@Override
	public Map<String, Object> getRankingDouYinListByConditionAndPage(RankingDouyinConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<RankingDouyinVo> list = new ArrayList<>();
		// 如果startDate和endDate全都为null，则查询最新一次榜单
		if (Objects.equals(conditionBo.getQueryLatest(),Boolean.TRUE) ||
				(StringUtils.isBlank(conditionBo.getSearchTimeBegin()) && StringUtils.isBlank(conditionBo.getSearchTimeEnd()))) {
			// 获取数据库中最新收集的数据（按照collect_time排序）
			RankingDouyinVo latestData = rankingDouyinMapper.getLatestRankingDouyinDateUuid(conditionBo);
			if (Objects.isNull(latestData)){
				map.put("list", list);
				map.put("count", ConstantsInteger.NUM_0);
				return map;
			}
			Date collectTime = latestData.getCollectTime();
			if (Objects.nonNull(collectTime)){
				String formatDate = DateUtil.formatDate(collectTime);
				conditionBo.setSearchTimeBegin(formatDate+" 00:00:00");
				conditionBo.setSearchTimeEnd(formatDate+" 23:59:59");
			}else {
				conditionBo.setUuid(latestData.getUuid());
			}

		}

		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = rankingDouyinMapper.getRankingDouyinListByCondition(conditionBo);
			count = list.size();
		} else {
			// 分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<RankingDouyinVo> prizeVos =
					rankingDouyinMapper.getRankingDouyinListByCondition(conditionBo);
			PageInfo<RankingDouyinVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

}