package com.fh.ai.business.service.impl;

import java.util.*;

import javax.annotation.Resource;

import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingJingdongBo;
import com.fh.ai.business.entity.bo.book.RankingJingdongConditionBo;
import com.fh.ai.business.entity.dto.book.RankingJingdongDto;
import com.fh.ai.business.entity.vo.book.RankingJingdongVo;
import com.fh.ai.business.mapper.RankingJingdongMapper;
import com.fh.ai.business.service.IRankingJingdongService;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 京东榜单表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-11 13:46:54
 */
@Service
public class RankingJingdongServiceImpl extends ServiceImpl<RankingJingdongMapper, RankingJingdongDto> implements IRankingJingdongService {

    @Resource
    private RankingJingdongMapper rankingJingdongMapper;

    @Override
    public Map<String, Object> getRankingJingDongListByConditionAndPage(RankingJingdongConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RankingJingdongVo> list = new ArrayList<>();
        // 如果startDate和endDate全都为null，则查询最新一次榜单
        if (Objects.equals(conditionBo.getQueryLatest(),Boolean.TRUE) ||
                (StringUtils.isBlank(conditionBo.getSearchTimeBegin()) && StringUtils.isBlank(conditionBo.getSearchTimeEnd()))) {
            RankingJingdongVo latestData = rankingJingdongMapper.getLatestRankingJingdongDateUuid(conditionBo);
            if(Objects.isNull(latestData)){
                map.put("list", list);
                map.put("count", ConstantsInteger.NUM_0);
                return map;
            }
            Date collectTime = latestData.getCollectTime();
            if (Objects.nonNull(collectTime)){
                String formatDate = DateUtil.formatDate(collectTime);
                conditionBo.setSearchTimeBegin(formatDate+" 00:00:00");
                conditionBo.setSearchTimeEnd(formatDate+" 23:59:59");
            }else {
                conditionBo.setUuid(latestData.getUuid());
            }
        }
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = rankingJingdongMapper.getRankingJingdongListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RankingJingdongVo> prizeVos =
                    rankingJingdongMapper.getRankingJingdongListByCondition(conditionBo);
            PageInfo<RankingJingdongVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public List<RankingJingdongVo> getRankingJingdongListByCondition(RankingJingdongConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingJingdongMapper.getRankingJingdongListByCondition(condition);
    }

    @Override
    public AjaxResult addRankingJingdong(RankingJingdongBo rankingJingdongBo) {
        RankingJingdongDto rankingJingdong = new RankingJingdongDto();
        BeanUtils.copyProperties(rankingJingdongBo, rankingJingdong);
        rankingJingdong.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(rankingJingdong)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRankingJingdong(RankingJingdongBo rankingJingdongBo) {
        RankingJingdongDto rankingJingdong = new RankingJingdongDto();
        BeanUtils.copyProperties(rankingJingdongBo, rankingJingdong);
        if (updateById(rankingJingdong)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public RankingJingdongVo getRankingJingdongByCondition(RankingJingdongConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        RankingJingdongVo vo = rankingJingdongMapper.getRankingJingdongByCondition(condition);
        return vo;
    }

}