package com.fh.ai.business.service.impl;

import java.time.YearMonth;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingPpmbookBo;
import com.fh.ai.business.entity.bo.book.RankingPpmbookConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPpmbookDto;
import com.fh.ai.business.entity.vo.book.RankingPpmbookVo;
import com.fh.ai.business.mapper.RankingPpmbookMapper;
import com.fh.ai.business.service.IRankingPpmbookService;
import com.fh.ai.business.util.BookCommonUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.RankingType;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 凤凰书苑ppmbook榜单表（书苑）接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingPpmbookServiceImpl extends ServiceImpl<RankingPpmbookMapper, RankingPpmbookDto>
    implements IRankingPpmbookService {

    @Resource
    private RankingPpmbookMapper rankingPpmbookMapper;

    @Override
    public Map<String, Object> getRankingPpmbookListByConditionAndPage(RankingPpmbookConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RankingPpmbookVo> list = null;
        // 查询最新一次榜单的批次id
        if (conditionBo.getQueryLatest() != null && conditionBo.getQueryLatest()) {
            // 畅销书和好书则查询最新一个年月的数据
            if (conditionBo.getRankingType() != null
                && (conditionBo.getRankingType().equals(RankingType.PPMBOOK_CX_MONTH.getValue())
                    || conditionBo.getRankingType().equals(RankingType.PPMBOOK_HS_MONTH.getValue()))) {
                RankingPpmbookConditionBo rankingPpmbookConditionBo = new RankingPpmbookConditionBo();
                rankingPpmbookConditionBo.setRankingType(conditionBo.getRankingType());
                RankingPpmbookVo latestRankingPpmbookInfo =
                    rankingPpmbookMapper.getLatestRankingPpmbookInfo(rankingPpmbookConditionBo);
                if (latestRankingPpmbookInfo != null) {
                    conditionBo.setRankingYear(latestRankingPpmbookInfo.getRankingYear());
                    conditionBo.setRankingMonth(latestRankingPpmbookInfo.getRankingMonth());
                }
            }
        }
        // 获取上个月的数据
        Map<Long, RankingPpmbookVo> lastMonthMap = getLastMonthRankingPpmbook(conditionBo);
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            List<RankingPpmbookVo> byCondition =
                rankingPpmbookMapper.getRankingPpmbookListWithBookByCondition(conditionBo);
            // 计算排名变化
            list = BookCommonUtil.calculateRankChange(lastMonthMap, byCondition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RankingPpmbookVo> prizeVos =
                rankingPpmbookMapper.getRankingPpmbookListWithBookByCondition(conditionBo);
            PageInfo<RankingPpmbookVo> pageInfo = new PageInfo<>(prizeVos);
            list = BookCommonUtil.calculateRankChange(lastMonthMap, pageInfo.getList());
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    /**
     * 查询上个月的数据
     * 
     * @param conditionBo
     * @return
     */
    private Map<Long, RankingPpmbookVo> getLastMonthRankingPpmbook(RankingPpmbookConditionBo conditionBo) {
        // 不传查询时间，则不计算排名变化，直接返回null。
        if (Objects.isNull(conditionBo.getRankingYear())) {
            return null;
        }
        // 构造条件查询前一个月的数据
        RankingPpmbookConditionBo yesterdayCondition = new RankingPpmbookConditionBo();
        BeanUtils.copyProperties(conditionBo, yesterdayCondition);
        // 查询上个月
        // 上个月
        YearMonth yearMonth =
            YearMonth.of(conditionBo.getRankingYear().intValue(), conditionBo.getRankingMonth().intValue());
        yesterdayCondition.setRankingMonth((long)yearMonth.minusMonths(1L).getMonthValue());
        yesterdayCondition.setUuid(null);
        List<RankingPpmbookVo> lastMonthDataList =
            rankingPpmbookMapper.getRankingPpmbookListWithBookByCondition(yesterdayCondition);
        Map<Long, RankingPpmbookVo> lastMonthMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(lastMonthDataList)) {
            lastMonthMap =
                lastMonthDataList.stream().collect(Collectors.toMap(RankingPpmbookVo::getBookId, x -> x, (a, b) -> b));
        }
        return lastMonthMap;

    }

    @Override
    public List<RankingPpmbookVo> getRankingPpmbookListByCondition(RankingPpmbookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingPpmbookMapper.getRankingPpmbookListByCondition(condition);
    }

    @Override
    public AjaxResult addRankingPpmbook(RankingPpmbookBo rankingPpmbookBo) {
        RankingPpmbookDto rankingPpmbook = new RankingPpmbookDto();
        BeanUtils.copyProperties(rankingPpmbookBo, rankingPpmbook);
        rankingPpmbook.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(rankingPpmbook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRankingPpmbook(RankingPpmbookBo rankingPpmbookBo) {
        RankingPpmbookDto rankingPpmbook = new RankingPpmbookDto();
        BeanUtils.copyProperties(rankingPpmbookBo, rankingPpmbook);
        if (updateById(rankingPpmbook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public RankingPpmbookVo getRankingPpmbookByCondition(RankingPpmbookConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        RankingPpmbookVo vo = rankingPpmbookMapper.getRankingPpmbookByCondition(condition);
        return vo;
    }

}