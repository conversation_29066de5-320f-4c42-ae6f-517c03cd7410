package com.fh.ai.business.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingPremiumBo;
import com.fh.ai.business.entity.bo.book.RankingPremiumConditionBo;
import com.fh.ai.business.entity.dto.book.RankingPremiumDto;
import com.fh.ai.business.entity.vo.book.RankingPremiumVo;
import com.fh.ai.business.mapper.RankingPremiumMapper;
import com.fh.ai.business.service.IRankingPremiumService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 精品书（奖项）表（不含凤凰好书）接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingPremiumServiceImpl extends ServiceImpl<RankingPremiumMapper, RankingPremiumDto> implements IRankingPremiumService {

	@Resource
	private RankingPremiumMapper rankingPremiumMapper;
	
    @Override
	public List<RankingPremiumVo> getRankingPremiumListByCondition(RankingPremiumConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingPremiumMapper.getRankingPremiumListByCondition(condition);
	}

	@Override
	public AjaxResult addRankingPremium(RankingPremiumBo rankingPremiumBo) {
		RankingPremiumDto rankingPremium = new RankingPremiumDto();
		BeanUtils.copyProperties(rankingPremiumBo, rankingPremium);
		rankingPremium.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(rankingPremium)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateRankingPremium(RankingPremiumBo rankingPremiumBo) {
		RankingPremiumDto rankingPremium = new RankingPremiumDto();
		BeanUtils.copyProperties(rankingPremiumBo, rankingPremium);
		if(updateById(rankingPremium)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public RankingPremiumVo getRankingPremiumByCondition(RankingPremiumConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		RankingPremiumVo vo = rankingPremiumMapper.getRankingPremiumByCondition(condition);
		return vo;
	}

	@Override
	public Map<String, Object> getRankingPremiumListByConditionAndPage(RankingPremiumConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<RankingPremiumVo> list = new ArrayList<>();
		long count = 0L;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = rankingPremiumMapper.getRankingPremiumListByCondition(conditionBo);
			count = list.size();
		} else {
			// 分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<RankingPremiumVo> rankingPremiumVos =
					rankingPremiumMapper.getRankingPremiumListByCondition(conditionBo);
			PageInfo<RankingPremiumVo> pageInfo = new PageInfo<>(rankingPremiumVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}
}