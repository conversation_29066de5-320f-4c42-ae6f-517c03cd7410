package com.fh.ai.business.service.impl;

import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.ai.business.util.BookCommonUtil;
import com.fh.ai.common.constants.ConstantsInteger;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingSmartBo;
import com.fh.ai.business.entity.bo.book.RankingSmartConditionBo;
import com.fh.ai.business.entity.dto.book.RankingSmartDto;
import com.fh.ai.business.entity.vo.book.RankingSmartVo;
import com.fh.ai.business.mapper.RankingSmartMapper;
import com.fh.ai.business.service.IRankingSmartService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;

/**
 * 凤凰本版畅销书表-有销售数据（开卷）接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingSmartServiceImpl extends ServiceImpl<RankingSmartMapper, RankingSmartDto> implements IRankingSmartService {

    @Resource
    private RankingSmartMapper rankingSmartMapper;

    @Override
    public List<RankingSmartVo> getRankingSmartListByCondition(RankingSmartConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingSmartMapper.getRankingSmartListByCondition(condition);
    }

    @Override
    public AjaxResult addRankingSmart(RankingSmartBo rankingSmartBo) {
        RankingSmartDto rankingSmart = new RankingSmartDto();
        BeanUtils.copyProperties(rankingSmartBo, rankingSmart);
        rankingSmart.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(rankingSmart)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRankingSmart(RankingSmartBo rankingSmartBo) {
        RankingSmartDto rankingSmart = new RankingSmartDto();
        BeanUtils.copyProperties(rankingSmartBo, rankingSmart);
        if (updateById(rankingSmart)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public RankingSmartVo getRankingSmartByCondition(RankingSmartConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        RankingSmartVo vo = rankingSmartMapper.getRankingSmartByCondition(condition);
        return vo;
    }

    /**
     * 查询开卷榜单表分页列表
     *
     * @param conditionBo
     * @return
     */
    @Override
    public Map<String, Object> getRankingSmartListByConditionAndPage(RankingSmartConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(16);
        List<RankingSmartVo> list = new ArrayList<>();
        // 没有传时间或者需要查最新的数据
        if (Objects.equals(conditionBo.getQueryLatest(), Boolean.TRUE) || Objects.isNull(conditionBo.getRankingYear())) {
            RankingSmartVo rankingSmartVo = rankingSmartMapper.getLatestRankingSmartUuid(conditionBo);
            if (Objects.isNull(rankingSmartVo)){
                map.put("list", list);
                map.put("count", ConstantsInteger.NUM_0);
                return map;
            }
            conditionBo.setUuid(rankingSmartVo.getUuid());
            // 凤凰本版书默认查最新的数据，因为前端不清楚最新数据的时间范围，需要手动设置查询时间范围，以此计算排名变化。
            conditionBo.setRankingYear(rankingSmartVo.getRankingYear());
            conditionBo.setRankingMonth(rankingSmartVo.getRankingMonth());
        }
        if (StringUtils.isNotBlank(conditionBo.getSearchDate())) {
            conditionBo.setSearchTimeBegin(conditionBo.getSearchDate() + " 00:00:00");
            conditionBo.setSearchTimeEnd(conditionBo.getSearchDate() + " 23:59:59");
        }
        Map<Long, RankingSmartVo> lastMonthMap = getLastMonthRankingSmartbook(conditionBo);
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            List<RankingSmartVo> byCondition = rankingSmartMapper.getRankingSmartListWithBookByCondition(conditionBo);
            // 计算排名
            list = BookCommonUtil.calculateRankChange(lastMonthMap, byCondition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RankingSmartVo> prizeVos =
                    rankingSmartMapper.getRankingSmartListWithBookByCondition(conditionBo);
            PageInfo<RankingSmartVo> pageInfo = new PageInfo<>(prizeVos);
            list = BookCommonUtil.calculateRankChange(lastMonthMap, pageInfo.getList());
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }


    /**
     * 获取上个月的排名数据
     * @param conditionBo
     * @return
     */
    private Map<Long, RankingSmartVo> getLastMonthRankingSmartbook(RankingSmartConditionBo conditionBo){
        // 如果不查询上个月的数据，直接返回null
        if (Objects.isNull(conditionBo.getRankingYear())){
            return null;
        }
        //构造条件查询前一个月的数据
        RankingSmartConditionBo lastMonthCondition = new RankingSmartConditionBo();
        BeanUtils.copyProperties(conditionBo, lastMonthCondition);
        // 上个月
        YearMonth yearMonth = YearMonth.of(conditionBo.getRankingYear().intValue(), conditionBo.getRankingMonth().intValue());
        lastMonthCondition.setRankingMonth((long)yearMonth.minusMonths(1L).getMonthValue());
        lastMonthCondition.setUuid(null);
        //
        List<RankingSmartVo> lastMonthDataList = rankingSmartMapper.getRankingSmartListWithBookByCondition(lastMonthCondition);
        Map<Long, RankingSmartVo> lastMonthMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(lastMonthDataList)) {
            lastMonthMap = lastMonthDataList.stream().collect(Collectors.toMap(RankingSmartVo::getBookId, x -> x, (a, b) -> b));
        }
        return lastMonthMap;

    }

}