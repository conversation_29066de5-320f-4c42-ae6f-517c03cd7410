package com.fh.ai.business.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.beust.jcommander.internal.Lists;
import com.fh.ai.business.entity.bo.book.RankingDoubanConditionBo;
import com.fh.ai.business.entity.vo.book.RankingDoubanVo;
import com.fh.ai.business.util.BookCommonUtil;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.book.RankingWeixinBo;
import com.fh.ai.business.entity.bo.book.RankingWeixinConditionBo;
import com.fh.ai.business.entity.dto.book.RankingWeixinDto;
import com.fh.ai.business.entity.vo.book.RankingWeixinVo;
import com.fh.ai.business.mapper.RankingWeixinMapper;
import com.fh.ai.business.service.IRankingWeixinService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 微信榜单表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Service
public class RankingWeixinServiceImpl extends ServiceImpl<RankingWeixinMapper, RankingWeixinDto>
        implements IRankingWeixinService {

    @Resource
    private RankingWeixinMapper rankingWeixinMapper;

    @Override
    public Map<String, Object> getRankingWeixinListByConditionAndPage(RankingWeixinConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RankingWeixinVo> list = Lists.newArrayList();
        // 查询最新一次榜单的批次id，如果没有传时间，默认查数据库最新的数据
        if (Objects.equals(Boolean.TRUE,conditionBo.getQueryLatest()) || StringUtils.isBlank(conditionBo.getSearchDate())) {
            RankingWeixinVo rankingWeixinVo = rankingWeixinMapper.getLatestRankingWeixinDateUuid(conditionBo);
            if (rankingWeixinVo == null) {
                map.put("list", list);
                map.put("count", ConstantsInteger.NUM_0);
                return map;
            }
            conditionBo.setUuid(rankingWeixinVo.getUuid());
            conditionBo.setSearchDate(DateUtil.formatDate(rankingWeixinVo.getCreateTime()));
        }
        // 若传查询时间，则查一天的数据
        if (StringUtils.isNotBlank(conditionBo.getSearchDate())){
            conditionBo.setSearchTimeBegin(conditionBo.getSearchDate() + " 00:00:00");
            conditionBo.setSearchTimeEnd(conditionBo.getSearchDate() + " 23:59:59");
        }
        // 获取前一天数据
        Map<Long, RankingWeixinVo> yesterMap = getYesterdayRankingWeixin(conditionBo);

        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            List<RankingWeixinVo> byCondition = rankingWeixinMapper.getRankingWeixinListWithBookByCondition(conditionBo);
            list = BookCommonUtil.calculateRankChange(yesterMap, byCondition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RankingWeixinVo> prizeVos = rankingWeixinMapper.getRankingWeixinListWithBookByCondition(conditionBo);
            PageInfo<RankingWeixinVo> pageInfo = new PageInfo<>(prizeVos);
            list = BookCommonUtil.calculateRankChange(yesterMap, pageInfo.getList());
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    /**
     * 获取前一天的排名
     * @param conditionBo
     * @return
     */
    private Map<Long, RankingWeixinVo> getYesterdayRankingWeixin(RankingWeixinConditionBo conditionBo){
        // 如果不传时间，则不计算排名
        if (StringUtils.isBlank(conditionBo.getSearchDate())) return null;
        //构造条件查询前一天的数据
        RankingWeixinConditionBo yesterdayCondition = new RankingWeixinConditionBo();
        BeanUtils.copyProperties(conditionBo, yesterdayCondition);
        // 计算前一天
        String yesterday = DateUtil.formatDateDefault(LocalDate.parse(conditionBo.getSearchDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                .minusDays(Constants.NUM_ONE));
        yesterdayCondition.setSearchTimeBegin(yesterday + " 00:00:00");
        yesterdayCondition.setSearchTimeEnd(yesterday + " 23:59:59");
        yesterdayCondition.setUuid(null);
        List<RankingWeixinVo> yesterdayDataList = rankingWeixinMapper.getRankingWeixinListWithBookByCondition(yesterdayCondition);
        Map<Long, RankingWeixinVo> yesterMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(yesterdayDataList)) {
            yesterMap = yesterdayDataList.stream().collect(Collectors.toMap(RankingWeixinVo::getBookId, x -> x, (a, b) -> b));
        }

        return yesterMap;
    }

    @Override
    public List<RankingWeixinVo> getRankingWeixinListByCondition(RankingWeixinConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return rankingWeixinMapper.getRankingWeixinListByCondition(condition);
    }

    @Override
    public AjaxResult addRankingWeixin(RankingWeixinBo rankingWeixinBo) {
        RankingWeixinDto rankingWeixin = new RankingWeixinDto();
        BeanUtils.copyProperties(rankingWeixinBo, rankingWeixin);
        rankingWeixin.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(rankingWeixin)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRankingWeixin(RankingWeixinBo rankingWeixinBo) {
        RankingWeixinDto rankingWeixin = new RankingWeixinDto();
        BeanUtils.copyProperties(rankingWeixinBo, rankingWeixin);
        if (updateById(rankingWeixin)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public RankingWeixinVo getRankingWeixinByCondition(RankingWeixinConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        RankingWeixinVo vo = rankingWeixinMapper.getRankingWeixinByCondition(condition);
        return vo;
    }

}