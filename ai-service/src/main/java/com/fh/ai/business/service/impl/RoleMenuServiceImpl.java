package com.fh.ai.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.roleMenu.RoleMenuBo;
import com.fh.ai.business.entity.bo.roleMenu.RoleMenuConditionBo;
import com.fh.ai.business.entity.dto.roleMenu.RoleMenuDto;
import com.fh.ai.business.entity.vo.menu.MenuVo;
import com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo;
import com.fh.ai.business.mapper.MenuMapper;
import com.fh.ai.business.mapper.RoleMenuMapper;
import com.fh.ai.business.service.IRoleMenuService;
import com.fh.ai.common.enums.RedisKeyEnum;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色菜单接口实现类
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenuDto> implements IRoleMenuService {

    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private RedisComponent redisComponent;


    @PostConstruct
    public void init(){
        Map<String,Object> roleMenuMap = new HashMap<>();
        List<RoleMenuVo> allRoleMenu = roleMenuMapper.getAllRoleMenu();
        if (CollectionUtils.isNotEmpty(allRoleMenu)){
            Map<Long, List<RoleMenuVo>> roleIdMenuIdMap = allRoleMenu.stream().collect(Collectors.groupingBy(RoleMenuVo::getRoleId));
            for (Map.Entry<Long, List<RoleMenuVo>> entry : roleIdMenuIdMap.entrySet()){
                List<RoleMenuVo> value = entry.getValue();
                List<MenuVo> menuVos = value.stream().map(x -> menuMapper.getMenuVoById(x.getMenuId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(menuVos)){
                    roleMenuMap.put(String.valueOf(entry.getKey()),menuVos);
                }
            }
        }
        if (MapUtils.isNotEmpty(roleMenuMap)){
            redisComponent.hmset(RedisKeyEnum.ROLE_MENU_KET.getValue(),roleMenuMap);
        }
    }

    @Override
    public List<MenuVo> getRoleMenuVosByKey(Long roleId){
        String roleIdStr = String.valueOf(roleId);
        if (!redisComponent.hasKey(RedisKeyEnum.ROLE_MENU_KET.getValue())){
            init();
        }
        if (!redisComponent.hHasKey(RedisKeyEnum.ROLE_MENU_KET.getValue(),roleIdStr)){
            // 找出角色对应的菜单id
            List<RoleMenuVo> roleMenus = roleMenuMapper.getRoleMenuByRoleId(roleId);
            if (CollectionUtils.isEmpty(roleMenus)){
                return null;
            }else {
                List<MenuVo> menuVoList = roleMenus.stream().map(x -> menuMapper.getMenuVoById(x.getMenuId())).collect(Collectors.toList());
                Map<String,Object> map = new HashMap<>();
                map.put(roleIdStr,menuVoList);
                redisComponent.hmset(RedisKeyEnum.ROLE_MENU_KET.getValue(),map);
                return menuVoList;
            }
        }
        Object hget = redisComponent.hget(RedisKeyEnum.ROLE_MENU_KET.getValue(), roleIdStr);
        return JSON.parseArray(JSON.toJSONString(hget), MenuVo.class);
    }

    @Override
    public Map<String, Object> getRoleMenuListByCondition(RoleMenuConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RoleMenuVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            //page为0，则不分页（查询全部）
            list = roleMenuMapper.getRoleMenuListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RoleMenuVo> roleMenuVos = roleMenuMapper.getRoleMenuListByCondition(conditionBo);
            PageInfo<RoleMenuVo> pageInfo = new PageInfo<>(roleMenuVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addRoleMenu(RoleMenuBo roleMenuBo) {
        RoleMenuDto roleMenu = new RoleMenuDto();
        BeanUtils.copyProperties(roleMenuBo, roleMenu);
        if (save(roleMenu)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateRoleMenu(RoleMenuBo roleMenuBo) {
        RoleMenuDto roleMenu = new RoleMenuDto();
        BeanUtils.copyProperties(roleMenuBo, roleMenu);
        if (updateById(roleMenu)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<RoleMenuDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RoleMenuDto::getId, id);
        RoleMenuDto roleMenu = getOne(lqw);

        if (null == roleMenu) {
            return AjaxResult.fail("角色权限不存在");
        }

        RoleMenuVo roleMenuVo = new RoleMenuVo();
        BeanUtils.copyProperties(roleMenu, roleMenuVo);

        return AjaxResult.success(roleMenuVo);
    }

}