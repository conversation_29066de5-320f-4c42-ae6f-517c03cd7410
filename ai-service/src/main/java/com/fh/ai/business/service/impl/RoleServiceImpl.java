package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.role.RoleBo;
import com.fh.ai.business.entity.bo.role.RoleListConditionBo;
import com.fh.ai.business.service.IAdminRoleService;
import com.fh.ai.business.service.IRoleMenuService;
import com.fh.ai.business.service.IRoleService;
import com.fh.ai.business.entity.dto.role.RoleDto;
import com.fh.ai.business.entity.dto.roleMenu.RoleMenuDto;
import com.fh.ai.business.entity.vo.role.RoleVo;
import com.fh.ai.business.mapper.RoleMapper;
import com.fh.ai.business.entity.dto.adminRole.AdminRoleDto;
import com.fh.ai.common.enums.IsLocked;
import com.fh.ai.common.enums.RedisKeyEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.jedis.JedisUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
@Service
@Slf4j
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleDto> implements IRoleService {

    @Resource
    private JedisUtil jedisUtil;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private IRoleMenuService roleMenuService;

    @Resource
    private IAdminRoleService adminRoleService;

    @Override
    public Map<String, Object> getRoleListByCondition(RoleListConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<RoleVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            //page为0，则不分页（查询全部）
            list = roleMapper.getRoleListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<RoleVo> adminRoleVos = roleMapper.getRoleListByCondition(conditionBo);
            PageInfo<RoleVo> pageInfo = new PageInfo<>(adminRoleVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addRole(RoleBo roleBo) {
        // 判断角色名称重复
        LambdaQueryWrapper<RoleDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RoleDto::getRoleName, roleBo.getRoleName());
        lqw.eq(RoleDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        RoleDto roleDto = roleMapper.selectOne(lqw);
        if (null != roleDto) {
            return AjaxResult.fail("角色名称重复");
        }

        // 新增
        RoleDto role = new RoleDto();
        BeanUtils.copyProperties(roleBo, role);
        role.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        role.setIsLocked(IsLocked.NO.getCode());
        save(role);

        // 新增角色菜单对应关系
        List<Long> menuIds = roleBo.getMenuIds();
        if (CollectionUtil.isNotEmpty(menuIds)) {
            List<RoleMenuDto> roleMenuDtos = Lists.newArrayList();
            Date now = new Date();
            for (Long menuId : menuIds) {
                RoleMenuDto roleMenuDto = new RoleMenuDto();
                roleMenuDto.setRoleId(role.getId());
                roleMenuDto.setMenuId(menuId);
                roleMenuDto.setCreateTime(now);

                roleMenuDtos.add(roleMenuDto);
            }

            roleMenuService.saveBatch(roleMenuDtos);
        }

        return AjaxResult.success("成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateRole(RoleBo roleBo) {
        // 判断角色名称重复
        LambdaQueryWrapper<RoleDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RoleDto::getRoleName, roleBo.getRoleName());
        lqw.eq(RoleDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.ne(RoleDto::getId, roleBo.getId());
        RoleDto roleDto = roleMapper.selectOne(lqw);
        if (null != roleDto) {
            return AjaxResult.fail("角色名称重复");
        }

        RoleDto role = new RoleDto();
        BeanUtils.copyProperties(roleBo, role);
        updateById(role);

        // 更新菜单
        LambdaQueryWrapper<RoleMenuDto> rmLqw = new LambdaQueryWrapper<>();
        rmLqw.eq(RoleMenuDto::getRoleId, role.getId());
        roleMenuService.remove(rmLqw);

        // 新增角色菜单对应关系
        List<Long> menuIds = roleBo.getMenuIds();
        if (CollectionUtil.isNotEmpty(menuIds)) {
            List<RoleMenuDto> roleMenuDtos = Lists.newArrayList();
            Date now = new Date();
            for (Long menuId : menuIds) {
                RoleMenuDto roleMenuDto = new RoleMenuDto();
                roleMenuDto.setRoleId(role.getId());
                roleMenuDto.setMenuId(menuId);
                roleMenuDto.setCreateTime(now);

                roleMenuDtos.add(roleMenuDto);
            }

            roleMenuService.saveBatch(roleMenuDtos);
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateState(RoleBo roleBo) {
        // 判断角色名称重复
        RoleDto roleDto = roleMapper.selectById(roleBo.getId());
        if (null == roleDto) {
            return AjaxResult.fail("角色不存在");
        }

        RoleDto role = new RoleDto();
        role.setId(roleBo.getId());
        role.setIsLocked(roleBo.getIsLocked());
        role.setUpdateTime(new Date());
        updateById(role);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteRole(RoleBo roleBo) {
        // 判断角色名称重复
        RoleDto roleDto = roleMapper.selectById(roleBo.getId());
        if (null == roleDto) {
            return AjaxResult.fail("角色不存在");
        }

        // 删除角色菜单关系
        LambdaQueryWrapper<RoleMenuDto> rmLqw = new LambdaQueryWrapper<>();
        rmLqw.eq(RoleMenuDto::getRoleId, roleBo.getId());
        roleMenuService.remove(rmLqw);

        // 删除角色账号关系
        LambdaQueryWrapper<AdminRoleDto> urLqw = new LambdaQueryWrapper<>();
        urLqw.eq(AdminRoleDto::getRoleId, roleBo.getId());
        adminRoleService.remove(urLqw);

        // 删除角色
        roleMapper.deleteById(roleBo.getId());

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Integer id) {
        LambdaQueryWrapper<RoleDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RoleDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(RoleDto::getId, id);
        RoleDto role = getOne(lqw);
        if (null == role) {
            return AjaxResult.fail("角色不存在");
        }

        RoleVo roleVo = new RoleVo();
        BeanUtils.copyProperties(role, roleVo);

        LambdaQueryWrapper<RoleMenuDto> roleMenuLqw = new LambdaQueryWrapper<>();
        roleMenuLqw.eq(RoleMenuDto::getRoleId, role.getId());
        List<RoleMenuDto> roleMenuDtos = roleMenuService.list(roleMenuLqw);
        if (CollectionUtil.isNotEmpty(roleMenuDtos)) {
            List<Long> menuIds = roleMenuDtos.stream().map(t -> t.getMenuId()).collect(Collectors.toList());
            roleVo.setMenuIds(menuIds);
        }

        return AjaxResult.success(roleVo);
    }

    @Override
    public List<RoleDto> getRoleListByUserId(Integer userId) {
        return roleMapper.getRoleListByUserId(userId);
    }

    @Override
    public RoleDto getRoleByIdCache(Integer roleId) {
        RoleDto role = null;
        try {
            role = jedisUtil.hgetObj(RedisKeyEnum.ROLE.getValue(), roleId.toString(), RoleDto.class);
        } catch (RuntimeException e) {
            log.warn("反序列化发生了错误{}", e.getMessage());
        }
        if (role == null) {
            role = getRoleById(roleId);
            if (role == null) {
                throw new BusinessException("无此角色");
            } else {
                jedisUtil.hsetObj(RedisKeyEnum.ROLE.getValue(), roleId.toString(), role);
            }
        }
        return role;
    }

    /**
     * 通过角色ID获取角色信息
     *
     * @param roleId
     * @return
     */
    private RoleDto getRoleById(Integer roleId) {
        return getById(roleId);
    }
}