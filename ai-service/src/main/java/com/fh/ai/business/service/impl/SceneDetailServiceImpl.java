package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.sceneDetail.SceneDetailBo;
import com.fh.ai.business.entity.bo.sceneDetail.SceneDetailConditionBo;
import com.fh.ai.business.entity.dto.sceneDetail.SceneDetailDto;
import com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo;
import com.fh.ai.business.mapper.SceneDetailMapper;
import com.fh.ai.business.service.ISceneDetailService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

/**
 * 场景详情表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:26
 */
@Service
public class SceneDetailServiceImpl extends ServiceImpl<SceneDetailMapper, SceneDetailDto> implements ISceneDetailService {

	@Resource
	private SceneDetailMapper sceneDetailMapper;
	
    @Override
	public List<SceneDetailVo> getSceneDetailListByCondition(SceneDetailConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return sceneDetailMapper.getSceneDetailListByCondition(condition);
	}

	@Override
	public AjaxResult addSceneDetail(SceneDetailBo sceneDetailBo) {
		SceneDetailDto sceneDetail = new SceneDetailDto();
		BeanUtils.copyProperties(sceneDetailBo, sceneDetail);
		sceneDetail.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(sceneDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSceneDetail(SceneDetailBo sceneDetailBo) {
		SceneDetailDto sceneDetail = new SceneDetailDto();
		BeanUtils.copyProperties(sceneDetailBo, sceneDetail);
		if(updateById(sceneDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SceneDetailVo getSceneDetailByCondition(SceneDetailConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return sceneDetailMapper.getSceneDetailByCondition(condition);
	}

}