package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.scene.SceneBo;
import com.fh.ai.business.entity.bo.scene.SceneConditionBo;
import com.fh.ai.business.entity.dto.scene.SceneDto;
import com.fh.ai.business.entity.vo.scene.SceneVo;
import com.fh.ai.business.entity.vo.userInstruction.UserInstructionVo;
import com.fh.ai.business.mapper.SceneMapper;
import com.fh.ai.business.service.ISceneService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

/**
 * 场景表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:12
 */
@Service
public class SceneServiceImpl extends ServiceImpl<SceneMapper, SceneDto> implements ISceneService {

	@Resource
	private SceneMapper sceneMapper;
	
    @Override
	public Map<String, Object> getSceneListByCondition(SceneConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		Map<String, Object> map = new HashMap<>(4);
		List<SceneVo> list = Lists.newArrayList();
		long count = 0L;
		if (null == condition.getPage() || null == condition.getLimit()) {
			list = sceneMapper.getSceneListByCondition(condition);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
			List<SceneVo> userInstructionVos = sceneMapper.getSceneListByCondition(condition);
			PageInfo<SceneVo> pageInfo = new PageInfo<>(userInstructionVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

	@Override
	public AjaxResult addScene(SceneBo sceneBo) {
		SceneDto scene = new SceneDto();
		BeanUtils.copyProperties(sceneBo, scene);
		scene.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(scene)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScene(SceneBo sceneBo) {
		SceneDto scene = new SceneDto();
		BeanUtils.copyProperties(sceneBo, scene);
		if(updateById(scene)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SceneVo getSceneByCondition(SceneConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return sceneMapper.getSceneByCondition(condition);
	}

	@Override
	public Map<String, Object> getSceneListWithDetailList(SceneConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		Map<String, Object> map = new HashMap<>(4);
		List<SceneVo> list = Lists.newArrayList();
		long count = 0L;
		if (null == condition.getPage() || null == condition.getLimit()) {
			list = sceneMapper.getSceneListWithDetailList(condition);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(condition.getPage(), condition.getLimit());
			List<SceneVo> userInstructionVos = sceneMapper.getSceneListWithDetailList(condition);
			PageInfo<SceneVo> pageInfo = new PageInfo<>(userInstructionVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}
		map.put("list", list);
		map.put("count", count);
		return map;
	}

}