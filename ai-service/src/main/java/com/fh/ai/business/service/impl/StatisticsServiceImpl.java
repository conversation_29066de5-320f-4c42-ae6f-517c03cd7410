package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.statistics.StatisticsBo;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.bo.user.UserConditionBo;
import com.fh.ai.business.entity.dto.conversation.ConversationDto;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.dto.modelUseHistory.ModelUseHistoryDto;
import com.fh.ai.business.entity.dto.statistics.StatisticsDto;
import com.fh.ai.business.entity.dto.statisticsUsage.StatisticsUsageDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.dto.userVisit.UserVisitDto;
import com.fh.ai.business.entity.vo.statistics.*;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.IStatisticsService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsNew;
import com.fh.ai.common.enums.ModelUseType;
import com.fh.ai.common.enums.UserVisitTypeEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计表接口实现类
 *
 * <AUTHOR>
 * @date 2024-05-15 14:17:53
 */
@Service
public class StatisticsServiceImpl extends ServiceImpl<StatisticsMapper, StatisticsDto> implements IStatisticsService {

    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private StatisticsUsageMapper statisticsUsageMapper;

    @Resource
    private UserVisitMapper userVisitMapper;

    @Resource
    private UserScoreRecordMapper userScoreRecordMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private ConversationMapper conversationMapper;

    @Resource
    private HistoryAppMapper historyAppMapper;

    @Autowired
    private ModelUseHistoryMapper modelUseHistoryMapper;

    @Override
    public StatisticsSettingVo getStatisticsSetting() {
        LambdaQueryWrapper<StatisticsDto> lqw = new LambdaQueryWrapper<>();
        lqw.orderByDesc(StatisticsDto::getUpdateTime);
        lqw.last("limit 1");
        StatisticsDto statisticsDto = statisticsMapper.selectOne(lqw);

        StatisticsSettingVo statisticsSettingVo = new StatisticsSettingVo();
        if (null != statisticsDto) {
            statisticsSettingVo.setLastUpdateTime(statisticsDto.getUpdateTime());
        }

        return statisticsSettingVo;
    }

    @Override
    public SysStatisticsInfoVo getSysStatistics(StatisticsConditionBo conditionBo) {
        SysStatisticsInfoVo sysStatisticsInfo = statisticsMapper.getSysStatistics(conditionBo);

//        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
//        lqw.gt(UserDto::getOrganizationId, 0L);
//
//        List<UserDto> userDtos = userMapper.selectList(lqw);

        UserConditionBo userConditionBo = new UserConditionBo();
        userConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        userConditionBo.setOrganizationId(conditionBo.getOrganizationId());
        List<UserVo> userVos = userMapper.getUserListByCondition(userConditionBo);

        //总会话数
        Long count = conversationMapper.selectCount(new LambdaQueryWrapper<ConversationDto>()
                .eq(ConversationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                .ge(ConversationDto::getCreateTime, conditionBo.getStartTime())
                .le(ConversationDto::getCreateTime, conditionBo.getEndTime())
        );

        if (count != 0) {
            //有用
            Long usefulCount = historyAppMapper.selectCount(new LambdaQueryWrapper<HistoryAppDto>()
                    .eq(HistoryAppDto::getUseful, 1)
                    .eq(HistoryAppDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .ge(HistoryAppDto::getCreateTime, conditionBo.getStartTime())
                    .le(HistoryAppDto::getCreateTime, conditionBo.getEndTime())
            );


            //复制
            Long copyCount = modelUseHistoryMapper.selectCount(new LambdaQueryWrapper<ModelUseHistoryDto>()
                    .eq(ModelUseHistoryDto::getUseType, ModelUseType.COPY.getCode())
                    .eq(ModelUseHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .ge(ModelUseHistoryDto::getCreateTime, conditionBo.getStartTime())
                    .le(ModelUseHistoryDto::getCreateTime, conditionBo.getEndTime())
            );

            //下载
            Long downloadCount = modelUseHistoryMapper.selectCount(new LambdaQueryWrapper<ModelUseHistoryDto>()
                    .eq(ModelUseHistoryDto::getUseType, ModelUseType.DOWNLOAD.getCode())
                    .eq(ModelUseHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .ge(ModelUseHistoryDto::getCreateTime, conditionBo.getStartTime())
                    .le(ModelUseHistoryDto::getCreateTime, conditionBo.getEndTime())
            );

            //计算采用率
            String usefulAdoptionRate = String.format("%.2f", ((usefulCount.doubleValue() / count.doubleValue()) * 100)) + "%";
            sysStatisticsInfo.setUsefulAdoptionRate(usefulAdoptionRate);

            String copyAdoptionRate = String.format("%.2f", ((copyCount.doubleValue() / count.doubleValue()) * 100)) + "%";
            sysStatisticsInfo.setCopyAdoptionRate(copyAdoptionRate);

            String downloadAdoptionRate = String.format("%.2f", ((downloadCount.doubleValue() / count.doubleValue()) * 100)) + "%";
            sysStatisticsInfo.setDownloadAdoptionRate(downloadAdoptionRate);
        } else {
            sysStatisticsInfo.setUsefulAdoptionRate("0%");
            sysStatisticsInfo.setCopyAdoptionRate("0%");
            sysStatisticsInfo.setDownloadAdoptionRate("0%");
        }

        if (CollectionUtil.isNotEmpty(userVos)) {
            sysStatisticsInfo.setUsers((long) userVos.size());
        }

        return sysStatisticsInfo;
    }

    @Override
    public List<SysDailyStatisticsInfoVo> getSysDailyStatistics(StatisticsConditionBo conditionBo) {
        List<SysDailyStatisticsInfoVo> dailyStatisticsInfo = statisticsMapper.getSysDailyStatistics(conditionBo);
        return dailyStatisticsInfo;
    }

    @Override
    public Map<String, Object> getStatisticsListByCondition(StatisticsConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<StatisticsVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = statisticsMapper.getStatisticsListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<StatisticsVo> statisticsVos = statisticsMapper.getStatisticsListByCondition(conditionBo);
            PageInfo<StatisticsVo> pageInfo = new PageInfo<>(statisticsVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addStatistics(StatisticsBo statisticsBo) {
        StatisticsDto statistics = new StatisticsDto();
        BeanUtils.copyProperties(statisticsBo, statistics);

        statistics.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        statistics.setCreateTime(new Date());
        save(statistics);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateStatistics(StatisticsBo statisticsBo) {
        StatisticsDto statistics = new StatisticsDto();
        BeanUtils.copyProperties(statisticsBo, statistics);

        statistics.setUpdateTime(new Date());
        updateById(statistics);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<StatisticsDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StatisticsDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(StatisticsDto::getId, id);

        StatisticsDto statistics = getOne(lqw);
        if (null == statistics) {
            return AjaxResult.fail("统计表数据不存在");
        }

        StatisticsVo statisticsVo = new StatisticsVo();
        BeanUtils.copyProperties(statistics, statisticsVo);

        return AjaxResult.success(statisticsVo);
    }

    @Override
    public AjaxResult deleteStatistics(StatisticsBo statisticsBo) {
        // 删除信息
        LambdaQueryWrapper<StatisticsDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StatisticsDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(StatisticsDto::getId, statisticsBo.getId());

        StatisticsDto statistics = getOne(lqw);
        if (null == statistics) {
            return AjaxResult.fail("统计表数据不存在");
        }

        StatisticsDto dto = new StatisticsDto();
        dto.setId(statistics.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(statisticsBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public Map<String, Object> getUserStatistics(StatisticsConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserStatisticsVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = statisticsMapper.getUserStatisticsWithoutNull(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserStatisticsVo> statisticsVos = statisticsMapper.getUserStatisticsWithoutNull(conditionBo);
            PageInfo<UserStatisticsVo> pageInfo = new PageInfo<>(statisticsVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public List<UserStatisticsVo> getAllUserStatistics(StatisticsConditionBo conditionBo) {
        return statisticsMapper.getUserStatistics(conditionBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatistics(Date nowDate) {
//        LocalDateTime now = LocalDateTime.now();
        Instant instant = nowDate.toInstant();
        LocalDateTime now = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();

        LocalDateTime oneHourAgo = now.minusHours(1);
        LocalDate todayOneHourAgo = oneHourAgo.toLocalDate();
        LocalDateTime startOfDayOneHourAgo = LocalDateTime.of(todayOneHourAgo, LocalTime.MIN);
        LocalDateTime endOfDayOneHourAgo = LocalDateTime.of(todayOneHourAgo, LocalTime.MAX);

        // 获取新增用户记录
        LambdaQueryWrapper<UserDto> userQuery = new LambdaQueryWrapper<>();
        userQuery.between(UserDto::getCreateTime, startOfDayOneHourAgo, oneHourAgo);
        List<UserDto> userDtos = userMapper.selectList(userQuery);
        Map<String, UserDto> userMap = null;
        if (CollectionUtil.isNotEmpty(userDtos)) {
            userMap = userDtos.stream().collect(Collectors.toMap(u -> u.getOid() + "_" + u.getOrganizationId() + "_" + u.getChannel(), u -> u));
        }

        // 获取用户访问记录
        LambdaQueryWrapper<UserVisitDto> userVisitQuery = new LambdaQueryWrapper<>();
        userVisitQuery.between(UserVisitDto::getCreateTime, startOfDayOneHourAgo, oneHourAgo);
        List<UserVisitDto> userVisitDtos = userVisitMapper.selectList(userVisitQuery);
        Map<String, List<UserVisitDto>> userVisitMap = null;
        if (CollectionUtil.isNotEmpty(userVisitDtos)) {
            userVisitMap = userVisitDtos.stream().collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getOrganizationId() + "_" + t.getChannel()));
        }

        // 获取用户使用记录
        LambdaQueryWrapper<StatisticsUsageDto> statisticsUsageQuery = new LambdaQueryWrapper<>();
        statisticsUsageQuery.eq(StatisticsUsageDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        statisticsUsageQuery.between(StatisticsUsageDto::getCreateTime, startOfDayOneHourAgo, endOfDayOneHourAgo);
        List<StatisticsUsageDto> statisticsUsageDtos = statisticsUsageMapper.selectList(statisticsUsageQuery);
        Map<String, List<StatisticsUsageDto>> statisticsUsageMap = null;
        if (CollectionUtil.isNotEmpty(statisticsUsageDtos)) {
            statisticsUsageMap = statisticsUsageDtos.stream().collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getOrganizationId() + "_" + t.getChannel()));
        }

        // 获取用户积分记录
        LambdaQueryWrapper<UserScoreRecordDto> userScoreRecordQuery = new LambdaQueryWrapper<>();
        userScoreRecordQuery.between(UserScoreRecordDto::getCreateTime, startOfDayOneHourAgo, oneHourAgo);
        List<UserScoreRecordDto> userScoreRecordDtos = userScoreRecordMapper.selectList(userScoreRecordQuery);
        Map<String, List<UserScoreRecordDto>> userScoreRecordMap = null;
        if (CollectionUtil.isNotEmpty(userScoreRecordDtos)) {
            userScoreRecordMap = userScoreRecordDtos.stream().collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getOrganizationId() + "_" + t.getChannel()));
        }

        Set<String> keySet = new HashSet<>();
        if (null != userMap) {
            keySet.addAll(userMap.keySet());
        }

        if (null != userVisitMap) {
            keySet.addAll(userVisitMap.keySet());
        }

        if (null != statisticsUsageMap) {
            keySet.addAll(statisticsUsageMap.keySet());
        }

        if (null != userScoreRecordMap) {
            keySet.addAll(userScoreRecordMap.keySet());
        }

        // 获取存在的数据
        LambdaQueryWrapper<StatisticsDto> statisticsQuery = new LambdaQueryWrapper<>();
        statisticsQuery.between(StatisticsDto::getCreateTime, startOfDayOneHourAgo, endOfDayOneHourAgo);
        List<StatisticsDto> existingRecords = statisticsMapper.selectList(statisticsQuery);
        Map<String, StatisticsDto> statisticsDtoMap = null;
        if (CollectionUtil.isNotEmpty(existingRecords)) {
            statisticsDtoMap = existingRecords.stream().collect(Collectors.toMap(u -> u.getUserOid() + "_" + u.getOrganizationId() + "_" + u.getChannel(), u -> u));
        }

//        Date nowDate = new Date();

        // 指定时区信息，将 LocalDateTime 转换为 Instant
        Instant todayOneHourAgoInstant = oneHourAgo.atZone(ZoneId.systemDefault()).toInstant();

        // 使用 Instant 创建 Date 对象
        Date todayOneHourAgoDate = Date.from(todayOneHourAgoInstant);

        List<StatisticsDto> statisticsDtos = Lists.newArrayList();
        for (String key : keySet) {
            String[] s = key.split("_");
            String userOid = s[0];
            Long organizationId = null;
            Integer channel = null;
            try {
                organizationId = Long.parseLong(s[1]);
                channel = Integer.parseInt(s[2]);
            } catch (Exception e) {
                // organizationId 为null时，说明是老数据
            }

            StatisticsDto statisticsDto = null != statisticsDtoMap ? statisticsDtoMap.get(key) : null;
            if (null == statisticsDto) {
                // 不存在
                statisticsDto = new StatisticsDto();

                statisticsDto.setUserOid(userOid);
                statisticsDto.setOrganizationId(organizationId);
                statisticsDto.setChannel(channel);
                statisticsDto.setCreateBy(userOid);
                statisticsDto.setCreateTime(todayOneHourAgoDate);
                statisticsDto.setUpdateBy(userOid);
                statisticsDto.setUpdateTime(nowDate);
            } else {
                // 已存在
                statisticsDto.setUpdateBy(userOid);
                statisticsDto.setChannel(channel);
                statisticsDto.setUpdateTime(nowDate);
            }

            if (null != userMap) {
                UserDto userDto = userMap.get(key);
                if (null != userDto) {
                    statisticsDto.setIsNew(IsNew.NEW.getCode());
                } else {
                    statisticsDto.setIsNew(IsNew.OLD.getCode());
                }
            } else {
                // 没有新用户
                statisticsDto.setIsNew(IsNew.OLD.getCode());
            }

            if (null != userVisitMap) {
                List<UserVisitDto> visitDtos = userVisitMap.get(key);
                if (CollectionUtil.isNotEmpty(visitDtos)) {
                    List<UserVisitDto> loginDtos = visitDtos.stream().filter(t -> UserVisitTypeEnum.LOGIN.getCode().equals(t.getType())).collect(Collectors.toList());

                    int loginSize = CollectionUtil.isEmpty(loginDtos) ? 0 : loginDtos.size();
                    int visitSize = visitDtos.size() - loginSize;

                    statisticsDto.setVisits((long) visitSize);
                    statisticsDto.setLogins((long) loginSize);
                }
            }

            if (null != statisticsUsageMap) {
                List<StatisticsUsageDto> usageDtos = statisticsUsageMap.get(key);
                if (CollectionUtil.isNotEmpty(usageDtos)) {
                    long appUsageCountSum = usageDtos.stream().filter(t -> null != t.getAppUsageCount() && t.getAppUsageCount().compareTo(0L) >= 0)
                            .mapToLong(StatisticsUsageDto::getAppUsageCount).sum();

                    statisticsDto.setAppUsageCount(appUsageCountSum);
                }
            }

            if (null != userScoreRecordMap) {
                List<UserScoreRecordDto> scoreRecordDtos = userScoreRecordMap.get(key);
                if (CollectionUtil.isNotEmpty(scoreRecordDtos)) {
                    long generateScoreSum = scoreRecordDtos.stream().filter(t -> null != t.getScore() && t.getScore().compareTo(0L) >= 0)
                            .mapToLong(UserScoreRecordDto::getScore).sum();

                    long redeemScoreSum = scoreRecordDtos.stream().filter(t -> null != t.getScore() && t.getScore().compareTo(0L) < 0)
                            .mapToLong(UserScoreRecordDto::getScore).sum();

                    statisticsDto.setGenerateScore(generateScoreSum);
                    statisticsDto.setRedeemScore(redeemScoreSum);
                }
            }

            statisticsDtos.add(statisticsDto);
        }

        if (CollectionUtil.isNotEmpty(statisticsDtos)) {
            saveOrUpdateBatch(statisticsDtos);
        }

    }


    public static void main(String[] args) {
        List<UserVisitDto> dtos = Lists.newArrayList();
        for (int i = 1; i <= 10; i++) {
            UserVisitDto userVisitDto = new UserVisitDto();
            userVisitDto.setUserOid("lll" + i);
            dtos.add(userVisitDto);
        }

        UserVisitDto userVisitDto = new UserVisitDto();
        userVisitDto.setUserOid("lll110");
        userVisitDto.setOrganizationId(100L);
        dtos.add(userVisitDto);

        Map<String, List<UserVisitDto>> userVisitMap = null;
        if (CollectionUtil.isNotEmpty(dtos)) {
            userVisitMap = dtos.stream().collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getOrganizationId()));
        }

        Map<String, UserVisitDto> userMap = null;
        if (CollectionUtil.isNotEmpty(dtos)) {
            userMap = dtos.stream().collect(Collectors.toMap(u -> u.getUserOid() + "_" + u.getOrganizationId(), u -> u));
        }


        System.out.println(userVisitMap);
        System.out.println(userMap);

        UserVisitDto userVisitDto1 = userMap.get("lll2_null");

        System.out.println(userVisitDto1);
    }
}