package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.bo.statistics.StatisticsConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageBo;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageConditionBo;
import com.fh.ai.business.entity.bo.statisticsUsage.UsageStatisticsTotalBo;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteConditionBo;
import com.fh.ai.business.entity.dto.historyApp.HistoryAppDto;
import com.fh.ai.business.entity.dto.mtHistory.MtHistoryDto;
import com.fh.ai.business.entity.dto.statisticsUsage.StatisticsUsageDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.dto.zegoHistory.ZegoHistoryDto;
import com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo;
import com.fh.ai.business.entity.vo.statistics.OrgStatisticsVo;
import com.fh.ai.business.entity.vo.statistics.SysStatisticsInfoVo;
import com.fh.ai.business.entity.vo.statisticsUsage.*;
import com.fh.ai.business.entity.vo.userFavorite.UserFavoriteVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.IStatisticsUsageService;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsFavorite;
import com.fh.ai.common.enums.TypeEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 使用情况统计表接口实现类
 *
 * <AUTHOR>
 * @date 2024-05-15 14:46:40
 */
@Service
public class StatisticsUsageServiceImpl extends ServiceImpl<StatisticsUsageMapper, StatisticsUsageDto>
    implements IStatisticsUsageService {

    @Resource
    private StatisticsUsageMapper statisticsUsageMapper;

    @Resource
    private HistoryAppMapper historyAppMapper;

    @Resource
    private MtHistoryMapper mtHistoryMapper;

    @Resource
    private ZegoHistoryMapper zegoHistoryMapper;

    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private UserFavoriteMapper userFavoriteMapper;

    @Resource
    private UserMapper userMapper;
    /**
     * 审校记录mapper
     */
    @Resource
    private ProofreadingRecordMapper proofreadingRecordMapper;

    @Override
    public Map<String, Object> getStatisticsUsageListByCondition(StatisticsUsageConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<StatisticsUsageVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = statisticsUsageMapper.getStatisticsUsageListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<StatisticsUsageVo> statisticsUsageVos =
                statisticsUsageMapper.getStatisticsUsageListByCondition(conditionBo);
            PageInfo<StatisticsUsageVo> pageInfo = new PageInfo<>(statisticsUsageVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addStatisticsUsage(StatisticsUsageBo statisticsUsageBo) {
        StatisticsUsageDto statisticsUsage = new StatisticsUsageDto();
        BeanUtils.copyProperties(statisticsUsageBo, statisticsUsage);

        statisticsUsage.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        statisticsUsage.setCreateTime(new Date());
        save(statisticsUsage);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateStatisticsUsage(StatisticsUsageBo statisticsUsageBo) {
        StatisticsUsageDto statisticsUsage = new StatisticsUsageDto();
        BeanUtils.copyProperties(statisticsUsageBo, statisticsUsage);

        statisticsUsage.setUpdateTime(new Date());
        updateById(statisticsUsage);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<StatisticsUsageDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StatisticsUsageDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(StatisticsUsageDto::getId, id);

        StatisticsUsageDto statisticsUsage = getOne(lqw);
        if (null == statisticsUsage) {
            return AjaxResult.fail("使用情况统计表数据不存在");
        }

        StatisticsUsageVo statisticsUsageVo = new StatisticsUsageVo();
        BeanUtils.copyProperties(statisticsUsage, statisticsUsageVo);

        return AjaxResult.success(statisticsUsageVo);
    }

    @Override
    public AjaxResult deleteStatisticsUsage(StatisticsUsageBo statisticsUsageBo) {
        // 删除信息
        LambdaQueryWrapper<StatisticsUsageDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StatisticsUsageDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(StatisticsUsageDto::getId, statisticsUsageBo.getId());

        StatisticsUsageDto statisticsUsage = getOne(lqw);
        if (null == statisticsUsage) {
            return AjaxResult.fail("使用情况统计表数据不存在");
        }

        StatisticsUsageDto dto = new StatisticsUsageDto();
        dto.setId(statisticsUsage.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(statisticsUsageBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatistics(Date nowDate) {
        // LocalDateTime now = LocalDateTime.now();
        Instant instant = nowDate.toInstant();
        LocalDateTime now = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();

        LocalDateTime oneHourAgo = now.minusHours(1);
        LocalDate todayOneHourAgo = oneHourAgo.toLocalDate();
        LocalDateTime startOfDayOneHourAgo = LocalDateTime.of(todayOneHourAgo, LocalTime.MIN);
        LocalDateTime endOfDayOneHourAgo = LocalDateTime.of(todayOneHourAgo, LocalTime.MAX);

        // 获取使用信息
        Map<String, List<HistoryAppDto>> recentHistoryMap = getHistoryAppDtoMap(startOfDayOneHourAgo, oneHourAgo);
        // 获取whee使用信息
        Map<String, List<MtHistoryDto>> recentMTHistoryMap = getMtHistoryDtoMap(startOfDayOneHourAgo, oneHourAgo);
        // 获取数智人使用信息
        Map<String, List<ZegoHistoryDto>> recentZegoHistoryMap = getZegoHistoryDtoMap(startOfDayOneHourAgo, oneHourAgo);

        // 指定时区信息，将 LocalDateTime 转换为 Instant
        Instant todayOneHourAgoInstant = oneHourAgo.atZone(ZoneId.systemDefault()).toInstant();
        // 使用 Instant 创建 Date 对象
        Date todayOneHourAgoDate = Date.from(todayOneHourAgoInstant);

        // 指定时区信息，将 LocalDateTime 转换为 Instant
        Instant startOfDayOneHourAgoInstant = startOfDayOneHourAgo.atZone(ZoneId.systemDefault()).toInstant();
        // 使用 Instant 创建 Date 对象
        Date startOfDayOneHourAgoDate = Date.from(startOfDayOneHourAgoInstant);

        // 审校记录
        Map<String, List<ProofreadingRecordVo>> proofreadingRecordMap = statisticProofreadRecord(startOfDayOneHourAgoDate, todayOneHourAgoDate);

        // 获取收藏信息
        Map<String, UserFavoriteVo> userFavoriteMap = getUserFavoriteVoMap(startOfDayOneHourAgoDate, todayOneHourAgoDate);

        // 判断有无用户操作数据
        if (CollectionUtil.isEmpty(recentHistoryMap)
                && CollectionUtil.isEmpty(userFavoriteMap)
                && CollectionUtil.isEmpty(recentMTHistoryMap)
                && CollectionUtil.isEmpty(recentZegoHistoryMap)
                && CollectionUtil.isEmpty(proofreadingRecordMap)) {
            return;
        }

        // 设置所有map的key
        Set<String> keySet = new HashSet<>();
        if (null != recentHistoryMap) {
            keySet.addAll(recentHistoryMap.keySet());
        }

        if (null != userFavoriteMap) {
            keySet.addAll(userFavoriteMap.keySet());
        }

        if (null != recentMTHistoryMap) {
            keySet.addAll(recentMTHistoryMap.keySet());
        }

        if (null != recentZegoHistoryMap) {
            keySet.addAll(recentZegoHistoryMap.keySet());
        }

        if (null != proofreadingRecordMap) {
            keySet.addAll(proofreadingRecordMap.keySet());
        }

        // 获取用户oids
        Set<String> userOids = collectUserOids(recentHistoryMap, userFavoriteMap, recentMTHistoryMap, proofreadingRecordMap);

        // 获取用户信息
        LambdaQueryWrapper<UserDto> userQuery = new LambdaQueryWrapper<>();
        userQuery.in(UserDto::getOid, userOids);
        List<UserDto> userDtos = userMapper.selectList(userQuery);
        Map<String, UserDto> userDtoMap = null;
        if (CollectionUtil.isNotEmpty(userDtos)) {
            userDtoMap = userDtos.stream().collect(Collectors.toMap(UserDto::getOid, u -> u));
        }

        // Fetch existing records in bulk
        LambdaQueryWrapper<StatisticsUsageDto> statisticsQuery = new LambdaQueryWrapper<>();
        statisticsQuery.in(StatisticsUsageDto::getUserOid, userOids)
            // .in(StatisticsUsageDto::getType, types)
            .between(StatisticsUsageDto::getCreateTime, startOfDayOneHourAgo, endOfDayOneHourAgo);
        List<StatisticsUsageDto> existingRecords = statisticsUsageMapper.selectList(statisticsQuery);

        // Map to store existing records for quick lookup
        Map<String, StatisticsUsageDto> existingRecordMap = new HashMap<>();
        for (StatisticsUsageDto record : existingRecords) {
            String key = record.getUserOid() + "_" + record.getType() + "_" + record.getChannel();
            existingRecordMap.put(key, record);
        }

        List<StatisticsUsageDto> recordsToInsert = new ArrayList<>();
        List<StatisticsUsageDto> recordsToUpdate = new ArrayList<>();

        // Process the usage count map
        for (String key : keySet) {
            String[] s = key.split("_");
            String userOid = s[0];
            Integer type = null;
            Integer channel = null;
            try {
                type = Integer.parseInt(s[1]);
                channel = Integer.parseInt(s[2]);
            } catch (Exception e) {
                // organizationId 为null时，说明是老数据。此时不做处理，处理下一个。
                log.error("updateStatistics exception key is :" + key);
                continue;
            }
            if (type == null) {
                log.error("updateStatistics type is null key is :" + key);
                continue;
            }

            UserDto userDto = null != userDtoMap ? userDtoMap.get(userOid) : null;
            Long organizationId = null != userDto ? userDto.getOrganizationId() : 0;

            // 使用数
            long usageCount = 0;
            if (null != recentHistoryMap) {
                List<HistoryAppDto> historyAppDtos = recentHistoryMap.get(key);
                if (CollectionUtil.isNotEmpty(historyAppDtos)) {
                    usageCount = historyAppDtos.size();
                }
            }

            // 是否收藏
            Integer favorite = null;
            if (null != userFavoriteMap) {
                UserFavoriteVo favoriteVo = userFavoriteMap.get(key);
                if (null != favoriteVo) {
                    // 有收藏操作
                    if (IsDeleteEnum.NOTDELETE.getCode().equals(favoriteVo.getIsDelete())) {
                        favorite = IsFavorite.FAVORITE.getCode();
                    } else {
                        favorite = IsFavorite.NOT_FAVORITE.getCode();
                    }
                }
            }

            // 是否使用美图
            if (null != recentMTHistoryMap) {
                List<MtHistoryDto> historyMTDtos = recentMTHistoryMap.get(key);
                if (CollectionUtil.isNotEmpty(historyMTDtos)) {
                    usageCount = historyMTDtos.size();
                }
            }
            // 是否使用数智人
            if (null != recentZegoHistoryMap) {
                List<ZegoHistoryDto> zegoHistoryDto = recentZegoHistoryMap.get(key);
                if (CollectionUtil.isNotEmpty(zegoHistoryDto)) {
                    usageCount = zegoHistoryDto.size();
                }
            }
            // 是否使用审校
            if (null != proofreadingRecordMap) {
                List<ProofreadingRecordVo> proofreadingRecordVos = proofreadingRecordMap.get(key);
                if (CollectionUtils.isNotEmpty(proofreadingRecordVos)){
                    usageCount = proofreadingRecordVos.stream().map(x -> {
                        // 找出当前审校记录预期执行的审校任务列表
                        List<Integer> executedTaskList =
                                JSON.parseObject(x.getExecutedTaskInfo(), new TypeReference<List<Integer>>() {
                                });

                        return executedTaskList.size();
                    }).reduce(ConstantsInteger.NUM_0, Integer::sum);
                }
            }

            StatisticsUsageDto statisticsUsage = existingRecordMap.get(key);
            if (statisticsUsage == null) {
                statisticsUsage = new StatisticsUsageDto();
                statisticsUsage.setUserOid(userOid);
                statisticsUsage.setOrganizationId(organizationId);
                statisticsUsage.setAppUsageCount(usageCount);
                statisticsUsage.setIsFavorite(favorite);
                statisticsUsage.setAppType(TypeEnum.getAppTypeEnumCodeByCode(type));
                statisticsUsage.setType(type);
                statisticsUsage.setChannel(channel);
                statisticsUsage.setCreateTime(todayOneHourAgoDate);
                statisticsUsage.setUpdateTime(nowDate);
                statisticsUsage.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

                recordsToInsert.add(statisticsUsage);
            } else {
                statisticsUsage.setOrganizationId(organizationId);
                statisticsUsage.setAppUsageCount(usageCount);
                statisticsUsage.setIsFavorite(favorite);
                statisticsUsage.setUpdateTime(nowDate);

                recordsToUpdate.add(statisticsUsage);
            }
        }

        // Batch insert new records
        if (!recordsToInsert.isEmpty()) {
            this.saveBatch(recordsToInsert);
        }

        // Batch update existing records
        if (!recordsToUpdate.isEmpty()) {
            this.updateBatchById(recordsToUpdate);
        }
    }

    @Override
    public List<UsageStatisticsTotalVo> getUsageStatistics(Long organizationId, String startDay) {
        return baseMapper.getUsageStatistics(organizationId, startDay);
    }

    @Override
    public List<UsageStatisticsTotalVo> getTop10UsageStatistics(UsageStatisticsTotalBo bo) {
        return baseMapper.getTop10UsageStatistics(bo);
    }

    @Override
    public PageInfo<UsageStatisticsTotalVo> getUsageStatistics(StatisticsUsageConditionBo bo) {
        PageHelper.startPage(bo.getPage(), bo.getLimit());
        List<UsageStatisticsTotalVo> list = baseMapper.findAllByType(bo);

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取收藏数
            List<Integer> types = list.stream().map(UsageStatisticsTotalVo::getType).collect(Collectors.toList());
            bo.setTypes(types);
            List<UsageStatisticsTotalVo> favoriteStatisticsVos = baseMapper.findFavoriteByType(bo);

            if (CollectionUtil.isNotEmpty(favoriteStatisticsVos)) {
                Map<Integer, UsageStatisticsTotalVo> typeMap =
                    favoriteStatisticsVos.stream().collect(Collectors.toMap(u -> u.getType(), u -> u));

                for (UsageStatisticsTotalVo totalVo : list) {
                    UsageStatisticsTotalVo favoriteVo = typeMap.get(totalVo.getType());
                    if (null != favoriteVo) {
                        totalVo.setFavoriteUsers(favoriteVo.getFavoriteUsers());
                    }
                }
            }
        }

        return new PageInfo<>(list);
    }

    @Override
    public List<UsageStatisticsTotalVo> getExportUsageStatistics(StatisticsUsageConditionBo bo) {
        List<UsageStatisticsTotalVo> list = baseMapper.findAllByType(bo);
        if (CollectionUtil.isNotEmpty(list)) {
            // 获取收藏数
            List<Integer> types = list.stream().map(UsageStatisticsTotalVo::getType).collect(Collectors.toList());
            bo.setTypes(types);
            List<UsageStatisticsTotalVo> favoriteStatisticsVos = baseMapper.findFavoriteByType(bo);

            if (CollectionUtil.isNotEmpty(favoriteStatisticsVos)) {
                Map<Integer, UsageStatisticsTotalVo> typeMap =
                    favoriteStatisticsVos.stream().collect(Collectors.toMap(u -> u.getType(), u -> u));

                for (UsageStatisticsTotalVo totalVo : list) {
                    UsageStatisticsTotalVo favoriteVo = typeMap.get(totalVo.getType());
                    if (null != favoriteVo) {
                        totalVo.setFavoriteUsers(favoriteVo.getFavoriteUsers());
                    }
                }
            }
        }
        return list;
    }

    public List<UsageStatisticsTotalVo> getAllUsageStatistics(StatisticsUsageConditionBo bo) {
        List<UsageStatisticsTotalVo> list = baseMapper.findClickAllByType(bo);
        if (CollectionUtil.isNotEmpty(list)) {
            // 获取收藏数
            List<Integer> types = list.stream().map(UsageStatisticsTotalVo::getType).collect(Collectors.toList());
            bo.setTypes(types);
            List<UsageStatisticsTotalVo> favoriteStatisticsVos = baseMapper.findFavoriteByType(bo);

            if (CollectionUtil.isNotEmpty(favoriteStatisticsVos)) {
                Map<Integer, UsageStatisticsTotalVo> typeMap =
                    favoriteStatisticsVos.stream().collect(Collectors.toMap(u -> u.getType(), u -> u));

                for (UsageStatisticsTotalVo totalVo : list) {
                    UsageStatisticsTotalVo favoriteVo = typeMap.get(totalVo.getType());
                    if (null != favoriteVo) {
                        totalVo.setFavoriteUsers(favoriteVo.getFavoriteUsers());
                    }
                }
            }
        }
        return list;
    }

    @Override
    public SummaryStatisticsVo getSummaryStatistics(String month, Long organizationId) {
        StatisticsUsageConditionBo bo = new StatisticsUsageConditionBo();
        bo.setMonth(month);
        bo.setOrganizationId(organizationId);
        List<UsageStatisticsTotalVo> statisticsTotalVos = baseMapper.findAll(bo);
        // statisticsTotalVosByType过滤掉[能力]应用，另一个不过滤（很产品、前端确认）
        bo.setExcludeTypes(TypeEnum.getFunctionCodes());
        List<UsageStatisticsTotalVo> statisticsTotalVosByType = baseMapper.findAllByType(bo);
        SummaryStatisticsVo summaryStatisticsVo = new SummaryStatisticsVo();
        summaryStatisticsVo.setTypeStatisticsTotalVos(statisticsTotalVosByType);
        summaryStatisticsVo.setAppTypeStatisticsTotalVos(statisticsTotalVos);
        return summaryStatisticsVo;
    }

    @Override
    public List<OrgUsageStatisticsVo> getOrgUsageStatistics(StatisticsConditionBo bo) {
        return baseMapper.getOrgUsageStatistics(bo);
    }

    @Override
    public List<OrgUsageStatisticsVo> getOrgUsageTop10(StatisticsConditionBo bo) {
        return baseMapper.getOrgUsageTop10(bo);
    }

    @Override
    public Map<String, Object> getOrgUsageTableInfo(StatisticsConditionBo bo) {
        // 获取单位使用情况统计
        List<OrgUsageStatisticsVo> orgUsageStatistics = baseMapper.getOrgUsageStatistics(bo);
        Map<Long, OrgUsageStatisticsVo> usageStatisticsMap = null;
        if (CollectionUtil.isNotEmpty(orgUsageStatistics)) {
            usageStatisticsMap =
                orgUsageStatistics.stream().collect(Collectors.toMap(u -> u.getOrganizationId(), u -> u));
        }

        // 获取单位积分统计
        List<OrgStatisticsVo> orgStatistics = statisticsMapper.getOrgStatistics(bo);
        Map<Long, OrgStatisticsVo> statisticsMap = null;
        if (CollectionUtil.isNotEmpty(orgStatistics)) {
            statisticsMap = orgStatistics.stream().collect(Collectors.toMap(u -> u.getOrganizationId(), u -> u));
        }

        Set<Long> keySet = new HashSet<>();
        if (null != usageStatisticsMap) {
            keySet.addAll(usageStatisticsMap.keySet());
        }

        if (null != statisticsMap) {
            keySet.addAll(statisticsMap.keySet());
        }

        List<OrgUsageStatisticsVo> orgUsageStatisticsVos = Lists.newArrayList();
        long usageCount = 0;
        long userCount = 0;
        long loginUsers = 0;
        long generateScore = 0;
        long redeemScore = 0;
        for (Long key : keySet) {
            OrgUsageStatisticsVo statisticsVo = new OrgUsageStatisticsVo();

            if (null != usageStatisticsMap) {
                OrgUsageStatisticsVo orgUsageStatisticsVo = usageStatisticsMap.get(key);

                statisticsVo.setOrganizationId(orgUsageStatisticsVo.getOrganizationId());
                statisticsVo.setOrganizationName(orgUsageStatisticsVo.getOrganizationName());
                statisticsVo.setSort(orgUsageStatisticsVo.getSort());
                statisticsVo.setUsageCount(orgUsageStatisticsVo.getUsageCount());
                statisticsVo.setUserCount(orgUsageStatisticsVo.getUserCount());

                usageCount += null != orgUsageStatisticsVo.getUsageCount() ? orgUsageStatisticsVo.getUsageCount() : 0;
                userCount += null != orgUsageStatisticsVo.getUserCount() ? orgUsageStatisticsVo.getUserCount() : 0;
            }

            if (null != statisticsMap) {
                OrgStatisticsVo orgStatisticsVo = statisticsMap.get(key);

                statisticsVo.setOrganizationId(orgStatisticsVo.getOrganizationId());
                statisticsVo.setOrganizationName(orgStatisticsVo.getOrganizationName());
                statisticsVo.setSort(orgStatisticsVo.getSort());
                statisticsVo.setLoginUsers(orgStatisticsVo.getLoginUsers());
                statisticsVo.setGenerateScore(orgStatisticsVo.getGenerateScore());
                statisticsVo.setRedeemScore(
                    null == orgStatisticsVo.getRedeemScore() ? 0 : Math.abs(orgStatisticsVo.getRedeemScore()));

                loginUsers += null != orgStatisticsVo.getLoginUsers() ? orgStatisticsVo.getLoginUsers() : 0;
                generateScore += null != orgStatisticsVo.getGenerateScore() ? orgStatisticsVo.getGenerateScore() : 0;
                redeemScore += null != orgStatisticsVo.getRedeemScore() ? orgStatisticsVo.getRedeemScore() : 0;
            }

            orgUsageStatisticsVos.add(statisticsVo);
        }

        // 对orgUsageStatisticsVos按照Sort字段升序排序
        List<OrgUsageStatisticsVo> sortedList = orgUsageStatisticsVos.stream()
            .sorted(Comparator.comparing(OrgUsageStatisticsVo::getSort)).collect(Collectors.toList());

        // 获取平台使用情况统计
        SysUsageStatisticsVo sysUsageStatistics = baseMapper.getSysUsageStatistics(bo);

        // 获取平台积分统计
        SysStatisticsInfoVo sysStatistics = statisticsMapper.getSysStatistics(bo);

        // 其他
        OrgUsageStatisticsVo otherStatisticsVo = new OrgUsageStatisticsVo();
        long sysUsageCount = null != sysUsageStatistics.getUsageCount() ? sysUsageStatistics.getUsageCount() : 0;
        long sysUserCount = null != sysUsageStatistics.getUserCount() ? sysUsageStatistics.getUserCount() : 0;
        long loginUsersCount = null != sysStatistics.getLoginUsers() ? sysStatistics.getLoginUsers() : 0;
        long sysGenerateScore = null != sysStatistics.getGenerateScore() ? sysStatistics.getGenerateScore() : 0;
        long sysRedeemScore = null != sysStatistics.getRedeemScore() ? sysStatistics.getRedeemScore() : 0;
        otherStatisticsVo.setOrganizationName("其他");
        otherStatisticsVo.setOrganizationShortName("其他");
        otherStatisticsVo.setUsageCount(sysUsageCount - usageCount);
        otherStatisticsVo.setUserCount(sysUserCount - userCount);
        otherStatisticsVo.setLoginUsers(loginUsersCount - loginUsers);
        otherStatisticsVo.setGenerateScore(sysGenerateScore - generateScore);
        otherStatisticsVo.setRedeemScore(Math.abs(sysRedeemScore) - Math.abs(redeemScore));
        sortedList.add(otherStatisticsVo);

        OrgUsageStatisticsTotalVo orgUsageStatisticsTotalVo = new OrgUsageStatisticsTotalVo();
        orgUsageStatisticsTotalVo.setUsageCount(sysUsageStatistics.getUsageCount());
        orgUsageStatisticsTotalVo.setUserCount(sysUsageStatistics.getUserCount());
        orgUsageStatisticsTotalVo.setLoginUsers(sysStatistics.getLoginUsers());
        orgUsageStatisticsTotalVo.setGenerateScore(sysStatistics.getGenerateScore());
        orgUsageStatisticsTotalVo.setRedeemScore(sysStatistics.getRedeemScore() != null ? Math.abs(sysStatistics.getRedeemScore()) : 0L);

        Map<String, Object> map = new HashMap<>(4);
        map.put("list", sortedList);
        map.put("total", orgUsageStatisticsTotalVo);
        return map;
    }

    @Override
    public OrgUsageStatisticsVo getOrgUsageTotalDetail(Long organizationId) {
        StatisticsConditionBo bo = new StatisticsConditionBo();
        bo.setOrganizationId(organizationId);
        List<OrgUsageStatisticsVo> orgUsageStatistics = baseMapper.getOrgUsageStatistics(bo);
        OrgUsageStatisticsVo orgUsageStatisticsVo = null;
        if (CollectionUtil.isNotEmpty(orgUsageStatistics)) {
            orgUsageStatisticsVo = orgUsageStatistics.get(0);
        }

        StatisticsConditionBo condition = new StatisticsConditionBo();
        condition.setOrganizationId(organizationId);
        List<OrgStatisticsVo> orgStatistics = statisticsMapper.getOrgStatistics(condition);
        OrgStatisticsVo orgStatisticsVo = null;
        if (CollectionUtil.isNotEmpty(orgStatistics)) {
            orgStatisticsVo = orgStatistics.get(0);
        }

        OrgUsageStatisticsVo statisticsVo = new OrgUsageStatisticsVo();
        if (null != orgUsageStatisticsVo) {
            statisticsVo.setOrganizationName(orgUsageStatisticsVo.getOrganizationName());
            statisticsVo.setUsageCount(orgUsageStatisticsVo.getUsageCount());
            statisticsVo.setUserCount(orgUsageStatisticsVo.getUserCount());
        }

        if (null != orgStatisticsVo) {
            statisticsVo.setOrganizationName(orgStatisticsVo.getOrganizationName());
            statisticsVo.setLoginUsers(orgStatisticsVo.getLoginUsers());
            statisticsVo.setGenerateScore(orgStatisticsVo.getGenerateScore());
            statisticsVo.setRedeemScore(Math.abs(orgStatisticsVo.getRedeemScore()));
        }

        return statisticsVo;
    }

    @Override
    public List<UsageStatisticsTotalVo> getTop10UserUsageStatistics(Long organizationId) {
        return baseMapper.getTop10UserUsageSStatistics(organizationId);
    }


    /**
     * 审校记录统计
     *
     * @param starDate
     * @param endDate
     * @return
     */
    public Map<String, List<ProofreadingRecordVo>> statisticProofreadRecord(Date starDate, Date endDate) {
        Map<String, List<ProofreadingRecordVo>> resultMap = Maps.newHashMap();
        ProofreadingRecordConditionBo conditionBo = new ProofreadingRecordConditionBo();
        conditionBo.setStartTime(starDate);
        conditionBo.setEndTime(endDate);
        List<ProofreadingRecordVo> proofreadingRecordList = proofreadingRecordMapper.getProofreadingRecordListByCondition(conditionBo);
        if (CollectionUtil.isNotEmpty(proofreadingRecordList)) {
            resultMap = proofreadingRecordList.stream()
                    .collect(Collectors.groupingBy(x -> x.getUserOid() + "_" + TypeEnum.FINISH.getCode() + "_" + x.getChannel()));
        }
        return resultMap;
    }

    /**
     * 应用使用统计
     *
     * @param start
     * @param end
     * @return
     */
    private Map<String, List<HistoryAppDto>> getHistoryAppDtoMap(LocalDateTime start, LocalDateTime end) {
        LambdaQueryWrapper<HistoryAppDto> query = new LambdaQueryWrapper<>();
        query.between(HistoryAppDto::getCreateTime, start, end);
        List<HistoryAppDto> historyAppDtos = historyAppMapper.selectList(query);
        return CollectionUtil.isNotEmpty(historyAppDtos) ? historyAppDtos.stream()
                .collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getType() + "_" + t.getChannel())) : new HashMap<>();
    }

    /**
     * whee生成统计
     *
     * @param start
     * @param end
     * @return
     */
    private Map<String, List<MtHistoryDto>> getMtHistoryDtoMap(LocalDateTime start, LocalDateTime end) {
        LambdaQueryWrapper<MtHistoryDto> query = new LambdaQueryWrapper<>();
        query.between(MtHistoryDto::getCreateTime, start, end);
        List<MtHistoryDto> mtHistoryDtos = mtHistoryMapper.selectList(query);
        Map<Integer, Integer> typeMap = new HashMap<>();
        typeMap.put(1, TypeEnum.TEXT2IMAGE.getCode());
        typeMap.put(11, TypeEnum.TEXT2IMAGE.getCode());
        typeMap.put(2, TypeEnum.IMAGE2IMAGE.getCode());
        typeMap.put(3, TypeEnum.PARTIAL_REDRAW.getCode());
        typeMap.put(4, TypeEnum.LOSSLESS_ZOOM.getCode());
        typeMap.put(5, TypeEnum.AI_TRACELESS_ELIMINATION.getCode());
        typeMap.put(6, TypeEnum.INTELLIGENT_CUTTING.getCode());
        return CollectionUtil.isNotEmpty(mtHistoryDtos) ? mtHistoryDtos.stream()
                .collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + typeMap.get(t.getType()) + "_" + t.getChannel())) : new HashMap<>();
    }

    /**
     * 数智人使用统计
     *
     * @param start
     * @param end
     * @return
     */
    private Map<String, List<ZegoHistoryDto>> getZegoHistoryDtoMap(LocalDateTime start, LocalDateTime end) {
        LambdaQueryWrapper<ZegoHistoryDto> query = new LambdaQueryWrapper<>();
        query.between(ZegoHistoryDto::getCreateTime, start, end);
        List<ZegoHistoryDto> zegoHistoryDtos = zegoHistoryMapper.selectList(query);
        return CollectionUtil.isNotEmpty(zegoHistoryDtos) ? zegoHistoryDtos.stream()
                .collect(Collectors.groupingBy(t -> t.getUserOid() + "_" + t.getAppType() + "_" + t.getChannel())) : new HashMap<>();
    }

    /**
     * 用户收藏统计
     *
     * @param startOfDayOneHourAgoDate
     * @param todayOneHourAgoDate
     * @return
     */
    private Map<String, UserFavoriteVo> getUserFavoriteVoMap(Date startOfDayOneHourAgoDate, Date todayOneHourAgoDate) {
        UserFavoriteConditionBo condition = new UserFavoriteConditionBo();
        condition.setStartTime(startOfDayOneHourAgoDate);
        condition.setEndTime(todayOneHourAgoDate);
        List<UserFavoriteVo> userFavoriteVos = userFavoriteMapper.findLatestFavoriteByType(condition);
        Map<String, UserFavoriteVo> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userFavoriteVos)) {
            for (UserFavoriteVo record : userFavoriteVos) {
                String key = record.getUserOid() + "_" + record.getType() + "_" + record.getChannel();
                map.put(key, record);
            }
        }
        return map;
    }

    /**
     * 获取userOids
     *
     * @param recentHistoryMap
     * @param userFavoriteMap
     * @param recentMTHistoryMap
     * @param proofreadingRecordMap
     * @return
     */
    private Set<String> collectUserOids(Map<String, List<HistoryAppDto>> recentHistoryMap,
                                        Map<String, UserFavoriteVo> userFavoriteMap,
                                        Map<String, List<MtHistoryDto>> recentMTHistoryMap,
                                        Map<String, List<ProofreadingRecordVo>> proofreadingRecordMap) {
        Set<String> userOids = new HashSet<>();
        for (String key : recentHistoryMap.keySet()) {
            List<String> userOidList = recentHistoryMap.get(key).stream().map(HistoryAppDto::getUserOid).collect(Collectors.toList());
            userOids.addAll(userOidList);
        }
        for (String key : userFavoriteMap.keySet()) {
            userOids.add(userFavoriteMap.get(key).getUserOid());
        }
        for (String key : recentMTHistoryMap.keySet()) {
            List<String> userOidList = recentMTHistoryMap.get(key).stream().map(MtHistoryDto::getUserOid).collect(Collectors.toList());
            userOids.addAll(userOidList);
        }
        for (String key : proofreadingRecordMap.keySet()) {
            List<String> userOidList = proofreadingRecordMap.get(key).stream().map(ProofreadingRecordVo::getUserOid).collect(Collectors.toList());
            userOids.addAll(userOidList);
        }
        return userOids;
    }
}