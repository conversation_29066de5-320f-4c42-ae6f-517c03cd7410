package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.systemSetting.SystemSettingBo;
import com.fh.ai.business.entity.bo.systemSetting.SystemSettingConditionBo;
import com.fh.ai.business.entity.dto.systemSetting.SystemSettingDto;
import com.fh.ai.business.entity.vo.systemSetting.SystemSettingVo;
import com.fh.ai.business.mapper.SystemSettingMapper;
import com.fh.ai.business.service.ISystemSettingService;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统设置表接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-23 14:05:25
 */
@Service
public class SystemSettingServiceImpl extends ServiceImpl<SystemSettingMapper, SystemSettingDto> implements ISystemSettingService {

    @Resource
    private SystemSettingMapper systemSettingMapper;

    @Override
    public Map<String, Object> getSystemSettingListByCondition(SystemSettingConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<SystemSettingVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = systemSettingMapper.getSystemSettingListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<SystemSettingVo> systemSettingVos = systemSettingMapper.getSystemSettingListByCondition(conditionBo);
            PageInfo<SystemSettingVo> pageInfo = new PageInfo<>(systemSettingVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addSystemSetting(SystemSettingBo systemSettingBo) {
        SystemSettingDto systemSetting = new SystemSettingDto();
        BeanUtils.copyProperties(systemSettingBo, systemSetting);

        systemSetting.setCreateTime(new Date());
        save(systemSetting);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateSystemSetting(SystemSettingBo systemSettingBo) {
        SystemSettingDto systemSetting = new SystemSettingDto();
        BeanUtils.copyProperties(systemSettingBo, systemSetting);

        updateById(systemSetting);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<SystemSettingDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SystemSettingDto::getId, id);

        SystemSettingDto systemSetting = getOne(lqw);
        if (null == systemSetting) {
            return AjaxResult.fail("系统设置表数据不存在");
        }

        SystemSettingVo systemSettingVo = new SystemSettingVo();
        BeanUtils.copyProperties(systemSetting, systemSettingVo);

        return AjaxResult.success(systemSettingVo);
    }

    @Override
    public SystemSettingVo getDetail(String name) {
        LambdaQueryWrapper<SystemSettingDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SystemSettingDto::getName, name);

        SystemSettingDto systemSetting = getOne(lqw);
        if (null == systemSetting) {
            return null;
        }

        SystemSettingVo systemSettingVo = new SystemSettingVo();
        BeanUtils.copyProperties(systemSetting, systemSettingVo);

        return systemSettingVo;
    }
}