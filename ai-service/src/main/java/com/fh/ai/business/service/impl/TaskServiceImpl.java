package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.task.TaskBo;
import com.fh.ai.business.entity.bo.task.TaskConditionBo;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentConditionBo;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.dto.task.TaskDto;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.dto.userTask.UserTaskDto;
import com.fh.ai.business.entity.dto.userTaskAttachment.UserTaskAttachmentDto;
import com.fh.ai.business.entity.vo.task.TaskVo;
import com.fh.ai.business.entity.vo.userTask.UserTaskVo;
import com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo;
import com.fh.ai.business.mapper.TaskMapper;
import com.fh.ai.business.mapper.UserScoreRecordMapper;
import com.fh.ai.business.mapper.UserTaskAttachmentMapper;
import com.fh.ai.business.mapper.UserTaskMapper;
import com.fh.ai.business.service.ITaskService;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ScoreTypeEnum;
import com.fh.ai.common.enums.UserTaskState;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务表接口实现类
 *
 * <AUTHOR>
 * @date 2024-06-04 13:49:38
 */
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, TaskDto> implements ITaskService {

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private UserTaskAttachmentMapper userTaskAttachmentMapper;

    @Resource
    private UserTaskMapper userTaskMapper;

    @Resource
    private UserScoreRecordMapper userScoreRecordMapper;

    @Override
    public Map<String, Object> getTaskListByCondition(TaskConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<TaskVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = taskMapper.getTaskListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<TaskVo> taskVos = taskMapper.getTaskListByCondition(conditionBo);
            PageInfo<TaskVo> pageInfo = new PageInfo<>(taskVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollUtil.isNotEmpty(list)) {
            Date d = new Date();
            Map<Long, List<UserTaskDto>> taskUserMap = new HashMap<>();
            if (StringUtils.isNotEmpty(conditionBo.getUserOid())) {
                Set<Long> collect = list.stream().map(TaskVo::getId).collect(Collectors.toSet());
                List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                        .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(UserTaskDto::getUserOid, conditionBo.getUserOid())
                        .in(UserTaskDto::getTaskId, collect)
                );
                taskUserMap = userTaskDtos.stream().collect(Collectors.groupingBy(UserTaskDto::getTaskId));
            }

            for (TaskVo vo : list) {
                if (vo.getStartTime() != null && vo.getEndTime() != null) {
                    if (d.before(vo.getStartTime())) {
                        vo.setTaskTimeState(1);
                    } else if (d.after(vo.getEndTime())) {
                        vo.setTaskTimeState(3);
                    } else {
                        vo.setTaskTimeState(2);
                    }
                }
                if (taskUserMap.containsKey(vo.getId())) {
                    List<UserTaskDto> userTaskDto = taskUserMap.get(vo.getId());
                    List<UserTaskVo> userTaskVos = BeanUtil.copyToList(userTaskDto, UserTaskVo.class);
                    vo.setUserTaskList(userTaskVos);
                }
            }
        }

        map.put("list", list);
        map.put("count", count);
        Integer noPass = 0;
        if(StringUtils.isNotEmpty(conditionBo.getUserOid())){
            noPass = userTaskMapper.noPass(conditionBo.getUserOid());
        }
        map.put("noPass", noPass);

        return map;
    }
    @Override
    public Map<String, Object> getMyTaskListByCondition(TaskConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<TaskVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = taskMapper.getMyTaskListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<TaskVo> taskVos = taskMapper.getMyTaskListByCondition(conditionBo);
            PageInfo<TaskVo> pageInfo = new PageInfo<>(taskVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        if (CollUtil.isNotEmpty(list)) {
            Date d = new Date();
            Map<Long, List<UserTaskDto>> taskUserMap = new HashMap<>();
            if (StringUtils.isNotEmpty(conditionBo.getUserOid())) {
                Set<Long> collect = list.stream().map(TaskVo::getId).collect(Collectors.toSet());
                List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                        .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(UserTaskDto::getUserOid, conditionBo.getUserOid())
                        .in(UserTaskDto::getTaskId, collect)
                );
                taskUserMap = userTaskDtos.stream().collect(Collectors.groupingBy(UserTaskDto::getTaskId));
            }

            for (TaskVo vo : list) {
                if (vo.getStartTime() != null && vo.getEndTime() != null) {
                    if (d.before(vo.getStartTime())) {
                        vo.setTaskTimeState(1);
                    } else if (d.after(vo.getEndTime())) {
                        vo.setTaskTimeState(3);
                    } else {
                        vo.setTaskTimeState(2);
                    }
                }
                if (taskUserMap.containsKey(vo.getId())) {
                    List<UserTaskDto> userTaskDto = taskUserMap.get(vo.getId());
                    List<UserTaskVo> userTaskVos = BeanUtil.copyToList(userTaskDto, UserTaskVo.class);
                    vo.setUserTaskList(userTaskVos);
                }
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addTask(TaskBo taskBo) {
        TaskDto task = new TaskDto();
        BeanUtils.copyProperties(taskBo, task);
        String multitask = taskBo.getMultitask();
        task.setNum(0);
        if(StringUtils.isNotEmpty(multitask)){
            JSONArray jsonArray = JSON.parseArray(multitask);
            List<String> secType = new ArrayList();
            List<String> appTyp = new ArrayList();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String secondType = jsonObject.getString("secondType");
                String appTypes = jsonObject.getString("appTypes");
                secType.add(secondType);
                appTyp.add(appTypes);
            }
            task.setSecondType(String.join(",",secType));
            task.setAppTypes(String.join(",",appTyp));
            task.setNum(jsonArray.size());
        }else{
            return AjaxResult.fail("参数错误");
        }
        task.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        task.setCreateTime(new Date());
        save(task);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateTask(TaskBo taskBo) {
        TaskDto task = new TaskDto();
        BeanUtils.copyProperties(taskBo, task);
        String multitask = taskBo.getMultitask();
        if(StringUtils.isNotEmpty(multitask)){
            JSONArray jsonArray = JSON.parseArray(multitask);
            List<String> secType = new ArrayList();
            List<String> appTyp = new ArrayList();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String secondType = jsonObject.getString("secondType");
                String appTypes = jsonObject.getString("appTypes");
                secType.add(secondType);
                appTyp.add(appTypes);
            }
            task.setSecondType(String.join(",",secType));
            task.setAppTypes(String.join(",",appTyp));
            task.setNum(jsonArray.size());
        }
        task.setUpdateTime(new Date());
        updateById(task);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id, String oid) {
        LambdaQueryWrapper<TaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(TaskDto::getId, id);

        TaskDto task = getOne(lqw);
        if (null == task) {
            return AjaxResult.fail("任务表数据不存在");
        }

        TaskVo taskVo = new TaskVo();
        BeanUtils.copyProperties(task, taskVo);
        if(taskVo.getStartTime() != null && taskVo.getEndTime() != null){
            Date d = new Date();
            if(d.before(taskVo.getStartTime())){
                taskVo.setTaskTimeState(1);
            }else if(d.after(taskVo.getEndTime())){
                taskVo.setTaskTimeState(3);
            }else{
                taskVo.setTaskTimeState(2);
            }
        }
        taskVo.setUserNum(0);
        if (StringUtils.isNotEmpty(oid)) {
            List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                    .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .eq(UserTaskDto::getTaskId, task.getId())
                    .eq(UserTaskDto::getUserOid, oid)
            );
            if (CollUtil.isNotEmpty(userTaskDtos)) {
                taskVo.setUserNum(userTaskDtos.size());
                Set<String> collect = userTaskDtos.stream().map(UserTaskDto::getUserOid).collect(Collectors.toSet());
                taskVo.setJoinCount(new Long(""+collect.size()));
                List<UserTaskVo> userTaskList= new ArrayList<>();
                UserScoreRecordDto userScoreRecordDto = userScoreRecordMapper.selectOne(new LambdaQueryWrapper<UserScoreRecordDto>()
                        .eq(UserScoreRecordDto::getUserOid, oid)
                        .eq(UserScoreRecordDto::getRelationId, task.getId())
                        .eq(UserScoreRecordDto::getType, ScoreTypeEnum.ACTIVITY_TASK.getCode())
                );
                if(userScoreRecordDto != null){
                    taskVo.setScoreTime(userScoreRecordDto.getCreateTime());
                }
                for (UserTaskDto dto :userTaskDtos){
                    UserTaskVo userTaskVo = new UserTaskVo();
                    BeanUtils.copyProperties(dto, userTaskVo);
                    userTaskList.add(userTaskVo);
                }
                taskVo.setUserTaskList(userTaskList);
                UserTaskAttachmentConditionBo bo = new UserTaskAttachmentConditionBo();
                bo.setTaskId(task.getId());
                bo.setUserOid(oid);
                bo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                List<UserTaskAttachmentVo> userTaskAttachmentDtos = userTaskAttachmentMapper.getUserTaskAttachmentInfoListByCondition(bo);
                taskVo.setAttachmentVoList(userTaskAttachmentDtos);
            }
        }

        return AjaxResult.success(taskVo);
    }

    @Override
    public AjaxResult updateState(TaskBo taskBo) {
        // 更新状态
        LambdaQueryWrapper<TaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(TaskDto::getId, taskBo.getId());

        TaskDto task = getOne(lqw);
        if (null == task) {
            return AjaxResult.fail("任务表数据不存在");
        }

        TaskDto dto = new TaskDto();
        dto.setId(task.getId());
        dto.setState(taskBo.getState());
        dto.setUpdateBy(taskBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);
        if(dto.getState() != null && dto.getState().equals(2)){
            NoticeUtil.addNew(task.getId(),task.getName());
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteTask(TaskBo taskBo) {


        // 删除信息
        LambdaQueryWrapper<TaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(TaskDto::getId, taskBo.getId());

        TaskDto task = getOne(lqw);
        if (null == task) {
            return AjaxResult.fail("任务表数据不存在");
        }
        List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                .eq(UserTaskDto::getTaskId, taskBo.getId())
        );
        if (CollUtil.isNotEmpty(userTaskDtos)) {
            return AjaxResult.fail("任务已有人参与,不可删除");
        }

        TaskDto dto = new TaskDto();
        dto.setId(task.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(taskBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateSort(TaskBo taskBo) {
        if (taskBo.getSort() != null && taskBo.getSort() > 0) {
            // 将相同的排序置为null
            update(null, new LambdaUpdateWrapper<TaskDto>()
                .eq(TaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                .eq(TaskDto::getSort, taskBo.getSort())
                .set(TaskDto::getSort, null)
            );
            // 设置序号
            update(null, new LambdaUpdateWrapper<TaskDto>()
                .eq(TaskDto::getId, taskBo.getId())
                .set(TaskDto::getSort, taskBo.getSort())
            );
        } else {
            // 清空排序
            update(null, new LambdaUpdateWrapper<TaskDto>()
                .eq(TaskDto::getId, taskBo.getId())
                .set(TaskDto::getSort, null)
            );
        }
        return AjaxResult.success();
    }

}