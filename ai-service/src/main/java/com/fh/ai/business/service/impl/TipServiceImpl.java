package com.fh.ai.business.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.tip.TipBo;
import com.fh.ai.business.entity.bo.tip.TipConditionBo;
import com.fh.ai.business.entity.dto.tip.TipDto;
import com.fh.ai.business.entity.vo.tip.TipVo;
import com.fh.ai.business.mapper.TipMapper;
import com.fh.ai.business.service.ITipService;
import com.fh.ai.common.enums.AppTagType;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 提示词接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class TipServiceImpl extends ServiceImpl<TipMapper, TipDto> implements ITipService {

	@Resource
	private TipMapper tipMapper;
	
    @Override
	public Map<String, Object> getTipListByCondition(TipConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<TipVo> list = null;
		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = tipMapper.getTipListByCondition(conditionBo);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<TipVo> prizeVos = tipMapper.getTipListByCondition(conditionBo);
			PageInfo<TipVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}

		map.put("list", list);
		map.put("count", count);

		return map;
	}

	@Override
	public AjaxResult addTip(TipBo tipBo) {
		TipDto tip = new TipDto();
		BeanUtils.copyProperties(tipBo, tip);
		tip.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(tip)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateTip(TipBo tipBo) {
		TipDto tip = new TipDto();
		BeanUtils.copyProperties(tipBo, tip);
		if(updateById(tip)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<TipDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(TipDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(TipDto::getId, id);
		TipDto tip = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		TipVo tipVo = new TipVo();
		if (tip != null) {
			BeanUtils.copyProperties(tip, tipVo);
		}
		reuslt.put("tipVo", tipVo);
		return reuslt;
	}

	@Override
	public void clearTag(TipConditionBo condition) {
		LambdaUpdateWrapper<TipDto> luw = new LambdaUpdateWrapper<>();
		luw.eq(TipDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		if (CollectionUtils.isNotEmpty(condition.getIds())) {
			luw.in(TipDto::getId, condition.getIds());
		}
		luw.set(TipDto::getTagType, AppTagType.DEFAULT.getCode());
		update(luw);
	}
}