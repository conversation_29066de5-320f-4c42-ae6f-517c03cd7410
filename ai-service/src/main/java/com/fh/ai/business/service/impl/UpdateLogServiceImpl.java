package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.ai.business.entity.bo.updateLog.UpdateLogBo;
import com.fh.ai.business.entity.bo.updateLog.UpdateLogConditionBo;
import com.fh.ai.business.entity.dto.updateLog.UpdateLogDto;
import com.fh.ai.business.entity.vo.updateLog.UpdateLogVo;
import com.fh.ai.business.mapper.UpdateLogMapper;
import com.fh.ai.business.service.IUpdateLogService;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

/**
 * 更新日志接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@Service
public class UpdateLogServiceImpl extends ServiceImpl<UpdateLogMapper, UpdateLogDto> implements IUpdateLogService {

    @Resource
    private UpdateLogMapper updateLogMapper;

    @Override
    public Map<String, Object> getUpdateLogListByCondition(UpdateLogConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UpdateLogVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = updateLogMapper.getUpdateLogListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UpdateLogVo> prizeVos = updateLogMapper.getUpdateLogListByCondition(conditionBo);
            PageInfo<UpdateLogVo> pageInfo = new PageInfo<>(prizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addUpdateLog(UpdateLogBo updateLogBo) {
        try {
            List<UpdateLogDto> list = list(new LambdaQueryWrapper<UpdateLogDto>()
                    .eq(UpdateLogDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode())
                    .eq(UpdateLogDto::getVersionName,updateLogBo.getVersionName())
            );
            if(CollUtil.isNotEmpty(list)){
                return AjaxResult.fail("版本号重复");
            }
            UpdateLogDto updateLog = new UpdateLogDto();
            BeanUtils.copyProperties(updateLogBo, updateLog);
            updateLog.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            if (save(updateLog)) {
                return AjaxResult.success("保存成功");
            }
        } catch (Exception e) {

        }
        return AjaxResult.fail("保存失败");
    }

    @Override
    public AjaxResult updateUpdateLog(UpdateLogBo updateLogBo) {
        List<UpdateLogDto> list = list(new LambdaQueryWrapper<UpdateLogDto>()
                .eq(UpdateLogDto::getIsDelete,IsDeleteEnum.NOTDELETE.getCode())
                .eq(UpdateLogDto::getVersionName,updateLogBo.getVersionName())
                .ne(UpdateLogDto::getId,updateLogBo.getId())
        );
        if(CollUtil.isNotEmpty(list)){
            return AjaxResult.fail("版本号重复");
        }
        UpdateLogDto updateLog = new UpdateLogDto();
        BeanUtils.copyProperties(updateLogBo, updateLog);
        if (updateById(updateLog)) {
            if(updateLogBo.getState() != null && updateLogBo.getState().equals(2)){
                UpdateLogDto byId = getById(updateLogBo.getId());
                NoticeUtil.addNotice(updateLog.getId(),byId.getTitle());
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<UpdateLogDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UpdateLogDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UpdateLogDto::getId, id);
        UpdateLogDto updateLog = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("updateLogVo", updateLog == null ? new UpdateLogVo() : updateLog);
        return reuslt;
    }

    @Override
    public AjaxResult delete(Long id) {
        updateLogMapper.updateUpdateLog(id);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult check(Long id, String oid) {
        return AjaxResult.success(updateLogMapper.check(id,oid));
    }

    @Override
    public AjaxResult read(Long id, String oid) {
        return AjaxResult.success(updateLogMapper.read(id,oid));
    }

}