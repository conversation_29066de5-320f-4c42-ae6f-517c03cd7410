package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.statisticsUsage.StatisticsUsageConditionBo;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteBo;
import com.fh.ai.business.entity.bo.userFavorite.UserFavoriteConditionBo;
import com.fh.ai.business.entity.dto.userFavorite.UserFavoriteDto;
import com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo;
import com.fh.ai.business.entity.vo.userFavorite.UserFavoriteVo;
import com.fh.ai.business.mapper.StatisticsUsageMapper;
import com.fh.ai.business.mapper.UserFavoriteMapper;
import com.fh.ai.business.service.IUserFavoriteService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户收藏表接口实现类
 *
 * <AUTHOR>
 * @date 2024-06-04 10:38:07
 */
@Service
public class UserFavoriteServiceImpl extends ServiceImpl<UserFavoriteMapper, UserFavoriteDto> implements IUserFavoriteService {

    @Resource
    private UserFavoriteMapper userFavoriteMapper;

    @Resource
    private StatisticsUsageMapper statisticsUsageMapper;

    @Override
    public Map<String, Object> getUserFavoriteListByCondition(UserFavoriteConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserFavoriteVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userFavoriteMapper.getUserFavoriteListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserFavoriteVo> userFavoriteVos = userFavoriteMapper.getUserFavoriteListByCondition(conditionBo);
            PageInfo<UserFavoriteVo> pageInfo = new PageInfo<>(userFavoriteVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取使用情况
            StatisticsUsageConditionBo condition = new StatisticsUsageConditionBo();
            List<UsageStatisticsTotalVo> usageStatisticsTotalVoList = statisticsUsageMapper.findAllByType(condition);
            if (CollectionUtil.isNotEmpty(usageStatisticsTotalVoList)) {
                Map<Integer, UsageStatisticsTotalVo> usageStatisticsMap = usageStatisticsTotalVoList.stream().collect(Collectors.toMap(UsageStatisticsTotalVo::getType, u -> u));
                for (UserFavoriteVo favoriteVo : list) {
                    long usageCount = null == usageStatisticsMap.get(favoriteVo.getType()) ? 0L : usageStatisticsMap.get(favoriteVo.getType()).getTotalUsageCount();
                    favoriteVo.setUsageCount(usageCount);
                }
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addUserFavorite(UserFavoriteBo userFavoriteBo) {
        UserFavoriteDto userFavorite = new UserFavoriteDto();
        BeanUtils.copyProperties(userFavoriteBo, userFavorite);

        userFavorite.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        userFavorite.setCreateTime(new Date());
        save(userFavorite);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserFavorite(UserFavoriteBo userFavoriteBo) {
        UserFavoriteDto userFavorite = new UserFavoriteDto();
        BeanUtils.copyProperties(userFavoriteBo, userFavorite);

        userFavorite.setUpdateTime(new Date());
        updateById(userFavorite);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFavoriteDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserFavoriteDto::getId, id);

        UserFavoriteDto userFavorite = getOne(lqw);
        if (null == userFavorite) {
            return AjaxResult.fail("用户收藏表数据不存在");
        }

        UserFavoriteVo userFavoriteVo = new UserFavoriteVo();
        BeanUtils.copyProperties(userFavorite, userFavoriteVo);

        return AjaxResult.success(userFavoriteVo);
    }

    @Override
    public AjaxResult deleteUserFavorite(UserFavoriteBo userFavoriteBo) {
        // 删除信息
        LambdaQueryWrapper<UserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFavoriteDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserFavoriteDto::getId, userFavoriteBo.getId());

        UserFavoriteDto userFavorite = getOne(lqw);
        if (null == userFavorite) {
            return AjaxResult.fail("用户收藏表数据不存在");
        }

        UserFavoriteDto dto = new UserFavoriteDto();
        dto.setId(userFavorite.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(userFavoriteBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult favorite(UserFavoriteBo userFavoriteBo) {
        // 删除信息
        LambdaQueryWrapper<UserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFavoriteDto::getUserOid, userFavoriteBo.getUserOid());
        lqw.eq(UserFavoriteDto::getType, userFavoriteBo.getType());

        UserFavoriteDto userFavorite = getOne(lqw);
        Date now = new Date();
        if (null == userFavorite) {
            userFavorite = new UserFavoriteDto();

            userFavorite.setUserOid(userFavoriteBo.getUserOid());
            userFavorite.setOrganizationId(userFavoriteBo.getOrganizationId());
            userFavorite.setType(userFavoriteBo.getType());
            userFavorite.setCreateBy(userFavoriteBo.getUserOid());
            userFavorite.setCreateTime(now);
            userFavorite.setUpdateBy(userFavoriteBo.getUserOid());
            userFavorite.setUpdateTime(now);
            userFavorite.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

            userFavoriteMapper.insert(userFavorite);
        } else {
            userFavorite.setUpdateBy(userFavoriteBo.getUserOid());
            userFavorite.setUpdateTime(now);
            userFavorite.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

            userFavoriteMapper.updateById(userFavorite);
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult cancelFavorite(UserFavoriteBo userFavoriteBo) {
        // 删除信息
        LambdaQueryWrapper<UserFavoriteDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFavoriteDto::getUserOid, userFavoriteBo.getUserOid());
        lqw.eq(UserFavoriteDto::getType, userFavoriteBo.getType());

        UserFavoriteDto userFavorite = getOne(lqw);
        Date now = new Date();
        if (null != userFavorite) {
            userFavorite.setUpdateBy(userFavoriteBo.getUserOid());
            userFavorite.setUpdateTime(now);
            userFavorite.setIsDelete(IsDeleteEnum.ISDELETE.getCode());

            userFavoriteMapper.updateById(userFavorite);
        }

        return AjaxResult.success();
    }
}