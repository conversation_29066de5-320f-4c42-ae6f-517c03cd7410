package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.user.UserBo;
import com.fh.ai.business.entity.bo.userFeedback.UserFeedbackBo;
import com.fh.ai.business.entity.bo.userFeedback.UserFeedbackConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.userFeedback.UserFeedbackDto;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.userFeedback.UserFeedbackVo;
import com.fh.ai.business.mapper.UserFeedbackMapper;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserFeedbackService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.enums.FeedbackStateEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ScoreTypeEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户反馈表接口实现类
 *
 * <AUTHOR>
 * @date 2024-07-01 10:31:51
 */
@Slf4j
@Service
public class UserFeedbackServiceImpl extends ServiceImpl<UserFeedbackMapper, UserFeedbackDto> implements IUserFeedbackService {

    @Resource
    private UserFeedbackMapper userFeedbackMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IOrganizationService organizationService;

    @Override
    public Map<String, Object> getUserFeedbackListByCondition(UserFeedbackConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserFeedbackVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userFeedbackMapper.getUserFeedbackListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserFeedbackVo> userFeedbackVos = userFeedbackMapper.getUserFeedbackListByCondition(conditionBo);
            PageInfo<UserFeedbackVo> pageInfo = new PageInfo<>(userFeedbackVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        if (CollectionUtil.isEmpty(list)) {
            return map;
        }

        // 获取单位信息
        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        List<OrgTreeNodeVo> orgVos = null;
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
        }

        for (UserFeedbackVo vo : list) {
            String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
            vo.setOrgPath(orgNamePath);
        }

        return map;
    }

    @Override
    public AjaxResult addUserFeedback(UserFeedbackBo userFeedbackBo) {
        UserFeedbackDto userFeedback = new UserFeedbackDto();
        BeanUtils.copyProperties(userFeedbackBo, userFeedback);

        userFeedback.setState(FeedbackStateEnum.UNREVIEWED.getCode());
        userFeedback.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        userFeedback.setCreateTime(new Date());
        save(userFeedback);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserFeedback(UserFeedbackBo userFeedbackBo) {
        UserFeedbackDto userFeedback = new UserFeedbackDto();
        BeanUtils.copyProperties(userFeedbackBo, userFeedback);

        userFeedback.setUpdateTime(new Date());
        updateById(userFeedback);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserFeedbackDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFeedbackDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserFeedbackDto::getId, id);

        UserFeedbackDto userFeedback = getOne(lqw);
        if (null == userFeedback) {
            return AjaxResult.fail("用户反馈表数据不存在");
        }

        UserFeedbackVo userFeedbackVo = new UserFeedbackVo();
        BeanUtils.copyProperties(userFeedback, userFeedbackVo);

        return AjaxResult.success(userFeedbackVo);
    }

    @Override
    public AjaxResult updateState(UserFeedbackBo userFeedbackBo) {
        // 更新状态
        LambdaQueryWrapper<UserFeedbackDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFeedbackDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserFeedbackDto::getId, userFeedbackBo.getId());

        UserFeedbackDto userFeedback = getOne(lqw);
        if (null == userFeedback) {
            return AjaxResult.fail("用户反馈表数据不存在");
        }

        UserFeedbackDto dto = new UserFeedbackDto();
        dto.setId(userFeedback.getId());
        dto.setState(userFeedbackBo.getState());
        dto.setUpdateBy(userFeedbackBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult giveFeedbackScore(UserFeedbackBo userFeedbackBo) {
        // 增加积分
        UserBo userBo = new UserBo();
        userBo.setOid(userFeedbackBo.getUserOid());
        userBo.setScore(userFeedbackBo.getGiveScore());
        userBo.setCreateBy(userFeedbackBo.getCreateBy());
        userBo.setRelationId(userFeedbackBo.getId());
        userBo.setScoreType(ScoreTypeEnum.FEEDBACK.getCode());
        userService.giveScore(userBo);

        // 更新状态
        Date now = new Date();
        UserFeedbackDto userFeedbackDto = new UserFeedbackDto();
        userFeedbackDto.setId(userFeedbackBo.getId());
        userFeedbackDto.setState(FeedbackStateEnum.ACCEPTED.getCode());
        userFeedbackDto.setGiveScore(userFeedbackBo.getGiveScore());
        userFeedbackDto.setGiveTime(now);
        userFeedbackDto.setUpdateBy(userFeedbackBo.getCreateBy());
        userFeedbackDto.setUpdateTime(now);
        userFeedbackMapper.updateById(userFeedbackDto);

        NoticeUtil.addOpinion(userFeedbackDto.getId(),userFeedbackBo.getUserOid(),userFeedbackBo.getGiveScore().toString());
        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteUserFeedback(UserFeedbackBo userFeedbackBo) {
        // 删除信息
        LambdaQueryWrapper<UserFeedbackDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserFeedbackDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserFeedbackDto::getId, userFeedbackBo.getId());

        UserFeedbackDto userFeedback = getOne(lqw);
        if (null == userFeedback) {
            return AjaxResult.fail("用户反馈表数据不存在");
        }

        UserFeedbackDto dto = new UserFeedbackDto();
        dto.setId(userFeedback.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(userFeedbackBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}