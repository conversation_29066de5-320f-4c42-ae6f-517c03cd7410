package com.fh.ai.business.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userInstruction.UserInstructionBo;
import com.fh.ai.business.entity.bo.userInstruction.UserInstructionConditionBo;
import com.fh.ai.business.entity.dto.userInstruction.UserInstructionDto;
import com.fh.ai.business.entity.vo.userInstruction.UserInstructionVo;
import com.fh.ai.business.mapper.UserInstructionMapper;
import com.fh.ai.business.service.IUserInstructionService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-08  16:24
 */
@Service
public class UserInstructionServiceImpl extends ServiceImpl<UserInstructionMapper, UserInstructionDto> implements IUserInstructionService {
    @Resource
    private UserInstructionMapper userInstructionMapper;

    @Override
    public AjaxResult addUserInstruct(UserInstructionBo userInstructionBo) {
        UserInstructionDto entity = new UserInstructionDto();
        BeanUtils.copyProperties(userInstructionBo, entity);
        entity.setUuid(IdUtil.simpleUUID());
        entity.setCreateTime(new Date());
        if (save(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updateUserInstruct(UserInstructionBo userInstructionBo) {
        UserInstructionConditionBo conditionBo = new UserInstructionConditionBo();
        conditionBo.setUuid(userInstructionBo.getUuid());
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        UserInstructionVo userInstructionVo = userInstructionMapper.getUserInstructionByOid(conditionBo);
        if (userInstructionVo == null) {
            return AjaxResult.fail("未找到相应数据");
        }
        UserInstructionDto entity = new UserInstructionDto();
        BeanUtils.copyProperties(userInstructionBo, entity);
        entity.setId(userInstructionVo.getId());
        entity.setUpdateTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public Map<String, Object> getUserInstructListByCondition(UserInstructionConditionBo conditionBo) {
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = new HashMap<>(4);
        List<UserInstructionVo> list = Lists.newArrayList();
        long count = 0L;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            list = userInstructionMapper.getUserInstructionListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserInstructionVo> userInstructionVos = userInstructionMapper.getUserInstructionListByCondition(conditionBo);
            PageInfo<UserInstructionVo> pageInfo = new PageInfo<>(userInstructionVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }
        map.put("list", list);
        map.put("count", count);
        return map;
    }

    @Override
    public AjaxResult deleteUserInstruct(UserInstructionBo userInstructionBo) {
        UserInstructionConditionBo conditionBo = new UserInstructionConditionBo();
        conditionBo.setUuid(userInstructionBo.getUuid());
        conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        UserInstructionVo userInstructionVo = userInstructionMapper.getUserInstructionByOid(conditionBo);
        if (userInstructionVo == null) {
            return AjaxResult.fail("未找到相应数据");
        }
        UserInstructionDto entity = new UserInstructionDto();
        entity.setId(userInstructionVo.getId());
        entity.setUpdateBy(userInstructionBo.getUpdateBy());
        entity.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        entity.setUpdateTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }
}
