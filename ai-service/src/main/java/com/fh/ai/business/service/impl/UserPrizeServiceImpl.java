package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.prize.PrizeConditionBo;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeBo;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.prize.PrizeDto;
import com.fh.ai.business.entity.dto.prizeRedeemcode.PrizeRedeemcodeDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.dto.userPrize.UserPrizeDto;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.prize.PrizeVo;
import com.fh.ai.business.entity.vo.systemSetting.SystemSettingVo;
import com.fh.ai.business.entity.vo.userPrize.UserPrizeTotalVo;
import com.fh.ai.business.entity.vo.userPrize.UserPrizeVo;
import com.fh.ai.business.entity.vo.userScoreRecord.ScoreTypeStatisticsVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.*;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.utils.DateTimeUtil;
import com.fh.ai.common.utils.RandomUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户奖品兑换表接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-20 17:00:33
 */
@Slf4j
@Service
public class UserPrizeServiceImpl extends ServiceImpl<UserPrizeMapper, UserPrizeDto> implements IUserPrizeService {

    @Resource
    private UserPrizeMapper userPrizeMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private PrizeMapper prizeMapper;

    @Resource
    private UserScoreRecordMapper userScoreRecordMapper;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private IPrizeService prizeService;

    @Resource
    private ISystemSettingService systemSettingService;

    @Resource
    private IUserScoreRecordService userScoreRecordService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private PrizeRedeemcodeMapper prizeRedeemcodeMapper;

    @Resource
    private IOrganizationService organizationService;

    @Override
    public Map<String, Object> getUserPrizeListByCondition(UserPrizeConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserPrizeVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userPrizeMapper.getUserPrizeListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserPrizeVo> userPrizeVos = userPrizeMapper.getUserPrizeListByCondition(conditionBo);
            PageInfo<UserPrizeVo> pageInfo = new PageInfo<>(userPrizeVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        // 获取单位信息
        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
            .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        List<OrgTreeNodeVo> orgVos = null;
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
        }
        for (UserPrizeVo vo : list) {
            String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
            vo.setOrgPath(orgNamePath);
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public UserPrizeTotalVo getUserPrizeTotal(UserPrizeConditionBo conditionBo) {
        UserPrizeTotalVo userPrizeTotal = userPrizeMapper.getUserPrizeTotal(conditionBo);
        return userPrizeTotal;
    }

    @Override
    public AjaxResult addUserPrize(UserPrizeBo userPrizeBo) {
        UserPrizeDto userPrize = new UserPrizeDto();
        BeanUtils.copyProperties(userPrizeBo, userPrize);

        userPrize.setCreateTime(new Date());
        save(userPrize);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserPrize(UserPrizeBo userPrizeBo) {
        UserPrizeDto userPrize = new UserPrizeDto();
        BeanUtils.copyProperties(userPrizeBo, userPrize);

        userPrize.setUpdateTime(new Date());
        updateById(userPrize);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserPrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserPrizeDto::getId, id);

        UserPrizeDto userPrize = getOne(lqw);
        if (null == userPrize) {
            return AjaxResult.fail("用户奖品兑换表数据不存在");
        }

        UserPrizeVo userPrizeVo = new UserPrizeVo();
        BeanUtils.copyProperties(userPrize, userPrizeVo);

        return AjaxResult.success(userPrizeVo);
    }

    @Override
    public AjaxResult updateState(UserPrizeBo userPrizeBo) {
        // 更新状态
        LambdaQueryWrapper<UserPrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserPrizeDto::getId, userPrizeBo.getId());

        UserPrizeDto userPrize = getOne(lqw);
        if (null == userPrize) {
            return AjaxResult.fail("用户奖品兑换表数据不存在");
        }

        UserPrizeDto dto = new UserPrizeDto();
        dto.setId(userPrize.getId());
        dto.setState(userPrizeBo.getState());
        dto.setUpdateBy(userPrizeBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteUserPrize(UserPrizeBo userPrizeBo) {
        // 删除信息
        LambdaQueryWrapper<UserPrizeDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserPrizeDto::getId, userPrizeBo.getId());

        UserPrizeDto userPrize = getOne(lqw);
        if (null == userPrize) {
            return AjaxResult.fail("用户奖品兑换表数据不存在");
        }

        removeById(userPrizeBo.getId());
        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult redeemPrize(UserPrizeBo userPrizeBo) {
        log.info("奖品兑换开始......");
        RLock lock = redissonClient.getLock("lock:user:" + userPrizeBo.getUserOid() + ":" + userPrizeBo.getPrizeId());
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");

            // 接口调用的幂等性：无论接口被调用多少次，以下业务执行一次
            UserDto userDto =
                userMapper.selectOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getOid, userPrizeBo.getUserOid()));
            if (null == userDto) {
                return AjaxResult.fail("抱歉，该用户不存在");
            }

            PrizeDto prizeDto = prizeMapper.selectById(userPrizeBo.getPrizeId());
            if (null == prizeDto) {
                return AjaxResult.fail("抱歉，该奖品不存在");
            }

            if (null != prizeDto.getScore()) {
                if (null == userDto.getScore() || userDto.getScore().compareTo(prizeDto.getScore()) < 0) {
                    return AjaxResult.fail("积分余额不足，去赚积分吧！");
                }
            }

            if (null == prizeDto.getSupply() || prizeDto.getSupply().compareTo(Constants.ZERO) == 0) {
                return AjaxResult.fail("奖品已兑完，建议选择其他奖品哦！");
            }

            boolean isReduce = prizeService.reduceStock(userPrizeBo.getPrizeId());
            if (!isReduce) {
                return AjaxResult.fail("奖品已兑完，建议选择其他奖品哦！");
            }

            String redeemCode = null;
            if (!RedeemcodeTypeEnum.ORDER.getCode().equals(prizeDto.getRedeemcodeType())) {
                // 获取一个码值
                redeemCode = getRedeemCode(prizeDto);
                if (StringUtils.isBlank(redeemCode)) {
                    return AjaxResult.fail("抱歉，兑换码生成异常");
                }
            }

            Date now = new Date();
            // 保存用户奖品兑换
            UserPrizeDto userPrizeDto = new UserPrizeDto();
            userPrizeDto.setOrderNo(createOrderNo());
            userPrizeDto.setUserOid(userPrizeBo.getUserOid());
            userPrizeDto.setOrganizationId(userDto.getOrganizationId());
            userPrizeDto.setPrizeId(userPrizeBo.getPrizeId());
            userPrizeDto.setPrizeName(prizeDto.getName());
            userPrizeDto.setRedeemCode(redeemCode);
            // 积分为奖品积分
            userPrizeDto.setScore(prizeDto.getScore());

            // update by sunqb at 20250410：放开实物判断，处理逻辑同虚拟
            if (PrizeTypeEnum.CARD.getCode().equals(prizeDto.getType())
                || PrizeTypeEnum.OBJECT.getCode().equals(prizeDto.getType())) {
                if (RedeemcodeTypeEnum.ORDER.getCode().equals(prizeDto.getRedeemcodeType())) {
                    // 不发券的商品兑换成功后为未领取，后台发货后状态为已领取
                    userPrizeDto.setState(UserPrizeStateEnum.NO_RECEIVE.getCode());
                    userPrizeDto.setNotes(Constants.USER_PRIZE_NOTES);
                } else {
                    // 发券
                    userPrizeDto.setState(UserPrizeStateEnum.RECEIVED.getCode());
                    userPrizeDto.setNotes("兑换码：" + redeemCode);
                }
            }

            userPrizeDto.setCreateTime(now);
            userPrizeDto.setCreateBy(userPrizeBo.getUserOid());
            userPrizeMapper.insert(userPrizeDto);

            // 保存用户积分记录信息
            UserScoreRecordDto userScoreRecordDto = new UserScoreRecordDto();
            userScoreRecordDto.setUserOid(userPrizeBo.getUserOid());
            userScoreRecordDto.setOrganizationId(userDto.getOrganizationId());
            userScoreRecordDto.setType(ScoreTypeEnum.PRIZE_EXCHANGE.getCode());
            userScoreRecordDto.setRelationId(prizeDto.getId());
            // 积分为负值
            userScoreRecordDto.setScore(null == prizeDto.getScore() ? 0L : -prizeDto.getScore());
            userScoreRecordDto.setCreateTime(now);
            userScoreRecordDto.setCreateBy(userPrizeBo.getUserOid());
            userScoreRecordMapper.insert(userScoreRecordDto);

            if (RedeemcodeTypeEnum.THIRD_EXIST.getCode().equals(prizeDto.getRedeemcodeType())) {
                // 第三方，更新成已使用
                LambdaQueryWrapper<PrizeRedeemcodeDto> lqw = new LambdaQueryWrapper<>();
                lqw.eq(PrizeRedeemcodeDto::getPrizeId, prizeDto.getId());
                lqw.eq(PrizeRedeemcodeDto::getCode, redeemCode);
                lqw.eq(PrizeRedeemcodeDto::getState, PrizeRedeemcodeState.NOT_USED.getCode());
                lqw.eq(PrizeRedeemcodeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                PrizeRedeemcodeDto prizeRedeemcodeDto = prizeRedeemcodeMapper.selectOne(lqw);
                if (null == prizeRedeemcodeDto) {
                    return AjaxResult.fail("抱歉，兑换码生成异常");
                }

                prizeRedeemcodeDto.setUpdateBy(userPrizeBo.getUserOid());
                prizeRedeemcodeDto.setUpdateTime(now);
                prizeRedeemcodeDto.setState(PrizeRedeemcodeState.USED.getCode());
                prizeRedeemcodeMapper.updateById(prizeRedeemcodeDto);
            }

            // 更新用户积分
            if (null != prizeDto.getScore()) {
                UserDto user = new UserDto();
                user.setId(userDto.getId());
                user.setScore(userDto.getScore() - prizeDto.getScore());
                userMapper.updateById(user);
                NoticeUtil.addExchange(prizeDto.getId(), userPrizeBo.getUserOid(), prizeDto.getScore().toString(),
                    prizeDto.getName());
            }

            return AjaxResult.success(redeemCode);

        } catch (Exception e) {
            log.error("奖品兑换异常:{}", e.getMessage());
            // 出错，谢谢参与
            return AjaxResult.success();
        } finally {
            lock.unlock();
            log.info("release lock");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult lottery(UserPrizeBo userPrizeBo) {
        log.info("抽奖开始......");
        RLock lock = redissonClient.getLock("lock:lottery");
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");

            UserDto userDto =
                userMapper.selectOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getOid, userPrizeBo.getUserOid()));
            if (null == userDto) {
                return AjaxResult.fail("抱歉，该用户不存在");
            }

            // 积分抽奖，每日上限50
            UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
            conditionBo.setUserOid(userPrizeBo.getUserOid());
            conditionBo.setReceiveTime(new Date());
            conditionBo.setType(ScoreTypeEnum.SCORE_LOTTERY.getCode());
            List<ScoreTypeStatisticsVo> scoreTypeStatistics =
                userScoreRecordService.getScoreTypeStatistics(conditionBo);
            if (CollectionUtil.isNotEmpty(scoreTypeStatistics)) {
                if (scoreTypeStatistics.get(0).isReachTop()) {
                    return AjaxResult.fail("抱歉，达到每日上限");
                }
            }

            if (null == userDto.getScore() || userDto.getScore().compareTo(Constants.SCORE_TOP_LOTTERY) < 0) {
                return AjaxResult.fail("抱歉，积分不够抽奖");
            }

            // 随机获取商品
            PrizeConditionBo condition = new PrizeConditionBo();
            condition.setState(PrizeStateEnum.ON_SHELF.getCode());
            condition.setMinSupply(1L);
            List<PrizeVo> prizeVos = prizeMapper.getPrizeListByCondition(condition);
            if (CollectionUtil.isEmpty(prizeVos)) {
                // 没有商品，谢谢参与
                return AjaxResult.fail("抱歉，商品没有了");
            }

            // 确定取值范围：奖品数除以获奖概率，默认100%
            int lotteryRate = 100;
            try {
                SystemSettingVo systemSettingVo = systemSettingService.getDetail(Constants.LOTTERY_RATE);
                lotteryRate = Integer.parseInt(systemSettingVo.getValue());
            } catch (Exception e) {
                log.info("获取积分抽奖获奖概率值失败", e);
            }

            int range = prizeVos.size() * 100 / lotteryRate;
            Random random = new Random();
            int randomNumber = random.nextInt(range);
            if (randomNumber > prizeVos.size()) {
                // 没有随机到，谢谢参与
                // 保存用户积分记录信息
                UserScoreRecordDto userScoreRecordDto = new UserScoreRecordDto();
                userScoreRecordDto.setUserOid(userPrizeBo.getUserOid());
                userScoreRecordDto.setOrganizationId(userDto.getOrganizationId());
                userScoreRecordDto.setType(ScoreTypeEnum.SCORE_LOTTERY.getCode());
                // userScoreRecordDto.setRelationId(prizeVo.getId());
                // 积分为负值
                userScoreRecordDto.setScore(-Constants.SCORE_TOP_LOTTERY);
                userScoreRecordDto.setCreateTime(new Date());
                userScoreRecordDto.setCreateBy(userPrizeBo.getUserOid());
                userScoreRecordMapper.insert(userScoreRecordDto);

                // 更新用户积分
                UserDto user = new UserDto();
                user.setId(userDto.getId());
                user.setScore(userDto.getScore() - Constants.SCORE_TOP_LOTTERY);
                userMapper.updateById(user);
                return AjaxResult.success();
            }

            int index = 0 == randomNumber ? 0 : randomNumber - 1;
            PrizeVo prizeVo = prizeVos.get(index);
            if (null == prizeVo) {
                // 没有随机到，谢谢参与
                // 保存用户积分记录信息
                UserScoreRecordDto userScoreRecordDto = new UserScoreRecordDto();
                userScoreRecordDto.setUserOid(userPrizeBo.getUserOid());
                userScoreRecordDto.setOrganizationId(userDto.getOrganizationId());
                userScoreRecordDto.setType(ScoreTypeEnum.SCORE_LOTTERY.getCode());
                // userScoreRecordDto.setRelationId(prizeVo.getId());
                // 积分为负值
                userScoreRecordDto.setScore(-Constants.SCORE_TOP_LOTTERY);
                userScoreRecordDto.setCreateTime(new Date());
                userScoreRecordDto.setCreateBy(userPrizeBo.getUserOid());
                userScoreRecordMapper.insert(userScoreRecordDto);

                // 更新用户积分
                UserDto user = new UserDto();
                user.setId(userDto.getId());
                user.setScore(userDto.getScore() - Constants.SCORE_TOP_LOTTERY);
                userMapper.updateById(user);
                return AjaxResult.success();
            }

            if (null == prizeVo.getSupply() || prizeVo.getSupply().compareTo(Constants.ZERO) == 0) {
                // 库存没了，谢谢参与
                return AjaxResult.fail("抱歉，商品库存没了");
            }

            boolean isReduce = prizeService.reduceStock(prizeVo.getId());
            if (!isReduce) {
                // 库存没了，谢谢参与
                return AjaxResult.fail("抱歉，商品库存没了");
            }

            Date now = new Date();
            String redeemCode = createRedeemCode();

            // 保存用户奖品兑换
            UserPrizeDto userPrizeDto = new UserPrizeDto();
            userPrizeDto.setUserOid(userPrizeBo.getUserOid());
            userPrizeDto.setOrganizationId(userDto.getOrganizationId());
            userPrizeDto.setPrizeId(prizeVo.getId());
            userPrizeDto.setPrizeName(prizeVo.getName());
            userPrizeDto.setRedeemCode(redeemCode);
            // 积分为消耗积分
            userPrizeDto.setScore(Constants.SCORE_TOP_LOTTERY);
            userPrizeDto.setState(UserPrizeStateEnum.NO_RECEIVE.getCode());
            userPrizeDto.setCreateTime(now);
            userPrizeDto.setCreateBy(userPrizeBo.getUserOid());
            userPrizeMapper.insert(userPrizeDto);

            // 保存用户积分记录信息
            UserScoreRecordDto userScoreRecordDto = new UserScoreRecordDto();
            userScoreRecordDto.setUserOid(userPrizeBo.getUserOid());
            userScoreRecordDto.setOrganizationId(userDto.getOrganizationId());
            userScoreRecordDto.setType(ScoreTypeEnum.SCORE_LOTTERY.getCode());
            userScoreRecordDto.setRelationId(prizeVo.getId());
            // 积分为负值
            userScoreRecordDto.setScore(-Constants.SCORE_TOP_LOTTERY);
            userScoreRecordDto.setCreateTime(now);
            userScoreRecordDto.setCreateBy(userPrizeBo.getUserOid());
            userScoreRecordMapper.insert(userScoreRecordDto);

            // 更新用户积分
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setScore(userDto.getScore() - Constants.SCORE_TOP_LOTTERY);
            userMapper.updateById(user);

            UserPrizeVo userPrizeVo = new UserPrizeVo();
            BeanUtils.copyProperties(userPrizeDto, userPrizeVo);
            userPrizeVo.setPicture(prizeVo.getPicture());

            return AjaxResult.success(userPrizeVo);
        } catch (Exception e) {
            log.error("抽奖异常:{}", e.getMessage());
            // 出错，谢谢参与
            return AjaxResult.success();
        } finally {
            lock.unlock();
            log.info("release lock");
        }
    }

    /**
     * 获取兑换码
     *
     * @param prizeDto
     * @return
     */
    public String getRedeemCode(PrizeDto prizeDto) {
        if (RedeemcodeTypeEnum.AUTO.getCode().equals(prizeDto.getRedeemcodeType())) {
            // 自动生成
            return createRedeemCode();
        } else if (RedeemcodeTypeEnum.THIRD_EXIST.getCode().equals(prizeDto.getRedeemcodeType())) {
            // 第三方现存，取其中一个码
            LambdaQueryWrapper<PrizeRedeemcodeDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(PrizeRedeemcodeDto::getPrizeId, prizeDto.getId());
            lqw.eq(PrizeRedeemcodeDto::getState, PrizeRedeemcodeState.NOT_USED.getCode());
            lqw.eq(PrizeRedeemcodeDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.last("limit 1");
            List<PrizeRedeemcodeDto> prizeRedeemcodeDtos = prizeRedeemcodeMapper.selectList(lqw);
            if (CollectionUtil.isNotEmpty(prizeRedeemcodeDtos)) {
                PrizeRedeemcodeDto prizeRedeemcodeDto = prizeRedeemcodeDtos.get(0);

                // 返回码值
                return prizeRedeemcodeDto.getCode();
            }
        }

        return null;
    }

    /**
     * 生成兑换码
     */
    public String createRedeemCode() {
        // 已生成的code
        HashSet<String> codeSet = getRedisCodeValueList();

        // 生成兑换码
        List<String> codeValueHashSet = new ArrayList<>();
        while (codeValueHashSet.size() == 0) {
            // 码值(不含数字0、1、2、5 字母 o、l、s、z)
            String redeemCode = RandomUtil.getStringRandomTextBook(8);
            if (!codeSet.contains(redeemCode)) {
                codeValueHashSet.add(redeemCode);
            }
        }

        String codeKey = RedisKeyEnum.REDEEM_CODE_LIST.getValue();
        String codeValue = codeValueHashSet.get(0);
        redisComponent.set(codeKey + codeValue, codeValue);

        return codeValue;
    }

    /**
     * 获取已生成的激活码
     *
     * @return
     */
    private HashSet<String> getRedisCodeValueList() {
        String codeKey = RedisKeyEnum.REDEEM_CODE_LIST.getValue();

        HashSet<String> codeSet = new HashSet<String>();
        Set<String> keysNew = redisComponent.keys(codeKey + "*");
        // 整合所有 该键类型 的码值
        for (String key : keysNew) {
            Object objCode = redisComponent.get(key);
            codeSet.add(objCode.toString());
        }

        return codeSet;
    }

    /**
     * 生成订单编号
     */
    public String createOrderNo() {
        String code = "ZL" + DateTimeUtil.formatYmdhmsS(new Date());

        Random random = new Random();
        int ends = random.nextInt(999);
        // 如果不足两位，前面补0
        String format = String.format("%03d", ends);

        return code + format;
    }

}