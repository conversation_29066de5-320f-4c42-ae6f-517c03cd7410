package com.fh.ai.business.service.impl;

import static com.fh.ai.common.enums.ScoreTypeEnum.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.dto.systemSetting.SystemSettingDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.vo.userScoreRecord.ScoreTypeStatisticsVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo;
import com.fh.ai.business.mapper.SystemSettingMapper;
import com.fh.ai.business.mapper.UserMapper;
import com.fh.ai.business.mapper.UserScoreRecordMapper;
import com.fh.ai.business.service.IUserScoreRecordService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.ScoreTypeEnum;
import com.fh.ai.common.enums.ScoreTypeValueEnum;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户积分记录接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-20 17:14:17
 */
@Slf4j
@Service
public class UserScoreRecordServiceImpl extends ServiceImpl<UserScoreRecordMapper, UserScoreRecordDto>
    implements IUserScoreRecordService {

    @Resource
    private UserScoreRecordMapper userScoreRecordMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private SystemSettingMapper systemSettingMapper;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Map<String, Object> getUserScoreRecordListByCondition(UserScoreRecordConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserScoreRecordVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userScoreRecordMapper.getUserScoreRecordListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserScoreRecordVo> userScoreRecordVos =
                userScoreRecordMapper.getUserScoreRecordListByCondition(conditionBo);
            PageInfo<UserScoreRecordVo> pageInfo = new PageInfo<>(userScoreRecordVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public List<ScoreTypeStatisticsVo> getScoreTypeStatistics(UserScoreRecordConditionBo conditionBo) {
        List<ScoreTypeStatisticsVo> scoreTypeStatistics = userScoreRecordMapper.getScoreTypeStatistics(conditionBo);
        if (null != conditionBo.getReceiveTime()) {
            // 获取每日积分上限配置
            Map<String, Long> scoreTopSetting = getScoreTopSetting();

            Map<String, Long> scoreOnceSetting = getScoreOnceSetting();

            // 查询具体某一天
            // 设置是否达到上限
            for (ScoreTypeStatisticsVo statisticsVo : scoreTypeStatistics) {
                Long totalScore = statisticsVo.getTotalScore();
                if (ScoreTypeEnum.LOGIN.getCode().equals(statisticsVo.getType())) {
                    // 每日登录，每日上限2
                    Long scoreTopLogin = null != scoreTopSetting.get(Constants.SCORE_TOP_LOGIN_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_LOGIN_NAME) : Constants.SCORE_TOP_LOGIN;
                    statisticsVo.setTopScore(scoreTopLogin);
                    if (null != totalScore && totalScore.compareTo(scoreTopLogin) >= 0) {
                        statisticsVo.setReachTop(true);
                    }

                    // 次数上限= 积分上限÷一次积分
                    long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                    if (score != 0L) {
                        statisticsVo.setTopCount(scoreTopLogin / score);
                    }
                } else if (ScoreTypeEnum.ANSWER.getCode().equals(statisticsVo.getType())) {
                    // 智能问答，每日上限18
                    Long scoreTopAnswer = null != scoreTopSetting.get(Constants.SCORE_TOP_ANSWER_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_ANSWER_NAME) : Constants.SCORE_TOP_ANSWER;
                    statisticsVo.setTopScore(scoreTopAnswer);
                    if (null != totalScore && totalScore.compareTo(scoreTopAnswer) >= 0) {
                        statisticsVo.setReachTop(true);
                    }

                    // 次数上限= 积分上限÷一次积分
                    long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                    if (score != 0L) {
                        statisticsVo.setTopCount(scoreTopAnswer / score);
                    }
                } else if (COPYWRITING_CREATE.getCode().equals(statisticsVo.getType())) {
                    // 文案生成，每日上限20
                    Long scoreTopCopywritingCreate =
                        null != scoreTopSetting.get(Constants.SCORE_TOP_COPYWRITING_CREATE_NAME)
                            ? scoreTopSetting.get(Constants.SCORE_TOP_COPYWRITING_CREATE_NAME)
                            : Constants.SCORE_TOP_COPYWRITING_CREATE;
                    statisticsVo.setTopScore(scoreTopCopywritingCreate);
                    if (null != totalScore && totalScore.compareTo(scoreTopCopywritingCreate) >= 0) {
                        statisticsVo.setReachTop(true);
                    }

                    // 次数上限= 积分上限÷一次积分
                    long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                    if (score != 0L) {
                        statisticsVo.setTopCount(scoreTopCopywritingCreate / score);
                    }
                } else if (ScoreTypeEnum.WORKS_CREATE.getCode().equals(statisticsVo.getType())) {
                    // 作品创作，每日上限10
                    Long scoreTopWorksCreate = null != scoreTopSetting.get(Constants.SCORE_TOP_WORKS_CREATE_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_WORKS_CREATE_NAME) : Constants.SCORE_TOP_WORKS_CREATE;
                    statisticsVo.setTopScore(scoreTopWorksCreate);
                    if (null != totalScore && totalScore.compareTo(scoreTopWorksCreate) >= 0) {
                        statisticsVo.setReachTop(true);
                    }

                    // 次数上限= 积分上限÷一次积分
                    long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                    if (score != 0L) {
                        statisticsVo.setTopCount(scoreTopWorksCreate / score);
                    }
                } else if (ScoreTypeEnum.SCORE_LOTTERY.getCode().equals(statisticsVo.getType())) {
                    // 积分抽奖，每日上限50
                    Long scoreTopLottery = null != scoreTopSetting.get(Constants.SCORE_TOP_LOTTERY_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_LOTTERY_NAME) : Constants.SCORE_TOP_LOTTERY;
                    if (null != totalScore && Math.abs(totalScore) >= scoreTopLottery) {
                        statisticsVo.setReachTop(true);
                    }
                } else if (ScoreTypeEnum.IMAGE_CREATE.getCode().equals(statisticsVo.getType())) {
                    // 作品创作，每日上限10
                    Long scoreTopImageCreate = null != scoreTopSetting.get(Constants.SCORE_TOP_IMAGE_CREATE_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_IMAGE_CREATE_NAME) : Constants.SCORE_TOP_IMAGE_CREATE;
                    statisticsVo.setTopScore(scoreTopImageCreate);
                    if (null != totalScore && totalScore.compareTo(scoreTopImageCreate) >= 0) {
                        statisticsVo.setReachTop(true);
                    }

                    // 次数上限= 积分上限÷一次积分
                    long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                    if (score != 0L) {
                        statisticsVo.setTopCount(scoreTopImageCreate / score);
                    }
                }
            }
        }

        return scoreTypeStatistics;
    }

    @Override
    public List<ScoreTypeStatisticsVo> getUserDailyScoreTypeStatistics(UserScoreRecordConditionBo conditionBo) {
        // 获取用户积分类型统计数据
        List<ScoreTypeStatisticsVo> scoreTypeStatistics = userScoreRecordMapper.getScoreTypeStatistics(conditionBo);
        Map<Integer, ScoreTypeStatisticsVo> statisticsMap = null;
        if (CollectionUtil.isNotEmpty(scoreTypeStatistics)) {
            statisticsMap = scoreTypeStatistics.stream().collect(Collectors.toMap(u -> u.getType(), u -> u));
        }

        // 获取每日积分上限配置
        Map<String, Long> scoreTopSetting = getScoreTopSetting();
        // 获取一次积分配置
        Map<String, Long> scoreOnceSetting = getScoreOnceSetting();

        List<ScoreTypeStatisticsVo> scoreTypeStatisticsVos = Lists.newArrayList();
        Set<String> keys = scoreOnceSetting.keySet();
        for (String key : keys) {
            if (Constants.SCORE_ONCE_LOGIN_NAME.equals(key)) {
                ScoreTypeStatisticsVo statisticsVo =
                    null != statisticsMap ? statisticsMap.get(ScoreTypeEnum.LOGIN.getCode()) : null;
                if (null == statisticsVo) {
                    statisticsVo = new ScoreTypeStatisticsVo();
                    statisticsVo.setType(ScoreTypeEnum.LOGIN.getCode());
                }

                // 每日登录，积分上限
                Long totalScore = statisticsVo.getTotalScore();
                Long scoreTopLogin = null != scoreTopSetting.get(Constants.SCORE_TOP_LOGIN_NAME)
                    ? scoreTopSetting.get(Constants.SCORE_TOP_LOGIN_NAME) : Constants.SCORE_TOP_LOGIN;
                statisticsVo.setTopScore(scoreTopLogin);
                if (null != totalScore && totalScore.compareTo(scoreTopLogin) >= 0) {
                    statisticsVo.setReachTop(true);
                }

                // 一次积分
                long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                statisticsVo.setOnceScore(score);

                // 次数上限= 积分上限÷一次积分
                if (score != 0L) {
                    statisticsVo.setTopCount(scoreTopLogin / score);
                }

                scoreTypeStatisticsVos.add(statisticsVo);
            } else if (Constants.SCORE_ONCE_ANSWER_NAME.equals(key)) {
                ScoreTypeStatisticsVo statisticsVo =
                    null != statisticsMap ? statisticsMap.get(ScoreTypeEnum.ANSWER.getCode()) : null;
                if (null == statisticsVo) {
                    statisticsVo = new ScoreTypeStatisticsVo();
                    statisticsVo.setType(ScoreTypeEnum.ANSWER.getCode());
                }

                // 智能问答，每日上限18
                Long totalScore = statisticsVo.getTotalScore();
                Long scoreTopAnswer = null != scoreTopSetting.get(Constants.SCORE_TOP_ANSWER_NAME)
                    ? scoreTopSetting.get(Constants.SCORE_TOP_ANSWER_NAME) : Constants.SCORE_TOP_ANSWER;
                statisticsVo.setTopScore(scoreTopAnswer);
                if (null != totalScore && totalScore.compareTo(scoreTopAnswer) >= 0) {
                    statisticsVo.setReachTop(true);
                }

                // 一次积分
                long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                statisticsVo.setOnceScore(score);

                // 次数上限= 积分上限÷一次积分
                if (score != 0L) {
                    statisticsVo.setTopCount(scoreTopAnswer / score);
                }

                scoreTypeStatisticsVos.add(statisticsVo);
            } else if (Constants.SCORE_ONCE_COPYWRITING_CREATE_NAME.equals(key)) {
                ScoreTypeStatisticsVo statisticsVo =
                    null != statisticsMap ? statisticsMap.get(COPYWRITING_CREATE.getCode()) : null;
                if (null == statisticsVo) {
                    statisticsVo = new ScoreTypeStatisticsVo();
                    statisticsVo.setType(COPYWRITING_CREATE.getCode());
                }

                // 文案生成，每日上限20
                Long totalScore = statisticsVo.getTotalScore();
                Long scoreTopCopywritingCreate =
                    null != scoreTopSetting.get(Constants.SCORE_TOP_COPYWRITING_CREATE_NAME)
                        ? scoreTopSetting.get(Constants.SCORE_TOP_COPYWRITING_CREATE_NAME)
                        : Constants.SCORE_TOP_COPYWRITING_CREATE;
                statisticsVo.setTopScore(scoreTopCopywritingCreate);
                if (null != totalScore && totalScore.compareTo(scoreTopCopywritingCreate) >= 0) {
                    statisticsVo.setReachTop(true);
                }

                // 一次积分
                long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                statisticsVo.setOnceScore(score);

                // 次数上限= 积分上限÷一次积分
                if (score != 0L) {
                    statisticsVo.setTopCount(scoreTopCopywritingCreate / score);
                }

                scoreTypeStatisticsVos.add(statisticsVo);
//            } else if (Constants.SCORE_ONCE_WORKS_CREATE_NAME.equals(key)) {
//                ScoreTypeStatisticsVo statisticsVo =
//                    null != statisticsMap ? statisticsMap.get(ScoreTypeEnum.WORKS_CREATE.getCode()) : null;
//                if (null == statisticsVo) {
//                    statisticsVo = new ScoreTypeStatisticsVo();
//                    statisticsVo.setType(ScoreTypeEnum.WORKS_CREATE.getCode());
//                }
//
//                // 作品创作，每日上限10
//                Long totalScore = statisticsVo.getTotalScore();
//                Long scoreTopWorksCreate = null != scoreTopSetting.get(Constants.SCORE_TOP_WORKS_CREATE_NAME)
//                    ? scoreTopSetting.get(Constants.SCORE_TOP_WORKS_CREATE_NAME) : Constants.SCORE_TOP_WORKS_CREATE;
//                statisticsVo.setTopScore(scoreTopWorksCreate);
//                if (null != totalScore && totalScore.compareTo(scoreTopWorksCreate) >= 0) {
//                    statisticsVo.setReachTop(true);
//                }
//
//                // 一次积分
//                long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
//                statisticsVo.setOnceScore(score);
//
//                // 次数上限= 积分上限÷一次积分
//                if (score != 0L) {
//                    statisticsVo.setTopCount(scoreTopWorksCreate / score);
//                }
//
//                scoreTypeStatisticsVos.add(statisticsVo);
            } else if (Constants.SCORE_ONCE_IMAGE_CREATE_NAME.equals(key)) {
                ScoreTypeStatisticsVo statisticsVo =
                    null != statisticsMap ? statisticsMap.get(ScoreTypeEnum.IMAGE_CREATE.getCode()) : null;
                if (null == statisticsVo) {
                    statisticsVo = new ScoreTypeStatisticsVo();
                    statisticsVo.setType(ScoreTypeEnum.IMAGE_CREATE.getCode());
                }

                // 作品创作，每日上限10
                Long totalScore = statisticsVo.getTotalScore();
                Long scoreTopImageCreate = null != scoreTopSetting.get(Constants.SCORE_TOP_IMAGE_CREATE_NAME)
                    ? scoreTopSetting.get(Constants.SCORE_TOP_IMAGE_CREATE_NAME) : Constants.SCORE_TOP_IMAGE_CREATE;
                statisticsVo.setTopScore(scoreTopImageCreate);
                if (null != totalScore && totalScore.compareTo(scoreTopImageCreate) >= 0) {
                    statisticsVo.setReachTop(true);
                }

                // 一次积分
                long score = getScoreByType(statisticsVo.getType(), scoreOnceSetting);
                statisticsVo.setOnceScore(score);

                // 次数上限= 积分上限÷一次积分
                if (score != 0L) {
                    statisticsVo.setTopCount(scoreTopImageCreate / score);
                }

                scoreTypeStatisticsVos.add(statisticsVo);
            }

        }

        return scoreTypeStatisticsVos;
    }

    @Override
    public UserScoreVo getUserScore(UserScoreRecordConditionBo conditionBo) {
        UserScoreVo userScore = userScoreRecordMapper.getUserScore(conditionBo);
        return userScore;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addUserScoreRecord(UserScoreRecordBo recordBo) {
        String lockKey = "lock:addUserScoreRecord:" + recordBo.getUserOid();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 获取分布式锁
            lock.lock(9, TimeUnit.SECONDS);
            log.info("获取分布式锁成功");

            // 校验用户是否存在
            UserDto userDto = checkAndGetUser(recordBo.getUserOid());

            // 检查积分上限
            if (!checkScoreLimit(recordBo)) {
                // 积分上线直接成功，忽略处理，也不提示
                return AjaxResult.success();
            }

            // 保存积分记录
            UserScoreRecordDto recordDto = saveScoreRecord(recordBo, userDto);

            // 更新用户总积分和成长值
            updateUserScore(recordBo.getUserOid(), userDto);

            return AjaxResult.success(recordDto);
        } finally {
            lock.unlock();
            log.info("释放分布式锁");
        }
    }

    /**
     * 校验并获取用户信息
     */
    private UserDto checkAndGetUser(String userOid) {
        UserDto userDto = userMapper.selectOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getOid, userOid));
        if (userDto == null) {
            throw new BusinessException("用户不存在");
        }
        return userDto;
    }

    /**
     * 检查积分上限
     */
    private boolean checkScoreLimit(UserScoreRecordBo recordBo) {
        // 获取积分上限配置
        Map<String, Long> scoreTopSetting = getScoreTopSetting();

        // 构建查询条件
        UserScoreRecordConditionBo condition = buildScoreCondition(recordBo);

        // 根据不同类型检查积分上限
        Integer type = recordBo.getType();
        if (ScoreTypeEnum.LOGIN.getCode().equals(type)) {
            return checkLoginScore(condition);
        } else if (ScoreTypeEnum.ANSWER.getCode().equals(type)) {
            return checkDailyLimit(condition, scoreTopSetting, Constants.SCORE_TOP_ANSWER_NAME,
                Constants.SCORE_TOP_ANSWER);
        } else if (ScoreTypeEnum.COPYWRITING_CREATE.getCode().equals(type)) {
            return checkDailyLimit(condition, scoreTopSetting, Constants.SCORE_TOP_COPYWRITING_CREATE_NAME,
                Constants.SCORE_TOP_COPYWRITING_CREATE);
        } else if (ScoreTypeEnum.WORKS_CREATE.getCode().equals(type)) {
            return checkDailyLimit(condition, scoreTopSetting, Constants.SCORE_TOP_WORKS_CREATE_NAME,
                Constants.SCORE_TOP_WORKS_CREATE);
        } else if (ScoreTypeEnum.SCORE_LOTTERY.getCode().equals(type)) {
            return checkLotteryLimit(condition, scoreTopSetting);
        } else if (ScoreTypeEnum.IMAGE_CREATE.getCode().equals(type)) {
            return checkDailyLimit(condition, scoreTopSetting, Constants.SCORE_TOP_IMAGE_CREATE_NAME,
                Constants.SCORE_TOP_IMAGE_CREATE);
        }
        return true;
    }

    /**
     * 构建积分记录查询条件
     */
    private UserScoreRecordConditionBo buildScoreCondition(UserScoreRecordBo recordBo) {
        UserScoreRecordConditionBo condition = new UserScoreRecordConditionBo();
        condition.setUserOid(recordBo.getUserOid());
        condition.setType(recordBo.getType());
        condition.setReceiveTime(recordBo.getReceiveTime() != null ? recordBo.getReceiveTime() : new Date());
        return condition;
    }

    /**
     * 检查登录积分限制
     */
    private boolean checkLoginScore(UserScoreRecordConditionBo condition) {
        List<UserScoreRecordVo> records = userScoreRecordMapper.getUserScoreRecordListByCondition(condition);
        return CollectionUtil.isEmpty(records);
    }

    /**
     * 检查每日积分限制
     */
    private boolean checkDailyLimit(UserScoreRecordConditionBo condition, Map<String, Long> scoreTopSetting,
        String settingKey, Long defaultLimit) {
        UserScoreVo userScore = userScoreRecordMapper.getUserScore(condition);
        if (userScore == null || userScore.getScore() == null) {
            return true;
        }

        Long dailyLimit = scoreTopSetting.get(settingKey);
        if (dailyLimit == null) {
            dailyLimit = defaultLimit;
        }

        return userScore.getScore() < dailyLimit;
    }

    /**
     * 检查抽奖积分限制
     */
    private boolean checkLotteryLimit(UserScoreRecordConditionBo condition, Map<String, Long> scoreTopSetting) {
        UserScoreVo userScore = userScoreRecordMapper.getUserScore(condition);
        if (userScore == null || userScore.getScore() == null) {
            return true;
        }

        Long lotteryLimit = scoreTopSetting.get(Constants.SCORE_TOP_LOTTERY_NAME);
        if (lotteryLimit == null) {
            lotteryLimit = Constants.SCORE_TOP_LOTTERY;
        }

        return Math.abs(userScore.getScore()) < lotteryLimit;
    }

    /**
     * 判断是否为增加积分的类型
     */
    private boolean isAddScoreType(Integer type) {
        return ScoreTypeEnum.LOGIN.getCode().equals(type) || ScoreTypeEnum.ANSWER.getCode().equals(type)
            || COPYWRITING_CREATE.getCode().equals(type) || ScoreTypeEnum.WORKS_CREATE.getCode().equals(type)
            || ScoreTypeEnum.IMAGE_CREATE.getCode().equals(type);
    }

    /**
     * 判断是否为消耗积分的类型
     */
    private boolean isConsumeScoreType(Integer type) {
        return ScoreTypeEnum.PRIZE_EXCHANGE.getCode().equals(type)
            || ScoreTypeEnum.SCORE_LOTTERY.getCode().equals(type);
    }

    /**
     * 计算消耗的积分值
     */
    private Long calculateConsumeScore(Long score) {
        return score != null ? -Math.abs(score) : 0L;
    }

    /**
     * 获取用户总积分
     */
    private UserScoreVo getUserTotalScore(String userOid) {
        UserScoreRecordConditionBo condition = new UserScoreRecordConditionBo();
        condition.setUserOid(userOid);
        return userScoreRecordMapper.getUserScore(condition);
    }

    /**
     * 保存积分记录
     */
    private UserScoreRecordDto saveScoreRecord(UserScoreRecordBo recordBo, UserDto userDto) {
        UserScoreRecordDto recordDto = new UserScoreRecordDto();
        BeanUtils.copyProperties(recordBo, recordDto);

        // 计算积分值
        Long score = calculateScore(recordBo);

        recordDto.setOrganizationId(userDto.getOrganizationId());
        recordDto.setScore(score);
        recordDto.setCreateBy(recordBo.getUserOid());
        recordDto.setCreateTime(new Date());

        // 保存积分值
        try {
            save(recordDto);
        } catch (DuplicateKeyException e) {
            // UNIQUE KEY `user_type_time` (`user_oid`,`type`,`create_time`) USING BTREE
            log.info("用户重复获取积分: {}", JSON.toJSONString(recordDto));
            throw new BusinessException("请勿重复获取积分");
        }

        return recordDto;
    }

    /**
     * 计算积分值
     */
    private Long calculateScore(UserScoreRecordBo recordBo) {
        // 新增积分的类型
        if (isAddScoreType(recordBo.getType())) {
            return getScoreByType(recordBo.getType());
        }

        // 消耗积分的类型
        if (isConsumeScoreType(recordBo.getType())) {
            return calculateConsumeScore(recordBo.getScore());
        }

        // 优秀作品没有积分，这里保留重构前的代码逻辑，后续再看
        if (ScoreTypeEnum.EXCELLENT_WORKS.getCode().equals(recordBo.getType())) {
            return recordBo.getScore();
        }

        return 0L;
    }

    /**
     * 更新用户总积分和成长值
     */
    private void updateUserScore(String userOid, UserDto userDto) {
        UserScoreVo userScore = getUserTotalScore(userOid);
        log.info("用户总积分统计: userId={}, score={}, growth={}", userOid, userScore.getScore(), userScore.getGrowth());

        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setScore(userScore.getScore());
        user.setGrowth(userScore.getGrowth());

        userMapper.updateById(user);
    }

    /**
     * 获取一次积分，优先从数据库获取，数据库获取为空则使用默认值
     * @param type
     * @return
     */
    private long getScoreByType(Integer type) {
        Long value = 0L;
        // 获取每次积分配置
        Map<String, Long> scoreOnceSetting = getScoreOnceSetting();
        if (ScoreTypeValueEnum.LOGIN.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_LOGIN_NAME);
        } else if (ScoreTypeValueEnum.ANSWER.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_ANSWER_NAME);
        } else if (ScoreTypeValueEnum.COPYWRITING_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_COPYWRITING_CREATE_NAME);
        } else if (ScoreTypeValueEnum.WORKS_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_WORKS_CREATE_NAME);
        } else if (ScoreTypeValueEnum.IMAGE_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_IMAGE_CREATE_NAME);
        }

        return null == value ? 0L : value;
    }

    /**
     * 获取一次积分配置，优先从数据库获取，数据库获取为空则使用默认值
     * 
     * @param type
     * @param scoreOnceSetting
     * @return
     */
    private long getScoreByType(Integer type, Map<String, Long> scoreOnceSetting) {
        Long value = 0L;
        // 获取每次积分配置
        if (null == scoreOnceSetting) {
            scoreOnceSetting = getScoreOnceSetting();
        }
        if (ScoreTypeValueEnum.LOGIN.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_LOGIN_NAME);
        } else if (ScoreTypeValueEnum.ANSWER.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_ANSWER_NAME);
        } else if (ScoreTypeValueEnum.COPYWRITING_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_COPYWRITING_CREATE_NAME);
        } else if (ScoreTypeValueEnum.WORKS_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_WORKS_CREATE_NAME);
        } else if (ScoreTypeValueEnum.IMAGE_CREATE.getCode().equals(type)) {
            value = null == scoreOnceSetting ? ScoreTypeValueEnum.getValue(type)
                : scoreOnceSetting.get(Constants.SCORE_ONCE_IMAGE_CREATE_NAME);
        }

        return null == value ? 0L : value;
    }

    @Override
    public boolean haveReceived(UserScoreRecordBo userScoreRecordBo) {
        if (ScoreTypeEnum.LOGIN.getCode().equals(userScoreRecordBo.getType())) {
            // 登录时判断当日是否已获取金豆
            UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
            conditionBo.setUserOid(userScoreRecordBo.getUserOid());
            conditionBo.setReceiveTime(
                null == userScoreRecordBo.getReceiveTime() ? new Date() : userScoreRecordBo.getReceiveTime());
            conditionBo.setType(userScoreRecordBo.getType());
            List<UserScoreRecordVo> list = userScoreRecordMapper.getUserScoreRecordListByCondition(conditionBo);
            if (CollectionUtil.isNotEmpty(list)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public AjaxResult updateUserScoreRecord(UserScoreRecordBo userScoreRecordBo) {
        UserScoreRecordDto userScoreRecord = new UserScoreRecordDto();
        BeanUtils.copyProperties(userScoreRecordBo, userScoreRecord);

        userScoreRecord.setUpdateTime(new Date());
        updateById(userScoreRecord);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserScoreRecordDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserScoreRecordDto::getId, id);

        UserScoreRecordDto userScoreRecord = getOne(lqw);
        if (null == userScoreRecord) {
            return AjaxResult.fail("用户积分记录数据不存在");
        }

        UserScoreRecordVo userScoreRecordVo = new UserScoreRecordVo();
        BeanUtils.copyProperties(userScoreRecord, userScoreRecordVo);

        return AjaxResult.success(userScoreRecordVo);
    }

    @Override
    public AjaxResult deleteUserScoreRecord(UserScoreRecordBo userScoreRecordBo) {
        // 删除信息
        LambdaQueryWrapper<UserScoreRecordDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserScoreRecordDto::getId, userScoreRecordBo.getId());

        UserScoreRecordDto userScoreRecord = getOne(lqw);
        if (null == userScoreRecord) {
            return AjaxResult.fail("用户积分记录数据不存在");
        }

        removeById(userScoreRecordBo.getId());
        return AjaxResult.success();
    }

    public Map<String, Long> getScoreTopSetting() {
        LambdaQueryWrapper<SystemSettingDto> lqw = new LambdaQueryWrapper<>();
        lqw.like(SystemSettingDto::getName, "SCORE_TOP");

        List<SystemSettingDto> systemSettingDtos = systemSettingMapper.selectList(lqw);
        Map<String, Long> scoreTopMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(systemSettingDtos)) {
            for (SystemSettingDto systemSettingDto : systemSettingDtos) {
                scoreTopMap.put(systemSettingDto.getName(), Long.parseLong(systemSettingDto.getValue()));
            }
        }

        return scoreTopMap;
    }

    @Override
    public Map<String, Long> getScoreOnceSetting() {
        LambdaQueryWrapper<SystemSettingDto> lqw = new LambdaQueryWrapper<>();
        lqw.like(SystemSettingDto::getName, "SCORE_ONCE");

        List<SystemSettingDto> systemSettingDtos = systemSettingMapper.selectList(lqw);
        Map<String, Long> scoreTopMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(systemSettingDtos)) {
            for (SystemSettingDto systemSettingDto : systemSettingDtos) {
                scoreTopMap.put(systemSettingDto.getName(), Long.parseLong(systemSettingDto.getValue()));
            }
        }

        return scoreTopMap;
    }
}