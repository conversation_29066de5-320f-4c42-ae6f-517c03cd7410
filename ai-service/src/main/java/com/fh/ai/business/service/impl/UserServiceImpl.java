package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.common.JwtUtil;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.bo.user.UserBo;
import com.fh.ai.business.entity.bo.user.UserConditionBo;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.user.UserDto;
import com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto;
import com.fh.ai.business.entity.vo.historyApp.UserUsageVo;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo;
import com.fh.ai.business.mapper.*;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserScoreRecordService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.common.constants.ConstHttp;
import com.fh.ai.common.enums.*;
import com.fh.ai.common.sms.douyin.DouyinSmsService;
import com.fh.ai.common.sms.enums.SmsSendTypeEnum;

import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.utils.*;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户表接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-20 16:29:06
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDto> implements IUserService {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private DouyinSmsService douyinSmsService;
    @Resource
    private IUserScoreRecordService userScoreRecordService;
    @Resource
    private IOrganizationService organizationService;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserVisitMapper userVisitMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private HistoryAppMapper historyAppMapper;
    @Resource
    private MtHistoryMapper mtHistoryMapper;
    @Resource
    private UserPrizeMapper userPrizeMapper;
    @Resource
    private UserScoreRecordMapper userScoreRecordMapper;

    @Value("${server.encry.privateKey}")
    private String privateKey;
    @Value("${loginSingle}")
    private boolean loginSingle;
    @Value("${initPassword}")
    private String initPassword;
    @Value("${lx.default.organizationId:1}")
    private String lxDefaultOrganizationId;
    @Value("${sso.default.organizationId:1}")
    private String ssoDefaultOrganizationId;

    // 登录最大错误次数阈值
    private static final int MAX_FAIL_ATTEMPTS = 5;
    // 登录失败记录的过期时间，单位为分钟
    private static final long FAIL_ATTEMPT_EXPIRE = 5;
    // 账号锁定时间，单位为分钟
    private static final long LOCK_TIME = 5;

    @Override
    public AjaxResult checkToken(String token) {
        String username = JwtUtil.getUserName(token);
        if (StrUtil.isBlank(token)) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
        }
        // 查询用户信息
        UserBo detailBo = new UserBo();
        detailBo.setAccount(username);
        AjaxResult detail = getDetail(detailBo);
        if (detail.failed() || null == detail.getData()) {
            return AjaxResult.fail(500, "用户名或者密码错误,请检查是否正确");
        }
        UserVo userVo = (UserVo)detail.getData();

        // 其次验证token
        try {
            JwtUtil.verify(token, username, userVo.getPassword());
        } catch (RuntimeException e) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
        }
        // 验证token是否在缓存中
        if (!verifyToken(userVo, token)) {
            return AjaxResult.fail(406, "哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
        }
        // 判断用户状态
        if (IsLocked.YES.getCode().equals(userVo.getIsLocked())) {
            return AjaxResult.fail(406, "该账号已被禁用，请联系管理员重新启用");
        }

        // 刷新缓存
        userVo.setPassword(null);
        String params = JSON.toJSONString(userVo);

        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.set(userKey, params);

        // 如果成功登陆则将用户信息存入request
        request.setAttribute("currentUser", userVo);

        return AjaxResult.success();
    }

    /**
     * 验证token是否在缓存内
     *
     * @param accountVo 用户信息
     * @param token token
     * @return 是否存在
     * @description
     * <AUTHOR>
     * @date 2020/9/2
     */
    private Boolean verifyToken(UserVo accountVo, String token) {
        // 先通过缓存取值
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + accountVo.getAccount();
        return redisComponent.sHasKey(tokenKey, token);
    }

    @Override
    public UserVo passwordLogin(UserBo userBo) {
        // 账号登录失败计数key
        String failLoginCountKey = RedisKeyEnum.USER_FAIL_LOGIN_COUNT_KEY+userBo.getAccount();
        // 锁定账号key
        String lockKey = RedisKeyEnum.USER_LOCK_KEY+userBo.getAccount();
        if (redisComponent.hasKey(lockKey)){
            throw new BusinessException("该账号已被锁定，请稍后重新登录");
        }
        // 判断密码是否正确
        String password = "";
        try {
            String passwordStr = userBo.getPassword();
            password = RSAUtil.privateDecrypt(passwordStr, privateKey);
        } catch (Exception e) {
            log.error("密码解密失败!原因:{}", e.getMessage());
        }

        Date now = new Date();
        // 先查账号
        UserDto userDto = userMapper.selectOne(new LambdaQueryWrapper<UserDto>()
            .eq(UserDto::getAccount, userBo.getAccount()).eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (null == userDto) {
            if (!initPassword.equals(password)) {
                // 不是约定的密码
                long count = failLoginCount(failLoginCountKey, lockKey,userBo.getAccount());
                long remaining = MAX_FAIL_ATTEMPTS - count;
                throw new BusinessException(500, "账号或密码错误，已失败"+count+"次，剩余"+remaining+"次，请联系企业管理员。");
            }
            // 注册账号
            userDto = new UserDto();
            userDto.setOid(IdUtil.simpleUUID());
            userDto.setAccount(userBo.getAccount());
            userDto.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            userDto.setPhone(userBo.getPhone());
            userDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            userDto.setQuota(Constants.MAX_QUOTA);
            userDto.setIsLocked(IsLocked.NO.getCode());
            userDto.setLastLoginTime(now);
            userDto.setChannel(userBo.getChannel());
            userDto.setCreateBy(userDto.getOid());
            userDto.setCreateTime(now);

            userMapper.insert(userDto);
        } else {
            if (!userDto.getPassword().equals(SecureUtil.sha1(userBo.getAccount() + password))) {
                long count = failLoginCount(failLoginCountKey, lockKey,userBo.getAccount());
                long remaining = MAX_FAIL_ATTEMPTS - count;
                throw new BusinessException(500, "账号或密码错误，已失败"+count+"次，剩余"+remaining+"次，请联系企业管理员。");
            }
        }
        // 清除计数key
        redisComponent.del(failLoginCountKey);

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        // 进行token签发
        String token = JwtUtil.signSevenDays(userDto.getAccount(), userDto.getPassword());

        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            throw new BusinessException(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        // //设置过期时间
        // redisComponent.expire(tokenKey, (expireTime / 1000));

        // // 设置用户积分信息
        // UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
        // conditionBo.setUserOid(userDto.getOid());
        // UserScoreVo userScore = userScoreRecordService.getUserScore(conditionBo);
        // if (null != userScore) {
        // userVo.setScore(userScore.getScore());
        // userVo.setGrowth(userScore.getGrowth());
        // }

        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        if (StringUtils.isBlank(userDto.getPassword())) {
            user.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
        }
        user.setLastLoginTime(now);
        userMapper.updateById(user);

        // 返回之前将密码置空
        userVo.setPassword(null);
        return userVo;
    }

    /**
     * 账号登录失败次数累加，锁定账号
     * @param failLoginCountKey
     * @param lockKey
     * @param account
     * @return
     */
    private long failLoginCount(String failLoginCountKey,String lockKey,String account){
        // 累加
        long incr = redisComponent.incr(failLoginCountKey, Constants.NUM_ONE);
        // 首次，设置计数key过期时间
        if (incr==Constants.NUM_ONE){
            redisComponent.expire(failLoginCountKey,FAIL_ATTEMPT_EXPIRE*60);
        }
        // 超过指定次数，锁定账号，删除计数key
        if (incr>=MAX_FAIL_ATTEMPTS){
            redisComponent.set(lockKey,incr,LOCK_TIME*60);
            redisComponent.del(failLoginCountKey);
            log.warn("账号{}，连续输错用户名或者密码超过5次，已被锁定",account);
            throw new BusinessException("连续输入密码"+MAX_FAIL_ATTEMPTS+"次错误，账号已被锁定，请"+LOCK_TIME+"分钟后重新登录");
        }
        return incr;
    }


    /**
     * Query and set organization info.
     *
     * @param organizationId the organization id
     * @param userVo the user vo
     * <AUTHOR>
     * @date 2024 -10-21 15:39:13
     */
    private void queryAndSetOrganizationInfo(Long organizationId, UserVo userVo) {
        // 查询机构信息
        if (organizationId != null) {
            OrganizationDto organizationDto = organizationService.getById(organizationId);
            if (organizationDto != null) {
                userVo.setOrganizationName(organizationDto.getName());

                // 机构起止时间
                Date authStartTime = organizationDto.getAuthStartTime();
                Date authEndTime = organizationDto.getAuthEndTime();
                if (authStartTime == null || authEndTime == null) {
                    // 获取第一层机构的起止时间
                    Long rootOrganizationId =
                        StringKit.getRootOrganizationId(organizationDto.getId(), organizationDto.getSuperiorIds());
                    if (rootOrganizationId != null) {
                        OrganizationDto rootOrganizationDto = organizationService.getById(rootOrganizationId);
                        if (rootOrganizationDto != null) {
                            authStartTime = rootOrganizationDto.getAuthStartTime();
                            authEndTime = rootOrganizationDto.getAuthEndTime();
                        }
                    }
                }
                userVo.setOrgAuthStartTime(authStartTime);
                userVo.setOrgAuthEndTime(authEndTime);
            }
        }
    }

    @Override
    public UserVo ppmLogin(UserBo userBo) {
        log.debug("ppmLogin account:" + userBo.getAccount());

        // 获取当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getAccount, userBo.getAccount())
            .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        // 判断当前用户是否存在
        Date now = new Date();
        if (userDto == null) {
            // 注册账号
            userDto = new UserDto();
            userDto.setOid(IdUtil.simpleUUID());
            userDto.setAccount(userBo.getAccount());
            userDto.setRealName(userBo.getRealName());
            userDto.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            userDto.setPhone(userBo.getPhone());
            userDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            userDto.setQuota(Constants.MAX_QUOTA);
            userDto.setIsLocked(IsLocked.NO.getCode());
            userDto.setLastLoginTime(now);
            userDto.setChannel(userBo.getChannel());
            userDto.setCreateBy(userDto.getOid());
            userDto.setCreateTime(now);

            // 解析部门
            OrganizationDto organizationDto =
                organizationService.analysisDepOrganization(userBo.getPpmDepName(), userDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }
            userDto.setOrganizationId(organizationDto.getId());

            // 新增
            baseMapper.insert(userDto);
        } else {
            // 解析部门
            OrganizationDto organizationDto =
                organizationService.analysisDepOrganization(userBo.getPpmDepName(), userDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }

            // 更新
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setRealName(userBo.getRealName());
            user.setPhone(userBo.getPhone());
            if (StringUtils.isBlank(userDto.getPassword())) {
                user.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            }
            if (!organizationDto.getId().equals(userDto.getOrganizationId())) {
                user.setOrganizationId(organizationDto.getId());
            }
            baseMapper.updateById(user);
        }

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        // 进行token签发
        String token = JwtUtil.sign(userDto.getAccount(), userDto.getPassword());

        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            throw new BusinessException(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        // //设置过期时间
        // redisComponent.expire(tokenKey, (expireTime / 1000));

        // // 设置用户积分信息
        // UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
        // conditionBo.setUserOid(userDto.getOid());
        // UserScoreVo userScore = userScoreRecordService.getUserScore(conditionBo);
        // if (null != userScore) {
        // userVo.setScore(userScore.getScore());
        // userVo.setGrowth(userScore.getGrowth());
        // }

        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setLastLoginTime(now);
        userMapper.updateById(user);

        // 返回之前将密码置空
        userVo.setPassword(null);
        return userVo;
    }

    @Override
    public UserVo ppmAppLogin(UserBo userBo) {
        log.debug("ppmAppLogin account:" + userBo.getAccount());

        // 获取当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getAccount, userBo.getAccount())
            .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        // 判断当前用户是否存在
        Date now = new Date();
        if (userDto == null) {
            // 注册账号
            userDto = new UserDto();
            userDto.setOid(IdUtil.simpleUUID());
            userDto.setAccount(userBo.getAccount());
            userDto.setRealName(userBo.getRealName());
            userDto.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            userDto.setPhone(userBo.getPhone());
            userDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            userDto.setQuota(Constants.MAX_QUOTA);
            userDto.setIsLocked(IsLocked.NO.getCode());
            userDto.setLastLoginTime(now);
            userDto.setChannel(userBo.getChannel());
            userDto.setCreateBy(userDto.getOid());
            userDto.setCreateTime(now);

            // 解析部门
            OrganizationDto organizationDto =
                organizationService.analysisDepOrganization(userBo.getPpmDepName(), userDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }
            userDto.setOrganizationId(organizationDto.getId());

            // 新增
            baseMapper.insert(userDto);
        } else {
            // 解析部门
            OrganizationDto organizationDto =
                organizationService.analysisDepOrganization(userBo.getPpmDepName(), userDto.getOid());
            if (null == organizationDto) {
                throw new BusinessException(500, "该账号所属部门解析为空，请联系管理员确保所属部门正确");
            }

            // 更新
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setRealName(userBo.getRealName());
            user.setPhone(userBo.getPhone());
            if (StringUtils.isBlank(userDto.getPassword())) {
                user.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            }
            if (!organizationDto.getId().equals(userDto.getOrganizationId())) {
                user.setOrganizationId(organizationDto.getId());
            }
            baseMapper.updateById(user);
        }

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }

        // 进行token签发
        String token = JwtUtil.sign(userDto.getAccount(), userDto.getPassword());

        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            throw new BusinessException(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        // //设置过期时间
        // redisComponent.expire(tokenKey, (expireTime / 1000));

        // // 设置用户积分信息
        // UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
        // conditionBo.setUserOid(userDto.getOid());
        // UserScoreVo userScore = userScoreRecordService.getUserScore(conditionBo);
        // if (null != userScore) {
        // userVo.setScore(userScore.getScore());
        // userVo.setGrowth(userScore.getGrowth());
        // }

        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setLastLoginTime(now);
        userMapper.updateById(user);

        // 返回之前将密码置空
        userVo.setPassword(null);
        return userVo;
    }

    @Override
    public AjaxResult userPhoneLogin(UserBo userBo) {
        Object randomCode = redisComponent.get(Constants.ZSFF_USER_RANDOM_CODE + "_" + userBo.getPhone());
        if (randomCode == null || !randomCode.toString().equalsIgnoreCase(userBo.getRandomCode())) {
            String errorCode = Constants.ZSFF_USER_RANDOM_CODE + "_" + userBo.getPhone() + "_error";
            if (redisComponent.hasKey(errorCode)) {
                String errorTime = (String)redisComponent.get(errorCode);
                if (errorTime.equals(Constants.ZSFF_USER_RANDOM_ERROR_TIME)) {
                    redisComponent.del(Constants.ZSFF_USER_RANDOM_CODE + "_" + userBo.getPhone());
                    return AjaxResult.fail("验证码验证已达上限，请重新发送验证码");
                } else {
                    int error = Integer.valueOf(errorTime) + 1;
                    redisComponent.set(errorCode, String.valueOf(error));
                    redisComponent.expire(errorCode, Constants.RANDOM_EXPIRE_SECONDS);
                }

            } else {
                redisComponent.set(errorCode, "1");
                redisComponent.expire(errorCode, Constants.RANDOM_EXPIRE_SECONDS);
            }
            return AjaxResult.fail("短信验证码不正确");
        }

        // 校验成功，清楚缓存code
        redisComponent.del(Constants.ZSFF_USER_RANDOM_CODE + "_" + userBo.getPhone());

        // 获取当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getPhone, userBo.getPhone())
            .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        Date now = new Date();
        if (userDto == null) {
            return AjaxResult.fail("该手机号未查到用户");
        }

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            return AjaxResult.fail("该账号已被禁用，请联系管理员重新启用");
        }

        // 进行token签发
        String token = JwtUtil.signSevenDays(userDto.getAccount(), userDto.getPassword());

        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            return AjaxResult.fail(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);
        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setLastLoginTime(now);
        userMapper.updateById(user);

        // 返回之前将密码置空
        userVo.setPassword(null);
        return AjaxResult.success(userVo);
    }

    @Override
    public AjaxResult userPhoneBind(UserBo userBo) {
        Object randomCode = redisComponent.get(Constants.ZSFF_USER_RANDOM_CODE_BIND_PHONE + "_" + userBo.getPhone());
        if (randomCode == null || !randomCode.toString().equalsIgnoreCase(userBo.getRandomCode())) {
            String errorCode = Constants.ZSFF_USER_RANDOM_CODE_BIND_PHONE + "_" + userBo.getPhone() + "_error";
            if (redisComponent.hasKey(errorCode)) {
                String errorTime = (String)redisComponent.get(errorCode);
                if (errorTime.equals(Constants.ZSFF_USER_RANDOM_ERROR_TIME)) {
                    redisComponent.del(Constants.ZSFF_USER_RANDOM_CODE_BIND_PHONE + "_" + userBo.getPhone());
                    return AjaxResult.fail("验证码验证已达上限，请重新发送验证码");
                } else {
                    int error = Integer.valueOf(errorTime) + 1;
                    redisComponent.set(errorCode, String.valueOf(error));
                    redisComponent.expire(errorCode, Constants.RANDOM_EXPIRE_SECONDS);
                }

            } else {
                redisComponent.set(errorCode, "1");
                redisComponent.expire(errorCode, Constants.RANDOM_EXPIRE_SECONDS);
            }
            return AjaxResult.fail("短信验证码不正确");
        }

        // 当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getOid, userBo.getOid())
            .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            return AjaxResult.fail("该账号已被禁用，请联系管理员重新启用");
        }
        Date now = new Date();
        userDto.setUpdateTime(now);
        userDto.setPhone(userBo.getPhone());
        userMapper.updateById(userDto);
        // 置空敏感信息
        userDto.setPassword(null);
        return AjaxResult.success(userDto);
    }

    @Override
    public AjaxResult sendCode(UserBo userBo) {
        // 校验图形验证码
        if (StringUtils.isBlank(userBo.getImageCode())) {
            return AjaxResult.fail("请输入图形验证码");
        }
        String imageCode = (String) redisComponent.get(Constants.ZSFF_USER_IMAGE_CODE + userBo.getTokenId());
        if (StringUtils.isBlank(imageCode)) {
            return AjaxResult.fail("图形验证码已失效，请重试");
        }
        if (!imageCode.equals(userBo.getImageCode())) {
            return AjaxResult.fail("图形验证码不正确，请重试");
        }

        final String phone = userBo.getPhone();
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.fail("参数错误");
        }
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getPhone, phone).eq(UserDto::getIsDelete,
            IsDeleteEnum.NOTDELETE.getCode()));
        if (userDto == null) {
            // 手机号不存在返回303特殊处理
            return AjaxResult.success(ConstHttp.USER_NOT_FOUND_BY_PHONE);
        }

        // 发送短信业务todo
        final String code = RandomUtil.randomNumbers(6);
        Map<String, String> param = Maps.newHashMap();
        param.put("code", code);
        final boolean b = this.douyinSmsService.sendMessage(phone, SmsSendTypeEnum.CODE, param);
        if (!b) {
            return AjaxResult.fail("短信发送失败");
        }
        this.redisComponent.set(Constants.ZSFF_USER_RANDOM_CODE + "_" + phone, code, Constants.RANDOM_EXPIRE_SECONDS);

        String errorCode = Constants.ZSFF_USER_RANDOM_CODE + "_" + phone + "_error";
        redisComponent.del(errorCode);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult sendCodeBindPhone(UserBo userBo) {
        final String phone = userBo.getPhone();
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.fail("参数错误");
        }
        // 判断手机号是否已经绑定账号
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getPhone, phone).eq(UserDto::getIsDelete,
            IsDeleteEnum.NOTDELETE.getCode()));
        if (userDto != null) {
            // 该手机号已绑定账号
            return AjaxResult.fail("该手机号已绑定账号");
        }

        // 发送短信业务todo
        final String code = RandomUtil.randomNumbers(6);
        Map<String, String> param = Maps.newHashMap();
        param.put("code", code);
        final boolean sendSuccess = this.douyinSmsService.sendMessage(phone, SmsSendTypeEnum.CODE, param);
        if (!sendSuccess) {
            log.error("sendCodeBindPhone短信发送失败:" + userBo.getPhone());
            return AjaxResult.fail("短信发送失败");
        }
        this.redisComponent.set(Constants.ZSFF_USER_RANDOM_CODE_BIND_PHONE + "_" + phone, code,
            Constants.RANDOM_EXPIRE_SECONDS);

        String errorCode = Constants.ZSFF_USER_RANDOM_CODE_BIND_PHONE + "_" + phone + "_error";
        redisComponent.del(errorCode);
        return AjaxResult.success();
    }

    @Override
    public Map<String, Object> getUserListByCondition(UserConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userMapper.getUserListByCondition(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserVo> userVos = userMapper.getUserListByCondition(conditionBo);
            PageInfo<UserVo> pageInfo = new PageInfo<>(userVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取单位信息
            List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            List<OrgTreeNodeVo> orgVos = null;
            List<Long> statisticOrgIds = null;
            if (CollectionUtil.isNotEmpty(orgDtos)) {
                orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
                List<OrganizationDto> statisticOrgList = orgDtos.stream()
                        .filter(o -> OrganizationIsStatistics.IS_STATISTICS.getValue().equals(o.getIsStatistics()))
                        .collect(Collectors.toList());
                statisticOrgIds = statisticOrgList.stream().map(OrganizationDto::getId).collect(Collectors.toList());
            }

            // 获取使用信息
            List<String> oids = list.stream().map(t -> t.getOid()).collect(Collectors.toList());
            HistoryAppConditionBo condition = new HistoryAppConditionBo();
            condition.setUserOidList(oids);
            condition.setStartTime(conditionBo.getStartTime());
            condition.setEndTime(conditionBo.getEndTime());
            List<UserUsageVo> userUsageCountList = historyAppMapper.getUserUsageCount(condition);
            Map<String, UserUsageVo> userUsageMap = null;
            if (CollectionUtil.isNotEmpty(userUsageCountList)) {
                userUsageMap = userUsageCountList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }
            List<UserUsageVo> userMTUsageCount = mtHistoryMapper.getUserUsageCount(condition);
            Map<String, UserUsageVo> userMTUsageMap = null;
            if (CollectionUtil.isNotEmpty(userMTUsageCount)) {
                userMTUsageMap = userMTUsageCount.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            // 获取兑换次数
            UserPrizeConditionBo prizeConditionBo = new UserPrizeConditionBo();
            prizeConditionBo.setUserOidList(oids);
            prizeConditionBo.setStartTime(conditionBo.getStartTime());
            prizeConditionBo.setEndTime(conditionBo.getEndTime());
            List<UserUsageVo> userRedeemCountList = userPrizeMapper.getUserRedeemCount(prizeConditionBo);
            Map<String, UserUsageVo> userRedeemMap = null;
            if (CollectionUtil.isNotEmpty(userRedeemCountList)) {
                userRedeemMap = userRedeemCountList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            // 获取上次登录访问记录
            UserScoreRecordConditionBo recordConditionBo = new UserScoreRecordConditionBo();
            recordConditionBo.setUserOidList(oids);
            recordConditionBo.setStartTime(conditionBo.getStartTime());
            recordConditionBo.setEndTime(conditionBo.getEndTime());
            List<UserScoreRecordVo> lastLoginRecordList =
                userScoreRecordMapper.getLastLoginRecordList(recordConditionBo);
            Map<String, UserScoreRecordVo> userLastLoginMap = null;
            if (CollectionUtil.isNotEmpty(lastLoginRecordList)) {
                userLastLoginMap = lastLoginRecordList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            List<UserScoreVo> userScoreList = userScoreRecordMapper.getUserScoreList(recordConditionBo);
            Map<String, UserScoreVo> userScoreMap = null;
            if (CollectionUtil.isNotEmpty(userScoreList)) {
                userScoreMap = userScoreList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            for (UserVo vo : list) {
                if (vo.getOrganizationId() != null && vo.getOrganizationId() > 0L) {
                    String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
                    vo.setOrgPath(orgNamePath);

                    OrgTreeNodeVo org = organizationService.findStatisticOrg(orgVos, statisticOrgIds, vo.getOrganizationId());
                    vo.setStatisticOrgId(org.getId());
                    vo.setStatisticOrgName(org.getName());
                }

                UserUsageVo userUsageVo = null != userUsageMap ? userUsageMap.get(vo.getOid()) : null;
                if (null != userUsageVo) {
                    vo.setUsageCount(userUsageVo.getUsageCount());
                }
                UserUsageVo userMTUsageVo = null != userMTUsageMap ? userMTUsageMap.get(vo.getOid()) : null;
                if (null != userMTUsageVo) {
                    if (vo.getUsageCount() == null) {
                        vo.setUsageCount(0L);
                    }
                    vo.setUsageCount(vo.getUsageCount() + userMTUsageVo.getUsageCount());
                }

                UserUsageVo userRedeemVo = null != userRedeemMap ? userRedeemMap.get(vo.getOid()) : null;
                if (null != userRedeemVo) {
                    vo.setRedeemCount(userRedeemVo.getRedeemCount());
                }

                UserScoreRecordVo userScoreRecordVo =
                    null != userLastLoginMap ? userLastLoginMap.get(vo.getOid()) : null;
                if (null != userScoreRecordVo) {
                    vo.setLastLoginScoreRecordTime(userScoreRecordVo.getCreateTime());
                }

                vo.setScore(null);
                vo.setGrowth(null);
                UserScoreVo userScoreVo = null != userScoreMap ? userScoreMap.get(vo.getOid()) : null;
                if (null != userScoreVo) {
                    vo.setScore(userScoreVo.getScore());
                    vo.setGrowth(userScoreVo.getGrowth());
                }

                vo.setLevel(getUserLevel(vo.getGrowth()));
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public Map<String, Object> getUserListByOrganizationAdmin(UserConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userMapper.getUserListWithUsageCount(conditionBo);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserVo> userVos = userMapper.getUserListWithUsageCount(conditionBo);
            PageInfo<UserVo> pageInfo = new PageInfo<>(userVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取单位信息
            List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            List<OrgTreeNodeVo> orgVos = null;
            if (CollectionUtil.isNotEmpty(orgDtos)) {
                orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            }

            // 获取使用信息
            List<String> oids = list.stream().map(t -> t.getOid()).collect(Collectors.toList());

            // 获取兑换次数
            UserPrizeConditionBo prizeConditionBo = new UserPrizeConditionBo();
            prizeConditionBo.setUserOidList(oids);
            prizeConditionBo.setStartTime(conditionBo.getStartTime());
            prizeConditionBo.setEndTime(conditionBo.getEndTime());
            List<UserUsageVo> userRedeemCountList = userPrizeMapper.getUserRedeemCount(prizeConditionBo);
            Map<String, UserUsageVo> userRedeemMap = null;
            if (CollectionUtil.isNotEmpty(userRedeemCountList)) {
                userRedeemMap = userRedeemCountList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            // 获取上次登录访问记录
            UserScoreRecordConditionBo recordConditionBo = new UserScoreRecordConditionBo();
            recordConditionBo.setUserOidList(oids);
            recordConditionBo.setStartTime(conditionBo.getStartTime());
            recordConditionBo.setEndTime(conditionBo.getEndTime());
            List<UserScoreRecordVo> lastLoginRecordList =
                    userScoreRecordMapper.getLastLoginRecordList(recordConditionBo);
            Map<String, UserScoreRecordVo> userLastLoginMap = null;
            if (CollectionUtil.isNotEmpty(lastLoginRecordList)) {
                userLastLoginMap = lastLoginRecordList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            List<UserScoreVo> userScoreList = userScoreRecordMapper.getUserScoreList(recordConditionBo);
            Map<String, UserScoreVo> userScoreMap = null;
            if (CollectionUtil.isNotEmpty(userScoreList)) {
                userScoreMap = userScoreList.stream().collect(Collectors.toMap(u -> u.getUserOid(), u -> u));
            }

            for (UserVo vo : list) {
                String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
                vo.setOrgPath(orgNamePath);

                UserUsageVo userRedeemVo = null != userRedeemMap ? userRedeemMap.get(vo.getOid()) : null;
                if (null != userRedeemVo) {
                    vo.setRedeemCount(userRedeemVo.getRedeemCount());
                }

                UserScoreRecordVo userScoreRecordVo =
                        null != userLastLoginMap ? userLastLoginMap.get(vo.getOid()) : null;
                if (null != userScoreRecordVo) {
                    vo.setLastLoginScoreRecordTime(userScoreRecordVo.getCreateTime());
                }

                vo.setScore(null);
                vo.setGrowth(null);
                UserScoreVo userScoreVo = null != userScoreMap ? userScoreMap.get(vo.getOid()) : null;
                if (null != userScoreVo) {
                    vo.setScore(userScoreVo.getScore());
                    vo.setGrowth(userScoreVo.getGrowth());
                }

                vo.setLevel(getUserLevel(vo.getGrowth()));
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult giveScore(UserBo userBo) {
        log.info("增加积分开始......");
        RLock lock = redissonClient.getLock("lock:giveScore:user:" + userBo.getOid());
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");
            // 接口调用的幂等性：无论接口被调用多少次，以下业务执行一次
            if (null != userBo.getRelationId()) {
                // 赠送具体内容
                if (ScoreTypeEnum.ACTIVITY_TASK.getCode().equals(userBo.getScoreType())
                    || ScoreTypeEnum.FEEDBACK.getCode().equals(userBo.getScoreType())) {
                    // 具体活动、意见反馈，只赠送一次，判断是否以赠送
                    List<UserScoreRecordDto> userScoreRecordDtos = userScoreRecordMapper.selectList(
                        new LambdaQueryWrapper<UserScoreRecordDto>().eq(UserScoreRecordDto::getUserOid, userBo.getOid())
                            .eq(UserScoreRecordDto::getRelationId, userBo.getRelationId())
                            .eq(UserScoreRecordDto::getType, userBo.getScoreType()));
                    if (CollectionUtil.isNotEmpty(userScoreRecordDtos)) {
                        // 已赠送
                        return AjaxResult.success();
                    }
                }
            }

            // 获取
            LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(UserDto::getOid, userBo.getOid());

            UserDto userDto = userMapper.selectOne(lqw);
            if (null == userDto) {
                return AjaxResult.fail("用户不存在");
            }

            log.info("当前用户积分:" + userDto.getScore());

            Long score = null == userDto.getScore() ? 0 : userDto.getScore();
            Long growth = null == userDto.getGrowth() ? 0 : userDto.getGrowth();

            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setScore(score + userBo.getScore());
            user.setGrowth(growth + userBo.getScore());
            userMapper.updateById(user);

            // Integer scoreType = ScoreTypeEnum.ACTIVITY_TASK.getCode();
            // if (null != userBo.getScoreType()) {
            // scoreType = userBo.getScoreType();
            // }
            // List<UserScoreRecordDto> oldRecordDto = userScoreRecordMapper.selectList(new
            // LambdaQueryWrapper<UserScoreRecordDto>()
            // .eq(UserScoreRecordDto::getUserOid, userBo.getOid())
            // .eq(UserScoreRecordDto::getRelationId, userBo.getRelationId())
            // .eq(UserScoreRecordDto::getType, scoreType)
            // );
            // if (CollUtil.isEmpty(oldRecordDto)) {
            // 增加积分记录
            UserScoreRecordDto userScoreRecordDto = new UserScoreRecordDto();
            userScoreRecordDto.setUserOid(userBo.getOid());
            userScoreRecordDto.setOrganizationId(userDto.getOrganizationId());
            userScoreRecordDto.setScore(userBo.getScore());
            userScoreRecordDto.setType(userBo.getScoreType());
            // if (null != userBo.getScoreType()) {
            // userScoreRecordDto.setType(userBo.getScoreType());
            // } else {
            // userScoreRecordDto.setType(ScoreTypeEnum.ACTIVITY_TASK.getCode());
            // }
            userScoreRecordDto.setRemark(userBo.getRemark());
            userScoreRecordDto.setRelationId(userBo.getRelationId());
            userScoreRecordDto.setCreateTime(new Date());
            userScoreRecordDto.setCreateBy(userBo.getCreateBy());

            userScoreRecordMapper.insert(userScoreRecordDto);
            // }

        } catch (Exception e) {
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult addUser(UserBo userBo) {
        UserDto user = new UserDto();
        BeanUtils.copyProperties(userBo, user);

        user.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        user.setCreateTime(new Date());
        save(user);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUser(UserBo userBo) {
        UserDto user = new UserDto();
        BeanUtils.copyProperties(userBo, user);

        user.setUpdateTime(new Date());
        updateById(user);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserDto::getId, id);

        UserDto user = getOne(lqw);
        if (null == user) {
            return AjaxResult.fail("用户表数据不存在");
        }

        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(user, userVo);
        // // 设置用户积分信息
        // UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
        // conditionBo.setUserOid(user.getOid());
        // UserScoreVo userScore = userScoreRecordService.getUserScore(conditionBo);
        // if (null != userScore) {
        // userVo.setScore(userScore.getScore());
        // userVo.setGrowth(userScore.getGrowth());
        // }

        return AjaxResult.success(userVo);
    }

    @Override
    public AjaxResult getDetail(String oid) {
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserDto::getOid, oid);

        UserDto user = getOne(lqw);
        if (null == user) {
            return AjaxResult.fail("用户表数据不存在");
        }

        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(user, userVo);

        // // 获取成长值，当前积分
        // // 设置用户积分信息
        // UserScoreRecordConditionBo conditionBo = new UserScoreRecordConditionBo();
        // conditionBo.setUserOid(user.getOid());
        // UserScoreVo userScore = userScoreRecordService.getUserScore(conditionBo);
        // if (null != userScore) {
        // userVo.setScore(userScore.getScore());
        // userVo.setGrowth(userScore.getGrowth());
        // }

        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
            .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            String orgNamePath = organizationService.findOrgNamePath(orgVos, userVo.getOrganizationId());
            userVo.setOrgPath(orgNamePath);
        }

        return AjaxResult.success(userVo);
    }

    @Override
    public AjaxResult getDetail(UserBo userBo) {
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        if (StringUtils.isNotBlank(userBo.getOid())) {
            lqw.eq(UserDto::getOid, userBo.getOid());
        }
        if (StringUtils.isNotBlank(userBo.getAccount())) {
            lqw.eq(UserDto::getAccount, userBo.getAccount());
        }
        lqw.last("limit 1");
        UserDto userDto = getOne(lqw);
        if (null == userDto) {
            return AjaxResult.fail("账号不存在");
        }

        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(userDto, userVo);

        List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
            .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isNotEmpty(orgDtos)) {
            List<OrgTreeNodeVo> orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            String orgNamePath = organizationService.findOrgNamePath(orgVos, userVo.getOrganizationId());
            userVo.setOrgPath(orgNamePath);
        }

        // 查询并塞入组织信息到userVo里面
        queryAndSetOrganizationInfo(userDto.getOrganizationId(), userVo);
        return AjaxResult.success(userVo);
    }

    @Override
    public AjaxResult deleteUser(UserBo userBo) {
        // 删除信息
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserDto::getId, userBo.getId());

        UserDto user = getOne(lqw);
        if (null == user) {
            return AjaxResult.fail("用户表数据不存在");
        }

        UserDto dto = new UserDto();
        dto.setId(user.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(userBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

    @Override
    public UserVo queryByAccount(String account) {
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserDto::getAccount, account);
        final UserDto userDto = this.baseMapper.selectOne(lqw);
        if (userDto == null) {
            return null;
        }
        return BeanUtil.toBean(userDto, UserVo.class);
    }

    @Override
    public boolean updatePasswordByAccount(String account, String rawPassword) {

        UserVo userVo = this.queryByAccount(account);
        if (userVo == null) {
            throw new BusinessException("账号不存在");
        }

        UpdateWrapper<UserDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(UserDto::getId, userVo.getId()).set(UserDto::getPassword,
            PasswordUtil.encode(account, rawPassword));
        return this.update(updateWrapper);
    }

    /**
     * 扣减配额
     *
     * @param userOid
     * @return
     */
    @Override
    public boolean reduceQuota(String userOid) {
        log.info("扣减库存开始......");
        RLock lock = redissonClient.getLock("lock:user:" + userOid);
        try {
            lock.lock(9, TimeUnit.SECONDS);
            log.info("get lock");
            // 接口调用的幂等性：指定时间（这里是9s）内无论接口被调用多少次，以下业务执行一次
            // 获取
            LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(UserDto::getOid, userOid);

            UserDto userDto = userMapper.selectOne(lqw);
            if (null == userDto) {
                return false;
            }

            log.info("当前用户配额:" + userDto.getQuota());
            // 扣减配额
            if (null == userDto.getQuota() || userDto.getQuota().compareTo(Constants.ZERO) <= 0) {
                log.info("扣减失败,无配额可用......");
                return false;
            }

            // 默认扣减1
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setQuota(userDto.getQuota() - 1);
            userMapper.updateById(user);
        } catch (Exception e) {
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }

        return true;
    }

    /**
     * 乐享登录
     * 
     * @param userBo 需要参数：account
     * @return
     */
    @Override
    public UserVo lxLogin(UserBo userBo) {
        log.debug("lxLogin account:" + userBo.getAccount());

        // 获取当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getAccount, userBo.getAccount())
            .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        // 判断当前用户是否存在
        Date now = new Date();
        if (userDto == null) {
            // 注册账号
            userDto = new UserDto();
            userDto.setOid(IdUtil.simpleUUID());
            userDto.setAccount(userBo.getAccount());
            userDto.setRealName(userBo.getRealName());
            userDto.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            userDto.setPhone(userBo.getPhone());
            userDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            userDto.setQuota(Constants.MAX_QUOTA);
            userDto.setIsLocked(IsLocked.NO.getCode());
            userDto.setLastLoginTime(now);
            userDto.setChannel(userBo.getChannel());
            userDto.setCreateBy(userDto.getOid());
            userDto.setCreateTime(now);

            // 乐学的部门id
            userDto.setOrganizationId(Long.valueOf(lxDefaultOrganizationId));
            // 新增
            baseMapper.insert(userDto);
        } else {
            // 更新
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setRealName(userBo.getRealName());
            user.setPhone(userBo.getPhone());
            if (StringUtils.isBlank(userDto.getPassword())) {
                user.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            }
            baseMapper.updateById(user);
        }

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }
        // 进行token签发
        String token = JwtUtil.sign(userDto.getAccount(), userDto.getPassword());
        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            throw new BusinessException(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setPassword(null);
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);

        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setLastLoginTime(now);

        userMapper.updateById(user);
        return userVo;
    }

    /**
     * 学伴（或者使用fh_sso项目）登录
     *
     * @param userBo 需要参数：account
     * @return
     */
    @Override
    public UserVo ssoLogin(UserBo userBo) {
        log.debug("ssoLogin account:" + userBo.getAccount());

        // 获取当前用户信息
        UserDto userDto = getOne(new LambdaQueryWrapper<UserDto>().eq(UserDto::getAccount, userBo.getAccount())
                .eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));

        // 判断当前用户是否存在
        Date now = new Date();
        if (userDto == null) {
            // 注册账号
            userDto = new UserDto();
            userDto.setOid(IdUtil.simpleUUID());
            userDto.setAccount(userBo.getAccount());
            userDto.setRealName(userBo.getRealName());
            userDto.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            userDto.setPhone(userBo.getPhone());
            userDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            userDto.setQuota(Constants.MAX_QUOTA);
            userDto.setIsLocked(IsLocked.NO.getCode());
            userDto.setLastLoginTime(now);
            userDto.setChannel(userBo.getChannel());
            userDto.setCreateBy(userDto.getOid());
            userDto.setCreateTime(now);

            // 乐学的部门id
            userDto.setOrganizationId(Long.valueOf(ssoDefaultOrganizationId));
            // 新增
            baseMapper.insert(userDto);
        } else {
            // 更新
            UserDto user = new UserDto();
            user.setId(userDto.getId());
            user.setRealName(userBo.getRealName());
            user.setPhone(userBo.getPhone());
            if (StringUtils.isBlank(userDto.getPassword())) {
                user.setPassword(SecureUtil.sha1(userBo.getAccount() + initPassword.trim()));
            }
            baseMapper.updateById(user);
        }

        // 验证当前用户是否已经禁用
        if (IsLocked.YES.getCode().equals(userDto.getIsLocked())) {
            throw new BusinessException(205, "该账号已被禁用，请联系管理员重新启用");
        }
        // 进行token签发
        String token = JwtUtil.sign(userDto.getAccount(), userDto.getPassword());
        UserBo detailBo = new UserBo();
        detailBo.setOid(userDto.getOid());
        AjaxResult detailResult = getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            throw new BusinessException(500, "登录失败，请重试");
        }

        UserVo userVo = (UserVo)detailResult.getData();
        userVo.setPassword(null);
        userVo.setToken(token);

        request.setAttribute("currentUser", userVo);
        // 每次登录完成后刷新缓存信息
        String userKey = RedisKeyEnum.USER.getValue() + userVo.getAccount();
        redisComponent.del(userKey);

        String params = JSON.toJSONString(userVo);
        redisComponent.set(userKey, params);
        // 登录完之后将token存入redisSet中
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        if (loginSingle) {
            // 如果登录互顶,则每次登录都进行删除之前所有的token
            redisComponent.del(tokenKey);
        }
        redisComponent.sSet(tokenKey, token);

        // 更新登录时间
        UserDto user = new UserDto();
        user.setId(userDto.getId());
        user.setLastLoginTime(now);

        userMapper.updateById(user);
        return userVo;
    }

    public static String getUserLevel(Long growth) {
        if (null == growth) {
            return "新手";
        }

        String level = "新手";
        if (growth >= 0 && growth <= 300) {
            level = "新手";
        } else if (growth >= 301 && growth <= 800) {
            level = "青铜";
        } else if (growth >= 801 && growth <= 1800) {
            level = "白银";
        } else if (growth >= 1801 && growth <= 3600) {
            level = "黄金";
        } else if (growth >= 3601 && growth <= 7200) {
            level = "铂金";
        } else if (growth >= 7201 && growth <= 12000) {
            level = "钻石";
        } else if (growth >= 12001 && growth <= 20000) {
            level = "大师";
        } else {
            level = "王者";
        }

        return level;
    }

    @Override
    public AjaxResult changePhone(String account, String phone) {
        if (userMapper.checkUserPhone(account, phone) > 0) {
            return AjaxResult.fail("手机号已存在");
        }
        UserVo userVo = this.queryByAccount(account);
        if (userVo == null) {
            throw new BusinessException("账号不存在");
        }
        UserDto entity = new UserDto();
        entity.setId(userVo.getId());
        entity.setPhone(phone);
        userMapper.updateById(entity);

        // 修改手机号成功，用户下线
        String tokenKey = RedisKeyEnum.USER_TOKEN.getValue() + userVo.getAccount();
        redisComponent.del(tokenKey);

        return AjaxResult.success("修改成功");
    }

    @Override
    public AjaxResult resetPassword(String oid) {
        LambdaQueryWrapper<UserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserDto::getOid, oid);

        UserDto user = getOne(lqw);
        if (null == user) {
            return AjaxResult.fail("用户表数据不存在");
        }
        String newPassword = PasswordGeneratorUtil.generateStrongPassword().trim();
        user.setPassword(SecureUtil.sha1(user.getAccount() + newPassword));
        this.updateById(user);

        return AjaxResult.success(newPassword);
    }

    public static void main(String[] args) {
        System.out.println(SecureUtil.sha1( "shimj" + "Goqmsf689%"));
    }
}
