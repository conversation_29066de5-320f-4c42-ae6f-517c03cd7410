package com.fh.ai.business.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userStorage.UserStorageBo;
import com.fh.ai.business.entity.bo.userStorage.UserStorageConditionBo;
import com.fh.ai.business.entity.dto.userStorage.UserStorageDto;
import com.fh.ai.business.entity.vo.userStorage.UserStorageVo;
import com.fh.ai.business.mapper.UserStorageMapper;
import com.fh.ai.business.service.IUserStorageService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.utils.PageUtils;
import com.fh.ai.common.vo.AjaxResult;
/**
 * 用户本地信息存储表，用于存储用户配置等信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-03 16:27:55
 */
@Service
public class UserStorageServiceImpl extends ServiceImpl<UserStorageMapper, UserStorageDto> implements IUserStorageService {

	@Resource
	private UserStorageMapper userStorageMapper;

	@Override
	public Map<String, Object> getUserStorageListByConditionPage(UserStorageConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return PageUtils.handlePagination(
				() -> {
					List<UserStorageVo> userStorageListByCondition = userStorageMapper.getUserStorageListByCondition(condition);
					return userStorageListByCondition;
				},
				condition.getPage(),
				condition.getLimit()
		);
	}

	@Override
	public List<UserStorageVo> getUserStorageListByCondition(UserStorageConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return userStorageMapper.getUserStorageListByCondition(condition);
	}

	@Override
	public AjaxResult addUserStorage(UserStorageBo userStorageBo) {
		UserStorageDto userStorage = new UserStorageDto();
		BeanUtils.copyProperties(userStorageBo, userStorage);
		userStorage.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(userStorage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateUserStorage(UserStorageBo userStorageBo) {
		UserStorageDto userStorage = new UserStorageDto();
		BeanUtils.copyProperties(userStorageBo, userStorage);
		if(updateById(userStorage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public UserStorageVo getUserStorageByCondition(UserStorageConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		UserStorageVo vo = userStorageMapper.getUserStorageByCondition(condition);
		return vo;
	}

}