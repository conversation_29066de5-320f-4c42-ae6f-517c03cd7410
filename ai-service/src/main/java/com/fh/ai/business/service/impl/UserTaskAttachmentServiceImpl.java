package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userTask.UserTaskBo;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentBo;
import com.fh.ai.business.entity.bo.userTaskAttachment.UserTaskAttachmentConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.task.TaskDto;
import com.fh.ai.business.entity.dto.userTask.UserTaskDto;
import com.fh.ai.business.entity.dto.userTaskAttachment.UserTaskAttachmentDto;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.userTask.UserTaskVo;
import com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo;
import com.fh.ai.business.mapper.TaskMapper;
import com.fh.ai.business.mapper.UserTaskAttachmentMapper;
import com.fh.ai.business.mapper.UserTaskMapper;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserTaskAttachmentService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.UserTaskState;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 任务附件表接口实现类
 *
 * <AUTHOR>
 * @date 2024-06-04 14:55:41
 */
@Service
public class UserTaskAttachmentServiceImpl extends ServiceImpl<UserTaskAttachmentMapper, UserTaskAttachmentDto> implements IUserTaskAttachmentService {

    @Resource
    private UserTaskAttachmentMapper userTaskAttachmentMapper;

    @Resource
    private UserTaskServiceImpl userTaskServiceImpl;

    @Resource
    private UserTaskMapper userTaskMapper;

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private IOrganizationService organizationService;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public Map<String, Object> getUserTaskAttachmentListByCondition(UserTaskAttachmentConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserTaskAttachmentVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userTaskAttachmentMapper.getUserTaskAttachmentInfoListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserTaskAttachmentVo> userTaskAttachmentVos = userTaskAttachmentMapper.getUserTaskAttachmentInfoListByCondition(conditionBo);
            PageInfo<UserTaskAttachmentVo> pageInfo = new PageInfo<>(userTaskAttachmentVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult userTaskInfo(UserTaskAttachmentBo userTaskAttachmentBo) {
        TaskDto taskDto = taskMapper.selectById(userTaskAttachmentBo.getTaskId());
        List<UserTaskAttachmentVo> userTaskAttachment = new ArrayList<>();
        List<UserTaskDto> userTask = new ArrayList<>();
        List<Map> arr = new ArrayList<>();
        String multitask = taskDto.getMultitask();
        if (StringUtils.isNotEmpty(multitask)) {
            JSONArray jsonArray = JSON.parseArray(multitask);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String secondType = jsonObject.getString("secondType");
                String appTypes = jsonObject.getString("appTypes");
                if (secondType.equals("2")) {
                    if (CollUtil.isEmpty(userTaskAttachment)) {
                        UserTaskAttachmentConditionBo bo = new UserTaskAttachmentConditionBo();
                        bo.setUserOid(userTaskAttachmentBo.getUserOid());
                        bo.setTaskId(userTaskAttachmentBo.getTaskId());
                        bo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                        userTaskAttachment = userTaskAttachmentMapper.getUserTaskAttachmentInfoListByCondition(bo);
                    }
                    List<UserTaskAttachmentVo> collect = new ArrayList();
                    if (CollUtil.isNotEmpty(userTaskAttachment)) {
                        collect = userTaskAttachment.stream().filter(o -> o.getAppType().toString().equals(appTypes)).collect(Collectors.toList());
                    }
                    if (CollUtil.isNotEmpty(collect)) {
                        for (UserTaskAttachmentVo vo : collect) {
                            HashMap<String, String> map = new HashMap();
                            map.put("secondType", secondType);
                            map.put("appTypes", appTypes);
                            map.put("originalName", vo.getOriginalName());
                            map.put("attachmentOid", vo.getAttachmentOid());
                            map.put("viewPath", vo.getViewPath());
                            map.put("state", "2");
                            arr.add(map);
                        }
                    } else {
                        HashMap<String, String> map = new HashMap();
                        map.put("secondType", secondType);
                        map.put("appTypes", appTypes);
                        map.put("state", "1");
                        map.put("originalName", "");
                        map.put("attachmentOid", "");
                        map.put("viewPath", "");
                        arr.add(map);
                    }

                } else {
                    if (CollUtil.isEmpty(userTask)) {
                        userTask = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                                .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                                .eq(UserTaskDto::getTaskId, userTaskAttachmentBo.getTaskId())
                                .eq(UserTaskDto::getUserOid, userTaskAttachmentBo.getUserOid())
                        );
                    }
                    HashMap<String, String> map = new HashMap();
                    map.put("secondType", secondType);
                    map.put("appTypes", appTypes);
                    map.put("originalName", "");
                    map.put("attachmentOid", "");
                    map.put("viewPath", "");
                    map.put("state", "1");
                    if (CollUtil.isNotEmpty(userTask)) {
                        boolean b = userTask.stream().anyMatch(o -> o.getAppType().toString().equals(appTypes) && o.getSecondType().toString().equals(secondType));
                        map.put("state", b ? "2" : "1");
                    }
                    arr.add(map);
                }
            }
        }
        return AjaxResult.success(arr);
    }

    @Override
    public List<UserTaskAttachmentVo> getUserTaskAttachmentListByTask(Long taskId) {
        List<UserTaskAttachmentVo> list = userTaskAttachmentMapper.getUserTaskAttachmentListByTask(taskId);
        if (CollectionUtil.isNotEmpty(list)) {
            // 获取单位信息
            List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            List<OrgTreeNodeVo> orgVos = null;
            if (CollectionUtil.isNotEmpty(orgDtos)) {
                orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            }

            for (UserTaskAttachmentVo vo : list) {
                String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
                vo.setOrgPath(orgNamePath);
            }
        }
        return list;
    }

    @Override
    public AjaxResult addUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo) {
        RLock lock = redissonClient.getLock("lock:addUserTask");
        try {
            lock.lock(10, TimeUnit.SECONDS);
            if (userTaskAttachmentBo.getSecondType() != null && userTaskAttachmentBo.getSecondType().equals(2)) {
                long count = count(new LambdaQueryWrapper<UserTaskAttachmentDto>()
                        .eq(UserTaskAttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(UserTaskAttachmentDto::getTaskId, userTaskAttachmentBo.getTaskId())
                        .eq(UserTaskAttachmentDto::getAppType, userTaskAttachmentBo.getAppType())
                        .eq(UserTaskAttachmentDto::getUserOid, userTaskAttachmentBo.getUserOid())
                );
                if (count >= 5) {
                    return AjaxResult.fail("作品最多上传5个");
                }
            }

            TaskDto taskDto = taskMapper.selectById(userTaskAttachmentBo.getTaskId());
            if (taskDto.getEndTime().before(new Date())) {
                return AjaxResult.fail("任务已经结束");
            }
            List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                    .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .eq(UserTaskDto::getTaskId, userTaskAttachmentBo.getTaskId())
                    .eq(UserTaskDto::getAppType, userTaskAttachmentBo.getAppType())
                    .eq(UserTaskDto::getSecondType, userTaskAttachmentBo.getSecondType())
                    .eq(UserTaskDto::getUserOid, userTaskAttachmentBo.getUserOid())
            );
            if (CollUtil.isEmpty(userTaskDtos)) {
                UserTaskDto userTaskDto = new UserTaskDto();
                userTaskDto.setUserOid(userTaskAttachmentBo.getUserOid());
                userTaskDto.setTaskId(userTaskAttachmentBo.getTaskId());
                userTaskDto.setOrganizationId(userTaskAttachmentBo.getOrganizationId());
                userTaskDto.setState(UserTaskState.WAIT.getCode());
                userTaskDto.setAppType(userTaskAttachmentBo.getAppType());
                userTaskDto.setSecondType(userTaskAttachmentBo.getSecondType());
                userTaskDto.setCreateBy(userTaskAttachmentBo.getUserOid());
                userTaskDto.setCreateTime(new Date());
                userTaskDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                userTaskMapper.insert(userTaskDto);
            } else {
                UserTaskDto userTaskDto = userTaskDtos.get(0);
                if (userTaskDto.getState().equals(UserTaskState.PASS.getCode())) {
                    return AjaxResult.fail("任务已经审核通过");
                }

                UserTaskBo bo = new UserTaskBo();
                bo.setUserOid(userTaskAttachmentBo.getUserOid());
                bo.setTaskId(taskDto.getId());
                bo.setUpdateBy(userTaskAttachmentBo.getUserOid());
                bo.setState(UserTaskState.WAIT.getCode());
                userTaskServiceImpl.updateState(bo);
            }
            if (StringUtils.isNotEmpty(userTaskAttachmentBo.getAttachmentOid())) {
                UserTaskAttachmentDto userTaskAttachment = new UserTaskAttachmentDto();
                BeanUtils.copyProperties(userTaskAttachmentBo, userTaskAttachment);

                userTaskAttachment.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                userTaskAttachment.setCreateTime(new Date());
                save(userTaskAttachment);
            }
            //任务全是仅体验，直接审核通过
            if (userTaskAttachmentBo.getSecondType() != null && userTaskAttachmentBo.getSecondType().equals(1)) {
                String secondType = taskDto.getSecondType();
                Integer num = taskDto.getNum();
                if (StringUtils.isNotEmpty(secondType) && !secondType.contains("2") ) {
                    Long aLong = userTaskMapper.selectCount(new LambdaQueryWrapper<UserTaskDto>()
                            .eq(UserTaskDto::getUserOid, userTaskAttachmentBo.getUserOid())
                            .eq(UserTaskDto::getTaskId, taskDto.getId())
                            .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    );
                    if(num.equals(aLong.intValue())){
                        UserTaskBo bo = new UserTaskBo();
                        bo.setUserOid(userTaskAttachmentBo.getUserOid());
                        bo.setTaskId(taskDto.getId());
                        bo.setUpdateBy(userTaskAttachmentBo.getUserOid());
                        bo.setState(UserTaskState.PASS.getCode());
                        userTaskServiceImpl.updateState(bo);
                    }
                }
            }

            } catch (Exception e) {

        } finally {
            lock.unlock();
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo) {
        UserTaskAttachmentDto userTaskAttachment = new UserTaskAttachmentDto();
        BeanUtils.copyProperties(userTaskAttachmentBo, userTaskAttachment);

        userTaskAttachment.setUpdateTime(new Date());
        updateById(userTaskAttachment);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserTaskAttachmentDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserTaskAttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserTaskAttachmentDto::getId, id);

        UserTaskAttachmentDto userTaskAttachment = getOne(lqw);
        if (null == userTaskAttachment) {
            return AjaxResult.fail("任务附件表数据不存在");
        }

        UserTaskAttachmentVo userTaskAttachmentVo = new UserTaskAttachmentVo();
        BeanUtils.copyProperties(userTaskAttachment, userTaskAttachmentVo);

        return AjaxResult.success(userTaskAttachmentVo);
    }

    @Override
    public AjaxResult deleteUserTaskAttachment(UserTaskAttachmentBo userTaskAttachmentBo) {
        RLock lock = redissonClient.getLock("lock:deleteUserTask");
        try {
            lock.lock(10, TimeUnit.SECONDS);
            // 查找审核成功的记录
            List<UserTaskDto> userTaskDtos = userTaskMapper.selectList(new LambdaQueryWrapper<UserTaskDto>()
                    .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                    .eq(UserTaskDto::getTaskId, userTaskAttachmentBo.getTaskId())
                    .eq(UserTaskDto::getState, UserTaskState.PASS.getCode())
                    .eq(UserTaskDto::getUserOid, userTaskAttachmentBo.getUserOid()));

            if (CollUtil.isNotEmpty(userTaskDtos)) {
                return AjaxResult.fail("任务已经审核,无法删除");
            }

            // 删除信息
            LambdaQueryWrapper<UserTaskAttachmentDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(UserTaskAttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw.eq(UserTaskAttachmentDto::getAttachmentOid, userTaskAttachmentBo.getAttachmentOid());
            lqw.eq(UserTaskAttachmentDto::getTaskId, userTaskAttachmentBo.getTaskId());
            lqw.eq(UserTaskAttachmentDto::getAppType, userTaskAttachmentBo.getAppType());
            lqw.eq(UserTaskAttachmentDto::getUserOid, userTaskAttachmentBo.getUserOid());

            UserTaskAttachmentDto userTaskAttachment = getOne(lqw);
            if (null == userTaskAttachment) {
                return AjaxResult.fail("任务附件表数据不存在");
            }

            TaskDto taskDto = taskMapper.selectById(userTaskAttachment.getTaskId());
            if (taskDto.getEndTime().before(new Date())) {
                return AjaxResult.fail("任务已经结束");
            }

            UserTaskAttachmentDto dto = new UserTaskAttachmentDto();
            dto.setId(userTaskAttachment.getId());
            dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
            dto.setUpdateBy(userTaskAttachmentBo.getUserOid());
            dto.setUpdateTime(new Date());
            updateById(dto);

            LambdaQueryWrapper<UserTaskAttachmentDto> lqw2 = new LambdaQueryWrapper<>();
            lqw2.eq(UserTaskAttachmentDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
            lqw2.eq(UserTaskAttachmentDto::getTaskId, userTaskAttachmentBo.getTaskId());
            lqw2.eq(UserTaskAttachmentDto::getUserOid, userTaskAttachmentBo.getUserOid());
            lqw2.eq(UserTaskAttachmentDto::getAppType, userTaskAttachmentBo.getAppType());

            Long count = count(lqw2);
            if (count.equals(0L)) {
                userTaskMapper.update(null, new LambdaUpdateWrapper<UserTaskDto>()
                        .eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode())
                        .eq(UserTaskDto::getTaskId, userTaskAttachmentBo.getTaskId())
                        .eq(UserTaskDto::getSecondType, 2)
                        .eq(UserTaskDto::getAppType, userTaskAttachmentBo.getAppType())
                        .eq(UserTaskDto::getUserOid, userTaskAttachmentBo.getUserOid())
                        .ne(UserTaskDto::getState, UserTaskState.PASS.getCode())
                        .set(UserTaskDto::getIsDelete, IsDeleteEnum.ISDELETE.getCode())
                );
            }
        } catch (Exception e) {

        } finally {
            lock.unlock();
        }

        return AjaxResult.success();
    }

}