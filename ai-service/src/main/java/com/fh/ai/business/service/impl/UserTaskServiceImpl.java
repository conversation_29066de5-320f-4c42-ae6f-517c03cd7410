package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppConditionBo;
import com.fh.ai.business.entity.bo.user.UserBo;
import com.fh.ai.business.entity.bo.userPrize.UserPrizeConditionBo;
import com.fh.ai.business.entity.bo.userScoreRecord.UserScoreRecordConditionBo;
import com.fh.ai.business.entity.bo.userTask.UserTaskBo;
import com.fh.ai.business.entity.bo.userTask.UserTaskConditionBo;
import com.fh.ai.business.entity.dto.organization.OrganizationDto;
import com.fh.ai.business.entity.dto.task.TaskDto;
import com.fh.ai.business.entity.dto.userTask.UserTaskDto;
import com.fh.ai.business.entity.vo.historyApp.UserUsageVo;
import com.fh.ai.business.entity.vo.organization.OrgTreeNodeVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo;
import com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo;
import com.fh.ai.business.entity.vo.userTask.UserTaskVo;
import com.fh.ai.business.mapper.TaskMapper;
import com.fh.ai.business.mapper.UserTaskMapper;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.business.service.IUserTaskService;
import com.fh.ai.business.util.NoticeUtil;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ScoreTypeEnum;
import com.fh.ai.common.enums.UserTaskState;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户任务表接口实现类
 *
 * <AUTHOR>
 * @date 2024-06-04 14:45:06
 */
@Service
public class UserTaskServiceImpl extends ServiceImpl<UserTaskMapper, UserTaskDto> implements IUserTaskService {

    @Resource
    private UserTaskMapper userTaskMapper;

    @Resource
    private IUserService userService;

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private IOrganizationService organizationService;

    @Override
    public Map<String, Object> getUserTaskListByCondition(UserTaskConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserTaskVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userTaskMapper.getUserTaskListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserTaskVo> userTaskVos = userTaskMapper.getUserTaskListByCondition(conditionBo);
            PageInfo<UserTaskVo> pageInfo = new PageInfo<>(userTaskVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        if (CollectionUtil.isNotEmpty(list)) {
            // 获取单位信息
            List<OrganizationDto> orgDtos = organizationService.list(new LambdaQueryWrapper<OrganizationDto>()
                    .eq(OrganizationDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode()));
            List<OrgTreeNodeVo> orgVos = null;
            if (CollectionUtil.isNotEmpty(orgDtos)) {
                orgVos = BeanUtil.copyToList(orgDtos, OrgTreeNodeVo.class);
            }

            for (UserTaskVo vo : list) {
                String orgNamePath = organizationService.findOrgNamePath(orgVos, vo.getOrganizationId());
                vo.setOrgPath(orgNamePath);
            }
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addUserTask(UserTaskBo userTaskBo) {
        UserTaskDto userTask = new UserTaskDto();
        BeanUtils.copyProperties(userTaskBo, userTask);

        userTask.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        userTask.setCreateTime(new Date());
        save(userTask);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserTask(UserTaskBo userTaskBo) {
        UserTaskDto userTask = new UserTaskDto();
        BeanUtils.copyProperties(userTaskBo, userTask);

        userTask.setUpdateTime(new Date());
        updateById(userTask);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserTaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserTaskDto::getId, id);

        UserTaskDto userTask = getOne(lqw);
        if (null == userTask) {
            return AjaxResult.fail("用户任务表数据不存在");
        }

        UserTaskVo userTaskVo = new UserTaskVo();
        BeanUtils.copyProperties(userTask, userTaskVo);

        return AjaxResult.success(userTaskVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateState(UserTaskBo userTaskBo) {
        // 更新状态
        LambdaQueryWrapper<UserTaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserTaskDto::getTaskId, userTaskBo.getTaskId());
        lqw.eq(UserTaskDto::getUserOid, userTaskBo.getUserOid());

        List<UserTaskDto> userTaskList = list(lqw);
        if (CollUtil.isEmpty(userTaskList)) {
            return AjaxResult.fail("用户任务表数据不存在");
        }
        if (userTaskList.get(0).getState().equals(UserTaskState.PASS.getCode())) {
            return AjaxResult.fail("用户任务已经审核通过");
        }

        TaskDto taskDto = taskMapper.selectById(userTaskBo.getTaskId());
        if (null == taskDto) {
            return AjaxResult.fail("任务不存在");
        }

        LambdaUpdateWrapper<UserTaskDto> uput = new LambdaUpdateWrapper<>();
        uput.eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        uput.eq(UserTaskDto::getTaskId, userTaskBo.getTaskId());
        uput.eq(UserTaskDto::getUserOid, userTaskBo.getUserOid());
        uput.set(UserTaskDto::getState, userTaskBo.getState());
        uput.set(UserTaskDto::getRemark, userTaskBo.getRemark());
        uput.set(UserTaskDto::getUpdateBy, userTaskBo.getUpdateBy());
        uput.set(UserTaskDto::getUpdateTime, new Date());

        update(null,uput);
        if (UserTaskState.PASS.getCode().equals(userTaskBo.getState())) {
            // 增加积分
            UserBo userBo = new UserBo();
            userBo.setOid(userTaskBo.getUserOid());
            userBo.setScore(taskDto.getScore());
            userBo.setCreateBy(userTaskBo.getUpdateBy());
            userBo.setRelationId(taskDto.getId());
            userBo.setScoreType(ScoreTypeEnum.ACTIVITY_TASK.getCode());
            userService.giveScore(userBo);
            NoticeUtil.addIntegral(taskDto.getId(),userTaskBo.getUserOid(),taskDto.getName());
        }else{
            NoticeUtil.addAudit(taskDto.getId(),userTaskBo.getUserOid(),taskDto.getName());
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteUserTask(UserTaskBo userTaskBo) {
        // 删除信息
        LambdaQueryWrapper<UserTaskDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserTaskDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(UserTaskDto::getId, userTaskBo.getId());

        UserTaskDto userTask = getOne(lqw);
        if (null == userTask) {
            return AjaxResult.fail("用户任务表数据不存在");
        }

        UserTaskDto dto = new UserTaskDto();
        dto.setId(userTask.getId());
        dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        dto.setUpdateBy(userTaskBo.getUpdateBy());
        dto.setUpdateTime(new Date());
        updateById(dto);

        return AjaxResult.success();
    }

}