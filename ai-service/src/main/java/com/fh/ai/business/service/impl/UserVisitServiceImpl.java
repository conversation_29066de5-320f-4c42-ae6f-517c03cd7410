package com.fh.ai.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.userVisit.UserVisitBo;
import com.fh.ai.business.entity.bo.userVisit.UserVisitConditionBo;
import com.fh.ai.business.entity.dto.userVisit.UserVisitDto;
import com.fh.ai.business.entity.vo.userVisit.UserVisitVo;
import com.fh.ai.business.mapper.UserVisitMapper;
import com.fh.ai.business.service.IUserVisitService;
import com.fh.ai.common.enums.UserVisitTypeEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用户访问记录表接口实现类
 *
 * <AUTHOR>
 * @date 2024-05-15 13:56:34
 */
@Service
@Slf4j
public class UserVisitServiceImpl extends ServiceImpl<UserVisitMapper, UserVisitDto> implements IUserVisitService {

    @Resource
    private UserVisitMapper userVisitMapper;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public Map<String, Object> getUserVisitListByCondition(UserVisitConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<UserVisitVo> list = null;
        long count = 0;
        if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
            // 不分页（查询全部）
            list = userVisitMapper.getUserVisitListByCondition(conditionBo);
            count = list.size();
        } else {
            //分页查询
            PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
            List<UserVisitVo> userVisitVos = userVisitMapper.getUserVisitListByCondition(conditionBo);
            PageInfo<UserVisitVo> pageInfo = new PageInfo<>(userVisitVos);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addUserVisit(UserVisitBo userVisitBo) {
        UserVisitDto userVisit = new UserVisitDto();
        BeanUtils.copyProperties(userVisitBo, userVisit);
        userVisit.setCreateTime(new Date());
        // 登录记录（每天记录一次）
        if (UserVisitTypeEnum.LOGIN.getCode().equals(userVisit.getType())) {
            RLock lock = redissonClient.getLock("lock:addUserLoginVisit:" + userVisit.getUserOid());
            try {
                lock.lock(9, TimeUnit.SECONDS);
                log.info("addUserLoginVisit getLock");

                UserVisitConditionBo conditionBo = new UserVisitConditionBo();
                conditionBo.setUserOid(userVisit.getUserOid());
                conditionBo.setType(userVisit.getType());
                conditionBo.setLoginVisitDay(userVisit.getCreateTime());
                List<UserVisitVo> list = userVisitMapper.getUserVisitListByCondition(conditionBo);
                if (CollectionUtil.isNotEmpty(list)) {
                    return AjaxResult.success();
                }
            } finally {
                lock.unlock();
                log.info("addUserLoginVisit releaseLock");
            }
        }

        save(userVisit);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateUserVisit(UserVisitBo userVisitBo) {
        UserVisitDto userVisit = new UserVisitDto();
        BeanUtils.copyProperties(userVisitBo, userVisit);

        userVisit.setUpdateTime(new Date());
        updateById(userVisit);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getDetail(Long id) {
        LambdaQueryWrapper<UserVisitDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserVisitDto::getId, id);

        UserVisitDto userVisit = getOne(lqw);
        if (null == userVisit) {
            return AjaxResult.fail("用户访问记录表数据不存在");
        }

        UserVisitVo userVisitVo = new UserVisitVo();
        BeanUtils.copyProperties(userVisit, userVisitVo);

        return AjaxResult.success(userVisitVo);
    }

    @Override
    public AjaxResult deleteUserVisit(UserVisitBo userVisitBo) {
        // 删除信息
        LambdaQueryWrapper<UserVisitDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserVisitDto::getId, userVisitBo.getId());

        UserVisitDto userVisit = getOne(lqw);
        if (null == userVisit) {
            return AjaxResult.fail("用户访问记录表数据不存在");
        }

        removeById(userVisitBo.getId());
        return AjaxResult.success();
    }

}