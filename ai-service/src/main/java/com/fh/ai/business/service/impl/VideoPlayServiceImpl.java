package com.fh.ai.business.service.impl;

import com.fh.ai.business.entity.bo.videoPlay.VideoPlayBo;
import com.fh.ai.business.entity.bo.videoPlay.VideoPlayConditionBo;
import com.fh.ai.business.entity.dto.videoPlay.VideoPlayDto;
import com.fh.ai.business.entity.vo.question.QuestionVo;
import com.fh.ai.business.entity.vo.videoPlay.VideoPlayVo;
import com.fh.ai.business.mapper.VideoPlayMapper;
import com.fh.ai.business.service.IVideoPlayService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;


/**
 * 视频播放表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-07-03 14:04:20
 */
@Service
public class VideoPlayServiceImpl extends ServiceImpl<VideoPlayMapper, VideoPlayDto> implements IVideoPlayService {

	@Resource
	private VideoPlayMapper videoPlayMapper;
	
    @Override
	public Map<String, Object> getVideoPlayListByCondition(VideoPlayConditionBo conditionBo) {
		Map<String, Object> map = new HashMap<>(4);
		List<VideoPlayVo> list = null;
		long count = 0;
		if (null == conditionBo.getPage() || null == conditionBo.getLimit()) {
			// 不分页（查询全部）
			list = videoPlayMapper.getVideoPlayListByCondition(conditionBo);
			count = list.size();
		} else {
			//分页查询
			PageHelper.startPage(conditionBo.getPage(), conditionBo.getLimit(), conditionBo.getOrderBy());
			List<VideoPlayVo> prizeVos = videoPlayMapper.getVideoPlayListByCondition(conditionBo);
			PageInfo<VideoPlayVo> pageInfo = new PageInfo<>(prizeVos);
			list = pageInfo.getList();
			count = pageInfo.getTotal();
		}

		map.put("list", list);
		map.put("count", count);

		return map;
	}

	@Override
	public AjaxResult addVideoPlay(VideoPlayBo videoPlayBo) {
		VideoPlayDto videoPlay = new VideoPlayDto();
		BeanUtils.copyProperties(videoPlayBo, videoPlay);
		videoPlay.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(videoPlay)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateVideoPlay(VideoPlayBo videoPlayBo) {
		VideoPlayDto videoPlay = new VideoPlayDto();
		BeanUtils.copyProperties(videoPlayBo, videoPlay);
		if(updateById(videoPlay)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<VideoPlayDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(VideoPlayDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(VideoPlayDto::getId, id);
		VideoPlayDto videoPlay = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("videoPlayVo", videoPlay==null?new VideoPlayVo():videoPlay);
		return reuslt;
	}

}