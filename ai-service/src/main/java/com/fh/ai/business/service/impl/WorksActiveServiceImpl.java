package com.fh.ai.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Sets;
import com.fh.ai.business.entity.bo.activity.ActivityBo;
import com.fh.ai.business.entity.bo.excellentWorks.ExcellentWorksConditionBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveConditionBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveVoteBo;
import com.fh.ai.business.entity.dto.activity.ActivityDto;
import com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto;
import com.fh.ai.business.entity.dto.worksActive.WorksActiveDto;
import com.fh.ai.business.entity.dto.worksActiveVoteRecord.WorksActiveVoteRecordDto;
import com.fh.ai.business.entity.vo.activity.ActivityVo;
import com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo;
import com.fh.ai.business.entity.vo.worksActive.WorksActiveVo;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteCountVo;
import com.fh.ai.business.mapper.ExcellentWorksMapper;
import com.fh.ai.business.mapper.WorksActiveMapper;
import com.fh.ai.business.mapper.WorksActiveVoteRecordMapper;
import com.fh.ai.business.service.IWorksActiveService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.constants.ConstantsRedis;
import com.fh.ai.common.enums.ActiveStatusEnum;
import com.fh.ai.common.enums.ExcellentWorksHoldTypeEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsSupportVoteEnum;
import com.fh.ai.common.utils.PageUtils;
import com.fh.ai.common.vo.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

/**
 * 作品活动表接口实现类
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 18:12:16
 */
@Slf4j
@Service
public class WorksActiveServiceImpl extends ServiceImpl<WorksActiveMapper, WorksActiveDto> implements IWorksActiveService {

	@Resource
	private WorksActiveMapper worksActiveMapper;

	@Resource
	private ExcellentWorksMapper excellentWorksMapper;

	@Resource
	private RedissonClient redissonClient;

	@Resource
	private WorksActiveVoteRecordMapper worksActiveVoteRecordMapper;

	@Override
	public Map<String, Object> getWorksActiveListByConditionPage(WorksActiveConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return PageUtils.handlePagination(
				() -> {
					List<WorksActiveVo> worksActiveListByCondition = worksActiveMapper.getWorksActiveListByCondition(condition);
					Date currentDate = new Date(); // 获取一次当前时间，避免多次调用 new Date()

					for (WorksActiveVo worksActiveVo : worksActiveListByCondition) {
						long currentTime = currentDate.getTime();
						long startTime = worksActiveVo.getActiveStartTime().getTime();
						long endTime = worksActiveVo.getActiveEndTime().getTime();

						// 判断活动状态
						if (currentTime > endTime) {
							worksActiveVo.setWorksActiveStartType(3); // 已结束
						} else if (currentTime < startTime) {
							worksActiveVo.setWorksActiveStartType(1); // 未开始
						} else {
							worksActiveVo.setWorksActiveStartType(2); // 已开始
						}
					}
					return worksActiveListByCondition;
				},
				condition.getPage(),
				condition.getLimit()
		);
	}

	@Override
	public List<WorksActiveVo> getWorksActiveListByCondition(WorksActiveConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		List<WorksActiveVo> worksActiveListByCondition = worksActiveMapper.getWorksActiveListByCondition(condition);
		Date currentDate = new Date(); // 获取一次当前时间，避免多次调用 new Date()

		for (WorksActiveVo worksActiveVo : worksActiveListByCondition) {
			long currentTime = currentDate.getTime();
			long startTime = worksActiveVo.getActiveStartTime().getTime();
			long endTime = worksActiveVo.getActiveEndTime().getTime();

			// 判断活动状态
			if (currentTime > endTime) {
				worksActiveVo.setWorksActiveStartType(3); // 已结束
			} else if (currentTime < startTime) {
				worksActiveVo.setWorksActiveStartType(1); // 未开始
			} else {
				worksActiveVo.setWorksActiveStartType(2); // 已开始
			}
		}
		return worksActiveListByCondition;
	}

	@Override
	public AjaxResult addWorksActive(WorksActiveBo worksActiveBo) {
		WorksActiveDto worksActive = new WorksActiveDto();
		BeanUtils.copyProperties(worksActiveBo, worksActive);
		worksActive.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		worksActive.setActiveStatus(ActiveStatusEnum.DISABLE.getCode());
		worksActive.setCreateTime(new Date());
		if(save(worksActive)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult getDetail(Long id) {
		LambdaQueryWrapper<WorksActiveDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(WorksActiveDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(WorksActiveDto::getId, id);

		WorksActiveDto activity = getOne(lqw);
		if(null == activity) {
			return AjaxResult.fail("活动作品表数据不存在");
		}

		WorksActiveVo activityVo = new WorksActiveVo();
		BeanUtils.copyProperties(activity, activityVo);

		return AjaxResult.success(activityVo);
	}

	@Override
	public AjaxResult deleteActivity(WorksActiveBo worksActiveBo) {
		// 删除信息
		LambdaQueryWrapper<WorksActiveDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(WorksActiveDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(WorksActiveDto::getId, worksActiveBo.getId());

		WorksActiveDto worksActivity = getOne(lqw);
		if(null == worksActivity) {
			return AjaxResult.fail("活动表数据不存在");
		}
		//查询活动下有没有作品，有就不允许删除
		LambdaQueryWrapper<ExcellentWorksDto> excellentWorksDtoLQW = new LambdaQueryWrapper<ExcellentWorksDto>()
				.eq(ExcellentWorksDto::getWorksActiveId, worksActiveBo.getId())
				.eq(ExcellentWorksDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		Long ExcellentWorksCount = excellentWorksMapper.selectCount(excellentWorksDtoLQW);
		if (ExcellentWorksCount>0){
			return AjaxResult.fail("活动下有作品，不允许删除");
		}

		WorksActiveDto dto = new WorksActiveDto();
		dto.setId(worksActivity.getId());
		dto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
		dto.setUpdateBy(worksActiveBo.getUpdateBy());
		dto.setUpdateTime(new Date());
		updateById(dto);

		return AjaxResult.success();
	}

	@Override
	public AjaxResult updateWorksActive(WorksActiveBo worksActiveBo) {
		WorksActiveDto worksActive = new WorksActiveDto();
		BeanUtils.copyProperties(worksActiveBo, worksActive);
		worksActiveBo.setUpdateTime(new Date());
		if(updateById(worksActive)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public WorksActiveVo getWorksActiveByCondition(WorksActiveConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return worksActiveMapper.getWorksActiveByCondition(condition);
	}

	@Override
	public WorksActiveVoteCountVo voteCount(Long worksActiveId) {
		WorksActiveVoteCountVo voteCountVo = worksActiveVoteRecordMapper.getWorksActiveVoteCount(worksActiveId);

		// 查询已发布作品
		ExcellentWorksConditionBo conditionBo = new ExcellentWorksConditionBo();
		conditionBo.setHoldType(ExcellentWorksHoldTypeEnum.ANSWER.getCode());
		conditionBo.setWorksActiveId(worksActiveId);
		conditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());

		List<ExcellentWorksVo> list = excellentWorksMapper.getExcellentWorksListByCondition(conditionBo);

		if (CollectionUtil.isEmpty(list)) {
			voteCountVo.setAuthorCount(ConstantsInteger.NUM_0);
			return voteCountVo;
		}

		Set<String> authorOids = list.stream()
				.map(ExcellentWorksVo::getUserOid)
				.filter(StringUtils::isNotBlank)
				.flatMap(userOid -> Arrays.stream(userOid.split(",")))
				.collect(Collectors.toSet());

		voteCountVo.setAuthorCount(authorOids.size());
		return voteCountVo;
	}

	@Override
	public AjaxResult voteCheck(String userOid, Long worksActiveId) {
		// 校验活动是否在投票时间
		WorksActiveConditionBo condition = new WorksActiveConditionBo();
		condition.setId(worksActiveId);
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		WorksActiveVo worksActiveVo = worksActiveMapper.getWorksActiveByCondition(condition);
		if (worksActiveVo == null) {
			return AjaxResult.fail("活动不存在");
		}
		if (ActiveStatusEnum.DISABLE.getCode().equals(worksActiveVo.getActiveStatus())) {
			return AjaxResult.fail("活动未开启");
		}
		if (IsSupportVoteEnum.NO.getValue().equals(worksActiveVo.getIsSupportVote())) {
			return AjaxResult.fail("当前活动不支持投票");
		}
		Date now = new Date();
//		if (!(now.after(worksActiveVo.getVoteStartTime()) && now.before(worksActiveVo.getVoteEndTime()))) {
//			return AjaxResult.success(Constants.WORKS_ACTIVE_VOTE_TIME_ERROR);
//		}
		// 未到投票时间
		if (now.before(worksActiveVo.getVoteStartTime())) {
			return AjaxResult.success(Constants.WORKS_ACTIVE_VOTE_NOT_START);
		}
		// 投票已结束
		if (now.after(worksActiveVo.getVoteEndTime())) {
			return AjaxResult.success(Constants.WORKS_ACTIVE_VOTE_END);
		}
		if (worksActiveVo.getDailyVoteLimit() > ConstantsInteger.NUM_0) {
			// 校验当天投票是否超出限制
			Integer voteCount = worksActiveVoteRecordMapper.getWorksActiveVoteCountByUserOid(userOid, worksActiveId);
			if (voteCount >= worksActiveVo.getDailyVoteLimit()) {
				return AjaxResult.success(Constants.WORKS_ACTIVE_VOTE_DAILY_LIMIT);
			}
		}
		return AjaxResult.success(Constants.CAN_WORKS_ACTIVE_VOTE);
	}

	@Override
	public AjaxResult vote(WorksActiveVoteBo voteBo) {
		// 校验活动是否在投票时间
		WorksActiveConditionBo condition = new WorksActiveConditionBo();
		condition.setId(voteBo.getWorksActiveId());
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		WorksActiveVo worksActiveVo = worksActiveMapper.getWorksActiveByCondition(condition);
		if (worksActiveVo == null) {
			return AjaxResult.fail("活动不存在");
		}
		if (ActiveStatusEnum.DISABLE.getCode().equals(worksActiveVo.getActiveStatus())) {
			return AjaxResult.fail("活动未开启");
		}
		if (IsSupportVoteEnum.NO.getValue().equals(worksActiveVo.getIsSupportVote())) {
			return AjaxResult.fail("当前活动不支持投票");
		}
		Date now = new Date();
		// 未到投票时间
		if (now.before(worksActiveVo.getVoteStartTime())) {
			return AjaxResult.fail("当前活动投票未开始");
		}
		// 投票已结束
		if (now.after(worksActiveVo.getVoteEndTime())) {
			return AjaxResult.fail("当前活动投票已结束");
		}
		// 投票
		RLock lock = redissonClient.getLock(ConstantsRedis.WORKS_ACTIVE_VOTE_LOCK + voteBo.getUserOid() + "_" + voteBo.getWorksActiveId());
		try {
			lock.lock(5, TimeUnit.SECONDS);
			if (worksActiveVo.getDailyVoteLimit() > ConstantsInteger.NUM_0) {
				// 校验当天投票是否超出限制
				Integer voteCount = worksActiveVoteRecordMapper.getWorksActiveVoteCountByUserOid(voteBo.getUserOid(), voteBo.getWorksActiveId());
				if (voteCount >= worksActiveVo.getDailyVoteLimit()) {
					return AjaxResult.fail("您当天投票已达上限，请明天再来");
				}
			}

			WorksActiveVoteRecordDto voteRecordDto = new WorksActiveVoteRecordDto();
			voteRecordDto.setWorksActiveId(voteBo.getWorksActiveId());
			voteRecordDto.setExcellentWorksId(voteBo.getExcellentWorksId());
			voteRecordDto.setUserOid(voteBo.getUserOid());
			voteRecordDto.setVoteTime(new Date());
			// 默认一次投一票
			voteRecordDto.setVoteNumber(ConstantsInteger.NUM_1);
			voteRecordDto.setCreateTime(new Date());
			voteRecordDto.setCreateBy(voteBo.getUserOid());
			voteRecordDto.setUpdateTime(new Date());
			voteRecordDto.setUpdateBy(voteBo.getUserOid());
			voteRecordDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
			if (worksActiveVoteRecordMapper.insert(voteRecordDto) > 0) {
				return AjaxResult.success("投票成功");
			}
		} catch (Exception e) {
			log.error("worksActiveVote error:" + e);
		} finally {
			lock.unlock();
		}
		return AjaxResult.fail("投票失败");
	}

}