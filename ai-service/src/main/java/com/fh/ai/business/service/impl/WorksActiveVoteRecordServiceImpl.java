package com.fh.ai.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.worksActiveVoteRecord.WorksActiveVoteRecordBo;
import com.fh.ai.business.entity.bo.worksActiveVoteRecord.WorksActiveVoteRecordConditionBo;
import com.fh.ai.business.entity.dto.worksActiveVoteRecord.WorksActiveVoteRecordDto;
import com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRecordVo;
import com.fh.ai.business.mapper.WorksActiveVoteRecordMapper;
import com.fh.ai.business.service.IWorksActiveVoteRecordService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-04  10:17
 */
@Service
public class WorksActiveVoteRecordServiceImpl extends ServiceImpl<WorksActiveVoteRecordMapper, WorksActiveVoteRecordDto> implements IWorksActiveVoteRecordService {
    @Resource
    private WorksActiveVoteRecordMapper worksActiveVoteRecordMapper;

    @Override
    public List<WorksActiveVoteRecordVo> getWorksActiveVoteRecordListByCondition(WorksActiveVoteRecordConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return worksActiveVoteRecordMapper.getWorksActiveVoteRecordListByCondition(condition);
    }

    @Override
    public AjaxResult addWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo) {
        WorksActiveVoteRecordDto worksActiveVoteRecordDto = new WorksActiveVoteRecordDto();
        BeanUtils.copyProperties(worksActiveVoteRecordBo, worksActiveVoteRecordDto);
        worksActiveVoteRecordDto.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(worksActiveVoteRecordDto)) {
            return AjaxResult.success("保存成功");
        }
        return AjaxResult.fail("保存失败");
    }

    @Override
    public AjaxResult updateWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo) {
        WorksActiveVoteRecordDto worksActiveVoteRecordDto = getById(worksActiveVoteRecordBo.getId());
        if (null == worksActiveVoteRecordDto) {
            return AjaxResult.fail("数据不存在");
        }
        BeanUtils.copyProperties(worksActiveVoteRecordBo, worksActiveVoteRecordDto);
        worksActiveVoteRecordDto.setUpdateTime(new Date());
        if (updateById(worksActiveVoteRecordDto)) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail("修改失败");
    }

    @Override
    public AjaxResult deleteWorksActiveVoteRecord(WorksActiveVoteRecordBo worksActiveVoteRecordBo) {
        WorksActiveVoteRecordDto worksActiveVoteRecordDto = getById(worksActiveVoteRecordBo.getId());
        if (null == worksActiveVoteRecordDto) {
            return AjaxResult.fail("数据不存在");
        }
        worksActiveVoteRecordDto.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        worksActiveVoteRecordDto.setUpdateTime(new Date());
        if (updateById(worksActiveVoteRecordDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public WorksActiveVoteRecordVo getWorksActiveVoteRecordByCondition(WorksActiveVoteRecordConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return worksActiveVoteRecordMapper.getWorksActiveVoteRecordByCondition(condition);
    }
}
