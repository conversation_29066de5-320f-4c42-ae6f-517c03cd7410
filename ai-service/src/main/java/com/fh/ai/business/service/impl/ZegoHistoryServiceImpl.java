package com.fh.ai.business.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.fh.ai.business.entity.bo.zegoHistory.ZegoHistoryBo;
import com.fh.ai.business.entity.bo.zegoHistory.ZegoHistoryConditionBo;
import com.fh.ai.business.entity.dto.zegoHistory.ZegoHistoryDto;
import com.fh.ai.business.entity.dto.zegoUser.ZegoUserDto;
import com.fh.ai.business.entity.vo.zegoHistory.ZegoHistoryVo;
import com.fh.ai.business.mapper.ZegoHistoryMapper;
import com.fh.ai.business.mapper.ZegoUserMapper;
import com.fh.ai.business.service.IZegoHistoryService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsFavorite;
import com.fh.ai.common.enums.ZegoStateEnum;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zego.HumanVideo;
import com.fh.ai.common.zego.MetaHumanVideoVo;
import com.fh.ai.common.zego.VideoOption;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import javax.imageio.ImageIO;


/**
 * 美图生成记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Service
public class ZegoHistoryServiceImpl extends ServiceImpl<ZegoHistoryMapper, ZegoHistoryDto> implements IZegoHistoryService {

	@Resource
	private ZegoHistoryMapper zegoHistoryMapper;

	@Resource
	private ZegoUserServiceImpl zegoUserServiceImpl;

	@Value("${filepath.windows}")
	private String windowsPath;

	@Value("${filepath.linux}")
	private String linuxPath;

	@Value("${filepath.viewPrefix}")
	private String viewPrefix;
	
    @Override
	public List<ZegoHistoryVo> getZegoHistoryListByCondition(ZegoHistoryConditionBo condition) {
        return zegoHistoryMapper.getZegoHistoryListByCondition(condition);
	}

	@Override
	public AjaxResult addZegoHistory(ZegoHistoryBo zegoHistoryBo) {
		ZegoHistoryDto zegoHistory = new ZegoHistoryDto();
		BeanUtils.copyProperties(zegoHistoryBo, zegoHistory);
		zegoHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(zegoHistory)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateZegoHistory(ZegoHistoryBo zegoHistoryBo) {
		ZegoHistoryDto zegoHistory = new ZegoHistoryDto();
		BeanUtils.copyProperties(zegoHistoryBo, zegoHistory);
		if(updateById(zegoHistory)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long id) {
		LambdaQueryWrapper<ZegoHistoryDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ZegoHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
		lqw.eq(ZegoHistoryDto::getId, id);
		ZegoHistoryDto zegoHistory = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("zegoHistoryVo", zegoHistory==null?new ZegoHistoryVo():zegoHistory);
		return reuslt;
	}

	@Override
	public void addCreateImageMetaHumanVideoHistoryApp(VideoOption videoOption,HumanVideo humanVideo, MetaHumanVideoVo imageMetaHuman, Long id, String oid, Long orgId) {
		Date now = new Date();
		ZegoUserDto zegoUser = new ZegoUserDto();
		if(humanVideo.getId() != null){
			ZegoUserDto byId = zegoUserServiceImpl.getById(humanVideo.getId());
			if(byId.getState().equals(ZegoStateEnum.FAIL.getCode())){
				zegoUser.setTaskId("");
				zegoUser.setResult("");
			}
		}else{
			zegoUser.setCreateTime(now);
		}
		zegoUser.setState(ZegoStateEnum.DEALING.getCode());
		zegoUser.setId(humanVideo.getId());

		ZegoHistoryDto zegoHistory = new ZegoHistoryDto();
		BeanUtils.copyProperties(humanVideo, zegoHistory);
		zegoHistory.setUserOid(oid);
		zegoHistory.setOrganizationId(orgId);
		if(imageMetaHuman.getMessage().equals("success")){
			zegoHistory.setTaskId(imageMetaHuman.getData().getTaskId());
		}
		zegoHistory.setParameterJson(imageMetaHuman.getParams());
		zegoHistory.setResponseData(imageMetaHuman.getResult());
		zegoHistory.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		zegoHistory.setCreateTime(now);
		zegoHistory.setId(null);
		save(zegoHistory);

		zegoUser.setUserOid(zegoHistory.getUserOid());
		zegoUser.setOrganizationId(zegoHistory.getOrganizationId());
		zegoUser.setTaskId(zegoHistory.getTaskId());
		zegoUser.setName(humanVideo.getName());
		zegoUser.setWidth(videoOption.getWidth());
		zegoUser.setHeight(videoOption.getHeight());
		zegoUser.setCover(humanVideo.getCover());
		if(StringUtils.isNotEmpty(zegoUser.getCover()) ) {
			String uuid = IdUtil.simpleUUID();
			String cover = zegoUser.getCover();
			String relativeDir = File.separator + "zego" + File.separator + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
			String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
			String coverName = uuid + "." + FileUtil.getSuffix(cover);
			HttpUtil.downloadFile(cover, baseDir + coverName);
			zegoUser.setCover(viewPrefix + relativeDir + coverName);
		}

//		zegoUser.setState(zegoHistory.getState());
		if(zegoHistory.getResult() != null){
			zegoUser.setResult(zegoHistory.getResult());
		}
		zegoUser.setChannel(zegoHistory.getChannel());
		zegoUser.setHistoryId(zegoHistory.getId());
		zegoUser.setAppType(zegoHistory.getAppType());
		zegoUser.setParams(humanVideo.getParams());
		zegoUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		zegoUser.setCreateBy(zegoHistory.getCreateBy());

		zegoUser.setUpdateBy(zegoHistory.getUpdateBy());
		zegoUser.setUpdateTime(zegoHistory.getUpdateTime());

		zegoUserServiceImpl.saveOrUpdate(zegoUser);
	}


}