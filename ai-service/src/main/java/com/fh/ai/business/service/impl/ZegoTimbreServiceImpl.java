package com.fh.ai.business.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.zego.ZegoTimbreBo;
import com.fh.ai.business.entity.bo.zego.ZegoTimbreConditionBo;
import com.fh.ai.business.entity.dto.zego.ZegoTimbreDto;
import com.fh.ai.business.entity.vo.zego.ZegoTimbreVo;
import com.fh.ai.business.mapper.ZegoTimbreMapper;
import com.fh.ai.business.service.IZegoTimbreService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import com.google.common.collect.Maps;

/**
 * 即构音色表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-14 16:34:49
 */
@Service
public class ZegoTimbreServiceImpl extends ServiceImpl<ZegoTimbreMapper, ZegoTimbreDto> implements IZegoTimbreService {

	@Resource
	private ZegoTimbreMapper zegoTimbreMapper;
	
    @Override
	public List<ZegoTimbreVo> getZegoTimbreListByCondition(ZegoTimbreConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return zegoTimbreMapper.getZegoTimbreListByCondition(condition);
	}

	@Override
	public AjaxResult addZegoTimbre(ZegoTimbreBo zegoTimbreBo) {
		ZegoTimbreDto zegoTimbre = new ZegoTimbreDto();
		BeanUtils.copyProperties(zegoTimbreBo, zegoTimbre);
		zegoTimbre.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		if(save(zegoTimbre)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateZegoTimbre(ZegoTimbreBo zegoTimbreBo) {
		ZegoTimbreDto zegoTimbre = new ZegoTimbreDto();
		BeanUtils.copyProperties(zegoTimbreBo, zegoTimbre);
		if(updateById(zegoTimbre)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ZegoTimbreVo getZegoTimbreByCondition(ZegoTimbreConditionBo condition) {
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		ZegoTimbreVo vo = zegoTimbreMapper.getZegoTimbreByCondition(condition);
		return vo;
	}

	@Override
	public Map<String, String> getZegoTimbreMapByCondition(ZegoTimbreConditionBo condition) {
		List<ZegoTimbreVo> zegoTimbreVos = getZegoTimbreListByCondition(condition);
		Map<String,String> resultMap = Maps.newHashMap();
		// 遍历音色列表，将音色id和音色音频地址放入map中，使用java8
		zegoTimbreVos.forEach(zegoTimbreVo -> resultMap.put(zegoTimbreVo.getTimbreId(), zegoTimbreVo.getTimbreUrl()));
		return resultMap;
	}
}