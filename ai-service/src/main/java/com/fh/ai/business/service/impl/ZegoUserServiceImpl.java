package com.fh.ai.business.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.ai.business.entity.bo.zegoUser.ZegoUserBo;
import com.fh.ai.business.entity.bo.zegoUser.ZegoUserConditionBo;
import com.fh.ai.business.entity.dto.zegoHistory.ZegoHistoryDto;
import com.fh.ai.business.entity.dto.zegoUser.ZegoUserDto;
import com.fh.ai.business.entity.vo.zegoUser.ZegoUserVo;
import com.fh.ai.business.mapper.ZegoHistoryMapper;
import com.fh.ai.business.mapper.ZegoUserMapper;
import com.fh.ai.business.service.IZegoUserService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ZegoStateEnum;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zego.ImageMetaHumanUtil;
import com.fh.ai.common.zego.MetaHumanVideoData;
import com.fh.ai.common.zego.MetaHumanVideoVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;

/**
 * 美图用户使用表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Service
public class ZegoUserServiceImpl extends ServiceImpl<ZegoUserMapper, ZegoUserDto> implements IZegoUserService {

    @Resource
    private ZegoUserMapper zegoUserMapper;

    @Resource
    private ZegoHistoryMapper zegoHistoryMapper;

    @Autowired
    private ImageMetaHumanUtil imageMetaHumanUtil;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    @Value("${filepath.viewPrefix}")
    private String viewPrefix;

    @Override
    public Map<String, Object> getZegoUserListByCondition(ZegoUserConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        List<ZegoUserVo> list = null;
        long count = 0;
        if (null == condition.getPage() || null == condition.getLimit()) {
            // 不分页（查询全部）
            list = zegoUserMapper.getZegoUserListByCondition(condition);
            count = list.size();
        } else {
            // 分页查询
            PageHelper.startPage(condition.getPage(), condition.getLimit(), condition.getOrderBy());
            List<ZegoUserVo> zegoUserList = zegoUserMapper.getZegoUserListByCondition(condition);
            PageInfo<ZegoUserVo> pageInfo = new PageInfo<>(zegoUserList);
            list = pageInfo.getList();
            count = pageInfo.getTotal();
        }

        map.put("list", list);
        map.put("count", count);

        return map;
    }

    @Override
    public AjaxResult addZegoUser(ZegoUserBo zegoUserBo) {
        ZegoUserDto zegoUser = new ZegoUserDto();
        BeanUtils.copyProperties(zegoUserBo, zegoUser);
        zegoUser.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        if (save(zegoUser)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateZegoUser(ZegoUserBo zegoUserBo) {
        ZegoUserDto zegoUser = new ZegoUserDto();
        BeanUtils.copyProperties(zegoUserBo, zegoUser);
        if (zegoUserBo.getId() == null) {
            zegoUser.setCreateTime(new Date());
        }
        zegoUser.setUpdateTime(new Date());
        // 更新不修改处理状态
        zegoUser.setState(null);
        // zegoUser.setState(ZegoStateEnum.DEALING.getCode());
        if (zegoUserBo.getId() != null) {
            ZegoUserDto byId = getById(zegoUserBo.getId());
            if (byId.getState().equals(ZegoStateEnum.FAIL.getCode())) {
                zegoUser.setTaskId("");
                zegoUser.setResult("");
            }
        }
        if (StringUtils.isNotEmpty(zegoUser.getCover())) {
            String uuid = IdUtil.simpleUUID();
            String cover = zegoUser.getCover();
            String relativeDir = File.separator + "zego" + File.separator
                + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
            String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
            String coverName = uuid + "." + FileUtil.getSuffix(cover);
            HttpUtil.downloadFile(cover, baseDir + coverName);
            zegoUser.setCover(viewPrefix + relativeDir + coverName);
            try {
                BufferedImage image = ImageIO.read(new File(baseDir + coverName));
                int width = image == null ? 1080 : image.getWidth();
                int height = image == null ? 1920 : image.getHeight();
                if (width > height) {
                    zegoUser.setWidth(1920);
                    zegoUser.setHeight(1080);
                } else {
                    zegoUser.setWidth(1080);
                    zegoUser.setHeight(1920);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (saveOrUpdate(zegoUser)) {
            return AjaxResult.success(zegoUser);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public Map<String, Object> getDetail(Long id) {
        LambdaQueryWrapper<ZegoUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ZegoUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ZegoUserDto::getId, id);
        ZegoUserDto zegoUser = getOne(lqw);
        Map<String, Object> reuslt = new HashMap<String, Object>(4);
        reuslt.put("zegoUserVo", zegoUser == null ? new ZegoUserVo() : zegoUser);
        return reuslt;
    }

    @Override
    public void performTask() {
        LambdaQueryWrapper<ZegoHistoryDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ZegoHistoryDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
        lqw.eq(ZegoHistoryDto::getState, ZegoStateEnum.DEALING.getCode());
        lqw.isNotNull(ZegoHistoryDto::getTaskId);

        List<ZegoHistoryDto> zegoHistoryList = zegoHistoryMapper.selectList(lqw);
        if (CollectionUtil.isEmpty(zegoHistoryList)) {
            return;
        }

        Date now = new Date();
        for (ZegoHistoryDto zhd : zegoHistoryList) {
            ThreadUtil.taskZegoSyncExecute(new Runnable() {
                @Override
                public void run() {
                    JSONObject result = imageMetaHumanUtil.describeMetaHumanVideoStr(zhd.getTaskId());

                    MetaHumanVideoVo metaHumanVideoVo = JSON.toJavaObject(result, MetaHumanVideoVo.class);

                    // 状态码，-1没有找到任务，0任务创建成功，1 任务执行中，2任务失败，10任务成功。
                    if (metaHumanVideoVo.getCode() == 0) {
                        int status = metaHumanVideoVo.getData().getStatus();
                        // 1 TTS 处理中。 2：TTS 处理失败。 3：视频合成中。 4：视频合成失败。 5：视频合成成功。6：字幕处理中。
                        MetaHumanVideoData data = metaHumanVideoVo.getData();
                        if (status == 4) {
                            log.error(
                                "即构生成失败：taskId：" + zhd.getTaskId() + ";返回值：" + JSON.toJSONString(metaHumanVideoVo));
                            ZegoHistoryDto updateDto = new ZegoHistoryDto();
                            updateDto.setId(zhd.getId());
                            updateDto.setState(ZegoStateEnum.FAIL.getCode());
                            updateDto.setUpdateTime(now);
                            updateDto.setResult(result.toString());
                            updateDto.setResponseData(result.toString());
                            zegoHistoryMapper.updateById(updateDto);
                            // 更新使用结果
                            LambdaQueryWrapper<ZegoUserDto> lqw = new LambdaQueryWrapper<>();
                            lqw.eq(ZegoUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                            lqw.eq(ZegoUserDto::getHistoryId, zhd.getId());

                            ZegoUserDto mtUser = zegoUserMapper.selectOne(lqw);
                            ZegoUserDto updateZegoUserDto = new ZegoUserDto();
                            updateZegoUserDto.setId(mtUser.getId());
                            updateZegoUserDto.setResult(updateDto.getResult());
                            updateZegoUserDto.setState(updateDto.getState());
                            updateZegoUserDto.setUpdateTime(now);

                            zegoUserMapper.updateById(updateZegoUserDto);
                        } else if (status == 5) {
                            if (null != data) {
                                data.setVideoUrl(imageMetaHumanUtil.handleEscapeCharacters(data.getVideoUrl()));
                                data.setAudioUrl(imageMetaHumanUtil.handleEscapeCharacters(data.getAudioUrl()));
                                data.setCoverUrl(imageMetaHumanUtil.handleEscapeCharacters(data.getCoverUrl()));
                            }
                            // 更新结果
                            ZegoHistoryDto updateDto = new ZegoHistoryDto();
                            updateDto.setId(zhd.getId());
                            updateDto.setState(ZegoStateEnum.FINISH.getCode());

                            updateDto.setResult(JSONObject.toJSONString(data));
                            updateDto.setUpdateTime(now);
                            updateDto.setResponseData(result.toString());
                            zegoHistoryMapper.updateById(updateDto);
                            // 更新使用结果
                            LambdaQueryWrapper<ZegoUserDto> lqw = new LambdaQueryWrapper<>();
                            lqw.eq(ZegoUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                            lqw.eq(ZegoUserDto::getHistoryId, zhd.getId());

                            ZegoUserDto zegoUser = zegoUserMapper.selectOne(lqw);
                            ZegoUserDto updateZegoUserDto = new ZegoUserDto();
                            updateZegoUserDto.setId(zegoUser.getId());
                            String dataRes = dealData(data);
                            updateZegoUserDto.setResult(dataRes);
                            updateZegoUserDto.setState(updateDto.getState());
                            updateZegoUserDto.setUpdateTime(now);

                            zegoUserMapper.updateById(updateZegoUserDto);

                        } else if (status == 2) {
                            ZegoHistoryDto updateDto = new ZegoHistoryDto();
                            updateDto.setId(zhd.getId());
                            updateDto.setState(ZegoStateEnum.FAIL.getCode());
                            updateDto.setUpdateTime(now);
                            updateDto.setResponseData(result.toString());

                            zegoHistoryMapper.updateById(updateDto);
                            // 更新使用结果
                            LambdaQueryWrapper<ZegoUserDto> lqw = new LambdaQueryWrapper<>();
                            lqw.eq(ZegoUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                            lqw.eq(ZegoUserDto::getHistoryId, zhd.getId());

                            ZegoUserDto mtUser = zegoUserMapper.selectOne(lqw);
                            ZegoUserDto updateZegoUserDto = new ZegoUserDto();
                            updateZegoUserDto.setId(mtUser.getId());
                            updateZegoUserDto.setResult(updateDto.getResult());
                            updateZegoUserDto.setState(updateDto.getState());
                            updateZegoUserDto.setUpdateTime(now);

                            zegoUserMapper.updateById(updateZegoUserDto);
                        }
                    } else {
                        log.error("即构生成失败：taskId：" + zhd.getTaskId() + ";返回值：" + JSON.toJSONString(metaHumanVideoVo));
                        ZegoHistoryDto updateDto = new ZegoHistoryDto();
                        updateDto.setId(zhd.getId());
                        updateDto.setState(ZegoStateEnum.FAIL.getCode());
                        updateDto.setUpdateTime(now);
                        updateDto.setResponseData(result.toString());

                        zegoHistoryMapper.updateById(updateDto);
                        // 更新使用结果
                        LambdaQueryWrapper<ZegoUserDto> lqw = new LambdaQueryWrapper<>();
                        lqw.eq(ZegoUserDto::getIsDelete, IsDeleteEnum.NOTDELETE.getCode());
                        lqw.eq(ZegoUserDto::getHistoryId, zhd.getId());

                        ZegoUserDto mtUser = zegoUserMapper.selectOne(lqw);
                        ZegoUserDto updateZegoUserDto = new ZegoUserDto();
                        updateZegoUserDto.setId(mtUser.getId());
                        updateZegoUserDto.setResult(updateDto.getResult());
                        updateZegoUserDto.setState(updateDto.getState());
                        updateZegoUserDto.setUpdateTime(now);

                        zegoUserMapper.updateById(updateZegoUserDto);
                    }
                }
            });
        }
    }

    public String dealData(MetaHumanVideoData data) {
        String uuid = IdUtil.simpleUUID();
        String relativeDir = File.separator + "zego" + File.separator
            + DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT) + File.separator + uuid + File.separator;
        String baseDir = (SystemUtil.isWindows() ? windowsPath : linuxPath) + relativeDir;
        String coverName = uuid + "." + FileUtil.getSuffix(data.getCoverUrl());
        String audioName = uuid + "." + FileUtil.getSuffix(data.getAudioUrl());
        String videoName = uuid + "." + FileUtil.getSuffix(data.getVideoUrl());
        HttpUtil.downloadFile(data.getCoverUrl(), baseDir + coverName);
        HttpUtil.downloadFile(data.getAudioUrl(), baseDir + audioName);
        HttpUtil.downloadFile(data.getVideoUrl(), baseDir + videoName);
        data.setCoverUrl(viewPrefix + relativeDir + coverName);
        data.setAudioUrl(viewPrefix + relativeDir + audioName);
        data.setVideoUrl(viewPrefix + relativeDir + videoName);
        return JSON.toJSONString(data);
    }

}