<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ActivityMapper">

	<select id="getActivityListByCondition" resultType="com.fh.ai.business.entity.vo.activity.ActivityVo">
		select t.* from (
			select a.*,ifnull(b.readCount,0) readCount from p_activity a
			left join (select activity_id ,count(1) readCount from p_activity_read where read_date = #{readDate}
        <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			and is_delete = 1 GROUP BY activity_id ) b
			on a.id  = b.activity_id
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="position != null and position != ''">and position = #{position}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="content != null and content != ''">and content = #{content}</if>
            <if test="details != null and details != ''">and details = #{details}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="jumpUrl != null and jumpUrl != ''">and jump_url = #{jumpUrl}</if>
            <if test="frequency != null and frequency != ''">and frequency = #{frequency}</if>
            <if test="publishTime != null and publishTime != ''">and publish_time = #{publishTime}</if>
            <if test="showType != null and showType != ''">and show_type = #{showType}</if>
            <if test="h5Content != null and h5Content != ''">and h5_content = #{h5Content}</if>
            <if test="h5JumpUrl != null and h5JumpUrl != ''">and h5_jump_url = #{h5JumpUrl}</if>
            <if test="detailSwitch != null">and detail_switch = #{detailSwitch}</if>
            <if test="detailContent != null and detailContent != ''">and detail_content like concat('%',#{detailContent},'%')</if>
            <if test="jumpType != null">and jump_type = #{jumpType}</if>
        </where>
	</select>
</mapper>