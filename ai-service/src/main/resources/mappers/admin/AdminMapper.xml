<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.AdminMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.admin.AdminDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="accountName" column="account_name"/>
        <result property="password" column="password"/>
        <result property="phone" column="phone"/>
        <result property="adminName" column="admin_name"/>
        <result property="weixinUnionid" column="weixin_unionid"/>
        <result property="contactNumber" column="contact_number"/>
        <result property="email" column="email"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="picture" column="picture"/>
        <result property="isLocked" column="is_locked"/>
        <result property="introduction" column="introduction"/>
        <result property="remark" column="remark"/>
        <result property="organizationId" column="organization_id"/>
        <result property="isActivation" column="is_activation"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getAdminListByCondition" resultType="com.fh.ai.business.entity.vo.admin.AdminVo">
        select t.* from (
        select a.*, ar.role_id, r.role_name
        from p_admin a
        LEFT JOIN p_admin_role ar on a.oid = ar.admin_oid
        LEFT JOIN p_role r on ar.role_id = r.id
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="password != null and password != ''">and password = #{password}</if>
            <if test="phone != null and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="adminName != null and adminName != ''">and admin_name like concat('%', #{adminName}, '%')</if>

            <if test="keyword != null and keyword != ''">
                and (
                admin_name like concat('%', #{keyword}, '%')
                or phone like concat('%', #{keyword}, '%')
                )
            </if>

            <if test="roleId != null">and role_id = #{roleId}</if>

            <if test="weixinUnionid != null and weixinUnionid != ''">and weixin_unionid = #{weixinUnionid}</if>
            <if test="contactNumber != null and contactNumber != ''">and contact_number = #{contactNumber}</if>
            <if test="email != null and email != ''">and email = #{email}</if>
            <if test="sex != null and sex != ''">and sex = #{sex}</if>
            <if test="birthday != null and birthday != ''">and birthday = #{birthday}</if>
            <if test="lastLoginTime != null and lastLoginTime != ''">and last_login_time = #{lastLoginTime}</if>
            <if test="picture != null and picture != ''">and picture = #{picture}</if>
            <if test="isLocked != null">and is_locked = #{isLocked}</if>
            <if test="introduction != null and introduction != ''">and introduction = #{introduction}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="isActivation != null and isActivation != ''">and is_activation = #{isActivation}</if>
            <if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="roleId != null">and role_id = #{roleId}</if>
        </where>
    </select>

</mapper>