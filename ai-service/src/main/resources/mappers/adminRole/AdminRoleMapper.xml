<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.AdminRoleMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.adminRole.AdminRoleDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="adminOid" column="admin_oid"/>
        <result property="roleId" column="role_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getAdminRoleListByCondition" resultType="com.fh.ai.business.entity.vo.adminRole.AdminRoleVo">
        select t.* from (
        select ar.*, r.role_name, r.is_locked
        from p_admin_role ar
        LEFT JOIN p_role r on ar.role_id = r.id
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="adminOid != null and adminOid != ''">and admin_oid = #{adminOid}</if>
            <if test="roleId != null and roleId != ''">and role_id = #{roleId}</if>
            <if test="isLocked != null">and is_locked = #{isLocked}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
        </where>
    </select>
    <select id="getAdminRoleByAdminOid" resultType="com.fh.ai.business.entity.vo.adminRole.AdminRoleVo">
        select role_id from p_admin_role where admin_oid = #{adminOid}
    </select>
</mapper>