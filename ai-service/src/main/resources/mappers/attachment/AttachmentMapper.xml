<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.AttachmentMapper">

	<select id="getAttachmentListByCondition" resultType="com.fh.ai.business.entity.vo.attachment.AttachmentVo">
		select t.* from (
            SELECT
            a.*
            FROM
            p_attachment a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="originalName != null and originalName != ''">and original_name = #{originalName}</if>
            <if test="newName != null and newName != ''">and new_name = #{newName}</if>
            <if test="suffix != null and suffix != ''">and suffix = #{suffix}</if>
            <if test="size != null and size != ''">and size = #{size}</if>
            <if test="cover != null and cover != ''">and cover = #{cover}</if>
            <if test="originPath != null and originPath != ''">and origin_path = #{originPath}</if>
            <if test="viewPath != null and viewPath != ''">and view_path = #{viewPath}</if>
            <if test="uploadState != null and uploadState != ''">and upload_state = #{uploadState}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="note != null and note != ''">and note = #{note}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
            <if test="taskType != null">and task_type = #{taskType}</if>
            <if test="thirdFileUrl != null and thirdFileUrl != ''">and third_file_url = #{thirdFileUrl}</if>
            <if test="thirdFileUrlMd5 != null and thirdFileUrlMd5 != ''">and third_file_url_md5 = #{thirdFileUrlMd5}</if>
        </where>
	</select>
</mapper>