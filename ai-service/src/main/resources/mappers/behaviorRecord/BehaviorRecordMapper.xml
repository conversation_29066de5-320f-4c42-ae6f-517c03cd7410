<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BehaviorRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.behaviorRecord.BehaviorRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="type" column="type"/>
        <result property="subType" column="sub_type"/>
        <result property="remarks" column="remarks"/>
        <result property="parameterJson" column="parameter_json"/>
        <result property="result" column="result"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getBehaviorRecordListByCondition" resultType="com.fh.ai.business.entity.vo.behaviorRecord.BehaviorRecordVo">
        select t.* from (
        select a.* from p_behavior_record a
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="subType != null and subType != ''">and sub_type = #{subType}</if>
            <if test="remarks != null and remarks != ''">and remarks = #{remarks}</if>
            <if test="parameterJson != null and parameterJson != ''">and parameter_json = #{parameterJson}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
    </select>

	<select id="getDistinctType" resultType="com.fh.ai.business.entity.vo.behaviorRecord.BehaviorRecordVo">
        select * from (select a.type,max(a.id) id from p_behavior_record a
        where a.user_oid = #{userOid} and is_delete = #{isDelete}  GROUP BY a.type
        ) t
	</select>
</mapper>