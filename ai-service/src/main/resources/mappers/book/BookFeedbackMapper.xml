<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookFeedbackMapper">
	<resultMap type="com.fh.ai.business.entity.dto.book.BookFeedbackDto" id="BookFeedbackResultMap">
		<id property="id" column="id"/>
		<result property="bookId" column="book_id"/>
		<result property="bookName" column="book_name"/>
		<result property="bookIsbn" column="book_isbn"/>
		<result property="sourceType" column="source_type"/>
		<result property="remark" column="remark"/>
		<result property="userOid" column="user_oid"/>
		<result property="phone" column="phone"/>
		<result property="createTime" column="create_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="isDelete" column="is_delete"/>
	</resultMap>

	<sql id="BookFeedbackCommonSelect">
		select
			t.id,
			t.book_id,
			t.book_name,
			t.book_isbn,
			t.source_type,
			t.remark,
			t.user_oid,
			t.phone,
			t.create_time,
			t.create_by,
			t.update_time,
			t.update_by,
			t.is_delete
		from p_book_feedback t
	</sql>

	<sql id="BookFeedbackCommonWhere">
		<where>
			<if test="id != null">and t.id = #{id}</if>
			<if test="bookId != null">and t.book_id = #{bookId}</if>
			<if test="bookName != null and bookName != ''">and t.book_name like concat('%', #{bookName}, '%')</if>
			<if test="bookIsbn != null and bookIsbn != ''">and t.book_isbn like concat('%', #{bookIsbn}, '%')</if>
			<if test="sourceType != null">and t.source_type = #{sourceType}</if>
			<if test="remark != null and remark != ''">and t.remark like concat('%', #{remark}, '%')</if>
			<if test="userOid != null and userOid != ''">and t.user_oid like concat('%', #{userOid}, '%')</if>
			<if test="phone != null and phone != ''">and t.phone like concat('%', #{phone}, '%')</if>
			<if test="createTime != null">and t.create_time = #{createTime}</if>
			<if test="createBy != null and createBy != ''">and t.create_by like concat('%', #{createBy}, '%')</if>
			<if test="updateTime != null">and t.update_time = #{updateTime}</if>
			<if test="updateBy != null and updateBy != ''">and t.update_by like concat('%', #{updateBy}, '%')</if>
			<if test="isDelete != null">and t.is_delete = #{isDelete}</if>
		</where>
	</sql>

	<select id="getBookFeedbackListByCondition" resultType="com.fh.ai.business.entity.vo.book.BookFeedbackVo">
		<include refid="BookFeedbackCommonSelect"/>
		<include refid="BookFeedbackCommonWhere"/>
	</select>

	<select id="getBookFeedbackByCondition" resultType="com.fh.ai.business.entity.vo.book.BookFeedbackVo">
		<include refid="BookFeedbackCommonSelect"/>
		<include refid="BookFeedbackCommonWhere"/>
		limit 1
	</select>

</mapper>