<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.book.BookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="sourceType" column="source_type"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="bookName" column="book_name"/>
        <result property="bookDescription" column="book_description"/>
        <result property="bookAuthor" column="book_author"/>
        <result property="bookCategory" column="book_category"/>
        <result property="bookCategoryAll" column="book_category_all"/>
        <result property="bookLabel" column="book_label"/>
        <result property="publishTimeStr" column="publish_time_str"/>
        <result property="publishName" column="publish_name"/>
        <result property="bookIsbn" column="book_isbn"/>
        <result property="bookRecommendation" column="book_recommendation"/>
        <result property="bookCover" column="book_cover"/>
        <result property="bookThirdId" column="book_third_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="bookRecommendRating" column="book_recommend_rating"/>
        <result property="bookPrice" column="book_price"/>
        <result property="bookShop" column="book_shop"/>
        <result property="salesCount" column="sales_count"/>
        <result property="inputType" column="input_type"/>
        <result property="coverType" column="cover_type"/>
        <result property="publishGroup" column="publish_group"/>
		<result property="publishTime" column="publish_time"/>
		<result property="bookDescriptionHandleRmark" column="book_description_handle_rmark"/>
		<result property="keywords" column="keywords"/>
		<result property="deleteReason" column="delete_reason"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="sortIndex != null ">and sort_index = #{sortIndex}</if>
			<if test="bookName != null and bookName != '' ">and book_name like concat('%', #{bookName}, '%')</if>
			<if test="bookDescription != null and bookDescription != '' ">and book_description like concat('%', #{bookDescription}, '%')</if>
			<if test="bookAuthor != null and bookAuthor != '' ">and book_author like concat('%', #{bookAuthor}, '%')</if>
			<if test="bookCategory != null and bookCategory != '' ">and book_category like concat('%', #{bookCategory}, '%')</if>
			<if test="bookCategoryAll != null and bookCategoryAll != '' ">and book_category_all like concat('%', #{bookCategoryAll}, '%')</if>
			<if test="bookLabel != null and bookLabel != '' ">and book_label like concat('%', #{bookLabel}, '%')</if>
			<if test="publishTimeStr != null and publishTimeStr != '' ">and publish_time_str like concat('%', #{publishTimeStr}, '%')</if>
			<if test="publishName != null and publishName != '' ">and publish_name like concat('%', #{publishName}, '%')</if>
			<if test="publishGroup != null and publishGroup != '' ">and publish_group like concat('%', #{publishGroup}, '%')</if>
			<if test="bookIsbn != null and bookIsbn != '' ">and book_isbn like concat('%', #{bookIsbn}, '%')</if>
			<if test="bookRecommendation != null and bookRecommendation != '' ">and book_recommendation like concat('%', #{bookRecommendation}, '%')</if>
			<if test="bookCover != null and bookCover != '' ">and book_cover like concat('%', #{bookCover}, '%')</if>
			<if test="bookThirdId != null and bookThirdId != '' ">and book_third_id like concat('%', #{bookThirdId}, '%')</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="bookRecommendRating != null and bookRecommendRating != ''">and book_recommend_rating = #{bookRecommendRating}</if>
			<if test="bookPrice != null and bookPrice != ''">and book_price = #{bookPrice}</if>
			<if test="bookShop != null and bookShop != ''">and book_shop = #{bookShop}</if>
			<if test="salesCount != null and salesCount != ''">and sales_count = #{salesCount}</if>
			<if test="inputType != null">and input_type = #{inputType}</if>
			<if test="coverType != null">and cover_type = #{coverType}</if>
			<if test="searchKey != null and searchKey != ''">
				and (
					book_name like concat('%', #{searchKey}, '%')
					or book_author like concat('%', #{searchKey}, '%')
					or book_isbn like concat('%', #{searchKey}, '%')
				)
			</if>
			<if test="publishTime != null">and publish_time = #{publishTime}</if>
			<if test="bookDescriptionHandleRmark != null and bookDescriptionHandleRmark != ''">and book_description_handle_rmark = #{bookDescriptionHandleRmark}</if>
			<if test="keywords != null and keywords != ''">and keywords like concat('%', #{keywords}, '%')</if>
			<if test="deleteReason != null and deleteReason != ''">and delete_reason = #{deleteReason}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.source_type
	 		,t.sort_index
	 		,t.book_name
	 		,t.book_description
	 		,t.book_author
	 		,t.book_category
	 		,t.book_category_all
	 		,t.book_label
	 		,t.publish_time_str
	 		,t.publish_name
	 		,t.book_isbn
	 		,t.book_recommendation
	 		,t.book_cover
	 		,t.book_third_id
	 		,t.remark
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.book_recommend_rating
			,t.book_price
			,t.book_shop
			,t.sales_count
			,t.input_type
			,t.cover_type
			,t.publish_group
			,t.publish_time
			,t.book_description_handle_rmark
		 	,t.keywords
			,t.delete_reason
		from (
			 select a.* from p_book a
		 ) t

	</sql>

	<sql id="biaoShengBang">

	</sql>


	<select id="getBookListByCondition" resultType="com.fh.ai.business.entity.vo.book.BookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getBookByCondition" resultType="com.fh.ai.business.entity.vo.book.BookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
    <select id="selectIds" resultType="com.fh.ai.business.entity.vo.book.BookVo">
		select id,book_third_id,book_isbn,book_price from p_book
		where source_type = #{sourceType}
	</select>

	<select id="getCategoryListBySourceType" parameterType="map" resultType="java.lang.String">
		select distinct book_category from p_book
		where is_delete=1 and source_type = #{sourceType} and book_category is not null and book_category != ''
	</select>
	<select id="getShopListBySourceType" parameterType="map" resultType="java.lang.String">
		select distinct book_shop from p_book
		where is_delete=1 and source_type = #{sourceType} and book_shop is not null and book_shop != ''
	</select>
	<select id="getPublishListBySourceType" parameterType="map" resultType="java.lang.String">
		select distinct publish_name from p_book
		where is_delete=1 and source_type = #{sourceType} and publish_name is not null and publish_name != ''
	</select>

	<select id="getWeiXinRankInfo" resultType="com.fh.ai.business.entity.vo.book.RankAndSaleInfo">
		select pb.book_name,pb.source_type,pb.book_third_id,pb.book_isbn,pb.publish_time_str,pb.book_price,DATE_FORMAT(pw.create_time, '%Y-%m-%d') AS create_day,pw.sort_index
		from p_book pb join p_ranking_weixin pw on pb.id = pw.book_id
		<where>
			pb.is_delete = 1 and pw.is_delete = 1
		<if test="isbn != null">
			and pb.book_isbn = #{isbn}
		</if>

		<if test="searchTimeBegin != null and searchTimeBegin != ''">
			and pw.create_time &gt;= #{searchTimeBegin}
		</if>
		<if test="searchTimeEnd != null and searchTimeEnd != ''">
			and pw.create_time &lt;= #{searchTimeEnd}
		</if>
		<if test="rankingType != null">
			and pw.ranking_type = #{rankingType}
		</if>
		</where>
		order by pw.create_time asc
	</select>

	<select id="getDangDangRankInfo" resultType="com.fh.ai.business.entity.vo.book.RankAndSaleInfo">
		select pb.book_name,pb.source_type,pb.book_third_id,pb.book_isbn,pb.publish_time_str,pb.book_price,DATE_FORMAT(pd.create_time, '%Y-%m-%d') AS create_day,pd.sort_index
		from p_book pb join p_ranking_dangdang pd on pb.id = pd.book_id
		<where>
			pb.is_delete = 1 and pd.is_delete = 1
			<if test="isbn != null">
				and pb.book_isbn = #{isbn}
			</if>

			<if test="searchTimeBegin != null and searchTimeBegin != ''">
				and pd.create_time &gt;= #{searchTimeBegin}
			</if>
			<if test="searchTimeEnd != null and searchTimeEnd != ''">
				and pd.create_time &lt;= #{searchTimeEnd}
			</if>
			<if test="rankingType != null">
				and pd.ranking_type = #{rankingType}
			</if>
		</where>
		order by pd.create_time asc
	</select>

	<select id="getDouBanRankInfo" resultType="com.fh.ai.business.entity.vo.book.RankAndSaleInfo">
		select pb.book_name,pb.source_type,pb.book_third_id,pb.book_isbn,pb.publish_time_str,pb.book_price,DATE_FORMAT(pdb.create_time, '%Y-%m-%d') AS create_day,pdb.sort_index
		from p_book pb join p_ranking_douban pdb on pb.id = pdb.book_id
		<where>
			pb.is_delete = 1 and pdb.is_delete = 1
			<if test="isbn != null">
				and pb.book_isbn = #{isbn}
			</if>

			<if test="searchTimeBegin != null and searchTimeBegin != ''">
				and pdb.create_time &gt;= #{searchTimeBegin}
			</if>
			<if test="searchTimeEnd != null and searchTimeEnd != ''">
				and pdb.create_time &lt;= #{searchTimeEnd}
			</if>
		</where>
		order by pdb.create_time asc
	</select>

	<select id="getDouYinSaleInfo" resultType="com.fh.ai.business.entity.vo.book.RankAndSaleInfo">
		select pb.book_name,pb.source_type,pb.book_third_id,pb.book_isbn,pb.publish_time_str,pb.book_price,
		       DATE_FORMAT(pdy.collect_time, '%Y-%m-%d') AS collect_day,pdy.sales_count,pdy.sort_index,pdy.book_shop,pdy.book_id,pdy.create_time
		from p_book pb join p_ranking_douyin pdy on pb.id = pdy.book_id
		where
			pb.is_delete = 1 and pdy.is_delete = 1
			<if test="isbn != null">
				and pb.book_isbn = #{isbn}
			</if>

			<if test="searchTimeBegin != null and searchTimeBegin != ''">
				and pdy.collect_time &gt;= #{searchTimeBegin}
			</if>
			<if test="searchTimeEnd != null and searchTimeEnd != ''">
				and pdy.collect_time &lt;= #{searchTimeEnd}
			</if>
			<if test="rankingType != null and rankingType == 3">
				and pb.publish_time_str &gt;= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 MONTH), '%Y-%m')
			</if>
		<choose>
			<when test="rankingType != null and rankingType == 1">
				order by pdy.sales_count desc
			</when>
			<when test="rankingType != null and rankingType == 2">
				order by pdy.rising_index desc
			</when>
		</choose>
	</select>


	<select id="getJingDongSaleInfo" resultType="com.fh.ai.business.entity.vo.book.RankAndSaleInfo">
		select pb.book_name,pb.source_type,pb.book_third_id,pb.book_isbn,pb.publish_time_str,pb.book_price,
		       DATE_FORMAT(pjd.collect_time, '%Y-%m-%d')AS collect_day,pjd.sales_count,pjd.comment_count,pjd.sort_index,pjd.book_shop,pjd.book_id,pjd.create_time
		from p_book pb join p_ranking_jingdong pjd on pb.id = pjd.book_id
		where
			pb.is_delete = 1 and pjd.is_delete = 1
			<if test="isbn != null">
				and pb.book_isbn = #{isbn}
			</if>

			<if test="searchTimeBegin != null and searchTimeBegin != ''">
				and pjd.collect_time &gt;= #{searchTimeBegin}
			</if>
			<if test="searchTimeEnd != null and searchTimeEnd != ''">
				and pjd.collect_time &lt;= #{searchTimeEnd}
			</if>
			<if test="rankingType != null and rankingType == 3">
				and pb.publish_time_str &gt;= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
			</if>
		order by pjd.comment_count desc
	</select>

	<select id="getSalesCountByIsbnOfSmart" resultType="com.fh.ai.business.entity.vo.book.SmartSaleInfo">
		select sum(prs.sales_count) as month_sales_count,ranking_month,ranking_year from p_book pb join p_ranking_smart prs on pb.id = prs.book_id
		    where pb.is_delete=1 and prs.is_delete = 1
			<if test="bookIsbn != null and bookIsbn != ''">
				and pb.book_isbn = #{bookIsbn}
			</if>
			<if test="rankingType != null ">
				and prs.ranking_type = #{rankingType}
			</if>
			<if test="rankingChannel != null">
				and prs.ranking_channel = #{rankingChannel}
			</if>
			<if test="searchTimeBegin != null and  searchTimeEnd != null">
				and (prs.ranking_year * 100 + prs.ranking_month) BETWEEN #{searchTimeBegin} AND #{searchTimeEnd}
			</if>
		group by prs.ranking_year,prs.ranking_month
		order by prs.ranking_year asc ,prs.ranking_month asc
	</select>

	<select id="getCozeBookListByCondition" resultType="com.fh.ai.business.entity.vo.book.BookVo">
		SELECT t1.id,t1.book_name,t1.book_cover,t1.book_isbn,t1.book_description,t1.publish_name,t1.book_author,t1.keywords,t1.source_type,t1.publish_time,t1.book_price
		FROM p_book t1
		JOIN (
		SELECT id
		FROM p_book
		<where>
			<if test="sourceType != null">
				AND source_type=#{sourceType}
			</if>
			AND is_delete=1
			<if test="cozeKeywordsQueryList != null and cozeKeywordsQueryList.size() >0">
				AND
				<foreach collection="cozeKeywordsQueryList" item="item" separator=" or " open="(" close=")">
					<if test="item != null and item != ''">
						keywords like concat('%', #{item}, '%')
					</if>
				</foreach>
			</if>
			<if test="cozePublishNameQueryList != null and cozePublishNameQueryList.size() >0">
				AND
				<foreach collection="cozePublishNameQueryList" item="item" separator=" or " open="(" close=")">
					<if test="item != null and item != ''">
						publish_name like concat('%', #{item}, '%')
					</if>
				</foreach>
			</if>
			<if test="publishTime != null">
				AND publish_time &gt; #{publishTime}
			</if>
		</where>
		ORDER BY RAND()
		<if test="limit != null">
			LIMIT #{limit}
		</if>
		) t2 ON t1.id = t2.id
	</select>
</mapper>