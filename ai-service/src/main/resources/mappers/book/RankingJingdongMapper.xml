<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.RankingJingdongMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.book.RankingJingdongDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="bookShop" column="book_shop"/>
        <result property="salesCount" column="sales_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="bookId" column="book_id"/>
        <result property="rankingUp" column="ranking_up"/>
        <result property="rankingDown" column="ranking_down"/>
        <result property="remark" column="remark"/>
        <result property="uuid" column="uuid"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
		<result property="collectTime" column="collect_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sortIndex != null ">and sort_index = #{sortIndex}</if>
			<if test="bookShop != null and bookShop != '' ">and book_shop like concat('%', #{bookShop}, '%')</if>
			<if test="salesCount != null and salesCount != '' ">and sales_count like concat('%', #{salesCount}, '%')</if>
			<if test="commentCount != null and commentCount != '' ">and comment_count like concat('%', #{commentCount}, '%')</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="rankingUp != null ">and ranking_up = #{rankingUp}</if>
			<if test="rankingDown != null ">and ranking_down = #{rankingDown}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.sort_index
	 		,t.book_shop
	 		,t.sales_count
	 		,t.comment_count
	 		,t.book_id
	 		,t.ranking_up
	 		,t.ranking_down
	 		,t.remark
	 		,t.uuid
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.collect_time
		from (
			 select a.* from p_ranking_jingdong a
		 ) t

	</sql>

	<select id="getRankingJingdongListByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingJingdongVo">
		select
		prj.*,pb.book_name,pb.book_shop,pb.book_author,pb.book_category,pb.book_description,pb.book_isbn,pb.book_cover,pb.book_label,pb.book_recommend_rating,pb.book_recommendation,pb.publish_name
		,pb.publish_time_str,pb.book_price
		from p_ranking_jingdong prj
		join p_book pb on prj.book_id = pb.id and pb.is_delete=1
		where
		prj.is_delete=1
		<if test="id != null ">and prj.id = #{id}</if>
		<if test="searchTimeBegin != null and searchTimeBegin != ''">
			and prj.collect_time &gt;= #{searchTimeBegin}
		</if>
		<if test="searchTimeEnd != null and searchTimeEnd != ''">
			and prj.collect_time &lt;= #{searchTimeEnd}
		</if>
		<if test="bookShop != null and bookShop != '' ">and book_shop like concat('%', #{bookShop}, '%')</if>
		<if test="bookId != null ">and prj.book_id = #{bookId}</if>
		<if test="rankingUp != null ">and prj.ranking_up = #{rankingUp}</if>
		<if test="rankingDown != null ">and prj.ranking_down = #{rankingDown}</if>
		<if test="uuid != null and uuid != ''">and prj.uuid = #{uuid}</if>
		<if test="searchKey != null and searchKey != ''">
			and (pb.book_name like concat('%', #{searchKey}, '%')
			or pb.book_author like concat('%', #{searchKey}, '%')
			or pb.book_isbn like concat('%', #{searchKey}, '%')
			or pb.publish_name like concat('%', #{searchKey}, '%'))
		</if>
		<if test="bookCategory != null and bookCategory != ''">
			and pb.book_category like concat('%', #{bookCategory}, '%')
		</if>
		<if test="sortType != null and sortType == 3">
			and pb.publish_time_str &gt;= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
		</if>
		order by prj.comment_count desc
	</select>

	<select id="getRankingJingdongByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingJingdongVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getLatestRankingJingdongDateUuid" resultType="com.fh.ai.business.entity.vo.book.RankingJingdongVo">
		select * from p_ranking_jingdong
		where is_delete=1
		order by collect_time desc limit 1
	</select>
</mapper>