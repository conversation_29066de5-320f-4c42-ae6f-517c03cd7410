<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.RankingPpmbookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.book.RankingPpmbookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="rankingType" column="ranking_type"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="bookId" column="book_id"/>
        <result property="rankingUp" column="ranking_up"/>
        <result property="rankingDown" column="ranking_down"/>
        <result property="rankingYear" column="ranking_year"/>
        <result property="rankingQuarter" column="ranking_quarter"/>
        <result property="rankingMonth" column="ranking_month"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
		<result property="uuid" column="uuid"/>
		<result property="collectTime" column="collect_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="rankingType != null ">and ranking_type = #{rankingType}</if>
			<if test="sortIndex != null ">and sort_index = #{sortIndex}</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="rankingUp != null ">and ranking_up = #{rankingUp}</if>
			<if test="rankingDown != null ">and ranking_down = #{rankingDown}</if>
			<if test="rankingYear != null ">and ranking_year = #{rankingYear}</if>
			<if test="rankingQuarter != null ">and ranking_quarter = #{rankingQuarter}</if>
			<if test="rankingMonth != null ">and ranking_month = #{rankingMonth}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="uuid != null and uuid != ''">and uuid = #{uuid}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.ranking_type
	 		,t.sort_index
	 		,t.book_id
	 		,t.ranking_up
	 		,t.ranking_down
	 		,t.ranking_year
	 		,t.ranking_quarter
	 		,t.ranking_month
	 		,t.remark
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.uuid
			,t.collect_time
		from (
			 select a.* from p_ranking_ppmbook a
		 ) t

	</sql>

	<select id="getRankingPpmbookListByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingPpmbookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getRankingPpmbookByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingPpmbookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getLatestRankingPpmbookDateUuid" resultType="java.lang.String">
		select uuid from p_ranking_ppmbook
		where is_delete=1
		<if test="rankingType != null ">and ranking_type = #{rankingType}</if>
		order by create_time desc limit 1
	</select>

	<select id="getLatestRankingPpmbookInfo" resultType="com.fh.ai.business.entity.vo.book.RankingPpmbookVo">
		select * from p_ranking_ppmbook
		where is_delete=1
		<if test="rankingType != null ">and ranking_type = #{rankingType}</if>
		order by create_time desc limit 1
	</select>

	<select id="getRankingPpmbookListWithBookByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingPpmbookVo">
		select prp.*,pb.book_name,pb.book_author,pb.book_category,pb.book_description,pb.book_isbn,pb.book_cover,pb.book_label,pb.book_recommend_rating,pb.book_recommendation,pb.publish_name
		,pb.publish_time_str,pb.book_price
		from p_ranking_ppmbook prp
		join p_book pb on prp.book_id = pb.id and pb.is_delete=1
		where prp.is_delete=1
		<if test="id != null ">and prp.id = #{id}</if>
		<if test="rankingType != null ">and prp.ranking_type = #{rankingType}</if>
		<if test="searchTimeBegin != null and searchTimeBegin != ''">
			and prp.create_time &gt;= #{searchTimeBegin}
		</if>
		<if test="searchTimeEnd != null and searchTimeEnd != ''">
			and prp.create_time &lt;= #{searchTimeEnd}
		</if>
		<if test="rankingYear != null">
			and prp.ranking_year = #{rankingYear}
		</if>
		<if test="rankingQuarter != null">
			and prp.ranking_quarter = #{rankingQuarter}
		</if>
		<if test="rankingMonth != null">
			and prp.ranking_month = #{rankingMonth}
		</if>
		<if test="bookId != null ">and prp.book_id = #{bookId}</if>
		<if test="rankingUp != null ">and prp.ranking_up = #{rankingUp}</if>
		<if test="rankingDown != null ">and prp.ranking_down = #{rankingDown}</if>
		<if test="uuid != null and uuid != ''">and prp.uuid = #{uuid}</if>
		<if test="searchKey != null and searchKey != ''">
			and (pb.book_name like concat('%', #{searchKey}, '%')
			or pb.book_author like concat('%', #{searchKey}, '%')
			or pb.book_isbn like concat('%', #{searchKey}, '%')
			or pb.publish_name like concat('%', #{searchKey}, '%'))
		</if>
		<if test="bookCategory != null and bookCategory != ''">and pb.book_category like concat('%', #{bookCategory}, '%')</if>
		<choose>
			<when test="sortType != null and sortType == 1">
				order by prp.sort_index asc
			</when>
			<when test="sortType != null and sortType == 2">
				order by prp.ranking_year desc,ranking_month desc
			</when>
		</choose>

	</select>
</mapper>