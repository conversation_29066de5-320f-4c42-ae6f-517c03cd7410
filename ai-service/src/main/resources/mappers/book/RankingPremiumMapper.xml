<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.RankingPremiumMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.book.RankingPremiumDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="rankingType" column="ranking_type"/>
        <result property="rankingGroupType" column="ranking_group_type"/>
        <result property="sortIndex" column="sort_index"/>
        <result property="bookId" column="book_id"/>
        <result property="rankingUp" column="ranking_up"/>
        <result property="rankingDown" column="ranking_down"/>
        <result property="rankingYear" column="ranking_year"/>
        <result property="rankingQuarter" column="ranking_quarter"/>
        <result property="rankingMonth" column="ranking_month"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
		<result property="uuid" column="uuid"/>
		<result property="collectTime" column="collect_time"/>
		<result property="subRankingName" column="sub_ranking_name"/>
		<result property="selectionTime" column="selection_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="rankingType != null ">and ranking_type = #{rankingType}</if>
			<if test="rankingGroupType != null ">and ranking_group_type = #{rankingGroupType}</if>
			<if test="sortIndex != null ">and sort_index = #{sortIndex}</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="rankingUp != null ">and ranking_up = #{rankingUp}</if>
			<if test="rankingDown != null ">and ranking_down = #{rankingDown}</if>
			<if test="rankingYear != null ">and ranking_year = #{rankingYear}</if>
			<if test="rankingQuarter != null ">and ranking_quarter = #{rankingQuarter}</if>
			<if test="rankingMonth != null ">and ranking_month = #{rankingMonth}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="uuid != null and uuid != ''">and uuid = #{uuid}</if>
			<if test="subRankingName != null and subRankingName != ''">and sub_ranking_name = #{subRankingName}</if>
			<if test="selectionTime != null and selectionTime != ''">and selection_time = #{selectionTime}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.ranking_type
	 		,t.ranking_group_type
	 		,t.sort_index
	 		,t.book_id
	 		,t.ranking_up
	 		,t.ranking_down
	 		,t.ranking_year
	 		,t.ranking_quarter
	 		,t.ranking_month
	 		,t.remark
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.uuid
			,t.collect_time
			,t.sub_ranking_name
			,t.selection_time
		from (
			 select a.* from p_ranking_premium a
		 ) t

	</sql>

	<select id="getRankingPremiumListByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingPremiumVo">
		select prp.*,pb.book_name,pb.book_author,pb.book_category,pb.book_description,pb.book_isbn,pb.book_cover,pb.book_label,pb.book_recommend_rating,pb.book_recommendation,pb.publish_name,pb.publish_group
		,pb.publish_time_str,pb.book_price
		from p_ranking_premium prp
		join p_book pb on prp.book_id = pb.id and pb.is_delete=1
		where prp.is_delete=1
		<if test="id != null ">and prp.id = #{id}</if>
		<if test="rankingType != null ">and prp.ranking_type = #{rankingType}</if>
		<if test="subRankingName != null and subRankingName != ''">and prp.sub_ranking_name = #{subRankingName}</if>
		<if test="selectionTime != null and selectionTime != ''">and prp.selection_time = #{selectionTime}</if>
		<if test="bookId != null ">and prp.book_id = #{bookId}</if>
		<if test="uuid != null and uuid != ''">and prp.uuid = #{uuid}</if>
		<if test="searchKey != null and searchKey != ''">
			and (pb.book_name like concat('%', #{searchKey}, '%')
			or pb.book_author like concat('%', #{searchKey}, '%')
			or pb.book_isbn like concat('%', #{searchKey}, '%')
			or pb.publish_name like concat('%', #{searchKey}, '%'))
		</if>
		<if test="bookCategory != null and bookCategory != ''">and pb.book_category like concat('%', #{bookCategory}, '%')</if>
		order by prp.sort_index
	</select>

	<select id="getRankingPremiumByCondition" resultType="com.fh.ai.business.entity.vo.book.RankingPremiumVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>