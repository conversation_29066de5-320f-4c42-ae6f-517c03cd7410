<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookCopywritingBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingBookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="copywritingId" column="copywriting_id"/>
        <result property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="bookCover" column="book_cover"/>
        <result property="modelResult" column="model_result"/>
        <result property="modelResultFinal" column="model_result_final"/>
        <result property="sellingPoints" column="selling_points"/>
        <result property="sellingPointsFinal" column="selling_points_final"/>
        <result property="generateStatus" column="generate_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="copywritingId != null ">and copywriting_id = #{copywritingId}</if>
			<if test="bookId != null ">and book_id = #{bookId}</if>
			<if test="bookName != null and bookName != '' ">and book_name like concat('%', #{bookName}, '%')</if>
			<if test="bookCover != null and bookCover != '' ">and book_cover like concat('%', #{bookCover}, '%')</if>
			<if test="modelResult != null and modelResult != '' ">and model_result like concat('%', #{modelResult}, '%')</if>
			<if test="modelResultFinal != null and modelResultFinal != '' ">and model_result_final like concat('%', #{modelResultFinal}, '%')</if>
			<if test="sellingPoints != null and sellingPoints != '' ">and selling_points like concat('%', #{sellingPoints}, '%')</if>
			<if test="sellingPointsFinal != null and sellingPointsFinal != '' ">and selling_points_final like concat('%', #{sellingPointsFinal}, '%')</if>
			<if test="generateStatus != null ">and generate_status = #{generateStatus}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="copywritingIdList != null and copywritingIdList.size() > 0">
				and copywriting_id in
				<foreach collection="copywritingIdList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.uuid
	 		,t.copywriting_id
	 		,t.book_id
	 		,t.book_name
	 		,t.book_cover
	 		,t.model_result
	 		,t.model_result_final
	 		,t.selling_points
	 		,t.selling_points_final
	 		,t.generate_status
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from p_book_copywriting_book a
		 ) t

	</sql>

	<select id="getBookCopywritingBookListByCondition" resultType="com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getBookCopywritingBookByCondition" resultType="com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>