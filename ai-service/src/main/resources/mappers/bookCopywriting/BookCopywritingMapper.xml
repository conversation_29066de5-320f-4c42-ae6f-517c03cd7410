<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookCopywritingMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.bookCopywriting.BookCopywritingDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="title" column="title"/>
        <result property="bookListId" column="book_list_id"/>
        <result property="uploadFileOid" column="upload_file_oid"/>
        <result property="contentType" column="content_type"/>
        <result property="sourceType" column="source_type"/>
        <result property="targetAudience" column="target_audience"/>
        <result property="writingStyleType" column="writing_style_type"/>
        <result property="referenceType" column="reference_type"/>
        <result property="referenceWords" column="reference_words"/>
        <result property="recommendTopic" column="recommend_topic"/>
        <result property="conditionJson" column="condition_json"/>
        <result property="overallSellingPoints" column="overall_selling_points"/>
        <result property="overallSellingPointsFinal" column="overall_selling_points_final"/>
        <result property="overallGenerateStatus" column="overall_generate_status"/>
		<result property="modelResult" column="model_result"/>
		<result property="modelResultFinal" column="model_result_final"/>
        <result property="sort" column="sort"/>
        <result property="userOid" column="user_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="bookListId != null ">and book_list_id = #{bookListId}</if>
			<if test="uploadFileOid != null and uploadFileOid != '' ">and upload_file_oid like concat('%', #{uploadFileOid}, '%')</if>
			<if test="contentType != null ">and content_type = #{contentType}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="targetAudience != null and targetAudience != '' ">and target_audience like concat('%', #{targetAudience}, '%')</if>
			<if test="writingStyleType != null ">and writing_style_type = #{writingStyleType}</if>
			<if test="referenceType != null ">and reference_type = #{referenceType}</if>
			<if test="referenceWords != null and referenceWords != '' ">and reference_words like concat('%', #{referenceWords}, '%')</if>
			<if test="recommendTopic != null and recommendTopic != '' ">and recommend_topic like concat('%', #{recommendTopic}, '%')</if>
			<if test="conditionJson != null and conditionJson != '' ">and condition_json like concat('%', #{conditionJson}, '%')</if>
			<if test="overallSellingPoints != null and overallSellingPoints != '' ">and overall_selling_points like concat('%', #{overallSellingPoints}, '%')</if>
			<if test="modelResult != null and modelResult != '' ">and model_result like concat('%', #{modelResult}, '%')</if>
			<if test="modelResultFinal != null and modelResultFinal != '' ">and model_result_final like concat('%', #{modelResultFinal}, '%')</if>
			<if test="overallSellingPointsFinal != null and overallSellingPointsFinal != '' ">and overall_selling_points_final like concat('%', #{overallSellingPointsFinal}, '%')</if>
			<if test="overallGenerateStatus != null ">and overall_generate_status = #{overallGenerateStatus}</if>
			<if test="sort != null ">and sort = #{sort}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="searchKey != null and searchKey != ''">
				and (t.title like concat('%', #{searchKey}, '%')
					or t.model_result like concat('%', #{searchKey}, '%')
					or t.recommend_topic like concat('%', #{searchKey}, '%')
					or t.overall_selling_points like concat('%', #{searchKey}, '%')
					)
			</if>
			<if test="generateDate != null">
				and DATE_FORMAT(t.create_time, '%Y-%m-%d') = DATE_FORMAT(#{generateDate}, '%Y-%m-%d')
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.uuid
	 		,t.title
	 		,t.book_list_id
	 		,t.upload_file_oid
	 		,t.content_type
	 		,t.source_type
	 		,t.target_audience
	 		,t.writing_style_type
	 		,t.reference_type
	 		,t.reference_words
	 		,t.recommend_topic
	 		,t.condition_json
	 		,t.overall_selling_points
	 		,t.overall_selling_points_final
	 		,t.overall_generate_status
		    ,t.model_result
		    ,t.model_result_final
	 		,t.sort
	 		,t.user_oid
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from p_book_copywriting a
		 ) t

	</sql>

	<select id="getBookCopywritingListByCondition" resultType="com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<if test="orderBy != null and orderBy != ''">order by ${orderBy}</if>
	</select>

	<select id="getBookCopywritingByCondition" resultType="com.fh.ai.business.entity.vo.bookCopywriting.BookCopywritingVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>