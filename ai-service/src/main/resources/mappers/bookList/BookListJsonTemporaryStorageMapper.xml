<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookListJsonTemporaryStorageMapper">
    <resultMap type="com.fh.ai.business.entity.dto.bookList.BookListJsonTemporaryStorageDto" id="BookListJsonTemporaryStorageResultMap">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="userOid" column="user_oid"/>
        <result property="bookListJson" column="book_list_json"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="BookListJsonTemporaryStorageCommonSelect">
        select
            t.id,
            t.uuid,
            t.user_oid,
            t.book_list_json,
            t.create_time,
            t.create_by,
            t.update_time,
            t.update_by,
            t.is_delete
        from p_book_list_json_temporary_storage t
    </sql>

    <sql id="BookListJsonTemporaryStorageCommonWhere">
        <where>
            <if test="id != null">and t.id = #{id}</if>
            <if test="uuid != null and uuid != ''">and t.uuid = #{uuid}</if>
            <if test="userOid != null and userOid != ''">and t.user_oid = #{userOid}</if>
            <if test="bookListJson != null and bookListJson != ''">and t.book_list_json like concat('%', #{bookListJson}, '%')</if>
            <if test="createTime != null">and t.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and t.create_by like concat('%', #{createBy}, '%')</if>
            <if test="updateTime != null">and t.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and t.update_by like concat('%', #{updateBy}, '%')</if>
            <if test="isDelete != null">and t.is_delete = #{isDelete}</if>
        </where>
    </sql>

    <select id="getBookListJsonTemporaryStorageListByCondition" resultType="com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo">
        <include refid="BookListJsonTemporaryStorageCommonSelect"/>
        <include refid="BookListJsonTemporaryStorageCommonWhere"/>
    </select>

    <select id="getBookListJsonTemporaryStorageByCondition" resultType="com.fh.ai.business.entity.vo.bookList.BookListJsonTemporaryStorageVo">
        <include refid="BookListJsonTemporaryStorageCommonSelect"/>
        <include refid="BookListJsonTemporaryStorageCommonWhere"/>
        limit 1
    </select>
</mapper>