<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookListMapper">

	<resultMap type="com.fh.ai.business.entity.dto.bookList.BookListDto" id="BookListResultMap">
		<result property="id" column="id"/>
		<result property="uuid" column="uuid"/>
		<result property="userOid" column="user_oid"/>
		<result property="name" column="name"/>
		<result property="referenceType" column="reference_type"/>
		<result property="referenceWords" column="reference_words"/>
		<result property="recommendTopic" column="recommend_topic"/>
		<result property="analysis" column="analysis"/>
		<result property="conditionJson" column="condition_json"/>
		<result property="modelResult" column="model_result"/>
		<result property="bookListJson" column="book_list_json"/>
		<result property="convertState" column="convert_state"/>
		<result property="sort" column="sort"/>
		<result property="createTime" column="create_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="isDelete" column="is_delete"/>
	</resultMap>

	<sql id="BookListCommonSelect">
		select
			t.id,
			t.uuid,
			t.user_oid,
			t.name,
			t.reference_type,
			t.reference_words,
			t.recommend_topic,
			t.analysis,
			t.condition_json,
			t.model_result,
			t.book_list_json,
			t.convert_state,
			t.sort,
			t.create_time,
			t.create_by,
			t.update_time,
			t.update_by,
			t.is_delete
		from p_book_list t
	</sql>

	<sql id="BookListCommonWhere">
		<where>
			<if test="id != null">and t.id = #{id}</if>
			<if test="uuid != null and uuid != ''">and t.uuid = #{uuid}</if>
			<if test="name != null and name != ''">and t.name like concat('%', #{name}, '%')</if>
			<if test="userOid != null and userOid != ''">and t.user_oid = #{userOid}</if>
			<if test="referenceType != null">and t.reference_type = #{referenceType}</if>
			<if test="referenceWords != null and referenceWords != ''">and t.reference_words like concat('%', #{referenceWords}, '%')</if>
			<if test="recommendTopic != null and recommendTopic != ''">and t.recommend_topic like concat('%', #{recommendTopic}, '%')</if>
			<if test="analysis != null and analysis != ''">and t.analysis like concat('%', #{analysis}, '%')</if>
			<if test="conditionJson != null and conditionJson != ''">and t.condition_json like concat('%', #{conditionJson}, '%')</if>
			<if test="modelResult != null and modelResult != ''">and t.model_result like concat('%', #{modelResult}, '%')</if>
			<if test="bookListJson != null and bookListJson != ''">and t.book_list_json like concat('%', #{bookListJson}, '%')</if>
			<if test="sort != null">and t.sort = #{sort}</if>
			<if test="createTime != null">and t.create_time = #{createTime}</if>
			<if test="createBy != null and createBy != ''">and t.create_by like concat('%', #{createBy}, '%')</if>
			<if test="updateTime != null">and t.update_time = #{updateTime}</if>
			<if test="updateBy != null and updateBy != ''">and t.update_by like concat('%', #{updateBy}, '%')</if>
			<if test="isDelete != null">and t.is_delete = #{isDelete}</if>
			<if test="convertState != null">and t.convert_state = #{convertState}</if>
			<if test="searchKey != null and searchKey != ''">
				and (t.name like concat('%', #{searchKey}, '%')
					or t.reference_words like concat('%', #{searchKey}, '%')
					or t.recommend_topic like concat('%', #{searchKey}, '%'))
			</if>
			<if test="generateDate != null">
				and DATE_FORMAT(t.create_time, '%Y-%m-%d') = DATE_FORMAT(#{generateDate}, '%Y-%m-%d')
			</if>
		</where>
	</sql>

	<select id="getBookListListByCondition" resultType="com.fh.ai.business.entity.vo.bookList.BookListVo">
		<include refid="BookListCommonSelect"/>
		<include refid="BookListCommonWhere"/>
		<if test="orderType != null">
			<if test="orderType == 1">
				order by t.create_time desc
			</if>
			<if test="orderType == 2">
				order by t.create_time asc
			</if>
		</if>
	</select>

	<select id="getBookListByCondition" resultType="com.fh.ai.business.entity.vo.bookList.BookListVo">
		<include refid="BookListCommonSelect"/>
		<include refid="BookListCommonWhere"/>
		limit 1
	</select>
</mapper>