<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.BookListPosterMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.bookListPoster.BookListPosterDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="bookListId" column="book_list_id"/>
        <result property="contentType" column="content_type"/>
        <result property="sourceType" column="source_type"/>
        <result property="conditionJson" column="condition_json"/>
        <result property="refHtmlStyle" column="ref_html_style"/>
        <result property="contentColor" column="content_color"/>
        <result property="modelResult" column="model_result"/>
        <result property="bookListPosterJson" column="book_list_poster_json"/>
        <result property="bookListPosterName" column="book_list_poster_name"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="userOid" column="user_oid"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null">
				and t.id = #{id}
			</if>
			<if test="uuid != null and uuid != ''">
				and t.uuid = #{uuid}
			</if>
			<if test="bookListId != null">
				and t.book_list_id = #{bookListId}
			</if>
			<if test="contentType != null">
				and t.content_type = #{contentType}
			</if>
			<if test="sourceType != null">
				and t.source_type = #{sourceType}
			</if>
			<if test="conditionJson != null and conditionJson != ''">
				and t.condition_json like CONCAT('%',#{conditionJson},'%')
			</if>
			<if test="refHtmlStyle != null and refHtmlStyle != ''">
				and t.ref_html_style like CONCAT('%',#{refHtmlStyle},'%')
			</if>
			<if test="contentColor != null and contentColor != ''">
				and t.content_color like CONCAT('%',#{contentColor},'%')
			</if>
			<if test="modelResult != null and modelResult != ''">
				and t.model_result like CONCAT('%',#{modelResult},'%')
			</if>
			<if test="bookListPosterJson != null and bookListPosterJson != ''">
				and t.book_list_poster_json like CONCAT('%',#{bookListPosterJson},'%')
			</if>
			<if test="bookListPosterName != null and bookListPosterName != ''">
				and t.book_list_poster_name like CONCAT('%',#{bookListPosterName},'%')
			</if>
			<if test="sort != null">
				and t.sort = #{sort}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="generateDate != null">
				and DATE_FORMAT(t.create_time, '%Y-%m-%d') = DATE_FORMAT(#{generateDate}, '%Y-%m-%d')
			</if>
			<if test="createBy != null and createBy != ''">
				and t.create_by = #{createBy}
			</if>
			<if test="updateTime != null">
				and t.update_time = #{updateTime}
			</if>
			<if test="updateBy != null and updateBy != ''">
				and t.update_by = #{updateBy}
			</if>
			<if test="isDelete != null">
				and t.is_delete = #{isDelete}
			</if>
			<if test="userOid != null and userOid != ''">
				and t.user_oid = #{userOid}
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.uuid
	 		,t.book_list_id
	 		,t.content_type
	 		,t.source_type
	 		,t.condition_json
	 		,t.ref_html_style
	 		,t.content_color
	 		,t.model_result
	 		,t.book_list_poster_json
	 		,t.book_list_poster_name
	 		,t.sort
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.user_oid
		from (
			 select a.* from p_book_list_poster a
		 ) t

	</sql>

	<select id="getBookListPosterListByCondition" resultType="com.fh.ai.business.entity.vo.bookListPoster.BookListPosterVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<if test="orderBy != null and orderBy != ''">order by ${orderBy}</if>
	</select>

	<select id="getBookListPosterByCondition" resultType="com.fh.ai.business.entity.vo.bookListPoster.BookListPosterVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

</mapper>
