<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.CalendarEventMapper">

	<select id="getCalendarEventListByCondition" resultType="com.fh.ai.business.entity.vo.calendarEvent.CalendarEventVo">
		select t.* from (
			select a.*
                ,replace(day, concat(year, '-'), '') monthDay, concat(year,date_format(day,'%m'))yearMonth
            from p_calendar_event a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="day != null and day != ''">and day = #{day}</if>
            <if test="startTime != null ">
                and day &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and day &lt;= #{endTime}
            </if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="recommendType != null">and recommend_type = #{recommendType}</if>
            <if test="introduce != null and introduce != ''">and introduce = #{introduce}</if>
            <if test="types != null and types.size() != 0">
                and type in
                <foreach collection="types" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="historyQueryDays != null and historyQueryDays.size() != 0">
                and replace(day, concat(year, '-'), '') in
                    <foreach collection="historyQueryDays" index="index" item="item" open="(" close=")" separator=",">
                        date_format(#{item}, '%m-%d')
                    </foreach>
            </if>
		    <if test="yearMonth !=null and yearMonth != ''">
                and yearMonth = #{yearMonth}
            </if>
        </where>
        <if test="yearMonth !=null and yearMonth != ''">
            order by day asc
        </if>
	</select>
    <select id="selectCalendarEventList"
            resultType="com.fh.ai.business.entity.vo.calendarEvent.CalendarEventVo">
        select * from p_calendar_event where year = 2024 and type in (1,2,3,4) and is_delete = 1
    </select>



</mapper>