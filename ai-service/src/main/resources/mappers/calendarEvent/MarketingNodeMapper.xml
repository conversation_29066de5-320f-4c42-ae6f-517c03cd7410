<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MarketingNodeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.calendarEvent.MarketingNodeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="marketingKeyPoints" column="marketing_key_points"/>
        <result property="marketingKeyWords" column="marketing_key_words"/>
        <result property="activityKeyPoints" column="activity_key_points"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="day != null and day != '' ">and day like concat('%', #{day}, '%')</if>
			<if test="year != null ">and year = #{year}</if>
			<if test="month != null ">and month = #{month}</if>
			<if test="marketingKeyPoints != null and marketingKeyPoints != '' ">and marketing_key_points like concat('%', #{marketingKeyPoints}, '%')</if>
			<if test="marketingKeyWords != null and marketingKeyWords != '' ">and marketing_key_words like concat('%', #{marketingKeyWords}, '%')</if>
			<if test="activityKeyPoints != null and activityKeyPoints != '' ">and activity_key_points like concat('%', #{activityKeyPoints}, '%')</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.day
	 		,t.year
	 		,t.month
	 		,t.marketing_key_points
	 		,t.marketing_key_words
	 		,t.activity_key_points
	 		,t.remark
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_marketing_node a
		 ) t

	</sql>

	<select id="getMarketingNodeListByCondition" resultType="com.fh.ai.business.entity.vo.calendarEvent.MarketingNodeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by id desc
	</select>

	<select id="getMarketingNodeByCondition" resultType="com.fh.ai.business.entity.vo.calendarEvent.MarketingNodeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>