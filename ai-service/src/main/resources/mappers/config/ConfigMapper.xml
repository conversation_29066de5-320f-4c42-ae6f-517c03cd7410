<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.config.ConfigDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="configKey" column="config_key"/>
        <result property="configValue" column="config_value"/>
        <result property="configDescription" column="config_description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="configKey != null and configKey != '' ">and config_key like concat('%', #{configKey}, '%')</if>
			<if test="configValue != null and configValue != '' ">and config_value like concat('%', #{configValue}, '%')</if>
			<if test="configDescription != null and configDescription != '' ">and config_description like concat('%', #{configDescription}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.config_key
	 		,t.config_value
	 		,t.config_description
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from p_config a
		 ) t

	</sql>

	<select id="getConfigListByCondition" resultType="com.fh.ai.business.entity.vo.config.ConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getConfigByCondition" resultType="com.fh.ai.business.entity.vo.config.ConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>