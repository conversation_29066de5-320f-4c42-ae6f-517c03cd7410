<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ConversationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.conversation.ConversationDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="conversationCode" column="conversation_code"/>
        <result property="userOid" column="user_oid"/>
        <result property="type" column="type"/>
        <result property="message" column="message"/>
        <result property="context" column="context"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="cozeConversationId" column="coze_conversation_id"/>
        <result property="cozeBotId" column="coze_bot_id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessJson" column="business_json"/>
        <result property="bookId" column="book_id"/>
        <result property="businessJsonBak" column="business_json_bak"/>
        <result property="parameterJson" column="parameter_json"/>
        <result property="customParameterJson" column="custom_parameter_json"/>
        <result property="cozeWorkflowId" column="coze_workflow_id"/>
    </resultMap>

	<select id="getConversationListByCondition" resultType="com.fh.ai.business.entity.vo.conversation.ConversationVo">
		select t.* from (
			select a.* from p_conversation a
            order by a.create_time desc
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="exceptTypeList != null and exceptTypeList.size() > 0">
                and type not in
                <foreach collection="exceptTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="typeList != null and typeList.size() > 0">
                and type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%') </if>
            <if test="message != null and message != ''">and message = #{message}</if>
            <if test="context != null and context != ''">and context = #{context}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="cozeConversationId != null and cozeConversationId != ''">and coze_conversation_id = #{cozeConversationId}</if>
            <if test="cozeBotId != null and cozeBotId != ''">and coze_bot_id = #{cozeBotId}</if>
            <if test="businessId != null and businessId != ''">and business_id = #{businessId}</if>
            <if test="businessJson != null and businessJson != ''">and business_json = #{businessJson}</if>
            <if test="bookId != null and bookId != ''">and book_id = #{bookId}</if>
            <if test="cozeWorkflowId != null and cozeWorkflowId != ''">and coze_workflow_id = #{cozeWorkflowId}</if>
        </where>
	</select>

    <select id="getConversationStatistics" resultType="com.fh.ai.business.entity.vo.conversation.ConversationStatisticVo">
        select count(distinct pc.user_oid) as userCount,
            count(1) as useCount,
            ifnull(sum(usage_total), 0) as usageSum,
            ifnull(sum(usage_in), 0) as usageInSum,
            ifnull(sum(usage_out), 0) as usageOutSum
        from p_conversation pc
        join p_history_app pha on pc.conversation_code = pha.conversation_code
        where pc.type='601'
        <if test="queryStartTime != null != null">and DATE_FORMAT(pc.create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{queryStartTime}, '%Y-%m-%d')</if>
        <if test="queryEndTime != null != null">and DATE_FORMAT(pc.create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{queryEndTime}, '%Y-%m-%d')</if>
    </select>
</mapper>