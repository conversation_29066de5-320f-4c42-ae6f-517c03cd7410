<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ConversationFileMapper">

	<select id="getConversationFileListByCondition" resultType="com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo">
		select t.* from (
            SELECT
            pcf.*,
            pa.suffix,
            pa.size,
            pa.original_name,
            pa.view_path,
            pa.duration
            FROM
            p_conversation_file pcf
            LEFT JOIN p_attachment pa ON pcf.file_oid = pa.oid
		) t
	    <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>

            <if test="conversationCodes != null and conversationCodes.size() > 0">
                and conversation_code in
                <foreach collection="conversationCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="fileOid != null and fileOid != ''">and file_oid = #{fileOid}</if>
            <if test="qwenFileId != null and qwenFileId != ''">and qwen_file_id = #{qwenFileId}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="taskId != null and isDelete != ''">and task_id = #{taskId}</if>
            <if test="taskType != null ">and task_type = #{taskType}</if>
            <if test="taskState != null ">and task_state = #{taskState}</if>
            <if test="historyId != null ">and history_id = #{historyId}</if>
            <if test="businessId != null and businessId!=''">and business_id =#{businessId}</if>
            <if test="xfOrderIdNotNull">and xf_order_id is not null</if>
            <if test="excludeTaskTypes != null and excludeTaskTypes.size() >0">
		        and task_type not in
                <foreach collection="excludeTaskTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="taskIds != null and taskIds.size() > 0">
                and task_id in
                <foreach collection="taskIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
		    <if test="fileContentCacheId != null ">
                and file_content_cache_id = #{fileContentCacheId}
            </if>
            <if test="ffmpegState != null">and ffmpeg_state = #{ffmpegState}</if>
        </where>
	</select>
</mapper>