<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.FileContentCacheMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.conversationFile.FileContentCacheDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="fileOid" column="file_oid"/>
        <result property="taskId" column="task_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="taskResult" column="task_result"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="fileOid != null and fileOid != '' ">and file_oid like concat('%', #{fileOid}, '%')</if>
			<if test="taskId != null and taskId != '' ">and task_id like concat('%', #{taskId}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="taskResult != null and taskResult != '' ">and task_result like concat('%', #{taskResult}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.file_oid
	 		,t.task_id
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
	 		,t.task_result
		from (
			 select a.* from p_file_content_cache a
		 ) t

	</sql>

	<select id="getFileContentCacheListByCondition" resultType="com.fh.ai.business.entity.vo.conversationFile.FileContentCacheVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<if test="ids != null and ids.size() > 0">
			and id in
			<foreach collection="ids" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getFileContentCacheByCondition" resultType="com.fh.ai.business.entity.vo.conversationFile.FileContentCacheVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>