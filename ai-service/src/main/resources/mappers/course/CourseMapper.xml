<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.CourseMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.course.CourseDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="imageUrl" column="image_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getCourseListByCondition" resultType="com.fh.ai.business.entity.vo.course.CourseVo">
		select t.* from (
		select a.*,b.count num,ifnull(c.count,0) userAnswerCount from p_course as a
		left join (SELECT  course_id,count(1) count FROM `p_course_detail` where is_delete = 1 and state = 2 group by course_id) b on a.id = b.course_id
		left join (SELECT  course_id,count(1) count FROM p_exam_answer pea left join `p_course_detail` pcd on pea.course_detail_id = pcd.id and pea.is_delete = 1 and pcd.is_delete = 1 group by course_id) c on a.id = c.course_id
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="name != null and name != ''">and name LIKE concat('%', #{name}, '%')</if>
				<if test="type != null and type != ''">and type = #{type}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="myList" resultType="com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo">
		SELECT t.* from
			(SELECT max(pea.id) id,pea.user_oid,
pea.organization_id,
pea.course_detail_id,
pea.exam_paper_id,
pea.is_delete,
max(pea.create_time) create_time,
max(pea.update_time) update_time,
pep.name paperName,pc.name courseName  FROM  p_exam_answer pea left join p_exam_paper pep on pea.exam_paper_id = pep.id and pep.is_delete = 1
		 left join p_course_detail pcd on pea.course_detail_id = pcd.id
		 left join p_course pc  on pcd.course_id = pc.id
        where pea.is_delete = 1 and pea.user_oid = #{userOid}
        GROUP BY user_oid ,course_detail_id
        )t

	</select>

	<select id="userAnswerCount" resultType="integer">
		SELECT ifnull(count(1),0) count FROM p_exam_answer pea left join `p_course_detail` pcd on pea.course_detail_id = pcd.id and pea.is_delete = 1 and pcd.is_delete = 1
		 where pcd.course_id = #{courseId}
	</select>
</mapper>