<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.CourseDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.courseDetail.CourseDetailDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="name" column="name"/>
        <result property="url" column="url"/>
        <result property="duration" column="duration"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getCourseDetailListByCondition" resultType="com.fh.ai.business.entity.vo.courseDetail.CourseDetailVo">
		select t.* from (
			select a.*,b.name 'paperName' from p_course_detail as a
			left join p_exam_paper b on a.exam_paper_id = b.id and b.is_delete = 1
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="courseId != null and courseId != ''">and course_id = #{courseId}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="url != null and url != ''">and url = #{url}</if>
				<if test="duration != null and duration != ''">and duration = #{duration}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="sort != null and sort != ''">and sort = #{sort}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
		<if test="orderBy != null and orderBy != ''">order by ${orderBy}</if>
	</select>
	<select id="getType" resultType="map">
      SELECT id 'type', name 'typeName'FROM `fh-ai`.`p_course_type`
	</select>

	<select id="otherCourse" resultType="com.fh.ai.business.entity.vo.course.CourseVo">
			select a.*  from p_course a
			where a.is_delete = 1 and a.id != #{id}  and a.state = 2  and type = #{type} ORDER BY RAND() limit 8
	</select>
	<sql id = "whereSql">
		<if test="organizationId != null and organizationId != ''">
			LEFT JOIN p_organization po ON x.organization_id = po.id
		</if>
		<where>
		1=1
			<if test="organizationId != null and organizationId != ''">
				and (po.superior_ids LIKE CONCAT( '%,', #{organizationId}, ',%' ) OR x.organization_id = #{organizationId})
			</if>
			<if test="startTime != null">and x.create_time >= #{startTime}</if>
			<if test="endTime != null">and x.create_time &lt;= #{endTime}</if>
		</where>
	</sql>

	<select id="statistics" resultType="map">
     SELECT pcd.id,pcd.name,if(pcd.state=1,'1','2') state,pep.name paperName,ifnull(a.count,0) videoCount,ifnull(b.count,0) videoUserCount ,ifnull(c.count,0) ansCount,ifnull(d.count,0) ansUserCount,ifnull(e.count,0) allCount,ifnull(f.count,0) rightCount FROM p_course_detail pcd LEFT JOIN p_exam_paper pep on pcd.exam_paper_id = pep.id
  left join (SELECT course_detail_id, count(1) count
  FROM p_video_play x <include refid="whereSql"/> GROUP BY x.course_detail_id) a on a.course_detail_id = pcd.id
	left join (SELECT course_detail_id, count(DISTINCT user_oid) count
  FROM p_video_play x <include refid="whereSql"/> GROUP BY x.course_detail_id) b on b.course_detail_id = pcd.id
  left join (SELECT course_detail_id, count(1) count
  FROM p_exam_answer x <include refid="whereSql"/> GROUP BY x.course_detail_id) c on c.course_detail_id = pcd.id
	left join (SELECT course_detail_id, count(DISTINCT user_oid) count
  FROM p_exam_answer x <include refid="whereSql"/> GROUP BY x.course_detail_id) d on d.course_detail_id = pcd.id
	left join (SELECT course_detail_id, count(1) count
  FROM p_exam_answer_detail x <include refid="whereSql"/> and is_right is not null
      GROUP BY x.course_detail_id) e on e.course_detail_id = pcd.id
    left join (SELECT course_detail_id, count(1) count
  FROM p_exam_answer_detail x <include refid="whereSql"/> and is_right=1
    GROUP BY  x.course_detail_id) f on f.course_detail_id = pcd.id
	where pcd.course_id = #{id} and pcd.is_delete = 1
		<if test="state != null and state != ''">and pcd.state = #{state}</if>
		order by pcd.sort asc
	</select>

	<select id="videoUserCount" resultType="integer">
		SELECT  count(DISTINCT user_oid) count
		FROM p_video_play x
		left join p_course_detail pcd on x.course_detail_id = pcd.id
		<include refid="whereSql"/>
		and x.course_id = #{id} and x.is_delete = 1 and pcd.is_delete = 1
	</select>
	<select id="ansUserCount" resultType="integer">
		SELECT count(DISTINCT user_oid) count
		FROM p_exam_answer x
		left join p_course_detail pcd on x.course_detail_id = pcd.id
		<include refid="whereSql"/>
		and pcd.course_id = #{id} and x.is_delete = 1 and pcd.is_delete = 1
	</select>
</mapper>