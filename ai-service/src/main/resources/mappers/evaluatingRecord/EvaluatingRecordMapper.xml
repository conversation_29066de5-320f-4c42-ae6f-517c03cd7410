<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.EvaluatingRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.evaluatingRecord.EvaluatingRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="taskUid" column="task_uid"/>
        <result property="audioToTextTaskUid" column="audio_to_text_task_uid"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="contents" column="contents"/>
        <result property="type" column="type"/>
		<result property="evaluatingResult" column="evaluating_result"/>
		<result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="thirdUniqueId" column="third_unique_id"/>
        <result property="returnResult" column="return_result"/>
    </resultMap>

    <select id="getEvaluatingRecordList" resultType="com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo">
        select *
        from p_evaluating_record
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="taskUid != null and taskUid != ''">and task_uid = #{taskUid}</if>
            <if test="audioToTextTaskUid != null and audioToTextTaskUid != ''">and audio_to_text_task_uid = #{audioToTextTaskUid}</if>
            <if test="contents != null and contents != ''">and contents like concat('%', #{contents}, '%')</if>
            <if test="type != null">and type = #{type}</if>
            <if test="evaluatingResult != null and evaluatingResult != ''">and evaluating_result like concat('%', #{evaluatingResult}, '%')</if>
            <if test="state != null">and state = #{state}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
            <if test="states != null and states.size() != 0">
                and state in
                <foreach collection="states" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="thirdUniqueId != null and thirdUniqueId != ''">and third_unique_id = #{thirdUniqueId}</if>
            <if test="returnResult != null and returnResult != ''">and return_result = #{returnResult}</if>
        </where>
    </select>

    <select id="getEvaluatingRecordByTaskUid"
            resultType="com.fh.ai.business.entity.vo.evaluatingRecord.EvaluatingRecordVo">
        select *
        from p_evaluating_record
        <where>
            is_delete = 1
            and task_uid = #{taskUid}
        </where>
        order by id desc
        limit 1
    </select>
</mapper>