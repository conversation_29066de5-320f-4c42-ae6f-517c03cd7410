<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ExamAnswerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="examPaperId" column="exam_paper_id"/>
        <result property="questionId" column="question_id"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="isRight" column="is_right"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getExamAnswerListByCondition" resultType="com.fh.ai.business.entity.vo.examAnswer.ExamAnswerVo">
		select t.* from (
			select a.* from p_exam_answer as a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
				<if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
				<if test="examPaperId != null and examPaperId != ''">and exam_paper_id = #{examPaperId}</if>
				<if test="questionId != null and questionId != ''">and question_id = #{questionId}</if>
				<if test="content != null and content != ''">and content = #{content}</if>
				<if test="type != null and type != ''">and type = #{type}</if>
				<if test="isRight != null and isRight != ''">and is_right = #{isRight}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<insert id="batchInsert" parameterType="java.util.List">
		INSERT INTO p_exam_answer_detail (
		user_oid,organization_id,exam_answer_id,course_detail_id,exam_paper_id,
		question_id,content,`type`,is_right,create_time
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.userOid}, #{item.organizationId}, #{item.examAnswerId},#{item.courseDetailId}, #{item.examPaperId},
			#{item.questionId},#{item.content},#{item.type},#{item.isRight},now()
			)
		</foreach>
	</insert>

	<select id="getExamAnswerDetail" resultType="com.fh.ai.business.entity.dto.examAnswer.ExamAnswerDetailDto">
		select a.* from p_exam_answer_detail as a where a.is_delete = 1 and exam_answer_id = #{examAnswerId}
	</select>

</mapper>