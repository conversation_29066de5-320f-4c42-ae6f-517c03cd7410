<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ExamPaperMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.examPaper.ExamPaperDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="questionIds" column="question_ids"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getExamPaperListByCondition" resultType="com.fh.ai.business.entity.vo.examPaper.ExamPaperVo">
		select t.* from (
			select a.*,count(pea.id) userAnswerCount, ifnull(b.count,0) bindCount from p_exam_paper as a
		left join p_exam_answer pea on pea.exam_paper_id = a.id and pea.is_delete = 1
		left join (select pcd.exam_paper_id,count(1) count from p_course_detail pcd where pcd.is_delete = 1 GROUP BY pcd.exam_paper_id) b on b.exam_paper_id = a.id
		GROUP BY a.id
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
				<if test="type != null and type != ''">and type = #{type}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="questionIds != null and questionIds != ''">and find_in_set(#{questionIds},question_ids)</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="statistics" resultType="map">
		SELECT pea.exam_paper_id, ifnull(c.count,0) ansCount,ifnull(d.count,0) ansUserCount,ifnull(e.count,0) allCount,ifnull(f.count,0) rightCount FROM p_exam_answer pea
		left join (SELECT x.exam_paper_id, count(1) count
		FROM p_exam_answer x where x.exam_paper_id = #{id} and x.is_delete = 1 GROUP BY x.exam_paper_id) c on c.exam_paper_id = pea.exam_paper_id
		left join (SELECT x.exam_paper_id, count(DISTINCT user_oid) count
		FROM p_exam_answer x where x.exam_paper_id = #{id} and x.is_delete = 1 GROUP BY x.exam_paper_id) d on d.exam_paper_id = pea.exam_paper_id
		left join (SELECT x.exam_paper_id, count(1) count
		FROM p_exam_answer_detail x  where is_right is not null and x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY x.exam_paper_id) e on e.exam_paper_id = pea.exam_paper_id
		left join (SELECT x.exam_paper_id, count(1) count
		FROM p_exam_answer_detail x  where is_right=1 and x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY x.exam_paper_id) f on f.exam_paper_id = pea.exam_paper_id
		where pea.exam_paper_id = #{id} and pea.is_delete = 1
		GROUP By pea.exam_paper_id
	</select>


	<sql id="org">
		left join p_organization poo on x.organization_id = poo.id
		left join p_organization poo1 on (poo.superior_ids LIKE CONCAT( '%,', poo1.id, ',%' )) and poo1.is_statistics = 1
		</sql>
	<select id="orgStatistics" resultType="map">
	   SELECT t.* from (
		SELECT if(po1.id != 0, po1.id, po.id) 'parent_id',po.id 'organization_id',if(po1.id != 0, po1.name, po.name) 'parentName',po.name,pea.exam_paper_id, ifnull(c.count,0) ansCount,ifnull(d.count,0) ansUserCount,ifnull(e.count,0) allCount,ifnull(f.count,0) rightCount FROM p_exam_answer pea
		left join p_organization po on pea.organization_id = po.id
		left join p_organization po1 on (po.superior_ids LIKE CONCAT( '%,', po1.id, ',%' )) and po1.is_statistics = 1
		left join (select aa.*,sum(ifnull(aa.tmpcount,0)) count from (SELECT if(poo1.id != 0, poo1.id, poo.id) 'organization_id',x.exam_paper_id, count(1) tmpcount
		FROM p_exam_answer x <include refid ="org" /> where x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY organization_id,x.exam_paper_id) aa GROUP BY organization_id)c on c.exam_paper_id = pea.exam_paper_id and c.organization_id = if(po1.id != 0, po1.id, po.id)
		left join (select bb.*,sum(ifnull(bb.tmpcount,0)) count from (SELECT if(poo1.id != 0, poo1.id, poo.id) 'organization_id',x.exam_paper_id, count(DISTINCT user_oid) tmpcount
		FROM p_exam_answer x <include refid ="org" /> where x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY organization_id,x.exam_paper_id) bb GROUP BY organization_id) d on d.exam_paper_id = pea.exam_paper_id and d.organization_id  = if(po1.id != 0, po1.id, po.id)
		left join (select cc.*,sum(ifnull(cc.tmpcount,0)) count from (SELECT if(poo1.id != 0, poo1.id, poo.id) 'organization_id',x.exam_paper_id, count(1) tmpcount
		FROM p_exam_answer_detail x <include refid ="org" /> where is_right is not null and x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY organization_id,x.exam_paper_id) cc GROUP BY organization_id) e on e.exam_paper_id = pea.exam_paper_id and e.organization_id = if(po1.id != 0, po1.id, po.id)
		left join (select dd.*,sum(ifnull(dd.tmpcount,0)) count from (SELECT if(poo1.id != 0, poo1.id, poo.id) 'organization_id',x.exam_paper_id, count(1) tmpcount
		FROM p_exam_answer_detail x <include refid ="org" /> where is_right=1 and x.exam_paper_id = #{id} and x.is_delete = 1
		GROUP BY organization_id,x.exam_paper_id) dd GROUP BY organization_id) f on f.exam_paper_id = pea.exam_paper_id and f.organization_id = if(po1.id != 0, po1.id, po.id)
		where pea.exam_paper_id = #{id} and pea.is_delete = 1
		GROUP BY parent_id,pea.exam_paper_id
		) t
	</select>

	<select id="orgUserStatistics" resultType="map">
  SELECT count(t.parent_id) count ,t.parent_id 'parent_id',t.parentName
	from
	(SELECT if(po1.id != 0, po1.id, po.id) parent_id,po.id organization_id,if(po1.id != 0, po1.name, po.name) parentName,po.name FROM p_user pu LEFT JOIN
	    p_organization po  on pu.organization_id = po.id
		left join p_organization po1 on (po.superior_ids LIKE CONCAT( '%,', po1.id, ',%' )) and po1.is_statistics = 1
		where po1.is_statistics = 1
		) t
		GROUP BY t.parent_id
	</select>

	<sql id = "whereSql">
		<if test="organizationId != null and organizationId != ''">
			LEFT JOIN p_organization po ON x.organization_id = po.id
		</if>
		<where>
			1=1
			<if test="organizationId != null and organizationId != ''">
				and (po.superior_ids LIKE CONCAT( '%,', #{organizationId}, ',%' ) OR x.organization_id = #{organizationId})
			</if>
		</where>
	</sql>

	<sql id = "maxAns">
	(SELECT * from p_exam_answer_detail where id in(SELECT max(id)
		FROM p_exam_answer_detail where exam_paper_id = #{id} and is_delete = 1
		GROUP BY user_oid, question_id ))
	</sql>
	<select id="orgNumStatistics" resultType="map">
		select (@i:=@i+1) as num, a.* from (SELECT t.* from (
		SELECT e.question_id,ifnull(e.count,0) allCount,ifnull(f.count,0) rightCount
		,(ifnull(e.count,0)-ifnull(f.count,0)) wrongCount FROM
		(SELECT x.question_id,x.exam_paper_id, count(1) count
		FROM <include refid="maxAns"/> x <include refid="whereSql"/>
		GROUP BY x.question_id,x.exam_paper_id) e
		left join (SELECT x.question_id,x.exam_paper_id, count(1) count
		FROM <include refid="maxAns"/> x <include refid="whereSql"/>
		and (is_right=1 or is_right is null )
		GROUP BY x.question_id,x.exam_paper_id) f on f.question_id = e.question_id
		) t order by find_in_set(question_id,#{questionIds})
		) a,(select @i:=0) b
	</select>

</mapper>