<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ExcellentWorksMapper">
    <resultMap id="BaseResultMap" type="com.fh.ai.business.entity.dto.excellentWorks.ExcellentWorksDto">
        <result property="id" column="id"/>
        <result property="worksActiveId" column="works_active_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="statisticsOrganizationId" column="statistics_organization_id"/>
        <result property="worksName" column="works_name"/>
        <result property="userName" column="user_name"/>
        <result property="organizationName" column="organization_name"/>
        <result property="sort" column="sort"/>
        <result property="auditType" column="audit_type"/>
        <result property="holdType" column="hold_type"/>
        <result property="appType" column="app_type"/>
        <result property="worksType" column="works_type"/>
        <result property="worksProfile" column="works_profile"/>
        <result property="worksDescribe" column="works_describe"/>
        <result property="sourceType" column="source_type"/>
        <result property="businessId" column="business_id"/>
        <result property="baseModel" column="base_model"/>
        <result property="imageGenModel" column="image_gen_model"/>
        <result property="styleModel" column="style_model"/>
        <result property="prompts" column="prompts"/>
        <result property="negativePrompt" column="negative_prompt"/>
        <result property="params" column="params"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <!-- ResultMap for p_excellent_works -->
    <resultMap id="ExcellentWorksResultMap" type="com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo">
        <result property="id" column="id"/>
        <result property="worksActiveId" column="works_active_id"/>
        <result property="activeName" column="active_name"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="statisticsOrganizationId" column="statistics_organization_id"/>
        <result property="worksName" column="works_name"/>
        <result property="userName" column="user_name"/>
        <result property="organizationName" column="organization_name"/>
        <result property="sort" column="sort"/>
        <result property="auditType" column="audit_type"/>
        <result property="holdType" column="hold_type"/>
        <result property="appType" column="app_type"/>
        <result property="worksType" column="works_type"/>
        <result property="worksProfile" column="works_profile"/>
        <result property="worksDescribe" column="works_describe"/>
        <result property="sourceType" column="source_type"/>
        <result property="baseModel" column="base_model"/>
        <result property="imageGenModel" column="image_gen_model"/>
        <result property="styleModel" column="style_model"/>
        <result property="prompts" column="prompts"/>
        <result property="params" column="params"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="voteNumber" column="vote_number"/>
        <result property="excellentWorksNumber" column="excellent_works_number"/>

        <!-- Collection for p_excellent_works_detail -->
        <collection property="excellentWorksDetailList" ofType="com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto">
            <result property="id" column="detail_id"/>
            <result property="excellentWorksId" column="excellent_works_id"/>
            <result property="userOid" column="user_oid"/>
            <result property="organizationId" column="organization_id"/>
            <result property="worksMediaType" column="works_media_type"/>
            <result property="sort" column="sort"/>
            <result property="fileOid" column="file_oid"/>
            <result property="fileName" column="file_name"/>
            <result property="fileUrl" column="file_url"/>
            <result property="compressFileUrl" column="compress_file_url"/>
            <result property="compressFileOid" column="compress_file_oid"/>
            <result property="compressFileName" column="compress_file_name"/>
            <result property="converOid" column="conver_oid"/>
            <result property="converUrl" column="conver_url"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="isDelete" column="is_delete"/>
        </collection>

    </resultMap>

    <sql id="commonSelect">
        select
        ew.id
        ,ew.works_active_id
        ,ew.user_oid
        ,ew.organization_id
        ,ew.statistics_organization_id
        ,ew.works_name
        ,ew.user_name
        ,ew.organization_name
        ,ew.sort
        ,ew.audit_type
        ,ew.hold_type
        ,ew.app_type
        ,ew.works_type
        ,ew.works_profile
        ,ew.works_describe
        ,ew.source_type
        ,ew.business_id
        ,ew.base_model
        ,ew.image_gen_model
        ,ew.style_model
        ,ew.prompts
        ,ew.negative_prompt
        ,ew.params
        ,ew.create_by
        ,ew.create_time
        ,ew.update_by
        ,ew.update_time
        ,ew.is_delete
        ,ew.excellent_works_number
        <if test="queryVoteAndRank">
        ,ifnull(vr.vote_number, 0) as vote_number
        </if>
        from p_excellent_works ew
        <if test="queryVoteAndRank">
            left join (
                select works_active_id,
                    excellent_works_id,
                    ifnull(sum(vote_number), 0) as vote_number
                from p_works_active_vote_record
                where is_delete = 1
                <if test="startDay != null">and date_format(vote_time, '%Y-%m-%d') >= date_format(#{startDay}, '%Y-%m-%d')</if>
                <if test="endDay != null">and date_format(vote_time, '%Y-%m-%d') &lt;= date_format(#{endDay}, '%Y-%m-%d')</if>
                group by works_active_id,excellent_works_id
            ) vr on vr.works_active_id = ew.works_active_id and vr.excellent_works_id = ew.id
        </if>
        <where>
            <if test="id != null">and ew.id = #{id}</if>
            <if test="userOid != null and userOid != ''">and ew.user_oid = #{userOid}</if>
            <if test="organizationId != null">and ew.statistics_organization_id = #{organizationId}</if>
            <if test="sort != null">and ew.sort = #{sort}</if>
            <if test="auditType != null">and ew.audit_type = #{auditType}</if>
            <if test="appType != null">and ew.app_type = #{appType}</if>
            <if test="worksType != null">and ew.works_type = #{worksType}</if>
            <if test="params != null and params != ''">and ew.params = #{params}</if>
            <if test="isDelete != null">and ew.is_delete = #{isDelete}</if>
            <if test="worksActiveId != null">and ew.works_active_id = #{worksActiveId}</if>
            <if test="holdType != null">and ew.hold_type = #{holdType}</if>
            <if test="keywords != null and keywords != ''">AND (ew.works_name LIKE concat('%', #{keywords}, '%') OR ew.user_name LIKE concat('%', #{keywords}, '%'))</if>
            <if test="searchKey != null and searchKey != ''">and (ew.user_name like concat('%', #{searchKey},'%') OR ew.excellent_works_number LIKE concat('%', #{searchKey},'%'))</if>
        </where>
    </sql>


    <select id="getExcellentWorksListByCondition"
            resultType="com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksVo">
        <include refid="commonSelect">
        </include>
        <if test="listType == 1">
            order by create_time desc
        </if>
        <if test="listType == 2">
            order by
            CASE
            WHEN sort IS NULL THEN 1 -- 值为 0 的排在最后
            ELSE 0 -- 其他值正常排序
            END,
            sort asc, update_time desc
        </if>
        <if test="listType == 3">
            order by excellent_works_number asc, update_time desc
        </if>
        <if test="queryVoteAndRank and listType == 4">
            order by vote_number desc, excellent_works_number asc, update_time desc
        </if>
    </select>

    <select id="getExcellentWorksWithDetailsById" resultMap="ExcellentWorksResultMap">
        SELECT
        ew.id,
        ew.works_active_id,
        ew.user_oid,
        ew.organization_id,
        ew.statistics_organization_id,
        ew.works_name,
        ew.user_name,
        ew.organization_name,
        ew.sort,
        ew.audit_type,
        ew.hold_type,
        ew.app_type,
        ew.works_type,
        ew.works_profile,
        ew.works_describe,
        ew.source_type,
        ew.business_id,
        ew.base_model,
        ew.image_gen_model ,
        ew.style_model,
        ew.prompts,
        ew.negative_prompt,
        ew.params,
        ew.create_by,
        ew.create_time,
        ew.update_by,
        ew.update_time,
        ew.is_delete,
        ew.excellent_works_number,
        ewd.id AS detail_id,
        ewd.excellent_works_id,
        ewd.user_oid AS detail_user_oid,
        ewd.organization_id AS detail_organization_id,
        ewd.works_media_type,
        ewd.sort AS detail_sort,
        ewd.file_oid,
        ewd.file_name,
        ewd.file_url,
        ewd.compress_file_oid,
        ewd.compress_file_name,
        ewd.compress_file_url,
        ewd.conver_oid,
        ewd.conver_url,
        ewd.create_by AS detail_create_by,
        ewd.create_time AS detail_create_time,
        ewd.update_by AS detail_update_by,
        ewd.update_time AS detail_update_time,
        ewd.is_delete AS detail_is_delete,
        ewa.active_name AS active_name
        FROM p_excellent_works ew
        LEFT JOIN p_works_active ewa ON ewa.id = ew.works_active_id and ewa.is_delete = 1
        LEFT JOIN p_excellent_works_detail ewd ON ew.id = ewd.excellent_works_id and ewd.is_delete = 1
        <where>
            ew.is_delete = 1
            <if test="id != null">and ew.id = #{id}</if>
        </where>
    </select>
    <select id="getPreviousBySort" resultMap="BaseResultMap" >
        SELECT * FROM p_excellent_works WHERE sort &lt; #{sort} ORDER BY sort DESC LIMIT 1
    </select>
    <select id="getNextBySort" resultMap="BaseResultMap">
        SELECT * FROM p_excellent_works WHERE sort &gt; #{sort} ORDER BY sort ASC LIMIT 1
    </select>
    <select id="getMax" resultMap="BaseResultMap">
        SELECT * FROM p_excellent_works  ORDER BY sort DESC LIMIT 1
    </select>


    <!-- 分组统计 -->
    <select id="getStatisticsByOrganization" parameterType="com.fh.ai.business.entity.bo.excellentWorks.ExcellentWorksUserStatisticsConditionBo"
            resultType="com.fh.ai.business.entity.vo.excellentWorks.ExcellentWorksUserStatisticsVo">
        SELECT
        pew.organization_name AS organization_name,
        pew.statistics_organization_id AS organization_id,
        COUNT( DISTINCT pew.id ) AS total_works_count,-- 入选作品个数
        COUNT( DISTINCT CASE WHEN pew.hold_type = 2 THEN pew.id END ) AS selected_works_count,-- 投稿作品人数
        COUNT( DISTINCT pewu.user_oid ) AS total_users_count,-- 入选作品人数
        COUNT( DISTINCT CASE WHEN pew.hold_type = 2 THEN pewu.user_oid END ) AS selected_users_count
        FROM
        p_excellent_works pew
        LEFT JOIN p_excellent_works_user pewu ON pew.id = pewu.excellent_works_id
        WHERE
        pew.is_delete = 1 and pewu.is_delete = 1
            AND pew.works_active_id=#{worksActiveId}
        <if test="startDate != null and endDate != null">
            AND pew.create_time BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY
        pew.statistics_organization_id
    </select>

    <select id="getExcellentWorksNumber" resultType="java.lang.Integer">
        select max(excellent_works_number)
        from p_excellent_works
        <where>
            is_delete = 1
            and works_active_id = #{worksActiveId}
        </where>
    </select>

</mapper>