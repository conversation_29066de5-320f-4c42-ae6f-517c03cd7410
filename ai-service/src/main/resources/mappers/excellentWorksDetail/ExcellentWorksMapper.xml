<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ExcellentWorksDetailMapper">
    <resultMap id="BaseResultMap" type="com.fh.ai.business.entity.dto.excellentWorksDetail.ExcellentWorksDetailDto">
        <result property="id" column="id"/>
        <result property="excellentWorksId" column="excellent_works_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="worksMediaType" column="works_media_type"/>
        <result property="sort" column="sort"/>
        <result property="fileOid" column="file_oid"/>
        <result property="fileName" column="file_name"/>
        <result property="compressFileUrl" column="compress_file_url"/>
        <result property="compressFileOid" column="compress_file_oid"/>
        <result property="compressFileName" column="compress_file_name"/>
        <result property="fileUrl" column="file_url"/>
        <result property="converOid" column="conver_oid"/>
        <result property="converUrl" column="conver_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="commonSelect">
        select
        id
        ,excellent_works_id
        ,user_oid
        ,organization_id
        ,works_media_type
        ,sort
        ,file_oid
        ,file_name
        ,file_url
        ,compress_file_oid
        ,compress_file_name
        ,compress_file_url
        ,conver_oid
        ,conver_url
        ,create_by
        ,create_time
        ,update_by
        ,update_time
        ,is_delete
        from p_excellent_works_detail
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="excellentWorksId != null">and excellent_works_id = #{excellentWorksId}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null">and organization_id = #{organizationId}</if>
            <if test="worksMediaType != null">and works_media_type = #{worksMediaType}</if>
            <if test="sort != null">and sort = #{sort}</if>
            <if test="fileOid != null and fileOid != ''">and file_oid = #{fileOid}</if>
            <if test="fileUrl != null and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <select id="getExcellentWorksDetailListByCondition"
            resultType="com.fh.ai.business.entity.vo.excellentWorksDetail.ExcellentWorksDetailVo">
        <include refid="commonSelect">
        </include>
    </select>
</mapper>