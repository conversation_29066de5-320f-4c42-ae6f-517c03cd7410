<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ExcellentWorksPrizeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
	<resultMap type="com.fh.ai.business.entity.dto.excellentWorksPrize.ExcellentWorksPrizeDto" id="BaseResultMap">
		<result property="id" column="id"/>
		<result property="excellentWorksId" column="excellent_works_id"/>
		<result property="userOid" column="user_oid"/>
		<result property="organizationId" column="organization_id"/>
		<result property="prizeReason" column="prize_reason"/>
		<result property="prizeScore" column="prize_score"/>
		<result property="prizeCardInfo" column="prize_card_info"/>
		<result property="prizeType" column="prize_type"/>
		<result property="createBy" column="create_by"/>
		<result property="createTime" column="create_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="isDelete" column="is_delete"/>
	</resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="excellentWorksId != null ">and excellent_works_id = #{excellentWorksId}</if>
			<if test="excellentWorksIdList != null and excellentWorksIdList.size() > 0 ">
			and excellent_works_id in
			<foreach collection="excellentWorksIdList" item="item" open="(" separator="," close=")">
			#{item}
			</foreach>
			</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="prizeReason != null and prizeReason != '' ">and prize_reason like concat('%', #{prizeReason}, '%')</if>
			<if test="prizeScore != null ">and prize_score = #{prizeScore}</if>
			<if test="prizeCardInfo != null and prizeCardInfo != '' ">and prize_card_info like concat('%', #{prizeCardInfo}, '%')</if>
			<if test="prizeType != null ">and prize_type = #{prizeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="excellentWorksId != null ">and excellent_works_id = #{excellentWorksId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="prizeReason != null and prizeReason != '' ">and prize_reason like concat('%', #{prizeReason}, '%')</if>
			<if test="prizeScore != null ">and prize_score = #{prizeScore}</if>
			<if test="prizeCardInfo != null and prizeCardInfo != '' ">and prize_card_info like concat('%', #{prizeCardInfo}, '%')</if>
			<if test="prizeType != null ">and prize_type = #{prizeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
		t.id
		,t.excellent_works_id
		,t.user_oid
		,t.organization_id
		,t.prize_reason
		,t.prize_score
		,t.prize_card_info
		,t.prize_type
		,t.create_by
		,t.create_time
		,t.update_by
		,t.update_time
		,t.is_delete
		,t.active_name
		from (
		select a.* ,w.active_name
		from p_excellent_works_prize a
		left join p_excellent_works_user pewu on a.id = pewu.excellent_works_prize_id
		left join p_excellent_works pew on pew.id = pewu.excellent_works_id
		left join p_works_active w on pew.works_active_id = w.id
		<if test="userOid != null and userOid != ''">
			where pewu.user_oid = #{userOid}
		</if>
		group by a.id
		) t
	</sql>

	<select id="getExcellentWorksPrizeListByCondition" resultType="com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by t.create_time desc
	</select>

	<select id="getExcellentWorksPrizeByCondition" resultType="com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getExcellentWorksPrizeCountVo" resultType="com.fh.ai.business.entity.vo.excellentWorksPrize.ExcellentWorksPrizeCountVo">
		SELECT excellent_works_id,COUNT(*) as awards_number,MAX(create_time) AS latest_award_time
		FROM p_excellent_works_prize
		WHERE
		is_delete = 1
		and excellent_works_id in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		GROUP BY excellent_works_id
	</select>
</mapper>