<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.FestivalMapper">

	<select id="getFestivalListByCondition" resultType="com.fh.ai.business.entity.vo.festival.FestivalVo">
		select t.* from (
			select a.* from p_festival a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="day != null and day != ''">and day = #{day}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>
</mapper>