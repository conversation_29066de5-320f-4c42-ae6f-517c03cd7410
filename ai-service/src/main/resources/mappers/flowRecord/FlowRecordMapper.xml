<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.FlowRecordMapper">
    <resultMap id="BaseResultMap" type="com.fh.ai.business.entity.dto.flowRecord.FlowRecordDto">
        <result property="id" column="id"/>
        <result property="flowCode" column="flow_code"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="agentId" column="agent_id"/>
        <result property="agentName" column="agent_name"/>
        <result property="appType" column="app_type"/>
        <result property="appBusinessId" column="app_business_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="topId" column="top_id"/>
        <result property="param" column="param"/>
        <result property="result" column="result"/>
        <result property="showType" column="show_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="commonSelect">
        select
            id
             ,flow_code
             ,user_oid
             ,organization_id
             ,agent_id
             ,agent_name
             ,app_type
             ,app_business_id
             ,parent_id
             ,top_id
             ,param
             ,result
             ,show_type
             ,create_by
             ,create_time
             ,update_by
             ,update_time
             ,is_delete
        from p_flow_record
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="flowCode != null and flowCode != ''">and flow_code = #{flowCode}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null">and organization_id = #{organizationId}</if>
            <if test="agentId != null">and agent_id = #{agentId}</if>
            <if test="agentName != null and agentName != ''">and agent_name like concat('%', #{agentName}, '%')</if>
            <if test="appType != null">and app_type = #{appType}</if>
            <if test="appBusinessId != null and appBusinessId != ''">and app_business_id = #{appBusinessId}</if>
            <if test="parentId != null">and parent_id = #{parentId}</if>
            <if test="topId != null">and top_id = #{topId}</if>
            <if test="param != null and param != ''">and param = #{param}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="showType != null">and show_type = #{showType}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <select id="getFlowRecordListByCondition" resultType="com.fh.ai.business.entity.vo.flowRecord.FlowRecordVo">
        <include refid="commonSelect">
        </include>
        <if test="orderBy == null or orderBy == ''">
            order by create_time desc
        </if>
    </select>

    <select id="getFlowRecordByCondition" resultType="com.fh.ai.business.entity.vo.flowRecord.FlowRecordVo">
        <include refid="commonSelect">
        </include>
        limit 1
    </select>
</mapper>