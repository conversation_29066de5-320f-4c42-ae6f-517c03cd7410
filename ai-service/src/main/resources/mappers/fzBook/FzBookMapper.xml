<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.FzBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.fzBook.FzBookDto" id="BaseResultMap">
        <result property="id" column="ID"/>
        <result property="sysTopic" column="SYS_TOPIC"/>
        <result property="isbn" column="ISBN"/>
        <result property="bookVersion" column="BOOK_VERSION"/>
        <result property="printVersion" column="PRINT_VERSION"/>
        <result property="sysAuthors" column="SYS_AUTHORS"/>
        <result property="sysDoclibid" column="SYS_DOCLIBID"/>
        <result property="sysDocumentid" column="SYS_DOCUMENTID"/>
        <result property="pubdate" column="PUBDATE"/>
        <result property="pressName" column="PRESS_NAME"/>
        <result property="bookCat" column="BOOK_CAT"/>
        <result property="sysCurrentstatus" column="SYS_CURRENTSTATUS"/>
        <result property="subtitle" column="SUBTITLE"/>
        <result property="edition" column="EDITION"/>
        <result property="draftsman" column="DRAFTSMAN"/>
        <result property="url" column="url"/>
        <result property="picurl" column="picUrl"/>
        <result property="sysLastmodified" column="SYS_LASTMODIFIED"/>
        <result property="editor" column="EDITOR"/>
        <result property="isAwad" column="is_awad"/>
        <result property="awadCat" column="awad_cat"/>
        <result property="oriPicUrl" column="ori_pic_url"/>
        <result property="fmPicUrl" column="fm_pic_url"/>
        <result property="imgWidth" column="img_width"/>
        <result property="imgHeight" column="img_height"/>
        <result property="qbPdfUrl" column="qb_pdf_url"/>
        <result property="totalPage" column="total_page"/>
        <result property="qbPdfName" column="qb_pdf_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and ID = #{id}</if>
			<if test="sysTopic != null and sysTopic != '' ">and SYS_TOPIC like concat('%', #{sysTopic}, '%')</if>
			<if test="isbn != null and isbn != '' ">and ISBN like concat('%', #{isbn}, '%')</if>
			<if test="bookVersion != null and bookVersion != '' ">and BOOK_VERSION like concat('%', #{bookVersion}, '%')</if>
			<if test="printVersion != null and printVersion != '' ">and PRINT_VERSION like concat('%', #{printVersion}, '%')</if>
			<if test="sysAuthors != null and sysAuthors != '' ">and SYS_AUTHORS like concat('%', #{sysAuthors}, '%')</if>
			<if test="sysDoclibid != null and sysDoclibid != '' ">and SYS_DOCLIBID like concat('%', #{sysDoclibid}, '%')</if>
			<if test="sysDocumentid != null and sysDocumentid != '' ">and SYS_DOCUMENTID like concat('%', #{sysDocumentid}, '%')</if>
			<if test="pubdate != null ">and PUBDATE = #{pubdate}</if>
			<if test="pressName != null and pressName != '' ">and PRESS_NAME like concat('%', #{pressName}, '%')</if>
			<if test="bookCat != null and bookCat != '' ">and BOOK_CAT like concat('%', #{bookCat}, '%')</if>
			<if test="sysCurrentstatus != null and sysCurrentstatus != '' ">and SYS_CURRENTSTATUS like concat('%', #{sysCurrentstatus}, '%')</if>
			<if test="subtitle != null and subtitle != '' ">and SUBTITLE like concat('%', #{subtitle}, '%')</if>
			<if test="edition != null and edition != '' ">and EDITION like concat('%', #{edition}, '%')</if>
			<if test="draftsman != null and draftsman != '' ">and DRAFTSMAN like concat('%', #{draftsman}, '%')</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="picurl != null and picurl != '' ">and picUrl like concat('%', #{picurl}, '%')</if>
			<if test="sysLastmodified != null ">and SYS_LASTMODIFIED = #{sysLastmodified}</if>
			<if test="editor != null and editor != '' ">and EDITOR like concat('%', #{editor}, '%')</if>
			<if test="isAwad != null ">and is_awad = #{isAwad}</if>
			<if test="awadCat != null and awadCat != '' ">and awad_cat like concat('%', #{awadCat}, '%')</if>
			<if test="oriPicUrl != null and oriPicUrl != '' ">and ori_pic_url like concat('%', #{oriPicUrl}, '%')</if>
			<if test="fmPicUrl != null and fmPicUrl != '' ">and fm_pic_url like concat('%', #{fmPicUrl}, '%')</if>
			<if test="imgWidth != null ">and img_width = #{imgWidth}</if>
			<if test="imgHeight != null ">and img_height = #{imgHeight}</if>
			<if test="qbPdfUrl != null and qbPdfUrl != '' ">and qb_pdf_url like concat('%', #{qbPdfUrl}, '%')</if>
			<if test="totalPage != null ">and total_page = #{totalPage}</if>
			<if test="qbPdfName != null and qbPdfName != '' ">and qb_pdf_name like concat('%', #{qbPdfName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and ID = #{id}</if>
			<if test="sysTopic != null and sysTopic != '' ">and SYS_TOPIC like concat('%', #{sysTopic}, '%')</if>
			<if test="isbn != null and isbn != '' ">and ISBN like concat('%', #{isbn}, '%')</if>
			<if test="bookVersion != null and bookVersion != '' ">and BOOK_VERSION like concat('%', #{bookVersion}, '%')</if>
			<if test="printVersion != null and printVersion != '' ">and PRINT_VERSION like concat('%', #{printVersion}, '%')</if>
			<if test="sysAuthors != null and sysAuthors != '' ">and SYS_AUTHORS like concat('%', #{sysAuthors}, '%')</if>
			<if test="sysDoclibid != null and sysDoclibid != '' ">and SYS_DOCLIBID like concat('%', #{sysDoclibid}, '%')</if>
			<if test="sysDocumentid != null and sysDocumentid != '' ">and SYS_DOCUMENTID like concat('%', #{sysDocumentid}, '%')</if>
			<if test="pubdate != null ">and PUBDATE = #{pubdate}</if>
			<if test="pressName != null and pressName != '' ">and PRESS_NAME like concat('%', #{pressName}, '%')</if>
			<if test="bookCat != null and bookCat != '' ">and BOOK_CAT like concat('%', #{bookCat}, '%')</if>
			<if test="sysCurrentstatus != null and sysCurrentstatus != '' ">and SYS_CURRENTSTATUS like concat('%', #{sysCurrentstatus}, '%')</if>
			<if test="subtitle != null and subtitle != '' ">and SUBTITLE like concat('%', #{subtitle}, '%')</if>
			<if test="edition != null and edition != '' ">and EDITION like concat('%', #{edition}, '%')</if>
			<if test="draftsman != null and draftsman != '' ">and DRAFTSMAN like concat('%', #{draftsman}, '%')</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="picurl != null and picurl != '' ">and picUrl like concat('%', #{picurl}, '%')</if>
			<if test="sysLastmodified != null ">and SYS_LASTMODIFIED = #{sysLastmodified}</if>
			<if test="editor != null and editor != '' ">and EDITOR like concat('%', #{editor}, '%')</if>
			<if test="isAwad != null ">and is_awad = #{isAwad}</if>
			<if test="awadCat != null and awadCat != '' ">and awad_cat like concat('%', #{awadCat}, '%')</if>
			<if test="oriPicUrl != null and oriPicUrl != '' ">and ori_pic_url like concat('%', #{oriPicUrl}, '%')</if>
			<if test="fmPicUrl != null and fmPicUrl != '' ">and fm_pic_url like concat('%', #{fmPicUrl}, '%')</if>
			<if test="imgWidth != null ">and img_width = #{imgWidth}</if>
			<if test="imgHeight != null ">and img_height = #{imgHeight}</if>
			<if test="qbPdfUrl != null and qbPdfUrl != '' ">and qb_pdf_url like concat('%', #{qbPdfUrl}, '%')</if>
			<if test="totalPage != null ">and total_page = #{totalPage}</if>
			<if test="qbPdfName != null and qbPdfName != '' ">and qb_pdf_name like concat('%', #{qbPdfName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.ID
	 		,t.SYS_TOPIC
	 		,t.ISBN
	 		,t.BOOK_VERSION
	 		,t.PRINT_VERSION
	 		,t.SYS_AUTHORS
	 		,t.SYS_DOCLIBID
	 		,t.SYS_DOCUMENTID
	 		,t.PUBDATE
	 		,t.PRESS_NAME
	 		,t.BOOK_CAT
	 		,t.SYS_CURRENTSTATUS
	 		,t.SUBTITLE
	 		,t.EDITION
	 		,t.DRAFTSMAN
	 		,t.url
	 		,t.picUrl
	 		,t.SYS_LASTMODIFIED
	 		,t.EDITOR
	 		,t.is_awad
	 		,t.awad_cat
	 		,t.ori_pic_url
	 		,t.fm_pic_url
	 		,t.img_width
	 		,t.img_height
	 		,t.qb_pdf_url
	 		,t.total_page
	 		,t.qb_pdf_name
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			select a.* from p_fz_book a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getFzBookListByCondition" resultType="com.fh.ai.business.entity.vo.fzBook.FzBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getFzBookByCondition" resultType="com.fh.ai.business.entity.vo.fzBook.FzBookVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>