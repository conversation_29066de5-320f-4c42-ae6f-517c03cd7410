<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.GuijiHistoryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.guiji.GuijiHistoryDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="taskId" column="task_id"/>
        <result property="state" column="state"/>
        <result property="parameterJson" column="parameter_json"/>
        <result property="result" column="result"/>
        <result property="responseData" column="response_data"/>
        <result property="channel" column="channel"/>
        <result property="appType" column="app_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="taskId != null and taskId != '' ">and task_id like concat('%', #{taskId}, '%')</if>
			<if test="state != null ">and state = #{state}</if>
			<if test="parameterJson != null and parameterJson != '' ">and parameter_json like concat('%', #{parameterJson}, '%')</if>
			<if test="result != null and result != '' ">and result like concat('%', #{result}, '%')</if>
			<if test="responseData != null and responseData != '' ">and response_data like concat('%', #{responseData}, '%')</if>
			<if test="channel != null ">and channel = #{channel}</if>
			<if test="appType != null and appType != '' ">and app_type like concat('%', #{appType}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.task_id
	 		,t.state
	 		,t.parameter_json
	 		,t.result
	 		,t.response_data
	 		,t.channel
	 		,t.app_type
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_guiji_history a
		 ) t

	</sql>

	<select id="getGuijiHistoryListByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiHistoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getGuijiHistoryByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiHistoryVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>