<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.GuijiTimbreMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.guiji.GuijiTimbreDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="timbreId" column="timbre_id"/>
        <result property="guijiTaskId" column="guiji_task_id"/>
        <result property="timbreUrl" column="timbre_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="timbreId != null and timbreId != '' ">and timbre_id like concat('%', #{timbreId}, '%')</if>
			<if test="guijiTaskId != null and guijiTaskId != '' ">and guiji_task_id like concat('%', #{guijiTaskId}, '%')</if>
			<if test="timbreUrl != null and timbreUrl != '' ">and timbre_url like concat('%', #{timbreUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.timbre_id
	 		,t.guiji_task_id
	 		,t.timbre_url
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_guiji_timbre a
		 ) t

	</sql>

	<select id="getGuijiTimbreListByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiTimbreVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getGuijiTimbreByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiTimbreVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>