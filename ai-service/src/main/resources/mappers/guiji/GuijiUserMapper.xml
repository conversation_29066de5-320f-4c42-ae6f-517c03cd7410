<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.GuijiUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.guiji.GuijiUserDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="name" column="name"/>
        <result property="cover" column="cover"/>
        <result property="result" column="result"/>
        <result property="channel" column="channel"/>
        <result property="params" column="params"/>
        <result property="historyId" column="history_id"/>
        <result property="state" column="state"/>
        <result property="taskId" column="task_id"/>
        <result property="appType" column="app_type"/>
        <result property="height" column="height"/>
        <result property="width" column="width"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="cover != null and cover != '' ">and cover like concat('%', #{cover}, '%')</if>
			<if test="result != null and result != '' ">and result like concat('%', #{result}, '%')</if>
			<if test="channel != null ">and channel = #{channel}</if>
			<if test="params != null and params != '' ">and params like concat('%', #{params}, '%')</if>
			<if test="historyId != null ">and history_id = #{historyId}</if>
			<if test="state != null ">and state = #{state}</if>
			<if test="taskId != null and taskId != '' ">and task_id like concat('%', #{taskId}, '%')</if>
			<if test="appType != null and appType != '' ">and app_type like concat('%', #{appType}, '%')</if>
			<if test="height != null ">and height = #{height}</if>
			<if test="width != null ">and width = #{width}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.name
	 		,t.cover
	 		,t.result
	 		,t.channel
	 		,t.params
	 		,t.history_id
	 		,t.state
	 		,t.task_id
	 		,t.app_type
	 		,t.height
	 		,t.width
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_guiji_user a
		 ) t

	</sql>

	<select id="getGuijiUserListByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiUserVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getZegoGuijiUserListByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiUserVo">
		select * from (
			select a.*,1 as source_type from p_zego_user a

		    union all

		    select b.*,2 as source_type from p_guiji_user b
					  )t
		<include refid="common_where"></include>
	</select>

	<select id="getGuijiUserByCondition" resultType="com.fh.ai.business.entity.vo.guiji.GuijiUserVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>