<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.HistoryAppMapper">

    <select id="getHistoryAppListByCondition" resultType="com.fh.ai.business.entity.vo.historyApp.HistoryAppVo">
        select t.* from (
            select a.* from p_history_app a
            <choose>
                <when test="orderBy != null and orderBy != ''">
                    ORDER BY ${orderBy}
                </when>
            </choose>
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="subType != null and subType != ''">and sub_type = #{subType}</if>
            <if test="exceptTypeList != null and exceptTypeList.size() > 0">
                and type not in
                <foreach collection="exceptTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="typeList != null and typeList.size() > 0">
                and type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="question != null and question != ''">and question = #{question}</if>
            <if test="parameterJson != null and parameterJson != ''">and parameter_json = #{parameterJson}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="cozeConversationId != null and cozeConversationId != ''">and coze_conversation_id = #{cozeConversationId}</if>
            <if test="cozeBotId != null and cozeBotId != ''">and coze_bot_id = #{cozeBotId}</if>
            <if test="cozeChatId != null and cozeChatId != ''">and coze_chat_id = #{cozeChatId}</if>
            <if test="usageTotal != null ">and usage_total = #{usageTotal}</if>
            <if test="usageIn != null ">and usage_in = #{usageIn}</if>
            <if test="usageOut != null ">and usage_out = #{usageOut}</if>
            <if test="statisticDay != null">and date_format(create_time, '%y%m%d') = date_format(#{statisticDay}, '%y%m%d')</if>
            <if test="organizationId != null">and organization_id = #{organizationId}</if>
            <if test="gaodingId != null and gaodingId != ''">and gaoding_id = #{gaodingId}</if>
            <if test="businessId != null and businessId != ''">and business_id = #{businessId}</if>
            <if test="businessJson != null and businessJson != ''">and business_json = #{businessJson}</if>
            <if test="businessJsonBak != null and businessJsonBak != ''">and business_json_bak = #{businessJsonBak}</if>
            <if test="customParameterJson != null and customParameterJson != ''">and custom_parameter_json = #{customParameterJson}</if>
            <if test="resultReasoning != null and resultReasoning != ''">and result_reasoning = #{resultReasoning}</if>
            <if test="cozeWorkflowId != null and cozeWorkflowId != ''">and coze_workflow_id = #{cozeWorkflowId}</if>
            <if test="cozeExecuteId != null and cozeExecuteId != ''">and coze_execute_id = #{cozeExecuteId}</if>
            <if test="cozeExecuteStatus != null and cozeExecuteStatus != ''">and coze_execute_status = #{cozeExecuteStatus}</if>
        </where>
    </select>

    <select id="getUserUsageCount" resultType="com.fh.ai.business.entity.vo.historyApp.UserUsageVo">
        SELECT
        ha.user_oid,
        COUNT(*) AS usageCount
        FROM
        p_history_app ha
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="userOidList != null and userOidList.size() > 0">
                and user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>

            <if test="question != null and question != ''">and question = #{question}</if>
            <if test="parameterJson != null and parameterJson != ''">and parameter_json = #{parameterJson}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>

        GROUP BY
        ha.user_oid
    </select>

    <select id="getActualTimeTop10" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT * from ( SELECT * from (
        SELECT ha.type,
        COUNT(*) AS total_usage_count
        FROM
        p_history_app ha
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="userOidList != null and userOidList.size() > 0">
                and user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="question != null and question != ''">and question = #{question}</if>
            <if test="parameterJson != null and parameterJson != ''">and parameter_json = #{parameterJson}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
        GROUP BY type
        ORDER BY total_usage_count desc
        limit 10
        ) a
        union all
        SELECT * from ( SELECT
        app_type 'type' ,
        COUNT(*) AS total_usage_count
        FROM
        p_mt_history ha
        where is_delete = 1 and (type  =1 or type =11)
        <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
        <if test="userOidList != null and userOidList.size() > 0">
            and user_oid in
            <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        union all
        SELECT app_type 'type' ,
        COUNT(*) AS total_usage_count
        FROM
        p_mt_history ha
        where is_delete = 1 and type =2
        <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
        <if test="userOidList != null and userOidList.size() > 0">
            and user_oid in
            <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) b
        )t  ORDER BY total_usage_count desc limit 10
    </select>

    <select id="getMeetingConvertText" resultType="com.fh.ai.business.entity.vo.historyApp.HistoryAppVo">
        SELECT
            *
        FROM
            p_history_app pha
        WHERE
            pha.is_delete = 1
            AND pha.type = 207
            AND pha.sub_type = 20701
            <if test="userOid != null and userOid != ''">and pha.user_oid = #{userOid}</if>
            <if test="conversationCode != null and conversationCode != ''">and pha.conversation_code = #{conversationCode}</if>
        ORDER BY
            pha.create_time DESC
            LIMIT 1
    </select>

    <select id="getMeetingAssistantList" resultType="com.fh.ai.business.entity.vo.historyApp.HistoryAppVo">
        SELECT
            s.*
        FROM
            p_history_app s
        INNER JOIN (
            SELECT
            a.conversation_code,
            a.type,
            a.sub_type,
            MAX( a.id ) AS max_id
            FROM
                p_history_app a
            WHERE
                a.is_delete = 1 AND a.type = 207
                <if test="userOid != null and userOid != ''">and a.user_oid = #{userOid}</if>
                <if test="conversationCode != null and conversationCode != ''">and a.conversation_code = #{conversationCode}</if>
                <if test="subType != null and subType != ''">and a.sub_type = #{subType}</if>
                <if test="subTypeList != null and subTypeList.size() > 0">
                    and a.sub_type in
                    <foreach collection="subTypeList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            GROUP BY a.conversation_code, a.type, a.sub_type
        ) s2 ON s.id = s2.max_id
    </select>

    <select id="getByIdOrMessageUUID" resultType="com.fh.ai.business.entity.vo.historyApp.HistoryAppVo">
        select *
        from p_history_app
        <where>
            is_delete = 1
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="messageUUID != null and messageUUID != ''">
                and message_uuid = #{messageUUID}
            </if>
        </where>
    </select>

    <select id="getRunningCozeWorkflowRecords" resultType="com.fh.ai.business.entity.vo.historyApp.HistoryAppVo">
        select *
        from p_history_app
        where is_delete = 1
          and coze_execute_id is not null
          and coze_execute_id != ''
          and coze_execute_status = 'Running'
        order by create_time desc
    </select>
</mapper>