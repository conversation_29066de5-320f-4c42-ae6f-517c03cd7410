<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.HotspotMapper">

	<select id="getHotspotListByCondition" resultType="com.fh.ai.business.entity.vo.hotspot.HotspotVo">
		select t.* from (
			select a.* from p_hotspot a
            ORDER BY
            a.title,
            CAST( REPLACE ( a.heat, '万', '' ) AS SIGNED ) DESC
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="title != null and title != ''">and title = #{title}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="heat != null and heat != ''">and heat = #{heat}</if>
            <if test="url != null and url != ''">and url = #{url}</if>
            <if test="sort != null and sort != ''">and sort = #{sort}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>
</mapper>