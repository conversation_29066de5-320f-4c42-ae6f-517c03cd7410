<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MaterialMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.vo.material.MaterialVo" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="materialType" column="material_type"/>
        <result property="fileName" column="file_name"/>
        <result property="fileOid" column="file_oid"/>
        <result property="fileUrl" column="file_url"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="materialType != null ">and material_type = #{materialType}</if>
			<if test="fileName != null and fileName != '' ">and file_name like concat('%', #{fileName}, '%')</if>
			<if test="fileOid != null and fileOid != '' ">and file_oid like concat('%', #{fileOid}, '%')</if>
			<if test="fileUrl != null and fileUrl != '' ">and file_url like concat('%', #{fileUrl}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.material_type
	 		,t.file_name
	 		,t.file_oid
	 		,t.file_url
	 		,t.user_oid
	 		,t.organization_id
	 		,t.is_delete
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
		from (
			 select a.* from p_material a
		 ) t

	</sql>
    <update id="updateDeleteFlag">
		update p_material set is_delete = 2 where id = #{id}
	</update>

    <select id="getMaterialListByCondition" resultType="com.fh.ai.business.entity.vo.material.MaterialVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by create_time desc
	</select>

	<select id="getMaterialByCondition" resultType="com.fh.ai.business.entity.vo.material.MaterialVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
	<select id="selectIdByIdAndDeleteFlag" resultType="com.fh.ai.business.entity.vo.material.MaterialVo">
		select * from p_material where id = #{id} and is_delete = 1;
	</select>
</mapper>