<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MenuMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.menu.MenuDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="menuName" column="menu_name"/>
        <result property="permission" column="permission"/>
        <result property="state" column="state"/>
        <result property="url" column="url"/>
        <result property="type" column="type"/>
        <result property="sort" column="sort"/>
        <result property="icon" column="icon"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <select id="getMenuListByCondition" resultType="com.fh.ai.business.entity.vo.menu.MenuVo">
        select t.* from (
        select m.* from p_menu m ORDER BY m.sort
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="roleId != null and roleId != ''">and role_id = #{roleId}</if>
            <if test="parentId != null and parentId != ''">and parent_id = #{parentId}</if>
            <if test="menuName != null and menuName != ''">and menu_name = #{menuName}</if>
            <if test="permission != null and permission != ''">and permission = #{permission}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="url != null and url != ''">and url = #{url}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="sort != null and sort != ''">and sort = #{sort}</if>
            <if test="icon != null and icon != ''">and icon = #{icon}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="createUser != null and createUser != ''">and create_user = #{createUser}</if>
            <if test="updateUser != null and updateUser != ''">and update_user = #{updateUser}</if>
        </where>
    </select>

    <select id="getMenuListByRole" resultType="com.fh.ai.business.entity.vo.menu.MenuVo">
        select m.* from p_menu m
        INNER JOIN p_role_menu rm ON rm.menu_id = m.id
        WHERE rm.role_id = #{roleId}
    </select>
    <select id="getMenuVoById" resultType="com.fh.ai.business.entity.vo.menu.MenuVo">
        select id,menu_name,url
        from p_menu
        where id = #{id}
    </select>
</mapper>