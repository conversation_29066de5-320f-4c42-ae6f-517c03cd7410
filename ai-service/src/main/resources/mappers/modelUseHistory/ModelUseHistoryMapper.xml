<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ModelUseHistoryMapper">

    <resultMap id="BaseResultMap" type="com.fh.ai.business.entity.dto.modelUseHistory.ModelUseHistoryDto">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="historyAppId" column="history_app_id"/>
        <result property="conversationCode" column="conversation_code"/>
        <result property="messageUUID" column="message_UUID"/>
        <result property="useType" column="use_type"/>
        <result property="useValue" column="use_value"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getModelUseHistoryListByCondition"
            resultType="com.fh.ai.business.entity.vo.modelUseHistory.ModelUseHistoryVo">
        select *
        from p_model_use_history
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="historyAppId != null">and history_app_id = #{historyAppId}</if>
            <if test="conversationCode != null and conversationCode != ''">and conversation_code = #{conversationCode}</if>
            <if test="messageUUID != null and messageUUID != ''">and message_UUID = #{messageUUID}</if>
            <if test="useType != null and useType != ''">and use_type = #{useType}</if>
            <if test="useValue != null and useValue != ''">and use_value = #{useValue}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
    </select>
</mapper>