<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MtConfigMapper">

	<select id="getMtConfigListByCondition" resultType="com.fh.ai.business.entity.vo.mtConfig.MtConfigVo">
		select t.* from (
			select a.* from p_mt_config a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="mtId != null and mtId != ''">and mt_id = #{mtId}</if>
            <if test="name != null and name != ''">and (name like concat('%', #{name}, '%') or description like concat('%', #{name}, '%')) </if>
            <if test="description != null and description != ''">and description = #{description}</if>
            <if test="category != null and category != ''">
                and categorys like concat('%,', #{category}, ',%')
            </if>
            <if test="model != null and model != ''">and model = #{model}</if>
            <if test="images != null and images != ''">and images = #{images}</if>
            <if test="width != null and width != ''">and width = #{width}</if>
            <if test="height != null and height != ''">and height = #{height}</if>
            <if test="ratio != null and ratio != ''">and ratio = #{ratio}</if>
            <if test="dataJson != null and dataJson != ''">and data_json = #{dataJson}</if>
            <if test="styleType != null and styleType != '' and styleType != 0">and style_type = #{styleType}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>
</mapper>