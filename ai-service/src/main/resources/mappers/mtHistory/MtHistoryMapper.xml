<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MtHistoryMapper">

	<select id="getMtHistoryListByCondition" resultType="com.fh.ai.business.entity.vo.mtHistory.MtHistoryVo">
		select t.* from (
			select a.* from p_mt_history a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="parameterJson != null and parameterJson != ''">and parameter_json = #{parameterJson}</if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="responseData != null and responseData != ''">and response_data = #{responseData}</if>
            <if test="channel != null and channel != ''">and channel = #{channel}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="statisticDay != null">and date_format(create_time, '%y%m%d') = date_format(#{statisticDay}, '%y%m%d')</if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
        </where>
	</select>

    <select id="getUserUsageCount" resultType="com.fh.ai.business.entity.vo.historyApp.UserUsageVo">
        SELECT
        ha.user_oid,
        COUNT(*) AS usageCount
        FROM
        p_mt_history ha
        <where>
        1=1
            <if test="userOidList != null and userOidList.size() > 0">
                and user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>
                  and is_delete = 1
        </where>

        GROUP BY
        ha.user_oid
    </select>

</mapper>