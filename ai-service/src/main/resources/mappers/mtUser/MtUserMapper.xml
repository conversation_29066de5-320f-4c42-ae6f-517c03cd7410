<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MtUserMapper">

	<select id="getMtUserListByCondition" resultType="com.fh.ai.business.entity.vo.mtUser.MtUserVo">
		select t.* from (
			select a.* from p_mt_user a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">
            <if test="type==1"> and (type = 1 or type = 11)</if>
            <if test="type==2"> and type = #{type}</if>
            </if>
            <if test="result != null and result != ''">and result = #{result}</if>
            <if test="isFavorite != null and isFavorite != ''">and is_favorite = #{isFavorite}</if>
            <if test="channel != null and channel != ''">and channel = #{channel}</if>
            <if test="historyId != null and historyId != ''">and history_id = #{historyId}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="responseData != null and responseData != ''">and response_data = #{responseData}</if>
            <if test="excludeAppTypes != null and excludeAppTypes.size() != 0">
                and app_type not in
                <foreach collection="excludeAppTypes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
        </where>
	</select>

	<select id="getMtUserListByHistoryIds" resultType="com.fh.ai.business.entity.vo.mtUser.MtUserVo">
        select *
        from p_mt_user
        <where>
            history_id in
            <foreach collection="historyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>