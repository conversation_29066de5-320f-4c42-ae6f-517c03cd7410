<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MtUserFavoriteMapper">

	<select id="getMtUserFavoriteListByCondition" resultType="com.fh.ai.business.entity.vo.mtUserFavorite.MtUserFavoriteVo">
		select t.* from (
			select a.* from p_mt_user_favorite a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="referKey != null and referKey != ''">and refer_key = #{referKey}</if>
            <if test="channel != null and channel != ''">and channel = #{channel}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>
</mapper>