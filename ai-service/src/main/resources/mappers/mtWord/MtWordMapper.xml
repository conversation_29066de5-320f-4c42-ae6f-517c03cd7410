<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MtWordMapper">

	<select id="getMtWordListByCondition" resultType="com.fh.ai.business.entity.vo.mtWord.MtWordVo">
		select t.* from (
			select a.* from p_mt_word a
		) t
	    <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="parentId != null and parentId >=0 ">and parent_id = #{parentId}</if>
            <if test="name != null and name != ''">and (name like concat('%', #{name}, '%') or name_english like concat('%', #{nameEnglish}, '%')) </if>
            <if test="searchName != null and searchName != ''">and (name like concat('%', #{searchName}, '%') or name_english like concat('%', #{searchName}, '%')) </if>
            <if test="nameEnglish != null and nameEnglish != ''">and name_english = #{nameEnglish}</if>
            <if test="type != null">and type = #{type}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="types != null and types.size() >0">
                and type in
                <foreach collection="types" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
        </where>
	</select>
</mapper>