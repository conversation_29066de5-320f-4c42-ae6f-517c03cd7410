<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.MyWorksMapper">

    <select id="getMyImageWorks" resultType="com.fh.ai.business.entity.vo.myWorks.MyWorksVo">
        select * from (
        select
            id
            ,app_type as type
            ,params
            ,result
            ,create_time
            ,channel
            ,null as conversation_code
        from p_mt_user
        <where>
            is_delete = 1
            and state = 2
            <if test="userOid != null and userOid != ''">
                and user_oid = #{userOid}
            </if>
            <if test="channel != null">and channel = #{channel}</if>
            <if test="id != null">
                and id = #{id}
            </if>
            and app_type != 701
            and result is not null
            and result != '[]'
        </where>
        union
        select
            id
            ,type
            ,parameter_json as params
            ,result
            ,create_time
            ,channel
            ,conversation_code
        from p_history_app
        <where>
            is_delete = 1
            and type in (406, 701)
            and result is not null
            and result != '[]'
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="channel != null">and channel = #{channel}</if>
            <if test="id != null">
                and id = #{id}
            </if>
        </where>
        ) as a
        <where>
            <if test="type != null">and type = #{type}</if>
        </where>
        order by create_time desc
    </select>

    <select id="getMyPPTWorks" resultType="com.fh.ai.business.entity.vo.myWorks.MyWorksVo">
        select id
            ,type
            ,parameter_json as params
            ,result
            ,ifnull(update_time, create_time) create_time
            ,business_id
            ,business_json
            ,conversation_code as conversationCode
            ,channel
        from p_history_app
        <where>
            type in (204, 205, 206, 301, 305, 309,314, 403, 408, 410, 415, 416)
            and business_json is not null
            and business_json != '[]'
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="channel != null">
                and channel = #{channel}
            </if>
        </where>
        order by ifnull(update_time, create_time) desc
    </select>
</mapper>