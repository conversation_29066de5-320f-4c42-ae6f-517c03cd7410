<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.NoticeMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.notice.NoticeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="relationId" column="relation_id"/>
        <result property="readState" column="read_state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getNoticeListByCondition" resultType="com.fh.ai.business.entity.vo.notice.NoticeVo">
        select t.* from (
        select a.* from p_notice as a
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="content != null and content != ''">and content = #{content}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="relationId != null and relationId != ''">and relation_id = #{relationId}</if>
            <if test="readState != null and readState != ''">and read_state = #{readState}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
        </where>
    </select>

	<insert id="insertNoticeBatch">
insert into p_notice(user_oid,type,relation_id,read_state,content,create_time)
select oid,#{type},#{relationId},1,#{content} ,now() from p_user where is_delete = 1
	</insert>
</mapper>