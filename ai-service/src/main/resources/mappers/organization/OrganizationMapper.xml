<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.OrganizationMapper">

	<select id="getOrganizationListByCondition" resultType="com.fh.ai.business.entity.vo.organization.OrganizationVo">
		select t.* from (
			select a.* from p_organization a
		) t
	    <where>
            <if test="id != null">and id = #{id}</if>
            <if test="parentId != null">and parent_id = #{parentId}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="authStartTime != null ">and auth_start_time = #{authStartTime}</if>
            <if test="authEndTime != null ">and auth_end_time = #{authEndTime}</if>
            <if test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
	</select>

    <select id="getStatisticsOrganizationList" resultType="com.fh.ai.business.entity.vo.organization.OrganizationVo">
        SELECT a.*
        FROM p_organization a
        WHERE
        is_delete = 1 AND is_statistics = 1
        ORDER BY a.sort ASC

    </select>
</mapper>