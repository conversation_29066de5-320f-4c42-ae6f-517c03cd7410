<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.OrganizationPackageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="rightsType" column="rights_type"/>
        <result property="organizationId" column="organization_id"/>
        <result property="packageInfoId" column="package_info_id"/>
        <result property="authTimeStart" column="auth_time_start"/>
        <result property="authTimeEnd" column="auth_time_end"/>
        <result property="accountNumTotal" column="account_num_total"/>
        <result property="inferenceNumTotal" column="inference_num_total"/>
        <result property="transliterateNumTotal" column="transliterate_num_total"/>
        <result property="mtNumTotal" column="mt_num_total"/>
        <result property="accountNum" column="account_num"/>
        <result property="inferenceNum" column="inference_num"/>
        <result property="transliterateNum" column="transliterate_num"/>
        <result property="mtNum" column="mt_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="quotaTotal" column="quota_total"/>
		<result property="quota" column="quota"/>
		<result property="inferenceUsageNum" column="inference_usage_num"/>
		<result property="transliterateUsageNum" column="transliterate_usage_num"/>
		<result property="mtUsageNum" column="mt_usage_num"/>
		<result property="balance" column="balance"/>
		<result property="consumeAmount" column="consume_amount"/>
		<result property="smsMarginRemind" column="sms_margin_remind"/>
		<result property="smsArrearsRemind" column="sms_arrears_remind"/>
		<result property="inferenceUsageAmount" column="inference_usage_amount"/>
		<result property="transliterateUsageAmount" column="transliterate_usage_amount"/>
		<result property="mtUsageAmount" column="mt_usage_amount"/>
		<result property="pptUsageNum" column="ppt_usage_num"/>
		<result property="pptUsageAmount" column="ppt_usage_amount"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="id != null ">and a.id = #{id}</if>
			<if test="rightsType != null ">and a.rights_type = #{rightsType}</if>
			<if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
			<if test="packageInfoId != null and packageInfoId != '' ">and a.package_info_id like concat('%', #{packageInfoId}, '%')</if>
			<if test="authTimeStart != null ">and a.auth_time_start = #{authTimeStart}</if>
			<if test="authTimeEnd != null ">and a.auth_time_end = #{authTimeEnd}</if>
			<if test="accountNumTotal != null ">and a.account_num_total = #{accountNumTotal}</if>
			<if test="inferenceNumTotal != null ">and a.inference_num_total = #{inferenceNumTotal}</if>
			<if test="transliterateNumTotal != null ">and a.transliterate_num_total = #{transliterateNumTotal}</if>
			<if test="mtNumTotal != null ">and a.mt_num_total = #{mtNumTotal}</if>
			<if test="accountNum != null ">and a.account_num = #{accountNum}</if>
			<if test="inferenceNum != null ">and a.inference_num = #{inferenceNum}</if>
			<if test="transliterateNum != null ">and a.transliterate_num = #{transliterateNum}</if>
			<if test="mtNum != null ">and a.mt_num = #{mtNum}</if>
			<if test="createBy != null ">and a.create_by = #{createBy}</if>
			<if test="updateBy != null ">and a.update_by = #{updateBy}</if>
			<if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
			<if test="quotaTotal != null">and a.quota_total = #{quotaTotal}</if>
			<if test="quota != null">and a.quota = #{quota}</if>
			<if test="organizationIds != null and organizationIds.size != 0">
				and a.organization_id in
				<foreach collection="organizationIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="inferenceUsageNum != null">and a.inference_usage_num = #{inferenceUsageNum}</if>
			<if test="transliterateUsageNum != null">and a.transliterate_usage_num = #{transliterateUsageNum}</if>
			<if test="mtUsageNum != null">and a.mt_usage_num = #{mtUsageNum}</if>
			<if test="balance != null">and a.balance = #{balance}</if>
			<if test="consumeAmount != null">and a.consume_amount = #{consumeAmount}</if>
			<if test="smsMarginRemind != null">and a.sms_margin_remind = #{smsMarginRemind}</if>
			<if test="smsArrearsRemind != null">and a.sms_arrears_remind = #{smsMarginRemind}</if>
			<if test="inferenceUsageAmount != null">and a.inference_usage_amount = #{inferenceUsageAmount}</if>
			<if test="transliterateUsageAmount != null">and a.transliterate_usage_amount = #{transliterateUsageAmount}</if>
			<if test="mtUsageAmount != null">and a.mt_usage_amount = #{mtUsageAmount}</if>
			<if test="pptUsageNum != null">and a.ppt_usage_num = #{pptUsageNum}</if>
			<if test="pptUsageAmount != null">and a.ppt_usage_amount = #{pptUsageAmount}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.rights_type
	 		,t.organization_id
	 		,t.package_info_id
	 		,t.auth_time_start
	 		,t.auth_time_end
	 		,t.account_num_total
	 		,t.inference_num_total
	 		,t.transliterate_num_total
	 		,t.mt_num_total
	 		,t.account_num
	 		,t.inference_num
	 		,t.transliterate_num
	 		,t.mt_num
	 		,t.remark
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
			,t.package_name
			,t.quota_total
			,t.quota
			,t.inference_usage_num
			,t.transliterate_usage_num
			,t.mt_usage_num
			,t.balance
			,t.consume_amount
			,t.sms_margin_remind
			,t.sms_arrears_remind
			,t.inference_usage_amount
			,t.transliterate_usage_amount
			,t.mt_usage_amount
			,t.ppt_usage_num
			,t.ppt_usage_amount
		from (
			select a.*, b.package_name, b.remark
			from p_organization_package a
			join p_package_info b on a.package_info_id = b.package_info_id
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationPackageListByCondition" resultType="com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo">
		<include refid="common_select"></include>
	</select>

	<select id="getOrganizationPackageByCondition" resultType="com.fh.ai.business.entity.vo.organizationPackage.OrganizationPackageVo">
		<include refid="common_select"></include>
		limit 1
	</select>

    <select id="getOrganizationAccumulateUsageStatistic"
			resultType="com.fh.ai.business.entity.vo.organizationPackage.OrganizationAccumulateUsageStatisticVo">
		select sum(ifnull(inference_usage_num, 0)) as inferenceUsageNum
			,ROUND(sum(ifnull(transliterate_usage_num, 0))/60, 2) as transliterateUsageNum
			,sum(ifnull(mt_usage_num, 0)) as mtUsageNum
			,sum(ifnull(ppt_usage_num, 0)) as pptUsageNum
		from p_organization_package
		<where>
			is_delete = 1
		</where>
	</select>
</mapper>