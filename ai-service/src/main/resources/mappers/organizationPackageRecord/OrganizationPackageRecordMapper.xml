<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.OrganizationPackageRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.organizationPackageRecord.OrganizationPackageRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="packageInfoId" column="package_info_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="amount" column="amount"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="packageInfoId != null and packageInfoId != '' ">and package_info_id like concat('%', #{packageInfoId}, '%')</if>
			<if test="createBy != null ">and create_by = #{createBy}</if>
			<if test="updateBy != null ">and update_by = #{updateBy}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="amount != null ">and amount = #{amount}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_id
	 		,t.package_info_id
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
			,t.amount
		from (
			select a.* from p_organization_package_record a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getPOrganizationPackageRecordListByCondition" resultType="com.fh.ai.business.entity.vo.organizationPackageRecord.OrganizationPackageRecordVo">
		<include refid="common_select"></include>
	</select>

	<select id="getPOrganizationPackageRecordByCondition" resultType="com.fh.ai.business.entity.vo.organizationPackageRecord.OrganizationPackageRecordVo">
		<include refid="common_select"></include>
	</select>
</mapper>