<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.OrganizationReduceQuotaRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
       	<result property="type" column="type"/>
		<result property="inferenceUsageNum" column="inference_usage_num"/>
		<result property="transliterateUsageNum" column="transliterate_usage_num"/>
		<result property="mtUsageNum" column="mt_usage_num"/>
		<result property="inferenceUsageAmount" column="inference_usage_amount"/>
		<result property="transliterateUsageAmount" column="transliterate_usage_amount"/>
		<result property="mtUsageAmount" column="mt_usage_amount"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="pptUsageNum" column="ppt_usage_num"/>
		<result property="pptUsageAmount" column="ppt_usage_amount"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			<if test="type != null">and `type` = #{type}</if>
			<if test="inferenceUsageNum != null">and inference_usage_num = #{inferenceUsageNum}</if>
			<if test="transliterateUsageNum != null">and transliterate_usage_num = #{transliterateUsageNum}</if>
			<if test="mtUsageNum != null">and mt_usage_num = #{mtUsageNum}</if>
			<if test="inferenceUsageAmount != null">and inference_usage_amount = #{inferenceUsageAmount}</if>
			<if test="transliterateUsageAmount != null">and transliterate_usage_amount = #{transliterateUsageAmount}</if>
			<if test="mtUsageAmount != null">and mt_usage_amount = #{mtUsageAmount}</if>
			<if test="createBy != null ">and create_by = #{createBy}</if>
			<if test="updateBy != null ">and update_by = #{updateBy}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="organizationIds != null and organizationIds.size != 0">
				and organization_id in
				<foreach collection="organizationIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="statisticDay != null">and date_format(create_time, '%y%m%d') = date_format(#{statisticDay}, '%y%m%d')</if>
			<if test="pptUsageNum != null">and ppt_usage_num = #{pptUsageNum}</if>
			<if test="pptUsageAmount != null">and ppt_usage_amount = #{pptUsageAmount}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_id
	 		,t.user_oid
			,t.type
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
			,t.inference_usage_num
			,t.transliterate_usage_num
			,t.mt_usage_num
			,t.inference_usage_amount
			,t.transliterate_usage_amount
			,t.mt_usage_amount
			,t.ppt_usage_num
			,t.ppt_usage_amount
		from (
			select *
			from p_organization_reduce_quota_record
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationReduceQuotaRecordListByCondition" resultType="com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo">
		<include refid="common_select"></include>
	</select>

	<select id="getOrganizationReduceQuotaRecordByCondition" resultType="com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo">
		<include refid="common_select"></include>
		limit 1
	</select>

    <select id="getOrganizationReduceQuotaRecordListByDay"
			resultType="com.fh.ai.business.entity.vo.organizationReduceQuotaRecord.OrganizationReduceQuotaRecordVo">
		select organization_id
			,sum(ifnull(inference_usage_num, 0)) inference_usage_num
			,sum(ifnull(transliterate_usage_num, 0)) transliterate_usage_num
			,sum(ifnull(mt_usage_num, 0)) mt_usage_num
			,sum(ifnull(inference_usage_amount, 0)) inference_usage_amount
			,sum(ifnull(transliterate_usage_amount, 0)) transliterate_usage_amount
			,sum(ifnull(mt_usage_amount, 0)) mt_usage_amount
			,sum(ifnull(ppt_usage_num, 0)) ppt_usage_num
			,sum(ifnull(ppt_usage_amount, 0)) ppt_usage_amount
		from p_organization_reduce_quota_record
		<include refid="table_where"></include>
		group by organization_id
	</select>
</mapper>