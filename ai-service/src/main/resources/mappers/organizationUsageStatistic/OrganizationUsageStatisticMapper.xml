<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.OrganizationUsageStatisticMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.organizationUsageStatistic.OrganizationUsageStatisticDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="statisticDay" column="statistic_day"/>
        <result property="inferenceUsageNum" column="inference_usage_num"/>
        <result property="transliterateUsageNum" column="transliterate_usage_num"/>
        <result property="mtUsageNum" column="mt_usage_num"/>
		<result property="usageQuota" column="usage_quota"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="pptUsageNum" column="ppt_usage_num"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="statisticDay != null ">and statistic_day = #{statisticDay}</if>
			<if test="inferenceUsageNum != null ">and inference_usage_num = #{inferenceUsageNum}</if>
			<if test="transliterateUsageNum != null ">and transliterate_usage_num = #{transliterateUsageNum}</if>
			<if test="mtUsageNum != null">and mt_usage_num = #{mtUsageNum}</if>
			<if test="usageQuota != null">and usage_quota = #{usageQuota}</if>
			<if test="isDelete != null">and is_delete = #{isDelete}</if>
			<if test="pptUsageNum != null">and ppt_usage_num = #{pptUsageNum}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_id
	 		,t.statistic_day
			,t.inference_usage_num
			,t.transliterate_usage_num
			,t.mt_usage_num
			,t.usage_quota
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
			,t.ppt_usage_num
		from (
			select *
			from p_organization_usage_statistic
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationUsageStatisticListByCondition" resultType="com.fh.ai.business.entity.vo.organizationUsageStatistic.OrganizationUsageStatisticVo">
		select pous.*,
			po.name as organization_name
		from p_organization_usage_statistic pous
		join p_organization po on po.id = pous.organization_id and po.is_delete = 1
		<where>
			<if test="id != null ">and pous.id = #{id}</if>
			<if test="organizationId != null ">and pous.organization_id = #{organizationId}</if>
			<if test="statisticDay != null ">and pous.statistic_day = #{statisticDay}</if>
			<if test="inferenceUsageNum != null ">and pous.inference_usage_num = #{inferenceUsageNum}</if>
			<if test="transliterateUsageNum != null ">and pous.transliterate_usage_num = #{transliterateUsageNum}</if>
			<if test="mtUsageNum != null">and pous.mt_usage_num = #{mtUsageNum}</if>
			<if test="usageQuota != null">and pous.usage_quota = #{usageQuota}</if>
			<if test="isDelete != null">and pous.is_delete = #{isDelete}</if>
			<if test="organizationName != null and organizationName != ''">and o.name like concat('%', #{organizationName}, '%')</if>
			<if test="startQueryDay != null">and date_format(pous.statistic_day, '%y%m%d') &gt;= date_format(#{startQueryDay}, '%y%m%d')</if>
			<if test="endQueryDay != null">and date_format(pous.statistic_day, '%y%m%d') &lt;= date_format(#{endQueryDay}, '%y%m%d')</if>
		</where>
		order by pous.statistic_day desc, pous.organization_id
	</select>

	<select id="getOrganizationUsageStatisticByCondition" resultType="com.fh.ai.business.entity.vo.organizationUsageStatistic.OrganizationUsageStatisticVo">
		<include refid="common_select"></include>
		limit 1
	</select>

</mapper>