<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PackageInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.packageInfo.PackageInfoDto" id="BaseResultMap">
        <result property="packageInfoId" column="package_info_id"/>
        <result property="packageName" column="package_name"/>
        <result property="authDays" column="auth_days"/>
        <result property="accountNumTotal" column="account_num_total"/>
        <result property="inferenceNumTotal" column="inference_num_total"/>
        <result property="transliterateNumTotal" column="transliterate_num_total"/>
        <result property="mtNumTotal" column="mt_num_total"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="amount" column="amount"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="packageInfoId != null ">and package_info_id = #{packageInfoId}</if>
			<if test="packageName != null and packageName != '' ">and package_name like concat('%', #{packageName}, '%')</if>
			<if test="authDays != null ">and auth_days = #{authDays}</if>
			<if test="accountNumTotal != null ">and account_num_total = #{accountNumTotal}</if>
			<if test="inferenceNumTotal != null ">and inference_num_total = #{inferenceNumTotal}</if>
			<if test="transliterateNumTotal != null ">and transliterate_num_total = #{transliterateNumTotal}</if>
			<if test="mtNumTotal != null ">and mt_num_total = #{mtNumTotal}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="createUser != null ">and create_by = #{createBy}</if>
			<if test="updateUser != null ">and update_by = #{updateBy}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="amount != null ">and amount = #{amount}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.package_info_id
	 		,t.package_name
	 		,t.auth_days
	 		,t.account_num_total
	 		,t.inference_num_total
	 		,t.transliterate_num_total
	 		,t.mt_num_total
	 		,t.remark
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
			,t.amount
		from (
			select a.* from p_package_info a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getPackageInfoListByCondition" resultType="com.fh.ai.business.entity.vo.packageInfo.PackageInfoVo">
		<include refid="common_select"></include>
	</select>

	<select id="getPackageInfoByCondition" resultType="com.fh.ai.business.entity.vo.packageInfo.PackageInfoVo">
		<include refid="common_select"></include>
	</select>
</mapper>