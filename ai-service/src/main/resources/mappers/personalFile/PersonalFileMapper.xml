<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PersonalFileMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.personalFile.PersonalFileDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileOid" column="file_oid"/>
        <result property="fileUrl" column="file_url"/>
        <result property="bizType" column="biz_type"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="userOid" column="user_oid"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null">
				and t.id = #{id}
			</if>
			<if test="fileName != null and fileName != ''">
				and t.file_name like concat('%', #{fileName}, '%')
			</if>
			<if test="fileOid != null and fileOid != ''">
				and t.file_oid = #{fileOid}
			</if>
			<if test="fileUrl != null and fileUrl != ''">
				and t.file_url like concat('%', #{fileUrl}, '%')
			</if>
			<if test="bizType != null">
				and t.biz_type = #{bizType}
			</if>
			<if test="sort != null">
				and t.sort = #{sort}
			</if>
			<if test="createTime != null">
				and t.create_time = #{createTime}
			</if>
			<if test="createBy != null and createBy != ''">
				and t.create_by = #{createBy}
			</if>
			<if test="updateTime != null">
				and t.update_time = #{updateTime}
			</if>
			<if test="updateBy != null and updateBy != ''">
				and t.update_by = #{updateBy}
			</if>
			<if test="isDelete != null">
				and t.is_delete = #{isDelete}
			</if>
			<if test="userOid != null and userOid != ''">
				and t.user_oid = #{userOid}
			</if>
			<if test="orderBy != null and orderBy != ''">
				order by ${orderBy}
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.file_name
	 		,t.file_oid
	 		,t.file_url
	 		,t.biz_type
	 		,t.sort
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.user_oid
		from (
			 select a.* from p_personal_file a
		 ) t

	</sql>

	<select id="getPersonalFileListByCondition" resultType="com.fh.ai.business.entity.vo.personalFile.PersonalFileVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPersonalFileByCondition" resultType="com.fh.ai.business.entity.vo.personalFile.PersonalFileVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

</mapper>
