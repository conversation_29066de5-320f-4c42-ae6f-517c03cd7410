<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PictureDownloadHistoryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.pictureDownloadHistory.PictureDownloadHistoryDto" id="BaseResultMap">
        <result property="id" column="id"/>
		<result property="userOid" column="user_oid"/>
		<result property="organizationId" column="organization_id"/>
		<result property="type" column="type"/>
		<result property="gaodingId" column="gaoding_id"/>
		<result property="result" column="result"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="table_where">
		<where>
			<if test="id != null">and id = #{id}</if>
			<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			<if test="organizationId != null">and organization_id = #{organizationId}</if>
			<if test="type != null">and type = #{type}</if>
			<if test="gaodingId != null">and gaoding_id = #{gaodingId}</if>
			<if test="result != null and result != ''">and result = #{result}</if>
			<if test="createUser != null ">and create_by = #{createBy}</if>
			<if test="updateUser != null ">and update_by = #{updateBy}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="statisticDay != null">and date_format(create_time,'%y%m%d') = date_format(#{statisticDay},'%y%m%d')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.id
			,t.user_oid
			,t.organization_id
			,t.type
			,t.gaoding_id
			,t.result
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from p_package_info a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getPictureDownloadHistoryListByCondition" resultType="com.fh.ai.business.entity.vo.pictureDownloadHistory.PictureDownloadHistoryVo">
		<include refid="common_select"></include>
	</select>

	<select id="getPictureDownloadHistoryByCondition" resultType="com.fh.ai.business.entity.vo.pictureDownloadHistory.PictureDownloadHistoryVo">
		<include refid="common_select"></include>
		limit 1
	</select>
</mapper>