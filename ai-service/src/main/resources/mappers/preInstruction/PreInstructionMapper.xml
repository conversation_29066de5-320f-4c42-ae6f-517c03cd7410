<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PreInstructionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.preInstruction.PreInstructionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="userOid" column="user_oid"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="uuid != null and uuid != '' ">and uuid like concat('%', #{uuid}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.uuid
	 		,t.user_oid
	 		,t.title
	 		,t.content
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_pre_instruction a
		 ) t

	</sql>

	<select id="getPreInstructionListByCondition" resultType="com.fh.ai.business.entity.vo.preInstruction.PreInstructionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPreInstructionByCondition" resultType="com.fh.ai.business.entity.vo.preInstruction.PreInstructionVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>