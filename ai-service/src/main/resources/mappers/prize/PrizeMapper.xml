<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PrizeMapper">

	<select id="getPrizeListByCondition" resultType="com.fh.ai.business.entity.vo.prize.PrizeVo">
		select t.* from (
			select a.*, ifnull(exchanged,0) exchanged from p_prize a
			left join
        (select prize_id ,count(1) exchanged from p_user_prize group by prize_id ) b
        on a.id = b.prize_id
        ) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null">and type = #{type}</if>
            <if test="score != null and score != ''">and score = #{score}</if>
            <if test="supply != null and supply != ''">and supply = #{supply}</if>
            <if test="redeemcodeType != null">and redeemcode_type = #{redeemcodeType}</if>
            <if test="minSupply != null and minSupply != ''">and supply >= #{minSupply}</if>
            <if test="picture != null and picture != ''">and picture = #{picture}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="code" resultType="map">
                select codeName,ifnull(count,0) count,ifnull(used,0) used,ifnull(exchanged,0) exchanged from (
(select code_name codeName,ifnull(count(1),0) count from p_prize_redeemcode where is_delete = 1 GROUP BY code_name
) a
LEFT JOIN
(select code_name,prize_id prizeId,ifnull(count(1),0) used from p_prize_redeemcode where is_delete = 1 and prize_id is not null GROUP BY code_name) b
on a.codeName = b.code_name
LEFT JOIN
(select code_name cn,count(1) exchanged from p_prize_redeemcode where is_delete = 1 and state = 2 GROUP BY code_name) c
on c.cn =b.code_name
 )
    </select>
    <update id="updateCode">
        update p_prize_redeemcode set prize_id = null where is_delete = 1 and code_name = #{codeName} and state = 1 and prize_id =#{id};
        update p_prize_redeemcode set prize_id = #{id} where is_delete = 1 and code_name = #{codeName} and state = 1 and prize_id is null limit #{num};
    </update>
</mapper>