<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PrizeRedeemcodeMapper">

	<select id="getPrizeRedeemcodeListByCondition" resultType="com.fh.ai.business.entity.vo.prizeRedeemcode.PrizeRedeemcodeVo">
		select t.* from (
			select a.* from p_prize_redeemcode a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="prizeId != null and prizeId != ''">and prize_id = #{prizeId}</if>
            <if test="code != null and code != ''">and code = #{code}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>
</mapper>