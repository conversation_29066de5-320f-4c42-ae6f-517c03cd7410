<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ProofreadSettingMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.proofreading.ProofreadSettingDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="settingType" column="setting_type"/>
        <result property="settingParams" column="setting_params"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="settingType != null ">and setting_type = #{settingType}</if>
			<if test="settingParams != null and settingParams != '' ">and setting_params like concat('%', #{settingParams}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.setting_type
	 		,t.setting_params
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_proofread_setting a
		 ) t

	</sql>

	<select id="getProofreadSettingListByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getProofreadSettingByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
	<select id="getUserProofreadSetting"
			resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadSettingVo">
		select * from p_proofread_setting
	     where
		  is_delete = 1
          and setting_type = #{condition.settingType}
	      <if test="condition.userOid != null and condition.userOid != ''">
			  and user_oid = #{condition.userOid}
		  </if>
		  <if test="condition.organizationId != null">
			  and organization_id = #{condition.organizationId}
		  </if>
		 limit 1
	</select>
</mapper>