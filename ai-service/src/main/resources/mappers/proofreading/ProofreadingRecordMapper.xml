<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ProofreadingRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="recordType" column="record_type"/>
        <result property="recordState" column="record_state"/>
		<result property="executedTaskInfo" column="executed_task_info"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="originalFileOid" column="original_file_oid"/>
        <result property="originalFileUrl" column="original_file_url"/>
        <result property="uploadState" column="upload_state"/>
        <result property="channel" column="channel"/>
        <result property="submitTime" column="submit_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="recordType != null ">and record_type = #{recordType}</if>
			<if test="recordState != null ">and record_state = #{recordState}</if>
			<if test="executedTaskInfo != null and executedTaskInfo != '' ">and executed_task_info like concat('%', #{executedTaskInfo}, '%')</if>
			<if test="originalFileName != null and originalFileName != '' ">and original_file_name like concat('%', #{originalFileName}, '%')</if>
			<if test="originalFileOid != null and originalFileOid != '' ">and original_file_oid like concat('%', #{originalFileOid}, '%')</if>
			<if test="originalFileUrl != null and originalFileUrl != '' ">and original_file_url like concat('%', #{originalFileUrl}, '%')</if>
			<if test="uploadState != null ">and upload_state = #{uploadState}</if>
			<if test="channel != null ">and channel = #{channel}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="startTime!= null ">and create_time &gt;= #{startTime}</if>
			<if test="endTime!= null ">and create_time &lt;= #{endTime}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.record_type
	 		,t.record_state
		    ,t.executed_task_info
	 		,t.original_file_name
	 		,t.original_file_oid
	 		,t.original_file_url
	 		,t.upload_state
	 		,t.channel
		    ,t.submit_time
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_proofreading_record a
		 ) t

	</sql>

	<select id="getProofreadingRecordListByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by id desc
	</select>

	<select id="getProofreadingRecordByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
    <select id="getHandingRecord" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo">
		select * from p_proofreading_record where is_delete = 1 and record_state = 1
	</select>
    <select id="selectByRecordId" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo">
		select * from p_proofreading_record where id = #{recordId} and is_delete = 1
	</select>
	<select id="getProofreadingRecordList"
			resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingRecordVo">

		select * from p_proofreading_record record
		where exists (
		select 1 from p_proofreading_task task
		  where task.proofreading_record_id = record.id  and task.is_delete = 1
		)
		<if test="id != null ">and record.id = #{id}</if>
		<if test="userOid != null and userOid != '' ">and record.user_oid like concat('%', #{userOid}, '%')</if>
		<if test="organizationId != null ">and record.organization_id = #{organizationId}</if>
		<if test="recordType != null ">and record.record_type = #{recordType}</if>
		<if test="recordState != null ">and record.record_state = #{recordState}</if>
		<if test="executedTaskInfo != null and executedTaskInfo != '' ">and record.executed_task_info like concat('%', #{executedTaskInfo}, '%')</if>
		<if test="originalFileName != null and originalFileName != '' ">and record.original_file_name like concat('%', #{originalFileName}, '%')</if>
		<if test="originalFileOid != null and originalFileOid != '' ">and record.original_file_oid like concat('%', #{originalFileOid}, '%')</if>
		<if test="originalFileUrl != null and originalFileUrl != '' ">and record.original_file_url like concat('%', #{originalFileUrl}, '%')</if>
		<if test="uploadState != null ">and record.upload_state = #{uploadState}</if>
		<if test="channel != null ">and record.channel = #{channel}</if>
		<if test="isDelete != null ">and record.is_delete = #{isDelete}</if>
		order by record.submit_time desc
	</select>


</mapper>