<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ProofreadingTaskMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="sourceType" column="source_type"/>
        <result property="taskType" column="task_type"/>
        <result property="taskState" column="task_state"/>
        <result property="thirdTaskId" column="third_task_id"/>
        <result property="thirdFileId" column="third_file_id"/>
        <result property="resultFileName" column="result_file_name"/>
        <result property="resultFileOid" column="result_file_oid"/>
        <result property="resultFileUrl" column="result_file_url"/>
		<result property="reportFileName" column="report_file_name"/>
		<result property="reportFileOid" column="report_file_oid"/>
		<result property="reportFileUrl" column="report_file_url"/>
		<result property="settingOptionInfo" column="setting_option_info"/>
        <result property="requestInfo" column="request_info"/>
        <result property="responseInfo" column="response_info"/>
        <result property="modifyResult" column="modify_result"/>
        <result property="submitTime" column="submit_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="proofreadingRecordId" column="proofreading_record_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="taskType != null ">and task_type = #{taskType}</if>
			<if test="taskState != null ">and task_state = #{taskState}</if>
			<if test="thirdTaskId != null and thirdTaskId != '' ">and third_task_id like concat('%', #{thirdTaskId}, '%')</if>
			<if test="thirdFileId != null and thirdFileId != '' ">and third_file_id like concat('%', #{thirdFileId}, '%')</if>
			<if test="resultFileName != null and resultFileName != '' ">and result_file_name like concat('%', #{resultFileName}, '%')</if>
			<if test="resultFileOid != null and resultFileOid != '' ">and result_file_oid like concat('%', #{resultFileOid}, '%')</if>
			<if test="resultFileUrl != null and resultFileUrl != '' ">and result_file_url like concat('%', #{resultFileUrl}, '%')</if>
			<if test="reportFileName != null and reportFileName != '' ">and report_file_name like concat('%', #{reportFileName}, '%')</if>
			<if test="reportFileOid != null and reportFileOid != '' ">and report_file_oid like concat('%', #{reportFileOid}, '%')</if>
			<if test="reportFileUrl != null and reportFileUrl != '' ">and report_file_url like concat('%', #{reportFileUrl}, '%')</if>
			<if test="settingOptionInfo != null and settingOptionInfo != '' ">and setting_option_info like concat('%', #{settingOptionInfo}, '%')</if>
			<if test="requestInfo != null and requestInfo != '' ">and request_info like concat('%', #{requestInfo}, '%')</if>
			<if test="responseInfo != null and responseInfo != '' ">and response_info like concat('%', #{responseInfo}, '%')</if>
			<if test="modifyResult != null and modifyResult != '' ">and modify_result like concat('%', #{modifyResult}, '%')</if>
			<if test="submitTime != null ">and submit_time = #{submitTime}</if>
			<if test="finishTime != null ">and finish_time = #{finishTime}</if>
			<if test="proofreadingRecordId != null ">and proofreading_record_id = #{proofreadingRecordId}</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.source_type
	 		,t.task_type
	 		,t.task_state
	 		,t.third_task_id
	 		,t.third_file_id
	 		,t.result_file_name
	 		,t.result_file_oid
	 		,t.result_file_url
		    ,t.report_file_name
		    ,t.report_file_oid
		    ,t.report_file_url
		    ,t.setting_option_info
	 		,t.request_info
	 		,t.response_info
		    ,t.modify_result
	 		,t.submit_time
	 		,t.finish_time
	 		,t.proofreading_record_id
	 		,t.remark
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_proofreading_task a
		 ) t

	</sql>
	<update id="updateTaskIsDeleteBatch">
		update p_proofreading_task set is_delete = 2
		where id in
		<foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</update>

	<update id="updateTaskFailByRecordId">
		update p_proofreading_task set task_state = -1
		where proofreading_record_id = #{recordId} and task_state = 1 and is_delete = 1
	</update>

	<select id="getProofreadingTaskListByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getProofreadingTaskByCondition" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
    <select id="getTaskByRecordId" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo">
		select * from p_proofreading_task
		         <where>
					is_delete = 1
					 <if test="proofreadingRecordId != null ">
						 and proofreading_record_id = #{proofreadingRecordId}
		             </if>
					 <if test="sourceType != null ">
		                and source_type = #{sourceType}
		             </if>
					 <if test="taskState != null ">
		         		and task_state = #{taskState}
		         	</if>
					 <if test="taskStates != null and taskStates.size() != 0">
						 and task_state in
						 <foreach collection="taskStates" index="index" item="item" open="(" close=")" separator=",">
							 #{item}
						 </foreach>
					 </if>

				 </where>
	</select>

	<select id="getTaskByRecordIdExcludeLongColumn" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo">
		select id, source_type, task_type, task_state, third_task_id, third_file_id, result_file_name, result_file_oid,
		       result_file_url, report_file_name, report_file_oid, report_file_url, submit_time, finish_time, proofreading_record_id, remark, create_by,
		       create_time, update_by, update_time, is_delete from p_proofreading_task
		<where>
			is_delete = 1
			<if test="proofreadingRecordId != null ">
				and proofreading_record_id = #{proofreadingRecordId}
			</if>
			<if test="proofreadingRecordIds != null and proofreadingRecordIds.size() != 0">
				and proofreading_record_id in
				<foreach collection="proofreadingRecordIds" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="sourceType != null ">
				and source_type = #{sourceType}
			</if>
			<if test="taskStates != null and taskStates.size() != 0">
				and task_state in
				<foreach collection="taskStates" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>

		</where>
	</select>
	<select id="getOneTaskById" resultType="com.fh.ai.business.entity.vo.proofreading.ProofreadingTaskVo">
		select id, source_type, task_type, task_state,  result_file_name, result_file_oid,
		       result_file_url, report_file_name, report_file_oid, report_file_url,
		       response_info,  submit_time, finish_time, proofreading_record_id, remark,
		       create_time
		from p_proofreading_task where id = #{id} and is_delete = 1
	</select>
</mapper>