<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.NewsHashMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.publishNews.NewsHashDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="newsType" column="news_type"/>
        <result property="newsCategory" column="news_category"/>
        <result property="channel" column="channel"/>
        <result property="hashId" column="hash_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="newsType != null ">and news_type = #{newsType}</if>
			<if test="newsCategory != null and newsCategory != '' ">and news_category like concat('%', #{newsCategory}, '%')</if>
			<if test="channel != null and channel != '' ">and channel like concat('%', #{channel}, '%')</if>
			<if test="hashId != null and hashId != '' ">and hash_id like concat('%', #{hashId}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.news_type
	 		,t.news_category
	 		,t.channel
	 		,t.hash_id
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_news_hash a
		 ) t

	</sql>

	<select id="getNewsHashListByCondition" resultType="com.fh.ai.business.entity.vo.publishNews.NewsHashVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getNewsHashByCondition" resultType="com.fh.ai.business.entity.vo.publishNews.NewsHashVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <select id="selectAllApi" resultType="com.fh.ai.business.entity.vo.publishNews.NewsHashVo">
		select * from p_news_hash where is_delete = 1;
	</select>
</mapper>