<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.PublishNewsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.publishNews.PublishNewsDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="sourceType" column="source_type"/>
        <result property="inputYpe" column="input_ype"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="content" column="content"/>
        <result property="viewCount" column="view_count"/>
        <result property="url" column="url"/>
        <result property="publishTime" column="publish_time"/>
        <result property="author" column="author"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="newsType" column="news_type"/>
        <result property="newsCategory" column="news_category"/>
        <result property="modelAnalysis" column="model_analysis"/>
        <result property="channel" column="channel"/>

    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="inputYpe != null ">and input_ype = #{inputYpe}</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="summary != null and summary != '' ">and summary like concat('%', #{summary}, '%')</if>
			<if test="viewCount != null ">and view_count = #{viewCount}</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="publishTime != null ">and publish_time = #{publishTime}</if>
			<if test="author != null and author != '' ">and author like concat('%', #{author}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="newsType != null ">and news_type = #{newsType}</if>
			<if test="newsCategory != null and newsCategory != ''">and news_category like concat('%',#{newsCategory},'%')</if>
			<if test="channel != null and channel != '' ">and channel like concat('%',#{channel},'%') </if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.source_type
	 		,t.input_ype
	 		,t.title
	 		,t.summary
	 		,t.content
	 		,t.view_count
	 		,t.url
	 		,t.publish_time
	 		,t.author
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.news_type
			,t.news_category
			,t.model_analysis
			,t.channel
		from (
			 select a.* from p_publish_news a
		 ) t

	</sql>

	<select id="getPublishNewsListByCondition" resultType="com.fh.ai.business.entity.vo.publishNews.PublishNewsVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getPublishNewsByCondition" resultType="com.fh.ai.business.entity.vo.publishNews.PublishNewsVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getPublishNewsDetail" resultType="com.fh.ai.business.entity.vo.publishNews.PublishNewsVo">
		select * from p_publish_news where id = #{id} and is_delete = 1
	</select>


	<select id="getPublishNewsList" resultType="com.fh.ai.business.entity.vo.publishNews.PublishNewsVo">
		select id, source_type, input_ype, title, summary, content, view_count, url,
		       publish_time, author, create_by, create_time, update_by, update_time,news_type,news_category,model_analysis,channel,refresh_time
		from p_publish_news
		<where>
			is_delete = 1
			<if test="id != null ">and id = #{id}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="inputYpe != null ">and input_ype = #{inputYpe}</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="summary != null and summary != '' ">and summary like concat('%', #{summary}, '%')</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="publishTimeStart != null ">and publish_time &gt;= #{publishTimeStart}</if>
			<if test="publishTimeEnd != null ">and publish_time &lt;= #{publishTimeEnd}</if>
			<if test="searchTimeBegin != null ">and create_time &gt;= #{searchTimeBegin}</if>
			<if test="searchTimeEnd != null ">and create_time &lt;= #{searchTimeEnd}</if>
			<if test="author != null and author != '' ">and author like concat('%', #{author}, '%')</if>
			<if test="newsType != null ">and news_type = #{newsType}</if>
			<if test="newsCategory != null and newsCategory != ''">and news_category like concat('%', #{newsCategory}, '%')</if>
			<if test="channel != null and channel != '' ">and channel like concat('%', #{channel}, '%') </if>
		</where>
		order by refresh_time desc
	</select>

	<select id="selectAllNews" resultType="com.fh.ai.business.entity.vo.publishNews.PublishNewsVo">
		select id,url from p_publish_news where is_delete = 1;
	</select>

	<select id="selectIdById" resultType="java.lang.Long">
		select id from p_publish_news where id = #{id} and is_delete = 1
	</select>

</mapper>