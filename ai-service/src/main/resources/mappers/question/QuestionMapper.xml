<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.QuestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.question.QuestionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="rightAnswer" column="right_answer"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getQuestionListByCondition" resultType="com.fh.ai.business.entity.vo.question.QuestionVo">
		select t.* from (
		select a.*,ifnull(b.count,0) userAnswerCount,count(pep.id) bindCount from p_question as a
		left join p_exam_paper pep on find_in_set(a.id,pep.question_ids) and pep.is_delete = 1
		left join (select pead.question_id,count(1) count FROM p_exam_answer_detail pead where pead.is_delete = 1 GROUP BY pead.question_id) b on b.question_id = a.id
		GROUP BY a.id
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="type != null and type != ''">and type = #{type}</if>
				<if test="title != null and title != ''">and title like concat('%',#{title},'%')</if>
				<if test="content != null and content != ''">and content = #{content}</if>
				<if test="rightAnswer != null and rightAnswer != ''">and right_answer = #{rightAnswer}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getQuestionListByIds" resultType="com.fh.ai.business.entity.vo.question.QuestionVo">
		select  * from p_question  where FIND_IN_SET(id, #{ids}) order by FIND_IN_SET(id, #{ids})
	</select>
	<select id="userAnswerCount" resultType="integer">
		select count(1) count FROM p_exam_answer_detail pead where pead.question_id = #{questionId} and pead.is_delete = 1
	</select>
</mapper>