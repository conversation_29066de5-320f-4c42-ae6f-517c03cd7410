<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fh.ai.business.mapper.RoleMapper">

	<!-- 通用查询映射结果 -->
    <resultMap type="com.fh.ai.business.entity.dto.role.RoleDto" id="BaseResultMap">
	        <result property="id" column="id"/>
	        <result property="roleName" column="role_name"/>
	        <result property="description" column="description"/>
	        <result property="createTime" column="create_time"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="createUser" column="create_user"/>
	        <result property="updateUser" column="update_user"/>
	        <result property="isDelete" column="is_delete"/>
	</resultMap>

	<select id="getRoleListByUserId" resultType="com.fh.ai.business.entity.dto.role.RoleDto">
        SELECT t1.id,
               t1.role_name,
               t1.description
        FROM p_role t1
                 LEFT JOIN p_user_role t2 ON t1.id = t2.role_id
        WHERE t2.user_id = #{userId} and t1.is_delete = 1
    </select>

	<select id="getRoleListByCondition" resultType="com.fh.ai.business.entity.vo.role.RoleVo">
		select t.* from (
		select a.* from p_role a
		) t
		<where>
			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="roleName != null and roleName != ''">and role_name like concat('%', #{roleName}, '%')</if>
			<if test="description != null and description != ''">and description = #{description}</if>
			<if test="isLocked != null">and is_locked = #{isLocked}</if>
			<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
			<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
			<if test="createUser != null and createUser != ''">and create_user = #{createUser}</if>
			<if test="updateUser != null and updateUser != ''">and update_user = #{updateUser}</if>
			<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		</where>
	</select>
</mapper>