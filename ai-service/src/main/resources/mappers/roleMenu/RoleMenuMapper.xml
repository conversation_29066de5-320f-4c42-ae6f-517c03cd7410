<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.RoleMenuMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.roleMenu.RoleMenuDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <select id="getRoleMenuListByCondition" resultType="com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo">
        select t.* from (
        select a.* from p_role_menu a
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="roleId != null and roleId != ''">and role_id = #{roleId}</if>
            <if test="menuId != null and menuId != ''">and menu_id = #{menuId}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="createUser != null and createUser != ''">and create_user = #{createUser}</if>
            <if test="updateUser != null and updateUser != ''">and update_user = #{updateUser}</if>
        </where>
    </select>
    <select id="getAllRoleMenu" resultType="com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo">
        select role_id,menu_id
        from p_role_menu
    </select>
    <select id="getRoleMenuByRoleId" resultType="com.fh.ai.business.entity.vo.roleMenu.RoleMenuVo">
        select role_id,menu_id
        from p_role_menu
        where role_id = #{roleId}
    </select>
</mapper>