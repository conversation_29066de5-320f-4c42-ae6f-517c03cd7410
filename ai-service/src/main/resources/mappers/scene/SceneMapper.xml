<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.SceneMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.scene.SceneDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.name
	 		,t.type
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			select a.* from p_scene a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getSceneListByCondition" resultType="com.fh.ai.business.entity.vo.scene.SceneVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getSceneByCondition" resultType="com.fh.ai.business.entity.vo.scene.SceneVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<resultMap id="sceneWithDetailMap" type="com.fh.ai.business.entity.vo.scene.SceneVo">
		<result property="id" column="id"/>
		<result property="name" column="name"/>
		<result property="type" column="type"/>
		<result property="createBy" column="create_by"/>
		<result property="createTime" column="create_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="isDelete" column="is_delete"/>
		<collection property="sceneDetailList" ofType="com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo">
			<result property="mediaType" column="media_type"/>
			<result property="fileOid" column="file_oid"/>
			<result property="fileUrl" column="file_url"/>
			<result property="directionType" column="direction_type"/>
		</collection>
	</resultMap>

    <select id="getSceneListWithDetailList" resultMap="sceneWithDetailMap">
		select s.*
			,sd.media_type
			,sd.file_oid
			,sd.file_url
			,sd.direction_type
		from p_scene s
		left join p_scene_detail sd on s.id = sd.scene_id and sd.is_delete = 1
		<where>
			<if test="id != null">and s.id = #{id}</if>
			<if test="isDelete != null">and s.is_delete = #{isDelete}</if>
		</where>
		order by s.id, sd.id
	</select>
</mapper>