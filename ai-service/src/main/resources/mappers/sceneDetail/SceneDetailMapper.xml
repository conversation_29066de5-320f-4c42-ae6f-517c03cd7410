<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.SceneDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.sceneDetail.SceneDetailDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="sceneId" column="scene_id"/>
        <result property="mediaType" column="media_type"/>
        <result property="fileOid" column="file_oid"/>
        <result property="fileUrl" column="file_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
		<result property="directionType" column="direction_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sceneId != null ">and scene_id = #{sceneId}</if>
			<if test="mediaType != null ">and media_type = #{mediaType}</if>
			<if test="fileOid != null and fileOid != '' ">and file_oid = #{fileOid}</if>
			<if test="fileUrl != null and fileUrl != '' ">and file_url like concat('%', #{fileUrl}, '%')</if>
			<if test="directionType != null">and direction_type = #{directionType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="sceneId != null ">and scene_id = #{sceneId}</if>
			<if test="mediaType != null ">and media_type = #{mediaType}</if>
			<if test="fileOid != null and fileOid != '' ">and file_oid = #{fileOid}</if>
			<if test="fileUrl != null and fileUrl != '' ">and file_url like concat('%', #{fileUrl}, '%')</if>
			<if test="directionType != null">and direction_type = #{directionType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.scene_id
	 		,t.media_type
	 		,t.file_oid
	 		,t.file_url
			,t.direction_type
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			select a.* from p_scene_detail a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getSceneDetailListByCondition" resultType="com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getSceneDetailByCondition" resultType="com.fh.ai.business.entity.vo.sceneDetail.SceneDetailVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>