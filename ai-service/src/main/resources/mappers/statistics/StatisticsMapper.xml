<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.StatisticsMapper">

	<select id="getStatisticsListByCondition" resultType="com.fh.ai.business.entity.vo.statistics.StatisticsVo">
		select t.* from (
			select a.* from p_statistics a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="visits != null and visits != ''">and visits = #{visits}</if>
            <if test="usageUserCount != null and usageUserCount != ''">and usage_user_count = #{usageUserCount}</if>
            <if test="newUserCount != null and newUserCount != ''">and new_user_count = #{newUserCount}</if>
            <if test="appUsageCount != null and appUsageCount != ''">and app_usage_count = #{appUsageCount}</if>
            <if test="generateScore != null and generateScore != ''">and generate_score = #{generateScore}</if>
            <if test="redeemScore != null and redeemScore != ''">and redeem_score = #{redeemScore}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="getSysStatistics" resultType="com.fh.ai.business.entity.vo.statistics.SysStatisticsInfoVo">
        SELECT SUM(ps.visits) AS visits,
        COUNT(DISTINCT IF(ps.logins > 0, ps.user_oid, NULL)) AS loginUsers,
        COUNT(DISTINCT IF(ps.visits > 0, ps.user_oid, NULL)) AS visitUsers,
        COUNT(DISTINCT IF(ps.is_new = 2, ps.user_oid, NULL)) AS newUsers,
        SUM(ps.app_usage_count) AS appUsageCount,
        SUM(ps.generate_score) AS generateScore,
        SUM(ps.redeem_score) AS redeemScore
        FROM p_statistics ps
        LEFT JOIN p_organization po ON ps.organization_id = po.id
        <where>
            ps.is_delete = 1
            <if test="organizationId != null">
                and (po.superior_ids like concat('%,', #{organizationId}, ',%') or po.id=#{organizationId})
            </if>

            <if test="startTime != null">and ps.create_time >= #{startTime}</if>
            <if test="endTime != null">and ps.create_time &lt;= #{endTime}</if>

        </where>
    </select>

    <select id="getSysDailyStatistics" resultType="com.fh.ai.business.entity.vo.statistics.SysDailyStatisticsInfoVo">
        SELECT
        DATE(ps.create_time) AS statisticsDate,
        SUM(ps.visits) AS visits,
        COUNT(DISTINCT IF(ps.visits > 0, ps.user_oid, NULL)) AS visitUsers,
        COUNT(DISTINCT IF(ps.is_new = 2, ps.user_oid, NULL)) AS newUsers,
        SUM(ps.app_usage_count) AS appUsageCount
        FROM p_statistics ps
        LEFT JOIN p_organization po ON ps.organization_id = po.id
        <where>
            ps.is_delete = 1
            <if test="organizationId != null and organizationId != -999">
                and (po.superior_ids like concat('%,', #{organizationId}, ',%') or po.id=#{organizationId})
            </if>

            <if test="organizationId != null and organizationId == -999">
                AND ps.user_oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>

            <if test="startTime != null">and ps.create_time >= #{startTime}</if>
            <if test="endTime != null">and ps.create_time &lt;= #{endTime}</if>

        </where>

        GROUP BY DATE(ps.create_time)
    </select>

    <select id="getOrgStatistics" resultType="com.fh.ai.business.entity.vo.statistics.OrgStatisticsVo">
        SELECT
        org.id organizationId,
        org.`name` organizationName,
        org.sort,
        COUNT(DISTINCT IF(statistics.logins > 0, statistics.user_oid, NULL)) AS loginUsers,
        IFNULL( SUM( statistics.generate_score ), 0 ) generateScore,
        IFNULL( SUM( statistics.redeem_score ), 0 ) redeemScore
        FROM
        p_organization org
        LEFT JOIN
        (
        SELECT po.superior_ids, ps.* FROM p_statistics ps
        LEFT JOIN p_organization po ON ps.organization_id = po.id
        <where>
            ps.is_delete = 1
            <if test="year != null ">
                and YEAR(ps.create_time) = #{year}
            </if>
            <if test="month != null ">
                and MONTH(ps.create_time) = #{month}
            </if>
            <if test="startTime != null ">
                and ps.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and ps.create_time &lt;= #{endTime}
            </if>
        </where>
        ) statistics ON statistics.superior_ids LIKE CONCAT( '%,', org.id, ',%' ) OR statistics.organization_id = org.id
        <where>
            org.is_statistics = 1
            <if test="organizationId != null and organizationId != ''">and org.id = #{organizationId}</if>
        </where>
        GROUP BY
        org.id
        ORDER BY
        org.sort ASC

    </select>

    <select id="getUserStatistics" resultType="com.fh.ai.business.entity.vo.statistics.UserStatisticsVo">
        SELECT
        u.oid,
        u.account,
        u.real_name,
        IFNULL( SUM( s.app_usage_count ), 0 ) AS appUsageCount,
        IFNULL( SUM( s.logins ), 0 ) AS logins
        FROM
        p_user u
        LEFT JOIN p_statistics s ON u.oid = s.user_oid
            AND u.organization_id = s.organization_id
        <if test="startTime != null ">
            AND s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            AND s.create_time &lt;= #{endTime}
        </if>
        INNER JOIN p_organization o ON u.organization_id = o.id
        <where>
            u.is_delete = 1
            AND s.is_delete = 1
            AND o.is_delete = 1

            <if test="keyword != null and keyword != ''">
                AND ( u.real_name LIKE concat('%', #{keyword}, '%') OR u.account LIKE concat('%', #{keyword}, '%') )
            </if>

            <if test="organizationId != null ">
                and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
            </if>
        </where>
        GROUP BY u.oid
        <if test="orderBy == null or orderBy == ''">
            ORDER BY appUsageCount DESC, u.oid
        </if>
    </select>

    <select id="getUserStatisticsWithoutNull" resultType="com.fh.ai.business.entity.vo.statistics.UserStatisticsVo">
        SELECT
        u.oid,
        u.account,
        u.real_name,
        IFNULL( SUM( s.app_usage_count ), 0 ) AS appUsageCount,
        IFNULL( SUM( s.logins ), 0 ) AS logins
        FROM
        p_user u
        LEFT JOIN p_statistics s ON u.oid = s.user_oid
        AND u.organization_id = s.organization_id
        <if test="startTime != null ">
            AND s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            AND s.create_time &lt;= #{endTime}
        </if>
        INNER JOIN p_organization o ON u.organization_id = o.id
        <where>
            u.is_delete = 1
            AND s.is_delete = 1
            AND o.is_delete = 1
            <if test="keyword != null and keyword != ''">
                AND ( u.real_name LIKE concat('%', #{keyword}, '%') OR u.account LIKE concat('%', #{keyword}, '%') )
            </if>

            <if test="organizationId != null ">
                and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
            </if>
            and (s.app_usage_count is not null or s.logins is not null)
        </where>
        GROUP BY u.oid
        <if test="orderBy == null or orderBy == ''">
            ORDER BY appUsageCount DESC, u.oid
        </if>
    </select>
</mapper>