<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.StatisticsUsageMapper">

	<select id="getStatisticsUsageListByCondition" resultType="com.fh.ai.business.entity.vo.statisticsUsage.StatisticsUsageVo">
		select t.* from (
			select a.* from p_statistics_usage a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="appType != null and appType != ''">and app_type = #{appType}</if>
            <if test="usageUserCount != null and usageUserCount != ''">and usage_user_count = #{usageUserCount}</if>
            <if test="appUsageCount != null and appUsageCount != ''">and app_usage_count = #{appUsageCount}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="getUsageStatistics" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT
            app_type,
            SUM(app_usage_count) AS total_usage_count,
            COUNT(DISTINCT user_oid) AS total_user_count,
            ROUND(SUM(app_usage_count) / (datediff(now(),#{startDay})+1)) AS avg_daily_usage_count,
            ROUND(COUNT(DISTINCT user_oid) / (datediff(now(),#{startDay})+1)) AS avg_daily_user_count
        FROM
            p_statistics_usage
        WHERE
            is_delete = 1
            <if test="organizationId != null">
                and organization_id in
                (
                select id
                from p_organization
                where id = #{organizationId} or superior_ids like concat('%,', #{organizationId}, ',%')
                )
            </if>
        GROUP BY
            app_type
        ORDER BY
            app_type;
    </select>

    <select id="getTop10UsageStatistics" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT
            type,
            SUM(app_usage_count) AS total_usage_count,
            COUNT(DISTINCT user_oid) AS total_user_count
        FROM
            p_statistics_usage psu
        WHERE
            is_delete = 1
        <if test="userOid != null and userOid!= '' ">
            and user_oid = #{userOid}
        </if>
        <if test="organizationId != null">
            and organization_id in
            (
                select id
                from p_organization
                where id = #{organizationId} or superior_ids like concat('%,', #{organizationId}, ',%')
            )
        </if>
        <if test="year != null ">
            and YEAR(create_time) = #{year}
        </if>
        <if test="month != null ">
            and MONTH(create_time) = #{month}
        </if>
        <if test="startTime != null ">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and create_time &lt;= #{endTime}
        </if>
        <if test="excludeTypes != null and excludeTypes.size() >0">
            and type not in
            <foreach collection="excludeTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY type
        ORDER BY total_usage_count DESC
        limit 10
    </select>

    <select id="findAll" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT s.app_type, s.type, SUM(s.app_usage_count) AS total_usage_count, COUNT(DISTINCT s.user_oid) AS total_user_count
        FROM p_statistics_usage s
        left join p_organization o on s.organization_id=o.id
        WHERE s.is_delete = 1
        <if test="startTime != null ">
            and s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and s.create_time &lt;= #{endTime}
        </if>
        <if test="type != null ">
            and s.type = #{type}
        </if>
        <if test="appType != null ">
            and s.app_type = #{appType}
        </if>
        <if test="month != null ">
            AND DATE_FORMAT(s.create_time, '%Y-%m') = #{month}
        </if>
        <if test="organizationId != null ">
            and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
        </if>
        GROUP BY s.app_type
        ORDER BY total_usage_count DESC
    </select>

    <select id="findAllUser" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT s.app_type, s.type, SUM(s.app_usage_count) AS total_usage_count, COUNT(DISTINCT s.user_oid) AS total_user_count
        FROM p_statistics_usage s
        left join p_organization o on s.organization_id=o.id
        WHERE s.is_delete = 1
        <if test="startTime != null ">
            and s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and s.create_time &lt;= #{endTime}
        </if>
        <if test="type != null ">
            and s.type = #{type}
        </if>
        <if test="appType != null ">
            and s.app_type = #{appType}
        </if>
        <if test="month != null ">
            AND DATE_FORMAT(s.create_time, '%Y-%m') = #{month}
        </if>
        <if test="organizationId != null ">
            and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
        </if>
        GROUP BY s.app_type
        ORDER BY total_usage_count DESC
    </select>

    <select id="findAllByType" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT s.type,s.app_type, SUM(s.app_usage_count) AS total_usage_count, COUNT(DISTINCT s.user_oid) AS total_user_count
        FROM p_statistics_usage s
        left join p_organization o on s.organization_id=o.id
        WHERE s.is_delete = 1
        <if test="startTime != null ">
            and s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and s.create_time &lt;= #{endTime}
        </if>
        <if test="type != null ">
            and s.type = #{type}
        </if>
        <if test="appType != null ">
            and s.app_type = #{appType}
        </if>
        <if test="month != null ">
            AND DATE_FORMAT(s.create_time, '%Y-%m') = #{month}
        </if>
        <if test="organizationId != null and organizationId != -999">
            and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
        </if>
        <if test="organizationId != null and organizationId == -999">
            AND s.user_oid NOT IN (
            SELECT users.oid
            FROM
            (
            SELECT org.superior_ids, pu.*
            FROM p_user pu
            LEFT JOIN p_organization org ON pu.organization_id = org.id
            ) users
            INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
            )
        </if>
        <if test="excludeTypes != null and excludeTypes.size() >0">
            and s.type not in
            <foreach collection="excludeTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY s.type
        ORDER BY total_usage_count DESC
    </select>

    <select id="findClickAllByType" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT a.type,FLOOR(a.type/100) app_type, (ifnull(SUM(s.app_usage_count),0)+a.count) AS total_usage_count, COUNT(DISTINCT s.user_oid) AS total_user_count
        FROM (SELECT type,count(1) count FROM p_behavior_record br
            left join p_user u on u.oid = br.user_oid
        <where>
            <if test="organizationId != null and organizationId != -999">
                and u.organization_id=#{organizationId}
            </if>
        </where>
        GROUP BY type  ) a
        left join p_statistics_usage s on s.type = a.type and s.is_delete = 1
        <where>
            <if test="type != null ">
                and a.type = #{type}
            </if>
            <if test="appType != null ">
                and FLOOR(a.type/100) = #{appType}
            </if>
        </where>
        GROUP BY a.type
        ORDER BY total_usage_count DESC
    </select>


    <select id="findAllUserByType" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT s.type,s.app_type, count(s.app_usage_count) AS total_usage_count, COUNT(DISTINCT s.user_oid) AS total_user_count
        FROM p_statistics_usage s
        left join p_organization o on s.organization_id=o.id
        WHERE s.is_delete = 1
        <if test="startTime != null ">
            and s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and s.create_time &lt;= #{endTime}
        </if>
        <if test="type != null ">
            and s.type = #{type}
        </if>
        <if test="appType != null ">
            and s.app_type = #{appType}
        </if>
        <if test="month != null ">
            AND DATE_FORMAT(s.create_time, '%Y-%m') = #{month}
        </if>
        <if test="organizationId != null and organizationId != -999">
            and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
        </if>
        <if test="organizationId != null and organizationId == -999">
            AND s.user_oid NOT IN (
            SELECT users.oid
            FROM
            (
            SELECT org.superior_ids, pu.*
            FROM p_user pu
            LEFT JOIN p_organization org ON pu.organization_id = org.id
            ) users
            INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
            )
        </if>
        GROUP BY s.type
        ORDER BY total_usage_count DESC
    </select>

    <select id="findFavoriteByType" resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        SELECT s.type, COUNT(DISTINCT IF(s.is_favorite = 1, s.user_oid, NULL)) AS favoriteUsers
        FROM p_statistics_usage s
        INNER JOIN (
            SELECT a.user_oid, a.type, MAX(DATE(a.create_time)) AS max_datetime
            FROM p_statistics_usage a
            WHERE a.is_favorite IS NOT NULL
            GROUP BY a.user_oid, a.type
        ) s2 ON s.user_oid = s2.user_oid AND s.type = s2.type AND DATE(s.create_time) = s2.max_datetime
        left join p_organization o on s.organization_id = o.id
        WHERE s.is_delete = 1
        <if test="startTime != null ">
            and s.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null ">
            and s.create_time &lt;= #{endTime}
        </if>
        <if test="types != null and types.size() > 0">
            and s.type in
            <foreach collection="types" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null ">
            and s.type = #{type}
        </if>
        <if test="appType != null ">
            and s.app_type = #{appType}
        </if>
        <if test="month != null ">
            AND DATE_FORMAT(s.create_time, '%Y-%m') = #{month}
        </if>
        <if test="organizationId != null and organizationId != -999">
            and (o.superior_ids like concat('%,', #{organizationId}, ',%') or o.id=#{organizationId})
        </if>
        <if test="organizationId != null and organizationId == -999">
            AND s.user_oid NOT IN (
            SELECT users.oid
            FROM
            (
            SELECT org.superior_ids, pu.*
            FROM p_user pu
            LEFT JOIN p_organization org ON pu.organization_id = org.id
            ) users
            INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
            )
        </if>
        GROUP BY s.type
    </select>

    <select id="getOrgUsageStatistics" resultType="com.fh.ai.business.entity.vo.statisticsUsage.OrgUsageStatisticsVo">
        SELECT
            org.id organizationId,
            org.`name` organizationName,
            org.`short_name` organizationShortName,
            org.sort,
            IFNULL( SUM( usages.app_usage_count ), 0 ) usageCount,
            COUNT( DISTINCT usages.user_oid ) userCount
        FROM
            p_organization org
        LEFT JOIN
        (
            SELECT po.superior_ids, psu.* FROM p_statistics_usage psu
            LEFT JOIN p_organization po ON psu.organization_id = po.id
            <where>
                po.is_delete = 1 and psu.is_delete = 1
                <if test="year != null ">
                    and YEAR(psu.create_time) = #{year}
                </if>
                <if test="month != null ">
                    and MONTH(psu.create_time) = #{month}
                </if>
                <if test="startTime != null ">
                    and psu.create_time &gt;= #{startTime}
                </if>
                <if test="endTime != null ">
                    and psu.create_time &lt;= #{endTime}
                </if>
            </where>
        ) usages ON ( usages.superior_ids LIKE CONCAT( '%,', org.id, ',%' ) OR usages.organization_id = org.id )
        <where>
            org.is_statistics = 1
            <if test="organizationId != null and organizationId != ''">and org.id = #{organizationId}</if>
        </where>
        GROUP BY
            org.id
        ORDER BY
            org.sort ASC

    </select>

    <select id="getOrgUsageTop10" resultType="com.fh.ai.business.entity.vo.statisticsUsage.OrgUsageStatisticsVo">
        SELECT
        org.id organizationId,
        org.`name` organizationName,
        org.`short_name` organizationShortName,
        org.sort,
        IFNULL( SUM( usages.app_usage_count ), 0 ) usageCount,
        COUNT( DISTINCT usages.user_oid ) userCount
        FROM
        p_organization org
        LEFT JOIN
        (
        SELECT po.superior_ids, psu.* FROM p_statistics_usage psu
        LEFT JOIN p_organization po ON psu.organization_id = po.id
        <where>
            po.is_delete = 1 and psu.is_delete = 1
            <if test="year != null ">
                and YEAR(psu.create_time) = #{year}
            </if>
            <if test="month != null ">
                and MONTH(psu.create_time) = #{month}
            </if>
            <if test="startTime != null ">
                and psu.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and psu.create_time &lt;= #{endTime}
            </if>
        </where>
        ) usages ON ( usages.superior_ids LIKE CONCAT( '%,', org.id, ',%' ) OR usages.organization_id = org.id )
        <where>
            org.is_statistics = 1
            <if test="organizationId != null and organizationId != ''">and org.id = #{organizationId}</if>
        </where>
        GROUP BY
        org.id
        ORDER BY
        usageCount DESC, org.sort ASC
        LIMIT 10

    </select>

    <select id="getSysUsageStatistics" resultType="com.fh.ai.business.entity.vo.statisticsUsage.SysUsageStatisticsVo">
        SELECT
        IFNULL( SUM( usages.app_usage_count ), 0 ) usageCount,
        COUNT( DISTINCT usages.user_oid ) userCount
        FROM p_statistics_usage usages
        LEFT JOIN p_organization po ON usages.organization_id = po.id
        <where>
            usages.is_delete = 1
            <if test="organizationId != null">
                and (po.superior_ids like concat('%,', #{organizationId}, ',%') or po.id=#{organizationId})
            </if>

            <if test="startTime != null">and usages.create_time >= #{startTime}</if>
            <if test="endTime != null">and usages.create_time &lt;= #{endTime}</if>

        </where>
    </select>

	<select id="getTop10UserUsageSStatistics"
            resultType="com.fh.ai.business.entity.vo.statisticsUsage.UsageStatisticsTotalVo">
        select  if(u.real_name is not null and u.real_name != '', u.real_name, if(u.phone is not null and u.phone != '', u.phone, u.account)) as real_name,
            SUM(app_usage_count) as total_usage_count
        from p_statistics_usage usages
        left join p_user u on u.oid = usages.user_oid
        <where>
            usages.is_delete = 1
            <if test="organizationId != null">
                and usages.organization_id in
                (
                    select id
                    from p_organization
                    where id = #{organizationId} or superior_ids like concat('%,', #{organizationId}, ',%')
                )
            </if>
        </where>
        group by u.oid
        order by total_usage_count desc
    </select>
</mapper>