<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.SystemSettingMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.systemSetting.SystemSettingDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="value" column="value"/>
        <result property="minValue" column="min_value"/>
        <result property="createTime" column="create_time"/>
        <result property="remarkTitle" column="remark_title"/>
        <result property="remark" column="remark"/>
        <result property="url" column="url"/>
    </resultMap>

	<select id="getSystemSettingListByCondition" resultType="com.fh.ai.business.entity.vo.systemSetting.SystemSettingVo">
		select t.* from (
			select a.* from p_system_setting a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="value != null and value != ''">and value = #{value}</if>
            <if test="minValue != null and minValue != ''">and min_value = #{minValue}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="remarkTitle != null and remarkTitle != ''">and remark_title = #{remarkTitle}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="url != null and url != ''">and url = #{url}</if>
        </where>
	</select>
</mapper>