<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.TaskMapper">

	<select id="getTaskListByCondition" resultType="com.fh.ai.business.entity.vo.task.TaskVo">
		select t.* from (
			select a.*,ifnull(b.count,0) joinCount,
        case
        when start_time > now() then  2
        when now() > end_time then  1
        else 3 end timeOrder
        from p_task a
			left join (SELECT task_id,count(DISTINCT user_oid) count FROM `p_user_task` where is_delete = 1 group by task_id) b on
			b.task_id = a.id
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="name != null and name != ''">and name LIKE concat('%', #{name}, '%')</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="secondType != null and secondType != ''">and second_type = #{secondType}</if>
            <if test="appTypes != null and appTypes != ''">and app_types = #{appTypes}</if>
            <if test="cover != null and cover != ''">and cover = #{cover}</if>
            <if test="startTime != null">and start_time = #{startTime}</if>

            <if test="searchStartTime != null and searchEndTime != null">
                and (
                (start_time >= #{searchStartTime} and start_time &lt;= #{searchEndTime})
                or (end_time >= #{searchStartTime} and end_time &lt;= #{searchEndTime})
                )
            </if>

            <if test="searchStartTime != null and searchEndTime == null">
                and start_time >= #{searchStartTime}
            </if>

            <if test="searchStartTime == null and searchEndTime != null">
                and end_time &lt;= #{searchEndTime}
            </if>

            <if test="endTime != null">and end_time = #{endTime}</if>
            <if test="score != null and score != ''">and score = #{score}</if>
            <if test="details != null and details != ''">and details = #{details}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="getMyTaskListByCondition" resultType="com.fh.ai.business.entity.vo.task.TaskVo">
        select t.* from (
        select a.*,
        case
        when a.start_time > now() then  2
        when now() > a.end_time then  1
        else 3 end timeOrder
        from  p_task a
        where  a.is_delete = 1 and a.state =2 and a.id in (
        SELECT DISTINCT task_id FROM  `p_user_task` WHERE `user_oid` = #{userOid}
         AND is_delete = 1 )
        ) t
    </select>

</mapper>