<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.TipMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.tip.TipDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="appType" column="app_type"/>
        <result property="remark" column="remark"/>
        <result property="publishTime" column="publish_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="categoryType" column="category_type"/>
        <result property="tagType" column="tag_type"/>
        <result property="comment" column="comment"/>
    </resultMap>

	<select id="getTipListByCondition" resultType="com.fh.ai.business.entity.vo.tip.TipVo">
		select t.* from (
			select a.* from p_tip as a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="type != null ">and type = #{type}</if>
				<if test="appType != null ">and app_type = #{appType}</if>
				<if test="remark != null and remark != ''">and remark = #{remark}</if>
				<if test="publishTime != null and publishTime != ''">and publish_time = #{publishTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null ) or isDelete == 0">and is_delete = #{isDelete}</if>
				<if test="categoryType != null ">and category_type = #{categoryType}</if>
				<if test="tagType != null ">and tag_type = #{tagType}</if>
				<if test="comment != null ">and comment = #{comment}</if>
		    </where>
	</select>
</mapper>