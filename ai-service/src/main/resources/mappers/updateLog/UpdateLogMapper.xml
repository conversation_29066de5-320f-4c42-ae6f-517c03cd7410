<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UpdateLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.updateLog.UpdateLogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="versionName" column="version_name"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="imageUrl" column="image_url"/>
        <result property="jumpUrl" column="jump_url"/>
        <result property="state" column="state"/>
        <result property="publishTime" column="publish_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getUpdateLogListByCondition" resultType="com.fh.ai.business.entity.vo.updateLog.UpdateLogVo">
		select t.* from (
			select a.* from p_update_log as a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="versionName != null and versionName != ''">and version_name = #{versionName}</if>
				<if test="title != null and title != ''">and title = #{title}</if>
				<if test="content != null and content != ''">and content = #{content}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="jumpUrl != null and jumpUrl != ''">and jump_url = #{jumpUrl}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="type != null and type != ''">and (type = #{type} or type = '3')</if>
				<if test="publishTime != null and publishTime != ''">and publish_time = #{publishTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<update id="updateUpdateLog">
		update p_update_log set is_delete = 2 , version_name = concat(id,'_',version_name,'_delete') where id = #{id}
	</update>

	<select id="check" resultType="int">
		SELECT count(1) FROM p_update_log_read
		where user_oid = #{oid} and update_log_id= #{id} and is_delete = 1
	</select>

	<insert id="read">
		insert into p_update_log_read(`user_oid`, `update_log_id`,`create_by`)
		 VALUES (#{oid}, #{id},#{oid})
	</insert>
</mapper>