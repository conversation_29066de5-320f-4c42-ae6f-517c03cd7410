<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.user.UserDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="account" column="account"/>
        <result property="password" column="password"/>
        <result property="phone" column="phone"/>
        <result property="realName" column="real_name"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="growth" column="growth"/>
        <result property="isLocked" column="is_locked"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getUserListByCondition" resultType="com.fh.ai.business.entity.vo.user.UserVo">
        select t.* from (
            SELECT
            pu.*,
            po.`name` AS organizationName,
            po.superior_ids,b.count AS loginTime
            FROM
            p_user pu
            LEFT JOIN p_organization po ON pu.organization_id = po.id
        LEFT JOIN (SELECT user_oid,count(1) count FROM `p_user_score_record`
        where type = 1
        <if test="startTime != null ">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null ">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY user_oid
        ) b ON pu.oid = b.user_oid

        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="account != null and account != ''">and account = #{account}</if>
            <if test="password != null and password != ''">and password = #{password}</if>
            <if test="phone != null and phone != ''">and phone = #{phone}</if>
            <if test="realName != null and realName != ''">and real_name = #{realName}</if>
            <if test="lastLoginTime != null and lastLoginTime != ''">and last_login_time = #{lastLoginTime}</if>
            <if test="growth != null and growth != ''">and growth = #{growth}</if>
            <if test="quota != null and quota != ''">and quota = #{quota}</if>
            <if test="organizationId != null">
                and (superior_ids like concat('%,', #{organizationId}, ',%') or organization_id = #{organizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (superior_ids like concat('%,', #{searchOrganizationId}, ',%') or organization_id=#{searchOrganizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId == -999">
                AND oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>

            <if test="keyword != null and keyword != ''">
                AND ( real_name LIKE concat('%', #{keyword}, '%') OR account LIKE concat('%', #{keyword}, '%') )
            </if>
            <if test="searchWord != null and searchWord != ''">
                and (account like concat('%', #{searchWord}, '%') or phone like concat('%', #{searchWord}, '%') )
            </if>
            <if test="isLocked != null and isLocked != ''">and is_locked = #{isLocked}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
        </where>
        order by growth desc
    </select>

    <select id="checkUserPhone" resultType="java.lang.Integer">
        select count(1)
        from p_user
        <where>
            account != #{account}
            and phone = #{phone}
            and is_delete = 1
        </where>
    </select>

    <select id="getUserListWithUsageCount" resultType="com.fh.ai.business.entity.vo.user.UserVo">
        select t.*, ifnull(x.usageCount,0) + ifnull(y.usageCount,0) as usageCount from (
            SELECT
            pu.*,
            po.`name` AS organizationName,
            po.superior_ids,b.count AS loginTime
            FROM
            p_user pu
            LEFT JOIN p_organization po ON pu.organization_id = po.id
            LEFT JOIN (SELECT user_oid,count(1) count FROM `p_user_visit`
            where type = 1
            <if test="startTime != null ">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null ">
                AND create_time &lt;= #{endTime}
            </if>
            GROUP BY user_oid
            ) b ON pu.oid = b.user_oid
        ) t
        left join (
            SELECT
            ha.user_oid,
            COUNT(*) AS usageCount
            FROM
            p_history_app ha
        <where>
            is_delete = 1
            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>
        </where>
            GROUP BY
            ha.user_oid
        ) x on x.user_oid = t.oid
        left join (
            SELECT
            ha.user_oid,
            COUNT(*) AS usageCount
            FROM
            p_mt_history ha
            <where>
                 is_delete = 1
                <if test="startTime != null">and create_time >= #{startTime}</if>
                <if test="endTime != null">and create_time &lt;= #{endTime}</if>
            </where>
            GROUP BY
            ha.user_oid
        ) y on t.oid = y.user_oid
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="account != null and account != ''">and account = #{account}</if>
            <if test="password != null and password != ''">and password = #{password}</if>
            <if test="phone != null and phone != ''">and phone = #{phone}</if>
            <if test="realName != null and realName != ''">and real_name = #{realName}</if>
            <if test="lastLoginTime != null and lastLoginTime != ''">and last_login_time = #{lastLoginTime}</if>
            <if test="growth != null and growth != ''">and growth = #{growth}</if>
            <if test="quota != null and quota != ''">and quota = #{quota}</if>
            <if test="organizationId != null">
                and (superior_ids like concat('%,', #{organizationId}, ',%') or organization_id = #{organizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (superior_ids like concat('%,', #{searchOrganizationId}, ',%') or organization_id=#{searchOrganizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId == -999">
                AND oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>

            <if test="keyword != null and keyword != ''">
                AND ( real_name LIKE concat('%', #{keyword}, '%') OR account LIKE concat('%', #{keyword}, '%') )
            </if>
            <if test="searchWord != null and searchWord != ''">
                and (account like concat('%', #{searchWord}, '%') or phone like concat('%', #{searchWord}, '%') )
            </if>
            <if test="isLocked != null and isLocked != ''">and is_locked = #{isLocked}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
        </where>
        order by ifnull(x.usageCount,0) + ifnull(y.usageCount,0) desc, create_time desc
    </select>

    <select id="getUserAccountCountByOrganizationIds" resultType="com.fh.ai.business.entity.vo.user.UserAccountCountVo">
        select organization_id,
            count(1) as accountNum
        from p_user
        <where>
            is_delete = 1
            <if test="organizationIds != null and organizationIds.size() != 0">
                and organization_id in
                <foreach collection="organizationIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by organization_id
    </select>
</mapper>