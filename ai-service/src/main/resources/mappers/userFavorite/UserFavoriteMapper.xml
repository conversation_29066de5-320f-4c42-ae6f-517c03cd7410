<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserFavoriteMapper">

	<select id="getUserFavoriteListByCondition" resultType="com.fh.ai.business.entity.vo.userFavorite.UserFavoriteVo">
		select t.* from (
			select a.* from p_user_favorite a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="findLatestFavoriteByType" resultType="com.fh.ai.business.entity.vo.userFavorite.UserFavoriteVo">
        SELECT s.*
        FROM p_user_favorite s
        INNER JOIN (
            SELECT a.user_oid, a.type, MAX(a.create_time) AS max_datetime
            FROM p_user_favorite a
            GROUP BY a.user_oid, a.type
        ) s2 ON s.user_oid = s2.user_oid AND s.type = s2.type AND s.create_time = s2.max_datetime
        <where>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="startTime != null ">
                and s.update_time &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and s.update_time &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>