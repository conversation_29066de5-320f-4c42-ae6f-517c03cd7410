<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserFeedbackMapper">

	<select id="getUserFeedbackListByCondition" resultType="com.fh.ai.business.entity.vo.userFeedback.UserFeedbackVo">
		select t.* from (
        SELECT
        puf.*,
        pu.account,
        pu.real_name,
        po.`name` AS organizationName,
        po.superior_ids
        FROM
        p_user_feedback puf
        LEFT JOIN p_user pu ON puf.user_oid = pu.oid
        LEFT JOIN p_organization po ON puf.organization_id = po.id
		) t
	    <where>
            <if test="keyword != null and keyword != ''">
                AND ( real_name LIKE concat('%', #{keyword}, '%') OR account LIKE concat('%', #{keyword}, '%') )
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (superior_ids like concat('%,', #{searchOrganizationId}, ',%') or organization_id=#{searchOrganizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId == -999">
                AND user_oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>
            <if test="createStartTime != null">and create_time >= #{createStartTime}</if>
            <if test="createEndTime != null">and create_time &lt;= #{createEndTime}</if>

            <if test="giveStartTime != null">and give_time >= #{giveStartTime}</if>
            <if test="giveEndTime != null">and give_time &lt;= #{giveEndTime}</if>

            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="content != null and content != ''">and content = #{content}</if>
            <if test="giveScore != null and giveScore != ''">and give_score = #{giveScore}</if>
            <if test="giveTime != null and giveTime != ''">and give_time = #{giveTime}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="type != null">and type = #{type}</if>
            <if test="appType != null">and app_type = #{appType}</if>
        </where>
	</select>
</mapper>