<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserInstructionMapper">
    <resultMap id="BaseResultMap" type="com.fh.ai.business.entity.dto.userInstruction.UserInstructionDto">
        <result property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="userOid" column="user_oid"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="commonSelect">
        select
            id
            ,uuid
            ,user_oid
            ,title
            ,content
            ,create_time
            ,create_by
            ,update_time
            ,update_by
            ,is_delete
        from p_user_instruction
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="uuid != null and uuid != ''">and uuid = #{uuid}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="title != null and title != ''">and title = #{title}</if>
            <if test="content != null and content != ''">and content = #{content}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <select id="getUserInstructionListByCondition"
            resultType="com.fh.ai.business.entity.vo.userInstruction.UserInstructionVo">
        <include refid="commonSelect"/>
        <if test="orderBy == null or orderBy == ''">
            order by create_time desc
        </if>
    </select>

    <select id="getUserInstructionByOid" resultType="com.fh.ai.business.entity.vo.userInstruction.UserInstructionVo">
        <include refid="commonSelect">
        </include>
        limit 1
    </select>
</mapper>