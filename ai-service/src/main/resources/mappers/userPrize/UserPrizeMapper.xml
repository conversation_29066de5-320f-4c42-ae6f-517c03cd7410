<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserPrizeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.userPrize.UserPrizeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="prizeId" column="prize_id"/>
        <result property="redeemCode" column="redeem_code"/>
        <result property="score" column="score"/>
        <result property="notes" column="notes"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<select id="getUserPrizeListByCondition" resultType="com.fh.ai.business.entity.vo.userPrize.UserPrizeVo">
		select t.* from (
            SELECT
            pup.*,
            pu.account,
            pu.real_name,
            po.`name` AS organizationName,
            po.superior_ids
            FROM
            p_user_prize pup
            LEFT JOIN p_user pu ON pup.user_oid = pu.oid
            LEFT JOIN p_organization po ON pu.organization_id = po.id
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="orderNo != null and orderNo != ''">and order_no = #{orderNo}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="prizeId != null">and prize_id = #{prizeId}</if>
            <if test="redeemCode != null and redeemCode != ''">and redeem_code = #{redeemCode}</if>
            <if test="score != null">and score = #{score}</if>
            <if test="notes != null and notes != ''">and notes = #{notes}</if>
            <if test="state != null">and state = #{state}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>

            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (superior_ids like concat('%,', #{searchOrganizationId}, ',%') or organization_id=#{searchOrganizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId == -999">
                AND user_oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>

            <if test="keyword != null and keyword != ''">
                AND ( real_name LIKE concat('%', #{keyword}, '%') OR account LIKE concat('%', #{keyword}, '%') )
            </if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>
        </where>
        order by create_time desc
	</select>

    <select id="getUserPrizeTotal" resultType="com.fh.ai.business.entity.vo.userPrize.UserPrizeTotalVo">
        SELECT
            COUNT(pup.id) redeemCount,
            SUM(pup.score) redeemScore
        FROM
        p_user_prize pup
        LEFT JOIN p_user pu ON pup.user_oid = pu.oid
        LEFT JOIN p_organization po ON pu.organization_id = po.id
        <where>
            <if test="id != null and id != ''">and pup.id = #{id}</if>
            <if test="orderNo != null and orderNo != ''">and pup.order_no = #{orderNo}</if>
            <if test="userOid != null and userOid != ''">and pup.user_oid = #{userOid}</if>
            <if test="prizeId != null">and pup.prize_id = #{prizeId}</if>
            <if test="redeemCode != null and redeemCode != ''">and pup.redeem_code = #{redeemCode}</if>
            <if test="score != null">and pup.score = #{score}</if>
            <if test="notes != null and notes != ''">and pup.notes = #{notes}</if>
            <if test="state != null">and pup.state = #{state}</if>
            <if test="createBy != null and createBy != ''">and pup.create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and pup.create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and pup.update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and pup.update_time = #{updateTime}</if>

            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (po.superior_ids like concat('%,', #{searchOrganizationId}, ',%') or po.id=#{searchOrganizationId})
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId == -999">
                AND pup.user_oid NOT IN (
                SELECT users.oid
                FROM
                (
                SELECT org.superior_ids, pu.*
                FROM p_user pu
                LEFT JOIN p_organization org ON pu.organization_id = org.id
                ) users
                INNER JOIN ( SELECT * FROM p_organization o WHERE o.is_statistics = 1 ) statistics ON users.superior_ids LIKE CONCAT( '%,', statistics.id, ',%' )
                )
            </if>

            <if test="keyword != null and keyword != ''">
                AND ( pu.real_name LIKE concat('%', #{keyword}, '%') OR pu.account LIKE concat('%', #{keyword}, '%') )
            </if>

            <if test="startTime != null">and pup.create_time >= #{startTime}</if>
            <if test="endTime != null">and pup.create_time &lt;= #{endTime}</if>
        </where>
    </select>

    <select id="getUserRedeemCount" resultType="com.fh.ai.business.entity.vo.historyApp.UserUsageVo">
        SELECT
        up.user_oid,
        COUNT(*) AS redeemCount
        FROM
        p_user_prize up
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="orderNo != null and orderNo != ''">and order_no = #{orderNo}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>

            <if test="userOidList != null and userOidList.size() > 0">
                and user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="prizeId != null and prizeId != ''">and prize_id = #{prizeId}</if>
            <if test="redeemCode != null and redeemCode != ''">and redeem_code = #{redeemCode}</if>
            <if test="score != null and score != ''">and score = #{score}</if>
            <if test="notes != null and notes != ''">and notes = #{notes}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>
        </where>
        GROUP BY
        up.user_oid
    </select>
</mapper>