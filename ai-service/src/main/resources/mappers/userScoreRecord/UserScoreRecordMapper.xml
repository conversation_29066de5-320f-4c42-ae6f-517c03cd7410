<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserScoreRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.userScoreRecord.UserScoreRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="type" column="type"/>
        <result property="relationId" column="relation_id"/>
        <result property="score" column="score"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<select id="getUserScoreRecordListByCondition" resultType="com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo">
		select t.* from (
			select a.* from p_user_score_record a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="relationId != null and relationId != ''">and relation_id = #{relationId}</if>
            <if test="score != null and score != ''">and score = #{score}</if>

            <if test="searchType != null and searchType == 1">and score >= 0</if>

            <if test="searchType != null and searchType == 2">and score &lt; 0</if>

            <if test="receiveTime != null">and date_format(create_time,'%y%m%d') = date_format(#{receiveTime},'%y%m%d')</if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>

            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
        </where>
	</select>

    <select id="getScoreTypeStatistics" resultType="com.fh.ai.business.entity.vo.userScoreRecord.ScoreTypeStatisticsVo">
        SELECT
        pusr.type,
        SUM( pusr.score ) AS totalScore,
        COUNT(*) as totalCount
        FROM
        `p_user_score_record` pusr
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="relationId != null and relationId != ''">and relation_id = #{relationId}</if>
            <if test="score != null and score != ''">and score = #{score}</if>

            <if test="receiveTime != null">and date_format(create_time,'%y%m%d') = date_format(#{receiveTime},'%y%m%d')</if>

            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
        </where>
        GROUP BY
        pusr.type
    </select>

    <select id="getUserScore" resultType="com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo">
        SELECT
        SUM( CASE WHEN pusr.score &lt; 0 THEN 0 ELSE pusr.score END ) AS growth,
        SUM( pusr.score ) AS score
        FROM
        `p_user_score_record` pusr
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="relationId != null and relationId != ''">and relation_id = #{relationId}</if>
            <if test="score != null and score != ''">and score = #{score}</if>

            <if test="receiveTime != null">and date_format(create_time,'%y%m%d') = date_format(#{receiveTime},'%y%m%d')</if>

            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="getLastLoginRecordList" resultType="com.fh.ai.business.entity.vo.userScoreRecord.UserScoreRecordVo">
        SELECT t1.*
        FROM p_user_score_record t1
        JOIN (
            SELECT pusr.user_oid, MAX(pusr.create_time) AS create_time
            FROM p_user_score_record pusr
            WHERE pusr.type = 1
            GROUP BY pusr.user_oid
        ) t2 ON t1.user_oid = t2.user_oid AND t1.create_time = t2.create_time
        <where>
            <if test="id != null and id != ''">and t1.id = #{id}</if>
            <if test="userOid != null and userOid != ''">and t1.user_oid = #{userOid}</if>
            <if test="userOidList != null and userOidList.size() > 0">
                and t1.user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="startTime != null">and t1.create_time >= #{startTime}</if>
            <if test="endTime != null">and t1.create_time &lt;= #{endTime}</if>
        </where>
    </select>

    <select id="getUserScoreList" resultType="com.fh.ai.business.entity.vo.userScoreRecord.UserScoreVo">
        SELECT
        pusr.user_oid,
        SUM( CASE WHEN pusr.score &lt; 0 THEN 0 ELSE pusr.score END ) AS growth,
        SUM( pusr.score ) AS score
        FROM
        `p_user_score_record` pusr
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="relationId != null and relationId != ''">and relation_id = #{relationId}</if>
            <if test="score != null and score != ''">and score = #{score}</if>

            <if test="receiveTime != null">and date_format(create_time,'%y%m%d') = date_format(#{receiveTime},'%y%m%d')</if>

            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>

            <if test="userOidList != null and userOidList.size() > 0">
                and user_oid in
                <foreach collection="userOidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="startTime != null">and create_time >= #{startTime}</if>
            <if test="endTime != null">and create_time &lt;= #{endTime}</if>
        </where>
        GROUP BY
        pusr.user_oid
    </select>
</mapper>