<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserStorageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.userStorage.UserStorageDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="stKey" column="st_key"/>
        <result property="stValue" column="st_value"/>
        <result property="channel" column="channel"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="oid != null and oid != '' ">and oid like concat('%', #{oid}, '%')</if>
			<if test="stKey != null and stKey != '' ">and st_key like concat('%', #{stKey}, '%')</if>
			<if test="stValue != null and stValue != '' ">and st_value like concat('%', #{stValue}, '%')</if>
			<if test="channel != null ">and channel = #{channel}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.oid
	 		,t.st_key
	 		,t.st_value
	 		,t.channel
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_user_storage a
		 ) t

	</sql>

	<select id="getUserStorageListByCondition" resultType="com.fh.ai.business.entity.vo.userStorage.UserStorageVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getUserStorageByCondition" resultType="com.fh.ai.business.entity.vo.userStorage.UserStorageVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>