<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserTaskMapper">

	<select id="getUserTaskListByCondition" resultType="com.fh.ai.business.entity.vo.userTask.UserTaskVo">
        select t.* from (
        select a.*,ifnull(pt.num,0) num,po.superior_ids,pu.account,pu.real_name ,pusr.create_time scoreTime from
        (select b.*,count(b.task_id) userNum  from p_user_task b where b.is_delete = 1 group by user_oid,task_id) a
        left join p_task pt on a.task_id = pt.id
        left join p_user pu on a.user_oid = pu.oid
        LEFT JOIN p_organization po ON pu.organization_id = po.id
        left join p_user_score_record pusr on pusr.user_oid = pu.oid and pusr.type = 8 and pusr.relation_id = a.task_id
        ) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="searchName != null and searchName != ''">and (account like concat('%', #{searchName}, '%') or real_name like concat('%', #{searchName}, '%')) </if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
            <if test="state != null and state != ''">and state = #{state}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
            <if test="progress != null ">
                <if test="progress == 1">
                   and num = userNum
                </if>
                <if test="progress == 2">
                    and num > userNum
                </if>
            </if>
            <if test="searchOrganizationId != null and searchOrganizationId != -999">
                and (superior_ids like concat('%,', #{searchOrganizationId}, ',%') or organization_id=#{searchOrganizationId})
            </if>
        </where>
	</select>

    <select id="noPass" resultType="integer">
       select ifnull(a.num,0) from (
       select COUNT(DISTINCT b.task_id) num  from p_user_task b
       left join p_task pt on b.task_id = pt.id
       where b.is_delete = 1 and b.state = 3 and pt.is_delete =1 and pt.state =2
       and b.user_oid = #{oid}
    ) a

    </select>
</mapper>