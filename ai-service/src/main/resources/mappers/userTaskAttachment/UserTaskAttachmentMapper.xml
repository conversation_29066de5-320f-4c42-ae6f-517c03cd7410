<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserTaskAttachmentMapper">

	<select id="getUserTaskAttachmentListByCondition" resultType="com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo">
		select t.* from (
			select a.* from p_user_task_attachment a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
            <if test="attachmentOid != null and attachmentOid != ''">and attachment_oid = #{attachmentOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="isDelete != null and isDelete != ''">and is_delete = #{isDelete}</if>
        </where>
	</select>

    <select id="getUserTaskAttachmentInfoListByCondition" resultType="com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo">
        select t.* from (
        select a.*,
        pa.oid,
        pa.original_name,
        pa.suffix,
        pa.size,
        pa.cover,
        pa.origin_path,
        pa.view_path,
        pa.upload_state,
        pa.state,
        pa.note
        from p_user_task_attachment a left join  p_attachment pa
        on pa.oid = a.attachment_oid
        ) t
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
            <if test="attachmentOid != null and attachmentOid != ''">and attachment_oid = #{attachmentOid}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="remark != null and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
        </where>
    </select>

    <select id="getUserTaskAttachmentListByTask" resultType="com.fh.ai.business.entity.vo.userTaskAttachment.UserTaskAttachmentVo">
        select t.* from (
        select a.*,pu.real_name,
        pa.oid,
        pa.original_name,
        pa.suffix,
        pa.size,
        pa.cover,
        pa.origin_path,
        pa.view_path,
        pa.upload_state,
        pa.state,
        pa.note
        from p_user_task_attachment a
        left join p_user pu on a.user_oid = pu.oid
         left join  p_attachment pa
        on pa.oid = a.attachment_oid
        ) t
        <where>
             task_id = #{taskId} and is_delete = 1
        </where>
    </select>

</mapper>