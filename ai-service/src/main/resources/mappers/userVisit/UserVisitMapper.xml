<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.UserVisitMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.userVisit.UserVisitDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="type" column="type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<select id="getUserVisitListByCondition" resultType="com.fh.ai.business.entity.vo.userVisit.UserVisitVo">
		select t.* from (
			select a.* from p_user_visit a
		) t
	    <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
            <if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="loginVisitDay != null">and date_format(create_time,'%y%m%d') = date_format(#{loginVisitDay},'%y%m%d')</if>
        </where>
	</select>
</mapper>