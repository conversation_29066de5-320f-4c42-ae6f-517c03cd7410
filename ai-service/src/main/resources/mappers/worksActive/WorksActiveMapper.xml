<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.WorksActiveMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.worksActive.WorksActiveDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="activeStatus" column="active_status"/>
        <result property="activeName" column="active_name"/>
        <result property="activeProfile" column="active_profile"/>
        <result property="activeStartTime" column="active_start_time"/>
        <result property="activeEndTime" column="active_end_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
		<result property="isSupportVote" column="is_support_vote"/>
		<result property="voteStartTime" column="vote_start_time"/>
		<result property="voteEndTime" column="vote_end_time"/>
		<result property="dailyVoteLimit" column="daily_vote_limit"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="activeStatus != null ">and active_status = #{activeStatus}</if>
			<if test="activeName != null and activeName != '' ">and active_name like concat('%', #{activeName}, '%')</if>
			<if test="activeProfile != null and activeProfile != '' ">and active_profile like concat('%', #{activeProfile}, '%')</if>
			<if test="activeStartTime != null ">and active_start_time = #{activeStartTime}</if>
			<if test="activeEndTime != null ">and active_end_time = #{activeEndTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="isSupportVote != null ">and is_support_vote = #{isSupportVote}</if>
			<if test="voteStartTime != null ">and vote_start_time = #{voteStartTime}</if>
			<if test="voteEndTime != null ">and vote_end_time = #{voteEndTime}</if>
			<if test="dailyVoteLimit != null ">and daily_vote_limit = #{dailyVoteLimit}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="activeName != null and activeName != '' ">and active_name like concat('%', #{activeName}, '%')</if>
			<if test="activeProfile != null and activeProfile != '' ">and active_profile like concat('%', #{activeProfile}, '%')</if>
			<if test="activeStartTime != null ">and active_start_time = #{activeStartTime}</if>
			<if test="activeEndTime != null ">and active_end_time = #{activeEndTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="isSupportVote != null ">and is_support_vote = #{isSupportVote}</if>
			<if test="voteStartTime != null ">and vote_start_time = #{voteStartTime}</if>
			<if test="voteEndTime != null ">and vote_end_time = #{voteEndTime}</if>
			<if test="dailyVoteLimit != null ">and daily_vote_limit = #{dailyVoteLimit}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.active_status
	 		,t.active_name
	 		,t.active_profile
	 		,t.active_start_time
	 		,t.active_end_time
	 		,t.create_by
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
			,t.is_support_vote
			,t.vote_start_time
			,t.vote_end_time
			,t.daily_vote_limit
		from (
			select a.* from p_works_active a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getWorksActiveListByCondition" resultType="com.fh.ai.business.entity.vo.worksActive.WorksActiveVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		 order by t.create_time desc
	</select>

	<select id="getWorksActiveByCondition" resultType="com.fh.ai.business.entity.vo.worksActive.WorksActiveVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>