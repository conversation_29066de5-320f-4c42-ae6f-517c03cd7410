<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.WorksActiveVoteRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.worksActiveVoteRecord.WorksActiveVoteRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
		<result property="worksActiveId" column="works_active_id"/>
		<result property="excellentWorksId" column="excellent_works_id"/>
		<result property="voteNumber" column="vote_number"/>
		<result property="voteTime" column="vote_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null">
				AND id = #{id}
			</if>
			<if test="userOid != null and userOid != ''">
				AND user_oid = #{userOid}
			</if>
			<if test="worksActiveId != null">
				AND works_active_id = #{worksActiveId}
			</if>
			<if test="excellentWorksId != null">
				AND excellent_works_id = #{excellentWorksId}
			</if>
			<if test="voteNumber != null">
				AND vote_number = #{voteNumber}
			</if>
			<if test="voteTime != null">
				AND vote_time = #{voteTime}
			</if>
			<if test="createTime != null">
				AND create_time = #{createTime}
			</if>
			<if test="createBy != null and createBy != ''">
				AND create_by = #{createBy}
			</if>
			<if test="updateTime != null">
				AND update_time = #{updateTime}
			</if>
			<if test="updateBy != null and updateBy != ''">
				AND update_by = #{updateBy}
			</if>
			<if test="isDelete != null">
				AND is_delete = #{isDelete}
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id,
			t.user_oid,
			t.works_active_id,
			t.excellent_works_id,
			t.vote_number,
			t.vote_time,
			t.create_time,
			t.create_by,
			t.update_time,
			t.update_by,
			t.is_delete
		from (
			select a.* from p_works_active_vote_record a
		 ) t

	</sql>

	<select id="getWorksActiveVoteRecordListByCondition" resultType="com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getWorksActiveVoteRecordByCondition" resultType="com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <select id="getWorksActiveVoteCountByUserOid" resultType="java.lang.Integer">
		select ifnull(sum(vote_number), 0)
		from p_works_active_vote_record
		<where>
			is_delete = 1
			and works_active_id = #{worksActiveId}
			and user_oid = #{userOid}
			and date_format(vote_time, '%y%m%d') = date_format(now(), '%y%m%d')
		</where>
	</select>

    <select id="getWorksActiveVoteCount"
			resultType="com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteCountVo">
		select ifnull(sum(vote_number), 0) as voteCount,
			   count(distinct user_oid) as voteUserCount
		from p_works_active_vote_record
		<where>
			is_delete = 1
			and works_active_id = #{worksActiveId}
		</where>
	</select>

    <select id="getWorksActiveVoteRank"
			resultType="com.fh.ai.business.entity.vo.worksActiveVoteRecord.WorksActiveVoteRankVo">
		select excellent_works_id,
			   ifnull(sum(vote_number), 0) as voteNumber
		from p_works_active_vote_record vr
		join p_excellent_works ew on vr.excellent_works_id = ew.id
		<where>
			vr.is_delete = 1
			and ew.is_delete = 1
			and vr.works_active_id = #{worksActiveId}
			and ew.hold_type = 2
			<if test="startDay != null">and date_format(vote_time, '%Y-%m-%d') >= date_format(#{startDay}, '%Y-%m-%d')</if>
			<if test="endDay != null">and date_format(vote_time, '%Y-%m-%d') &lt;= date_format(#{endDay}, '%Y-%m-%d')</if>
		</where>
		group by vr.excellent_works_id
		order by ifnull(sum(vr.vote_number), 0) desc, ew.excellent_works_number asc, ew.id
	</select>
</mapper>