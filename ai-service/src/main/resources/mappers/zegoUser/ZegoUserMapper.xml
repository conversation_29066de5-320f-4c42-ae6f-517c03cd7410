<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.ai.business.mapper.ZegoUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.ai.business.entity.dto.zegoUser.ZegoUserDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="type" column="type"/>
        <result property="result" column="result"/>
        <result property="isFavorite" column="is_favorite"/>
        <result property="channel" column="channel"/>
        <result property="params" column="params"/>
        <result property="historyId" column="history_id"/>
        <result property="state" column="state"/>
        <result property="taskId" column="task_id"/>
        <result property="appType" column="app_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getZegoUserListByCondition" resultType="com.fh.ai.business.entity.vo.zegoUser.ZegoUserVo">
		select t.* from (
			select a.* from p_zego_user as a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
				<if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
				<if test="result != null and result != ''">and result = #{result}</if>
				<if test="isFavorite != null and isFavorite != ''">and is_favorite = #{isFavorite}</if>
				<if test="channel != null and channel != ''">and channel = #{channel}</if>
				<if test="params != null and params != ''">and params = #{params}</if>
				<if test="historyId != null and historyId != ''">and history_id = #{historyId}</if>
				<if test="state != null and state != ''">and state = #{state}</if>
				<if test="taskId != null and taskId != ''">and task_id = #{taskId}</if>
				<if test="appType != null and appType != ''">and app_type = #{appType}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
				<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>